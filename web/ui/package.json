{"name": "@tencent/tcic-web-ui", "version": "1.8.21", "description": "桌面端离线包", "main": "./main.js", "types": "./main.d.ts", "files": ["main.js", "main.d.ts", "dist", "!dist/cache", "cache/tiwcache.json"], "scripts": {"pack-version": "pnpm version prerelease --preid commit-$(git rev-parse --short HEAD)", "jq": "sh ./scripts/jq.sh", "version": "node setVersion.js", "prebuild": "./cache/build.sh", "build": "npm run scan-check && webpack --config webpack.config.build.js", "dev": "webpack-dev-server --progress --public localhost:8080 --watch --open --config webpack.config.dev.js", "tv-dev": "webpack --config webpack.config.tv-dev.js", "test": "cd test && open-cli 'http://localhost:8080/login.html?debugcss=http://localhost:8081/myLib.css&debugjs=http://localhost:8081/myLib.umd.js' && live-server --port=8081 --no-browser", "storybook": "start-storybook -p 6006", "build-storybook": "build-storybook", "pre-commit": "npm run scan-check && npm run eslint-nofix", "eslint": "eslint ./src --ext .js,.vue --fix", "eslint-nofix": "eslint ./src --ext .js,.vue", "scan-class": "i18next-scanner --config i18next-scanner-class.config.js", "scan-login": "i18next-scanner --config i18next-scanner-login.config.js", "scan-externalCourseware": "i18next-scanner --config i18next-scanner-externalCourseware.config.js", "scan": "npm run scan-class && npm run scan-login", "scan-check": "npm run scan && node i18next-check.js"}, "repository": {"type": "git", "url": "https://git.woa.com/kennethmiao/apaas-sdk.git"}, "author": "", "license": "ISC", "devDependencies": {"@babel/cli": "7.14.8", "@babel/core": "7.14.8", "@babel/plugin-transform-runtime": "^7.13.10", "@babel/preset-env": "7.14.8", "@babel/runtime-corejs3": "^7.13.10", "@panter/vue-i18next": "^0.15.2", "@storybook/addon-a11y": "^6.5.15", "@storybook/addon-actions": "^6.5.15", "@storybook/addon-controls": "^6.5.15", "@storybook/addon-essentials": "^6.5.15", "@storybook/addon-links": "^6.5.15", "@storybook/addon-notes": "^5.3.21", "@storybook/addon-storysource": "^6.5.15", "@storybook/vue": "6.5.15", "@tencent/eslint-config-tencent": "^0.13.3", "@tweenjs/tween.js": "^21.0.0", "@types/semver": "^7.5.0", "@vue/babel-helper-vue-jsx-merge-props": "1.0.0", "@vue/babel-preset-jsx": "1.1.2", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-standard": "^5.1.2", "@vue/runtime-dom": "^3.5.13", "babel-eslint": "^10.1.0", "babel-loader": "8.1.0", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "1.13.1", "cache-loader": "^4.1.0", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "6.1.0", "core-js": "^3.33.2", "cross-env": "^7.0.3", "css-loader": "4.3.0", "eslint": "7.27.0", "eslint-loader": "4.0.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "9.32.0", "eslint-webpack-plugin": "^2.7.0", "file-loader": "6.1.0", "file-saver": "^2.0.5", "friendly-errors-webpack-plugin": "^1.7.0", "glob": "7.1.6", "hard-source-webpack-plugin": "^0.13.1", "html-replace-webpack-plugin": "^2.6.0", "html-webpack-plugin": "4.5.0", "i18next-scanner": "^4.2.0", "idb-keyval": "^6.0.3", "less": "3.12.2", "less-loader": "7.0.1", "live-server": "^1.2.1", "mini-css-extract-plugin": "0.11.2", "open-cli": "^6.0.1", "optimize-css-assets-webpack-plugin": "^5.0.4", "portfinder": "^1.0.28", "pretty-bytes": "5.4.1", "purgecss-webpack-plugin": "^4.0.3", "script-loader": "^0.7.2", "speed-measure-webpack-plugin": "^1.5.0", "storybook": "^6.4.1", "storybook-mobile": "^1.0.0", "style-loader": "1.2.1", "thread-loader": "^3.0.4", "typescript": "^4.9.5", "uglifyjs-webpack-plugin": "^2.2.0", "url-loader": "4.1.0", "vue-awesome-swiper": "^3.1.3", "vue-loader": "^15.10.0", "vue-svg-loader": "0.16.0", "webpack": "4.44.1", "webpack-cli": "3.3.12", "webpack-dev-server": "3.11.3", "webpack-merge": "5.1.4"}, "dependencies": {"@tencent/easy-postmessage": "^1.0.12", "@vueuse/core": "^11.3.0", "decode-uri-component": "^0.2.2", "document-register-element": "^1.14.10", "draggable-vue-directive": "^2.1.0", "element-ui": "^2.15.13", "i18next": "^23.5.1", "i18next-http-backend": "^1.4.5", "js-base64": "^3.7.5", "lodash": "^4.17.21", "mic-check": "^1.1.0", "moment": "^2.29.4", "resize-observer": "^1.0.4", "screenfull": "^6.0.2", "semver": "^7.5.1", "store": "2.0.12", "svg-to-vue-component": "^0.3.8", "swiper": "^3.4.2", "tippy.js": "^6.3.1", "ua-parser-js": "^2.0.0-alpha.2", "vconsole": "^3.15.1", "vue": "2.7.16", "vue-clipboard2": "^0.3.1", "vue-custom-element": "3.2.14", "vue-dompurify-html": "^2.3.0", "vue-lazyload": "^1.3.5", "vue-qr": "2.3.0", "vue-toasted": "1.1.28", "vue2-touch-events": "3.1.1"}, "resolutions": {"@jridgewell/gen-mapping": "0.3.2"}, "volta": {"extends": "../../package.json"}}