const { merge } = require('webpack-merge');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const HtmlReplaceWebpackPlugin = require('html-replace-webpack-plugin');
const HardSourceWebpackPlugin = require('hard-source-webpack-plugin');
// SpeedMeasurePlugin跟HardSourceWebpackPlugin插件一起用, HardSourceWebpackPlugin.ExcludeModulePlugin会报错..
// const SpeedMeasurePlugin = require('speed-measure-webpack-plugin');
// const smp = new SpeedMeasurePlugin();

const path = require('path');
const fs = require('fs');
const portfinder = require('portfinder');
const versionConfig = require('../version');
const baseConfig = require('./webpack.config.base.js');
const replaceHelper = require('./webpack-replace-helper.js');
const htmlReplaceOptions = replaceHelper.getHtmlReplaceReplaceOptions('development');

const developmentConfig = {
  mode: 'development',
  devServer: {
    contentBase: path.join(__dirname, 'dist'),
    compress: true,
    index: 'login.html',
    port: 8080,
    hot: false, // 禁用热更新
    liveReload: false, // 禁用自动刷新
    host: '0.0.0.0', // 绑定本机所有IP
    // https: true,
    // https: {
    //   key: fs.readFileSync('/path/to/server.key'), // SSL私钥路径
    //   cert: fs.readFileSync('/path/to/server.crt'), // SSL证书路径
    //   ca: fs.readFileSync('/path/to/ca.pem'), // CA证书路径
    // },
    disableHostCheck: true,
    // --disable-host-check
  },
  devtool: 'inline-cheap-module-source-map', // 开发环境用于调试，启动会比较慢，但是断点可以清晰看到源码
  plugins: [

    new HtmlWebpackPlugin({
      chunks: ['main'],
      title: '空课',
      template: 'src/pages/class/class.html',
      filename: 'class.html',
      inject: false,
      hot: false,
    }),
    new HtmlWebpackPlugin({
      chunks: ['login'],
      title: '空课登录',
      template: 'src/pages/login/login.html',
      filename: 'login.html',
    }),
    new HtmlWebpackPlugin({
      chunks: ['mainIndex'],
      title: '空课',
      template: 'src/pages/main/index.html',
      filename: 'index.html',
    }),
    new HtmlReplaceWebpackPlugin(htmlReplaceOptions),

    new HtmlWebpackPlugin({
      chunks: ['externalCourseware'],
      title: '外部课件',
      template: 'src/pages/externalCourseware/externalCourseware.html',
      filename: 'externalCourseware.html',
    }),

    new CopyWebpackPlugin({
      patterns: [{
        from: 'static',
        to: 'static/',
        globOptions: {
          dot: false, // 是否包括以 . 开头的文件, 比如 .gitignore
          gitignore: true, // 是否包括 .gitignore 中忽略的文件
          ignore: ['**/tcic/**/*'],
        },
      }, {
        from: 'static/tcic',
        to: `static/tcic/${versionConfig.mainVersion}/`,
      }],
    }),
    new HardSourceWebpackPlugin({
      // configHash: 1,
    }),
    new HardSourceWebpackPlugin.ExcludeModulePlugin([
      {
        // HardSource works with mini-css-extract-plugin but due to how
        // mini-css emits assets, assets are not emitted on repeated builds with
        // mini-css and hard-source together. Ignoring the mini-css loader
        // modules, but not the other css loader modules, excludes the modules
        // that mini-css needs rebuilt to output assets every time.
        test: /mini-css-extract-plugin[\\/]dist[\\/]loader/,
      },
      // {
      //   // 返回true的文件不缓存
      //   test: (source) => {
      //     // 不包括html
      //     if (source.endsWith('.html')) {
      //       return true;
      //     }
      //     // html-webpack-plugin
      //     console.log('%c [ source ]-82', 'font-size:13px; background:pink; color:#bf2c9f;', source);
      //     return false;
      //   },
      //   // include: path.join(__dirname, 'vendor'),
      // },
    ]),

  ],
};

module.exports = new Promise((resolve, reject) => {
  const config = merge(baseConfig, developmentConfig);
  portfinder.basePort = 8080;
  portfinder.getPort((err, port) => {
    if (err) {
      reject(err);
    } else {
      // publish the new Port, necessary for e2e tests
      process.env.PORT = port;
      // add port to devServer config
      config.devServer.port = port;

      // resolve(smp.wrap(config));
      resolve(config);
    }
  });
});
