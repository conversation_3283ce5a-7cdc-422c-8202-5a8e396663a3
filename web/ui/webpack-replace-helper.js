const fs = require('fs');
const path = require('path');
const moment = require('moment');

const versionConfig = require('../version');
const sdkFilenameNoHash = 'tcic-sdk.min.js';

function getSdkFilename(mode) {
  const folderPath = path.resolve(__dirname, 'static/tcic/sdk');
  if (!folderPath || !fs.existsSync(folderPath)) {
    throw new Error('sdk folder not found');
  }

  // development模式，优先判断不带hash的sdk
  if (mode === 'development' && fs.existsSync(path.resolve(folderPath, sdkFilenameNoHash))) {
    return sdkFilenameNoHash;
  }

  // production模式，优先判断带hash的sdk
  let sdkFilename = fs.readdirSync(folderPath)
    .find(filename => /^tcic-sdk\.\w{8}\.js$/.test(filename)); // 注意和sdk的 output.filename 格式一致
  if (!sdkFilename) {
    // 开发/测试环境，sdk用的development，ui用的production
    // production模式下找不到带hash的就找不带的，然后加个query预防缓存
    if (!fs.existsSync(path.resolve(folderPath, sdkFilenameNoHash))) {
      throw new Error('sdk file not found');
    }
    sdkFilename = `${sdkFilenameNoHash}?t=${Date.now()}`;
  }
  console.log(`${mode}: sdkFilename`, sdkFilename);
  return sdkFilename;
}

function getBuildVersion(mode) {
  // js里为了不影响js的hash去掉了 BUILD_VERSION，这里放一个兜底，以免后面哪里要用
  const buildTime = moment.unix(Math.round(new Date().getTime() / 1000)).format('YYYYMMDD_HHmm');
  const buildVersion = `${versionConfig.mainVersion}-${buildTime}`;
  console.log(`${mode}: buildVersion`, buildVersion);
  return buildVersion;
}

function getHtmlReplaceReplaceOptions(mode) {
  const sdkFilename = getSdkFilename(mode);
  const buildVersion = getBuildVersion(mode);
  console.log(`${mode}: getHtmlReplaceReplaceOptions success\n`);

  return [
    {
      pattern: /{TCIC_MAIN_VERSION}/g,
      replacement: versionConfig.mainVersion,
    },
    {
      pattern: /{TCIC_SDK_FILENAME}/g,
      replacement: sdkFilename,
    },
    {
      pattern: /{TCIC_UI_BUILD_VERSION}/g,
      replacement: buildVersion,
    },
    {
      pattern: /{TCIC_NODE_SERVER_HTML_VERSION}/g,
      replacement: "TCIC_NODE_SERVER_HTML_VERSION_NEED_REPLACE", 
    }
  ];
}

module.exports = {
  getHtmlReplaceReplaceOptions,
};
