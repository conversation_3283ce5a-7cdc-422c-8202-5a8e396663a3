<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LCIC Network Test</title>
    <style>
        html,
        body {
            margin: 0;
        }

        body {
            background-color: #14181d;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #ffffff;
            font-size: 14px;
        }
        a, a:active {
            text-decoration: none;
            color: #fff;
        }

        select {
            height: 100%;
            flex: 1;
        }

        /* 新css of login */
        #app {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
        }

        .main {
            position: absolute;
            top: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: space-between;
            overflow-x: hidden;
        }

        .init-hide {
            display: none;
        }


        .main .setting-wrap {
            width: 100%;
            min-width: 320px;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .setting {
            position: relative;
            width: 100%;
            max-width: 500px;
            min-width: 320px;
            height: 90%;
            max-height: 750px;
            min-height: 100px;
            overflow-y: auto;
            border-radius: 8px;
            box-shadow: 0px 0px 5px #2b4b96;
            z-index: 2;
            background: #1d2233;
        }
        .setting > .full {
            height: 100%;
            overflow-y: auto;
        }
        .scroll {
            overflow-y: auto;
        }
        .scroll::-webkit-scrollbar {
            display: none;
        }
        .env-dot {
            width: 10px;
            height: 10px;
            border-radius: 5px;
            background-color: orange;
        }

        /* 垂直flex */
        .flex-column {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-direction: column;
        }
        .flex-column > div {
            width: 100%;
        }

        /* svg icon，默认 20x20 */
        .svg-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
        }

        /* switch 切换界面 */

        .dropdown-content span {
            cursor: pointer;
            text-align: center;
            width: 100%;
            height: 25px;
            font-size: 14px;
            display: block;
            border-bottom: 1px solid #eee;
        }
        .dropdown-content span:hover {
            background-color: #ccc;
            color: #007fff;
        }

        /* create 创建界面*/
        .pc-detail {
            box-sizing: border-box;
            width: 100%;
            padding: 0 5%;
        }
        .tab-head {
            box-sizing: border-box;
            position: relative;
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-bottom: 1px solid #474b58;
            flex-shrink: 0;
        }
        .tab-head .back {
            position: absolute;
            left: 0;
            cursor: pointer;
        }
        .tab-head .title {
            font-size: 16px;
            margin: 0;
        }
        .tab-head .env-dot {
            position: absolute;
            right: 0;
        }

        .tab-body {
            box-sizing: border-box;
            width: 100%;
            padding: 0;
            margin: 0;
            overflow: hidden;
        }
        .input-type {
            color: #969FB4;
            font-size: 14px;
            margin: 12px 0;
        }
        .input-item {
            width: 100%;
            position: relative;
            display: inline-flex;
            margin: 8px 0;
        }
        .input-item .label {
            flex: 1;
            min-width: 80px;
            max-width: 120px;
            line-height: 30px;
            text-align: left;
            display: inline-block;
        }

        .input-item .input-area {
            flex:2;
            width: 120px;
            height: 30px;
            line-height: 30px;
            float: right;
            color: #fff;
            position: relative;
        }
        .input-area > input {
            height: 30px;
            color: #fff;
            position: relative;
        }

        .input-item input.input-control {
            text-indent: 6px;
            position: relative;
            background: #2f3443;
            color: #fff;
            border-radius: 4px;
            border: none;
            outline: none;
        }
        .input-item input.input-control:hover {
            outline: 1px solid #0c68f3;
        }

        .input-item .success {
            color: #0ABF5B;
        }
        .input-item .warning {
            color: #FFAA00;
        }
        .input-item .error {
            color: red;
        }
        .input-item .hide {
            display: none;
        }

        .pc-detail.full .pc-section {
            width: 100%;
            flex: 1;
        }
        .pc-detail.full .pc-section .tab-body {
            height: 100%;
        }
        .pc-detail.full .pc-section .tab-body .pc-form-wrap {
            flex: 1;
        }
        .pc-detail.full .pc-section .tab-body .pc-button-wrap {
            height: 70px;
            flex-direction: row;
            justify-content: center;
        }
        .pc-detail.full .pc-section .tab-body .pc-button-wrap .main-button,.other-button-wrap{
            width: 170px;
        }
        .pc-detail.full .pc-section .tab-body.tab-success .pc-form-wrap {
            justify-content: center;
        }
        .pc-detail.full .pc-section .tab-body.tab-success .pc-button-wrap {
            height: 150px;
        }
        .pc-button-wrap {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .pc-button-wrap .hide{
            visibility: hidden;
        }

        .pc-button-wrap .main-button.disabled{
            background: #666;
            cursor: default;
        }

        .pc-button-wrap .main-button {
            width: 100%;
            height: 40px;
            line-height: 40px;
            border-radius: 6px;
            overflow: hidden;
            font-size: 16px;
            text-align: center;
            cursor: pointer;
            background-color: #006cff;
        }
        .pc-button-wrap .other-button-wrap {
            font-size: 16px;
            height: 40px;
            line-height: 40px;
            border-radius: 6px;
            border: 1px solid #1c66e5;
            overflow: hidden;
            font-size: 16px;
            text-align: center;
            cursor: pointer;
        }
        .pc-button-wrap .other-button-wrap a {
            color: #fff;
        }

        .pc-button-wrap .other-button-wrap:hover a {
            color: #006cff;
        }

        /* element ui 修复*/
        .el-radio__input.is-checked .el-radio__inner {
            border-color: #006cff;
            background: #006cff;
        }
        .el-radio__input.is-checked+.el-radio__label {
            color: #006cff;
        }

        .err-remind {
            margin-left: 120px;
            color: red;
            height: auto;
            line-height: 20px;
        }

        /* pc 效果 end */

        /* mobile 效果 start */

        .loader {
            border: .5rem solid #f3f3f3;
            /* Light grey */
            border-top: 8px solid #3498db;
            /* Blue */
            border-radius: 50%;
            width: 3.75rem;
            height: 3.75rem;
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
        ::placeholder {
            font-size: 14px;
        }

        .el-input--prefix .el-input__inner {
            padding-right: 0;
        }

        .role-info-value {
            font-weight: 500;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            color: #969FB4;
        }
        .role-info-value input {
            text-align: left;
        }
        .el-input__inner {
            font-family: 'PingFang SC';
            font-style: normal;
            font-weight: 400;
            font-size: .875rem;
            line-height: 1.375rem;
        }
        .enter-role-value {
            position: absolute;
            left: 35%;
        }

        .selector-cancel {
            height: 3.6rem;
            color: #D42E4A;
        }
        .el-input{
            width: auto;

        }
        .active1 {
            display: none;
        }

        .active {
            display: block;
        }

        /* phone demo detail */
        .select {
            width: 100%;
        }
        .select .el-input.el-input--suffix {
            width: 100%;
        }

        /* 手机端初始状态 */
        .phone-demo {display: none;}

        .phone-demo-button:hover {
            background-color: #2f7fff;
        }


        .create{
            padding-top:15px;
        }

        .group-qr:hover .qr-img-wrapper{
            display: block;
        }
        .group-qr:hover .qr-img-wrapper img{
            margin: 0px;
        }
        .more-info{
            color: #4791FF;
            cursor: pointer;
        }

        /* 平板版本 */
        @media (max-width: 800px) {

            body {
                background-color: #1d2233;
            }
            .main .intro-wrap {
                display: none;
            }
            .main .setting-wrap {
                width: 100%;
            }
            .main .pc-link-list {
                display: none;
            }
            .setting {
                width: 100%;
                max-width: 800px;
                height: 100%;
                max-height: 100%;
                overflow-y: auto;
                box-shadow: none;
            }
            .more-env .tab-body {
                margin: 20px 0 100px 0;
            }
        }
        /* 手机版 */
        @media (max-width: 500px) {
            .main .pc-detail, .main .pc-area {
                display: block;
            }
            .main .input-area>input{
                width: 60px;
            }
        }
        /* table 版本 */
        @media (min-width: 800px) and (max-width: 980px) {
            .setting {
                max-width: 600px;
                height: 100%;
            }
        }

        @media (min-width: 980px) and (max-width: 1280px) {
            .setting {
                max-width: 800px;
                height: 100%;
            }
        }

    </style>
</head>

<body>
<!-- 新登录页面 -->
<div id="app">
    <div class="main">
        <div class="setting-wrap">
            <div id="setting" class="setting scroll init-hide" style="display: block;">
                <div class="pc-detail full flex-column" style="">
                    <div class="tab-head"><a class="svg-icon back"><span class="icon-back"></span></a>
                        <div class="title">
                            Network Test
                        </div>
                        <div class="env-dot" style="display: none;"></div>
                    </div>
                    <div class="pc-section scroll create" style="">
                        <div class="tab-body flex-column">
                            <div class="pc-form-wrap scroll" id="js-main-cont">
                                <div>
                                    <div class="input-item">
                                        <div class="label"><span>课堂名称</span></div>
                                        <div class="input-area" style="width: 120px">
                                            <input type="text" maxlength="30" placeholder=""
                                                   class="input-control">
                                        </div>
                                    </div>
                                    <div class="err-remind"></div>
                                </div>
                                <div>
                                    <div class="input-item">
                                        <div class="label"><span>老师昵称</span></div>
                                        <div class="input-area"><input type="text" maxlength="30" placeholder=""
                                                                       class="input-control">
                                        </div>
                                    </div>
                                    <div class="err-remind"><!----></div>
                                </div>
                                <div>
                                    <div class="input-item">
                                        <div class="label"><span>老师昵称</span></div>
                                        <div class="input-area"><input type="text" maxlength="30" placeholder=""
                                                                       class="input-control">
                                        </div>
                                    </div>
                                    <div class="err-remind"><!----></div>
                                </div>
                                <div>
                                    <div class="input-item">
                                        <div class="label"><span>老师昵称</span></div>
                                        <div class="input-area"><input type="text" maxlength="30" placeholder=""
                                                                       class="input-control">
                                        </div>
                                    </div>
                                    <div class="err-remind"><!----></div>
                                </div>

                            </div>
                            <div class="pc-button-wrap">
                                <div class="main-button" id="js-btn-run">
                                    Run Tests
                                </div>
                                <button id="js-btn-copy" class="hide">Copy result</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="js-test-cont" style="display: none"></div>

</div>

<script>
    const CONFIG = {
        'class.qcloudclass.com': { path: '/latest/static/libs/vue/vue_2.7.10.js', type: 'script' },
        'class-cf.qcloudclass.com': { path: '/latest/static/libs/vue/vue_2.7.10.js', type: 'script' },
        'tcic-api.qcloudclass.com': { path: '/v2/member/heartbeat' },
        'tcic-os-api.qcloudclass.com': { path: '/v2/member/heartbeat' },
        'res.qcloudclass.com': { path: '/Web/rtc-beauty-plugin.js', type: 'script' },
        'res.qcloudtiw.com': { path: '/board/third/cos/5.1.0/cos.min.js', type: 'script' },
        'log.qcloudtiw.com': { path: '/config/tiwlogger-server-config'},
        'tcic-prod-1257307760.qcloudclass.com': { path: '/doc/0nmot3jnt3gvpv6j3nfc_tiw/h5/data/img0.jpg', type: 'img' },
        'tcic-source.qcloudclass.com': { path: '/uploads/2120ea9b-1fdb-44b4-be38-9c688a90be45/3923193/1676867746_Xrtl9pQ7.jpg', type: 'img' },
        'yun.tim.qq.com': {},
        'api.my-imcloud.com': {},
        'report-log-lv1.api.qcloud.com': {},
    }


    const mainCont = document.getElementById('js-main-cont');
    const testCont = document.getElementById('js-test-cont');
    const runBtn = document.getElementById('js-btn-run');
    const copyBtn = document.getElementById('js-btn-copy');
    const initBtnText = runBtn.innerText;
    const loadingText = 'Running...';
    let clipEnable = false;

    let conts = '';
    let index = 0;
    for(let key in CONFIG) {
        const name = 'Domain' + index;
        index++;
        const item = `<div>
                                    <div class="input-item">
                                        <div class="label"><span>${name}</span></div>
                                        <div class="input-area">
                                            <input type="text" maxlength="30" value="${key}"
                                            id="${key}" placeholder="" class="input-control"/>
                                            <span class="label"></span>
                                            <button class="hide"><a href="https://itango.tencent.com/app/data/huatuoen?domain=${key}" target="_blank" style="color: black">Analyse</a></button>
                                        </div>
                                    </div>
                                    <div class="err-remind"></div>
                                </div>`;
        conts = conts + item;
    }
    mainCont.innerHTML = conts;

    async function permissionsCheck() {
        try {
            const read = await navigator.permissions.query({
                name: 'clipboard-read',
            });
            const write = await navigator.permissions.query({
                name: 'clipboard-write',
            });
            return write.state === 'granted' && read.state !== 'denied';
        } catch(e) {
            console.error(e);
        }
    }


    function fetchWithTimeout(domain, options, timeout = 10000) {
        let url = 'https://' + domain;
        const path = CONFIG[domain].path || '';
        url = url + path;
        const tagType = CONFIG[domain].type;

        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Request timeout')), timeout);
        });
        const fetchPromise = new Promise((resolve, reject) => {
            if (tagType) {
                const ele = document.createElement(tagType);
                ele.onload = () => {
                    resolve({
                        url: url,
                        status: 'ok',
                    });
                }
                ele.onerror = (e) => {
                    resolve({
                        url: url,
                        status: 'error',
                    });
                }
                ele.setAttribute('src', url);
                testCont.appendChild(ele);
            } else {
                fetch(url).then((resp)=>{
                    resolve(resp);
                }).catch((e)=>{
                    resolve(new Error('Fetch error'));
                });
            }
        });
        return Promise.race([
            fetchPromise,
            timeoutPromise
        ]);
    }

    const testSpeed = (domain) => {
        return new Promise((resolve) => {
            setTimeout(async () => {
                let t1 = +new Date();
                const res = await fetchWithTimeout(domain)
                let t2 = +new Date();
                const cost = t2 - t1;
                console.log(domain, cost, res);
                CONFIG[domain].cost = cost;
                const e1 = document.getElementById(domain);
                const e2 = e1.nextElementSibling;
                const parent = e1.parentElement;
                const anaBtn = e2.nextElementSibling;
                e2.innerText = cost + 'ms';
                const isError = (typeof res === 'Error' || (res.status && res.status === 'error'));
                if (isError) {
                    CONFIG[domain].status = 'error';
                    parent.classList.add('error');
                    anaBtn.classList.remove('hide');
                } else {
                    if (cost > 5000 && cost < 10000) {
                        CONFIG[domain].status = 'warning';
                        parent.classList.add('warning')
                        anaBtn.classList.remove('hide');
                    } else if(cost < 5000) {
                        CONFIG[domain].status = 'success';
                        parent.classList.add('success')
                    }
                }
                resolve();
            }, 0);
        })
    }

    const loadingBtn = (dom) => {
        dom.innerText = loadingText;
        dom.setAttribute('disabled', true);
        dom.classList.add('disabled');
    }

    const showBtn = (dom) => {
        dom.innerText = initBtnText;
        dom.removeAttribute('disabled');
        dom.classList.remove('disabled');
    }

    const showCopyBtn = (dom) =>  {
        if(clipEnable) {
            dom.classList.remove('hide');
        }
    }
    const hideCopyBtn = (dom) =>  {
        dom.classList.add('hide');
    }

    const run = () => {
        testCont.innerHTML = '';
        const task = []
        for(let domain in CONFIG) {
            task.push(testSpeed(domain));
        }
        hideCopyBtn(copyBtn);
        loadingBtn(runBtn);
        Promise.all(task).then(()=>{
            showBtn(runBtn);
            showCopyBtn(copyBtn);
        });
    };

    runBtn.addEventListener('click', ()=>{
        const isDisabled = runBtn.getAttribute('disabled');
        if (isDisabled) {
            console.error('disabled, return;')
            return;
        }
        run();
    })


    permissionsCheck().then((res)=>{
        clipEnable = !!res;
        if(clipEnable) {
            copyBtn.addEventListener('click', async ()=>{
                try {
                    const text = JSON.stringify(CONFIG)
                    await navigator.clipboard.writeText(text);
                    alert('Result has copied to clipboard')
                } catch(e) {
                    alert('Failed to copied');
                    console.error(e);
                }


            })
        }
    });




</script>
</body>
</html>
