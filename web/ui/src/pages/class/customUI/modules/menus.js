const initMenu = () => TCIC.SDK.instance.loadComponent('header-component').then(() => menus);

const getHeaderController = () => TCIC.SDK.instance.getComponent('header-component').getVueInstance().$refs.header;
const getMenuArray = () => getHeaderController().menu;

const assertMenuIsReady = () => {
  try {
    // 不能用 typeof === 'array' 来判断
    if (!(getMenuArray() instanceof Array)) {
      throw new Error('menu is not ready');
    }
    return true;
  } catch (error) {
    console.error(error);
    throw new Error('TCICCustomUI.menus: menu is not ready, please call TCICCustomUI.menus.init() first');
  }
};

const getMenuConfig = (name) => {
  const menus = getMenuArray();
  const config = menus.find(menu => menu.name === name);
  if (!config) {
    throw new Error(`TCICCustomUI.menus: menu [${name}] not found`);
  }

  return config;
};

const getMenuIndex = (name) => {
  const menus = getMenuArray();
  return menus.findIndex(menu => menu.name === name);
};

const insertMenu = (index, name, config) => {
  if (getMenuIndex(name) !== -1) {
    throw new Error(`TCICCustomUI.menus.insertMenu: menu to be inserted [${name}] already exist`);
  }

  getMenuArray().splice(index, 0, {
    badge: 0,
    enable: true,
    active: false,
    ...config,
    name,
  });
};

const updateMenu = (name, config) => {
  assertMenuIsReady();
  Object.assign(getMenuConfig(name), config);
};

const insertBefore = (target, name, config) => {
  assertMenuIsReady();
  const index = getMenuIndex(target);
  if (index === -1) {
    throw new Error(`TCICCustomUI.menus.insertBefore: menu [${name}] not exist`);
  }
  insertMenu(index, name, config);
};

const insertAfter = (target, name, config) => {
  assertMenuIsReady();
  const index = getMenuIndex(target);
  if (index === -1) {
    throw new Error(`TCICCustomUI.menus.insertAfter: menu [${name}] not exist`);
  }
  insertMenu(index + 1, name, config);
};

const unshiftMenu = (name, config) => {
  assertMenuIsReady();
  insertMenu(0, name, config);
};

const pushMenu = (name, config) => {
  assertMenuIsReady();
  const index = getMenuArray().length;
  insertMenu(index, name, config);
};

const setVisible = (name, isVisible) => {
  updateMenu(name, {
    enable: isVisible,
  });
};

export const menus = {
  init: initMenu,
  insertBefore,
  insertAfter,
  unshift: unshiftMenu,
  push: pushMenu,
  update: updateMenu,
  setVisible,
};
