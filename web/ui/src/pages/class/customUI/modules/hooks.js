/* eslint-disable no-underscore-dangle */
/**
 * @template T
 */
export class HookEvent {
  /** @private */
  _cancelled = false;

  /** @private */
  _supportCancel = false;

  /** @private */
  _hookName = '';

  /** @type {T} */
  payload;

  /**
   * @param {T} payload
   */
  constructor(hookName, payload, supportCancel = false) {
    this._hookName = hookName;
    this.payload = payload;
    this._supportCancel = supportCancel;
    this._cancelled = false;
  }

  cancel() {
    if (!this._supportCancel) {
      throw new Error(`Hook [${this._hookName}] does not support cancel`);
    }
    this._cancelled = true;
  }

  isCancelled() {
    return this._cancelled;
  }
}

/**
 * @template T
 * @typedef {(event: HookEvent<T>) => void} HookEventHandler
 */

/**
 * @template T
 */
export class Hooks {
  /** @private */
  _supportCancel = false;

  /** @private */
  _name = '';

  /**
   * @type {Array<{ handler: HookEventHandler<T>; priority: number }>}
   * @private
   */
  _handlers = [];

  constructor(options = {}) {
    this._supportCancel = !!options.supportCancel;
    this._name = options.name;
  }

  /**
   * @param {HookEventHandler<T>} handler
   * @param {number} [priority=0]
   */
  tap(handler, priority = 0) {
    this._handlers.push({ handler, priority });
    this._handlers.sort((a, b) => b.priority - a.priority);
  }

  /**
   * @param {T} payload
   * @returns {HookEvent}
   */
  invokeSync(payload) {
    const event = new HookEvent(this._name, payload, this._supportCancel);

    for (const { handler } of this._handlers) {
      if (event.isCancelled()) {
        break;
      }

      handler(event);
    }

    return event;
  }

  /**
   * @param {T} payload
   * @returns {Promise<HookEvent>}
   */
  async invokeAsync(payload) {
    const event = new HookEvent(this._name, payload, this._supportCancel);

    for (const { handler } of this._handlers) {
      if (event.isCancelled()) {
        break;
      }

      await handler(event);
    }

    return event;
  }
}


/**
 * @type {Record<string, Hooks<any>>}
 */
const _hooksMap = {};

/**
 * @param {string} name
 * @returns {Hooks<any>}
 */
export const hooks = (name) => {
  if (!_hooksMap[name]) {
    throw new Error(`TCICCustomUI.hooks: unknown hook [${name}]`);
  }

  return _hooksMap[name];
};

/**
 * @param {string} name
 * @returns {Hooks<any>}
 */
export const registerHooks = (name, options = {}) => {
  if (_hooksMap[name]) {
    console.warn(`TCICCustomUI.registerHooks: hook [${name}] already registered`);
    return;
  }

  _hooksMap[name] = new Hooks({
    name,
    ...options,
  });
};

export const invokeMsgBoxHooks = (hookName, msgBoxParams, callback) => {
  try {
    const hookResult = hooks(hookName).invokeSync(msgBoxParams);
    if (!hookResult.isCancelled()) {
      const { title, message, onClose, onComplete } = hookResult.payload;
      const buttons = hookResult.payload.buttons.map(button => button.text);
      const callbacks = hookResult.payload.buttons.map(button => button.onClick);
      callback({
        title,
        message,
        report: false,
        buttons,
        callback: (index) => {
          if (index === -1 && typeof onClose === 'function') {
            onClose();
          } else if (index >= 0 && typeof callbacks[index] === 'function') {
            callbacks[index]();
          }

          if (typeof onComplete === 'function') {
            onComplete(index, index >= 0 ? hookResult.payload.buttons[index] : undefined);
          }
        },
      });
    }
  } catch (err) {
    console.error('[invokeMsgBoxHooks] error', hookName, err);
    throw err;
  }
};

/**
 * 调用同步 Hook，以返回值的方式返回结果（一般用于不支持取消的 hook）
 *
 * (1) 若事件未被取消则触发 callback 回调;
 * (2) 若事件被取消则抛出异常
 * (2) 若发生异常则直接抛出
 */
export const invokeSyncHooks = (hookName, payload) => {
  try {
    const hookResult = hooks(hookName).invokeSync(payload);

    if (hookResult.isCancelled()) {
      throw new Error('Cancelled');
    }

    return hookResult.payload;
  } catch (err) {
    console.error('[invokeSyncHooks] error', hookName, err);
    throw err;
  }
};

/**
 * 调用同步 Hook，以回调的方式返回结果
 *
 * (1) 若事件未被取消则触发 complete 回调;
 * (2) 若事件被取消则触发 cancel 回调;
 * (3) 若发生异常则触发 error 回调
 */
export const invokeSyncHooksAsCallback = (hookName, payload, callbacks = {}) => {
  try {
    const hookResult = hooks(hookName).invokeSync(payload);
    if (!hookResult.isCancelled()) {
      if (typeof callbacks.complete === 'function') {
        callbacks.complete(hookResult.payload);
      }
    } else {
      if (typeof callbacks.cancel === 'function') {
        callbacks.cancel(hookResult.payload);
      }
    }
  } catch (err) {
    console.error('[invokeSyncHooksAsCallback] error', hookName, err);
    if (typeof callbacks.error === 'function') {
      callbacks.error(err);
    }
  }
};

/**
 * 调用异步 Hook，返回一个 Promise
 *
 * (1) 若事件未被取消 / 被取消则 Promise resolve;
 * (2) 若发生异常则 Promise reject
 */
export const invokeAsyncHooks = (hookName, payload) => {
  hooks(hookName).invokeAsync(payload)
    .then(hookResult => ({ payload: hookResult.payload, isCancelled: hookResult.isCancelled() }));
};

/**
 * 调用异步 Hook，以回调的方式返回结果
 *
 * (1) 若事件未被取消则触发 complete 回调;
 * (2) 若事件被取消则触发 cancel 回调;
 * (3) 若发生异常则触发 error 回调
 */
export const invokeAsyncHooksAsCallback = (hookName, payload, callbacks = {}) => {
  hooks(hookName).invokeAsync(payload)
    .then((hookResult) => {
      if (!hookResult.isCancelled()) {
        if (typeof callbacks.complete === 'function') {
          callbacks.complete(hookResult.payload);
        }
      } else {
        if (typeof callbacks.cancel === 'function') {
          callbacks.cancel(hookResult.payload);
        }
      }
    })
    .catch((err) => {
      console.error('invokeAsyncHooksAsCallback error', hookName, err);
      if (typeof callbacks.error === 'function') {
        callbacks.error(err);
      }
    });
};
