import i18next from 'i18next';

/**
 * 根据给定的 key 获取翻译后的文本
 *
 * @param {string} key - 要获取翻译文本的 key
 * @param {Record<string, string>} [params] - 要在翻译文本中替换的参数
 * @returns {string | null} 翻译后的文本或未找到时返回 null
 */
const get = (key, params) => i18next.t(key, params);

/**
 * 为给定的 key 和语言设置翻译后的文本
 *
 * @param {string} lang - 要设置翻译文本的语言
 * @param {string} key - 要设置翻译文本的 key
 * @param {string} text - 翻译后的文本
 * @returns {void}
 */
const set = (lang, key, text) => {
  i18next.addResource(lang, 'translation', key, text);
};

/**
 * 批量设置给定语言的多个翻译文本
 *
 * @param {string} lang - 要设置翻译文本的语言
 * @param {Record<string, string>} map - 包含 key 和翻译文本的对象
 * @returns {void}
 */
const batchSet = (lang, map) => {
  i18next.addResources(lang, 'translation', map);
};

export const i18n = {
  get,
  set,
  batchSet,
};
