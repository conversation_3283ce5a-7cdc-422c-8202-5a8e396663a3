import { THookType } from './constants';
import {
  hooks,
  registerHooks,
  invokeMsg<PERSON>oxHooks,
  invokeSyncHooks,
  invokeSyncHooksAsCallback,
  invokeAsyncHooks,
  invokeAsyncHooksAsCallback,
} from './modules/hooks';
import { menus } from './modules/menus';
import { i18n } from './modules/i18n';
import Util from '@util/Util';

// 挂在 window.TCICCustomUI
// 将 UI 自定义接口暴露给自定义 JS
const TCICCustomUI = {
  THookType,

  hooks,
  menus,
  i18n,
  layout: {
    setRightSideWidth: Util.setRightSideWidth,
    setRightVideoHeight: Util.setRightVideoHeight,
  },
};

registerHooks(THookType.MsgBox_ClassEnded_BeforeShow, { supportCancel: true });
registerHooks(THookType.MsgBox_LeaveClass_BeforeShow, { supportCancel: true });
registerHooks(THookType.MsgBox_LeaveClass_IsEndClassAllowed);
registerHooks(THookType.Error_JoinClassFail, { supportCancel: true });

export {
  TCICCustomUI,

  invokeMsgBoxHooks,
  invokeSyncHooks,
  invokeSyncHooksAsCallback,
  invokeAsyncHooks,
  invokeAsyncHooksAsCallback,
};
