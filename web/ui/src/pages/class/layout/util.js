// 白板实际尺寸，16:9
export function getBoardInnerRect(boardParentRect) {
  const boardInnerRect = {
    ...boardParentRect,
  };
  if (boardParentRect.width > 0 && boardParentRect.height > 0) {
    // boardInnerRect 和 Board.vue 里规则一致，16:9
    if (boardParentRect.width * 9 / 16 > boardParentRect.height) {
      boardInnerRect.height = boardParentRect.height;
      boardInnerRect.width = boardInnerRect.height * 16 / 9;
      boardInnerRect.left += (boardParentRect.width - boardInnerRect.width) / 2;
    } else {
      boardInnerRect.width = boardParentRect.width;
      boardInnerRect.height = boardInnerRect.width * 9 / 16;
      boardInnerRect.top += (boardParentRect.height - boardInnerRect.height) / 2;
    }
  }
  return boardInnerRect;
}

// 白板工具栏 LayoutProps
export function getBoardToolLayoutProps({
  visible,
  isScreenShareOnElectron,
  isScreenShareAdvanceMode,
  boardParentRect,
  boardInnerRect,
  isSmallScreen = false,
}) {
  const isScreenShareSimpleModeOnElectron = isScreenShareOnElectron && !isScreenShareAdvanceMode;

  // 平板下按钮大小进行缩放
  // let boardToolWidth = isPad ? 60 : 80;
  // const boardToolHeight = isPad ? 508 : 718;
  // 不用区分平板了，都变小了
  // eslint-disable-next-line no-nested-ternary
  const boardToolOriWidth = isScreenShareSimpleModeOnElectron ? 0 : 40; // 简单模式屏幕分享下不显示白板工具栏（宽度为0）
  const boardToolOriHeight =  isSmallScreen ? 320 : 566;
  let boardToolWidth = boardToolOriWidth;
  let boardToolHeight = boardToolOriHeight;

  // scale，相对 boardInnerRect
  const preserveInner = 10; // 留点缝
  const maxHeight = boardInnerRect.height - preserveInner * 2;
  const boardToolOrigin = 'top left';
  let boardToolScale = 1;
  if (maxHeight < boardToolHeight && !isSmallScreen) {   // 白板工具栏显示不下
    boardToolScale = Math.max((maxHeight / boardToolHeight).toFixed(2), 0);
    boardToolWidth *= boardToolScale;
    boardToolHeight *= boardToolScale;
  }

  // 计算白板工具位置
  const isVerticalCenter = true;
  // 上下，相对 boardInnerRect
  let boardToolTopInCSS;
  if (isVerticalCenter) {
    // 中
    boardToolTopInCSS = `(${boardInnerRect.top}px + ${preserveInner}px + (${boardInnerRect.height}px - ${preserveInner * 2}px - ${boardToolHeight}px) / 2)`;
  } else {
    // 上
    boardToolTopInCSS = `(${boardInnerRect.top}px + ${preserveInner}px)`;
  }
  // 左右，相对 boardParentRect
  let boardToolLeftInCSS = `(${boardParentRect.left}px + ${boardParentRect.width}px - ${preserveInner}px - ${boardToolWidth}px)`;
  if (isScreenShareOnElectron) {
    // 屏幕分享时不受白板位置限制，直接靠右
    boardToolLeftInCSS = `(100% - ${preserveInner}px - ${boardToolWidth}px)`;
  }

  return {
    boardToolWidth,
    boardToolHeight,
    boardToolTopInCSS,
    boardToolLeftInCSS,
    layoutProps: {
      top: `calc(${boardToolTopInCSS})`,
      left: `calc(${boardToolLeftInCSS})`,
      width: `${boardToolOriWidth}px`,
      height: `${boardToolOriHeight}px`,
      transform: `scale(${boardToolScale})`,
      transformOrigin: boardToolOrigin,
      display: visible ? 'block' : 'none',
      style: 'overflow: visible;',  // 由于白板工具栏需要展示tips，需要允许展示超出部分
    },
  };
}
