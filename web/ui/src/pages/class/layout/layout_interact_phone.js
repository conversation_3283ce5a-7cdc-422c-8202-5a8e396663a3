import { LayoutBase } from '@/pages/class/layout/layout_base';
import Constant from '@/util/Constant';
import Util from '@/util/Util';

export class LayoutInteractPhone extends LayoutBase {
  constructor() {
    super('LayoutInteractPhone');
    this.instance = null;
  }
  static getInstance() {
    if (!this.instance) {
      this.instance = new LayoutInteractPhone();
    }
    return this.instance;
  }
  updateLayout() {
    const promiseArray = [];
    const deviceDetecting = false;
    const isFullscreen = this.sdk.getState(Constant.TStateFullScreen, false);
    const showScreenPlayer = this.sdk.getState(Constant.TStateScreenPlayerVisible, false);
    const showVodPlayer = this.sdk.getState(Constant.TStateVodPlayerVisible, false);
    const isShowFooterComponent = this.sdk.getState(Constant.TStateIsShowFooterComponent, false);
    const isJoined = this.sdk.getState(TCIC.TMainState.Joined_Class);

    const isSmallScreen = this.sdk.isMobile() && !this.sdk.isPad();
    const classLayout = isSmallScreen ? TCIC.TClassLayout.Right : this.sdk.getClassLayout();
    const isTopLayout = (classLayout === TCIC.TClassLayout.Top);
    const isDoubleLayout = (classLayout === TCIC.TClassLayout.Double);

    // 高度不足以显示的情况下，一些组件需要缩小展示
    const autoFitScale = this.getFitScale(classLayout, false);

    // 屏幕分享时，Loading页面背景透明
    this.sdk.getComponent('loading-component')
      .getVueInstance()
      .setBackgroundTransparent(false);

    // 更新导航栏布局
    const headerHeight = 0; // 移动端的header脱离文档流，所以高度为0
    if (!this.sdk.getState(Constant.TStateRecordMode)) {
      promiseArray.push(this.sdk.updateComponent('header-component', {
        top: '0',
        left: '0',
        width: '100%',
        height: '45px',
        display: isJoined ? 'block' : 'none',
      }));
    }

    // 更新视频列表布局
    const videoVisible = !deviceDetecting;
    let videoWidth = 0;  // 视频占用空间
    let videoHeight = 0;
    let videoWidthInCSS;  // 视频展示大小
    let videoHeightInCSS;
    if (isTopLayout || isDoubleLayout) {
      videoHeight = isFullscreen ? 0 : 110;  // 缩起后不占空间
      videoWidthInCSS = '100%';
      videoHeightInCSS = '110px';
    } else {
      videoWidth = isFullscreen ? 0 : 180;
      if (isSmallScreen) {
        videoWidth += 20;
      }
      videoWidthInCSS = isSmallScreen ? '200px' : '180px';
      videoHeightInCSS = `calc(100% - ${headerHeight}px)`;
    }

    promiseArray.push(this.sdk.updateComponent('videowrap-component', {
      top: `${headerHeight}px`,
      left: `calc(100% - ${videoWidthInCSS})`,
      width: videoWidthInCSS,
      height: videoHeightInCSS,
      display: videoVisible ? 'block' : 'none',
      style: 'overflow: visible;',
    })
      .then((success) => {
        if (success) {
          const comp = this.sdk.getComponent('videowrap-component');
          if (comp) {
            comp.getVueInstance()
              .setWrapLayout(classLayout);
            comp.getVueInstance()
              .setWrapMode('full');   // 屏幕分享结束后需恢复视频模式
          }
        }
      }));

    // 更新缩略图布局
    promiseArray.push(this.sdk.updateComponent('thumbnail-component', {
      display: 'none',
    }));

    // 更新白板布局
    const boardVisible = !deviceDetecting;
    const headerVideoHeight = headerHeight + videoHeight;
    const headerVideoThumbnailHeight = headerVideoHeight;
    promiseArray.push(this.sdk.updateComponent('board-component', {
      top: `${headerVideoHeight}px`,
      left: '0',
      width: `calc(100% - ${videoWidth}px)`,
      height: `calc(100% - ${headerVideoThumbnailHeight}px)`,
      display: boardVisible ? 'block' : 'none',
    }));

    // 更新提示布局（宽高和白板一致）
    promiseArray.push(this.sdk.updateComponent('live-reminder-component', {
      top: `${headerVideoHeight}px`,
      width: `calc(100% - ${videoWidth}px)`,
      height: `calc(100% - ${headerVideoThumbnailHeight}px)`,
      display: 'block',
    }));

    // 更新视频播放器
    const vodPlayerVisible = !deviceDetecting
        && showVodPlayer;
    promiseArray.push(this.sdk.updateComponent('vod-player-component', {
      top: `${headerVideoHeight}px`,
      left: '0',
      width: `calc(100% - ${videoWidth}px)`,
      height: `calc(100% - ${headerVideoThumbnailHeight}px)`,
      display: vodPlayerVisible ? 'block' : 'none',
    }));

    // 更新网络组件位置
    promiseArray.push(this.sdk.updateComponent('network-tips-component', {
      top: `${headerVideoHeight}px`,
      left: 'calc(50% - 208px)',
      width: '416px',
      height: 'auto',
      display: 'block',
    }));

    // 屏幕分享观看布局和白板一致
    promiseArray.push(this.sdk.updateComponent('screen-player-component', {
      top: `${headerVideoHeight}px`,
      left: '0',
      width: `calc(100% - ${videoWidth}px)`,
      height: `calc(100% - ${headerVideoThumbnailHeight}px)`,
    }));

    // 更新PPT工具栏布局
    const boardFooterVisible = !deviceDetecting
      && isShowFooterComponent;
    const footerHeight = 80;
    promiseArray.push(this.sdk.updateComponent('footer-component', {
      left: '0px',
      top: `calc(100% - ${footerHeight}px)`,
      width: `calc(100% - ${videoWidth}px)`,
      display: boardFooterVisible ? 'block' : 'none',
      height: `${footerHeight}px`,
      transformOrigin: 'bottom',
      style: 'overflow: visible;',
    }));

    // 视频工具组件的宽高和白板保持一致
    const vodToolVisible = !deviceDetecting
        && showVodPlayer;
    const vodToolScale = autoFitScale;
    const vodToolHeight = 72;
    const vodToolBottomMargin = 24;
    promiseArray.push(this.sdk.updateComponent('vod-tool-component', {
      top: `calc(100% - ${vodToolHeight}px - ${vodToolBottomMargin}px)`,
      left: '0',
      width: `calc(100% - ${videoWidth}px)`,
      height: `${vodToolHeight}px`,
      display: vodToolVisible ? 'block' : 'none',
      transform: `scale(${vodToolScale})`,
      transformOrigin: 'bottom',
      style: 'overflow: visible;',
    }));

    promiseArray.push(this.sdk.updateComponent('image-preview', {
      display: 'none',
    }));

    // 更新计时器组件
    const clockToolScale = autoFitScale;
    promiseArray.push(this.sdk.updateComponent('clock-tool-component', {
      transform: `scale(${clockToolScale})`,
    }));

    // 更新定时器组件
    const timerToolScale = autoFitScale;
    promiseArray.push(this.sdk.updateComponent('timer-tool-component', {
      transform: `scale(${timerToolScale})`,
    }));

    // 更新答题器组件
    const quizScale = this.getFitScaleBasedOnHeight(classLayout, 660);
    promiseArray.push(this.sdk.updateComponent('quiz-component', {
      transform: `scale(${quizScale})`,
    }));

    promiseArray.push(this.sdk.updateComponent('mobile-im-input-bar-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
      display: 'block',
    }));

    return Promise.all(promiseArray);
  }
  updateLayoutAfterJoinClass() {
    const promiseArray = [];
    const classLayout = this.sdk.getClassLayout();
    const autoFitScale = this.getFitScale(classLayout, false);  // 高度不足以显示的情况下，一些组件需要缩小展示

    const videoWrap = this.sdk.getComponent('videowrap-component');
    if (videoWrap) {
      videoWrap.getVueInstance()
        .setWrapMode('full');
    }

    // 更新计时器组件
    const clockToolScale = autoFitScale;
    const clockWidth = (this.sdk.isTeacher() || this.sdk.isAssistant()) ? 469 : 424;
    const clockHeight = 390;
    promiseArray.push(this.sdk.updateComponent('clock-tool-component', {
      left: `calc(50% - ${clockWidth}px / 2)`,
      top: `calc(50% - ${clockWidth}px / 2)`,
      width: `${clockWidth}px`,
      height: `${clockHeight}px`,
      transform: `scale(${clockToolScale})`,
      style: 'overflow: visible;',
    }));

    // 更新答题器组件
    const quizScale = autoFitScale;
    const quizHeight = (this.sdk.isTeacher() || this.sdk.isAssistant()) ? 551 : 455;
    const quizWidth = (this.sdk.isTeacher() || this.sdk.isAssistant()) ? 400 : 360;
    // const quizScale = this.getFitScaleBasedOnHeight(classLayout, quizHeight);  // 高度不足以显示的情况下，一些组件需要缩小展示
    promiseArray.push(this.sdk.updateComponent('quiz-component', {
      left: `calc(50% - ${quizWidth}px / 2)`,
      top: `calc(50% - ${quizHeight}px / 2)`,
      width: `${quizWidth}px`,
      height: `${quizHeight}px`,
      transform: `scale(${quizScale})`,
    }));
    // 答题组件在缩放的情况下，内部的popover也需要缩放
    Util.addStyle(`.sta-popover { transform: scale(${quizScale}) }`);

    // 更新定时器组件
    const timerToolScale = autoFitScale;
    const timerWidth = 424;
    const timerHeight = 390;
    promiseArray.push(this.sdk.updateComponent('timer-tool-component', {
      left: `calc(50% - ${timerWidth}px / 2)`,
      top: `calc(50% - ${timerWidth}px / 2)`,
      width: `${timerWidth}px`,
      height: `${timerHeight}px`,
      transform: `scale(${timerToolScale})`,
      style: 'overflow: visible;',
    }));

    // 更新进房提醒组件
    promiseArray.push(this.sdk.updateComponent('interact-reminder-component', {
      display: 'block',
    }));

    // 更新公开课进房提醒组件
    promiseArray.push(this.sdk.updateComponent('live-reminder-component', {
      display: 'block',
    }));

    // 政策提示
    promiseArray.push(this.sdk.updateComponent('class-edupolicy-component', {
      display: 'block',
    }));

    // 关闭音频上课提示
    promiseArray.push(this.sdk.updateComponent('class-closeaudio-component', {
      display: 'block',
    }));

    // 滚动消息组建
    promiseArray.push(this.sdk.updateComponent('quickmsg-show-component', {
      display: 'block',
    }));

    // 移动端无需加载设备检测
    // 以下场景无需设备检测
    // 1. 直播课 classType => 'live' 并且不是老师
    // 2. 录制模式下
    // 3. 移动端
    this.sdk.setState(Constant.TStateDeviceDetect, false);

    return Promise.all(promiseArray);
  }
}
