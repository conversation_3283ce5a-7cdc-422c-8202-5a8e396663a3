import { LayoutBase } from '@/pages/class/layout/layout_base';
import Constant from '@/util/Constant';
import Util from '@/util/Util';

export class LayoutOneOnOneDesktop extends LayoutBase {
  constructor() {
    super('LayoutOneOnOneDesktop');
    this.videoShrinkedBeforeScreenShare = false;
    this.instance = null;
  }
  static getInstance() {
    if (!this.instance) {
      this.instance = new LayoutOneOnOneDesktop();
    }
    return this.instance;
  }
  initLayout() {
    // TStateLocalAVBeforeClassBegin状态需要在VideoWrapComponent加载前设置
    this.sdk.setState(Constant.TStateLocalAVBeforeClassBegin, true);
    let initialScale = 1.0;
    if (this.sdk.isPad() && this.sdk.isAndroid()) {
      // 如果android pad的逻辑像素小于1280，通过全局scale撑满1280像素
      if (window.screen.width < 1280) {
        initialScale = window.screen.width / 1280;
        this.sdk.setState(Constant.TStateWebScale, initialScale);
      }
    }
    document.querySelector('meta[name=viewport]').
      setAttribute(
        'content',
        `width=device-width, initial-scale=${initialScale},maximum-scale=1,user-scalable=no,viewport-fit=cover`,
      );
  }
  updateLayout() {
    const promiseArray = [];
    const isTeacher = this.sdk.isTeacher();
    const screenShare = this.sdk.getState(TCIC.TMainState.Screen_Share, 2);
    const vodPlay = this.sdk.getState(TCIC.TMainState.Vod_Play, 2);
    const isShowBoardToolComponent = this.sdk.getState(Constant.TStateIsShowBoardToolComponent, false);
    const isShowFooterComponent = this.sdk.getState(Constant.TStateIsShowFooterComponent, false);
    const deviceDetecting = this.sdk.getState(Constant.TStateDeviceDetect, true);
    const isFullscreen = this.sdk.getState(Constant.TStateFullScreen, false);
    const showThumbnail = this.sdk.getState(Constant.TStateIsShowThumbnailComponent, false);
    const showHeader = this.sdk.getState(Constant.TStateHeaderVisible, true);
    const showScreenPlayer = this.sdk.getState(Constant.TStateScreenPlayerVisible, false);
    const showVodPlayer = this.sdk.getState(Constant.TStateVodPlayerVisible, false);
    const isChatTips = this.sdk.getState(Constant.TStateChatTipsEnable, true);
    const isSubCameraStarted = this.sdk.getState(Constant.TStateStartSubCamera, false);
    const isSharingPPt = this.sdk.getState(Constant.TStatePPtSharingState, false);
    // 屏幕共享、点播功能、辅助摄像头功能互斥
    const isVodPlayOnElectron = this.sdk.isTeacherOrAssistant()
        && this.sdk.isElectron()
        && (vodPlay < 2);
    let isScreenShareOnElectron = this.sdk.isElectron()
        && (screenShare < 2)
        && !isSubCameraStarted
        && !isVodPlayOnElectron; // 老师在electron下屏幕分享
    if (screenShare === 2 && isSharingPPt) {
      isScreenShareOnElectron = true;
    }
    const isScreenShareAdvanceMode = this.sdk.isFeatureAvailable('ScreenShareAdvanceMode');
    const isScreenShareAdvanceModeOnElectron = isScreenShareOnElectron && isScreenShareAdvanceMode;
    const isScreenShareSimpleModeOnElectron = isScreenShareOnElectron && !isScreenShareAdvanceMode;

    const classLayout = this.sdk.getClassLayout();
    const isVideoLayout = (classLayout === TCIC.TClassLayout.Video);
    const isThreeLayout = (classLayout === TCIC.TClassLayout.Three);

    // 高度不足以显示的情况下，一些组件需要缩小展示
    const autoFitScale = this.getFitScale(classLayout, isScreenShareOnElectron);

    if (isScreenShareSimpleModeOnElectron) {
      document.body.style.backgroundColor = 'var(--primary-color, #14181D)';
    } else if (isScreenShareAdvanceModeOnElectron) {
      // 屏幕共享时，背景颜色透明
      document.body.style.backgroundColor = 'transparent';
    }

    // 屏幕分享时，Loading页面背景透明
    this.sdk.getComponent('loading-component')
      .getVueInstance()
      .setBackgroundTransparent(isScreenShareAdvanceModeOnElectron);

    // 更新导航栏布局
    const headerVisible = showHeader
        && !isScreenShareAdvanceModeOnElectron
        && !this.sdk.getState(Constant.TStateRecordMode);
    const headerHeight = headerVisible ? 64 : 0;
    if (!this.sdk.getState(Constant.TStateRecordMode)) {
      promiseArray.push(this.sdk.updateComponent('header-component', {
        top: '0',
        left: '0',
        width: '100%',
        height: `${headerHeight}px`,
        display: headerVisible ? 'block' : 'none',
      }));
    }
    // 更新缩略图布局
    const thumbnailVisible = !deviceDetecting
        && showThumbnail
        && !isScreenShareOnElectron
        && isShowBoardToolComponent
        && !showVodPlayer;
    const thumbnailHeight = thumbnailVisible ? 110 : 0;
    const headerVideoThumbnailHeight = headerHeight + thumbnailHeight;
    let rightSideW = 0;
    if (isThreeLayout) {  // 三分布局
      rightSideW = isFullscreen ? 0 : 286;  // 三分屏右侧宽度
      const topRightH = 160;
      const sideIMVisible = true;
      promiseArray.push(this.sdk.updateComponent('ooo-video-wrap-component', {
        top: `${headerHeight}px`,
        left: `calc(100% - ${rightSideW}px)`,
        width: `${rightSideW}px`,
        height: `${topRightH}px`,
        display: sideIMVisible ? 'block' : 'none',
      }));
      promiseArray.push(this.sdk.updateComponent('introduction-discuss-component', {
        top: `${headerHeight + topRightH}px`,
        left: `calc(100% - ${rightSideW}px)`,
        width: `${rightSideW}px`,
        height: `calc(100% - ${headerHeight + topRightH}px)`,
        display: sideIMVisible ? 'block' : 'none',
      }));
      const boardVisible = !deviceDetecting
        && !isScreenShareOnElectron;
      promiseArray.push(this.sdk.updateComponent('board-component', {
        top: `${headerHeight}px`,
        left: '0',
        width: `calc(100% - ${rightSideW}px)`,
        height: `calc(100% - ${headerVideoThumbnailHeight}px)`,
        display: boardVisible ? 'block' : 'none',
        zIndex: 1,
        transform: 'scale(1)',
        transformOrigin: 'center',
      }));
    }
    promiseArray.push(this.sdk.updateComponent('thumbnail-component', {
      top: `calc(100% - ${thumbnailHeight}px)`,
      left: '0',
      width: `calc(100% - ${rightSideW}px)`,
      height: `${thumbnailHeight}px`,
      display: thumbnailVisible ? 'block' : 'none',
    }));
    promiseArray.push(this.sdk.updateComponent('trophy-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
    }));
    // 更新视频播放器
    const vodPlayerVisible = !deviceDetecting
        && !isScreenShareOnElectron
        && showVodPlayer;
    promiseArray.push(this.sdk.updateComponent('vod-player-component', {
      top: `${headerHeight}px`,
      left: '0',
      width: `calc(100% - ${rightSideW}px)`,
      height: `calc(100% - ${headerVideoThumbnailHeight}px)`,
      display: vodPlayerVisible ? 'block' : 'none',
    }));

    // 更新网络组件位置
    promiseArray.push(this.sdk.updateComponent('network-tips-component', {
      top: `${headerHeight}px`,
      left: 'calc(50% - 208px)',
      width: '416px',
      height: 'auto',
      display: 'block',
    }));

    // 屏幕分享观看布局和白板一致
    promiseArray.push(this.sdk.updateComponent('screen-player-component', {
      top: `${headerHeight}px`,
      left: '0',
      width: `calc(100% - ${rightSideW}px)`,
      height: `calc(100% - ${headerVideoThumbnailHeight}px)`,
    }));

    // 更新PPT工具栏布局
    const boardFooterVisible = !deviceDetecting
        && !isScreenShareOnElectron
        && isShowFooterComponent;
    const footerHeight = 60;
    let footerWidth;
    if (isScreenShareOnElectron) {
      footerWidth = isChatTips ? '500px' : '230px';
    } else {
      footerWidth =  `calc(100% - ${rightSideW}px)`;
    }
    promiseArray.push(this.sdk.updateComponent('footer-component', {
      left: '0px',
      top: `calc(100% - ${footerHeight}px - ${thumbnailHeight}px)`,
      width: footerWidth,
      display: boardFooterVisible ? 'block' : 'none',
      height: `${footerHeight}px`,
      transformOrigin: 'bottom',
      style: 'overflow: visible;',
    }));

    // 视频工具组件的宽高和白板保持一致
    const vodToolVisible = !deviceDetecting
        && isVodPlayOnElectron
        && showVodPlayer;
    const vodToolScale = autoFitScale;
    const vodToolHeight = 72;
    const vodToolBottomMargin = thumbnailHeight + (thumbnailVisible ? 30 : 24);
    promiseArray.push(this.sdk.updateComponent('vod-tool-component', {
      top: `calc(100% - ${vodToolHeight}px - ${vodToolBottomMargin}px)`,
      left: '0',
      width: `calc(100% - ${rightSideW}px)`,
      height: `${vodToolHeight}px`,
      display: vodToolVisible ? 'block' : 'none',
      transform: `scale(${vodToolScale})`,
      transformOrigin: 'bottom',
      style: 'overflow: visible;',
    }));

    promiseArray.push(this.sdk.updateComponent('image-preview', {
      display: 'none',
    }));

    // 更新白板工具栏布局
    let boardToolScale = 1;
    const boardToolVisible = !deviceDetecting
        && (isTeacher || !showScreenPlayer)
        && isShowBoardToolComponent
        && !showVodPlayer
        && !isVideoLayout;
    // 简单模式屏幕分享下不显示白板工具栏（宽度为0）
    const boardToolWidth = isScreenShareSimpleModeOnElectron ? 0 : 80;
    const boardToolHeight = 718;
    // 计算白板工作左上位置
    let boardToolTopInCSS;
    let boardToolOrigin;
    if (isScreenShareOnElectron) {  // 屏幕分享及右侧布局时时居中展示
      boardToolOrigin = 'right';
    } else {  // 顶部布局时留下视频栏空间
      boardToolOrigin = 'top right';
    }
    let boardToolLeftInCSS;
    if (isScreenShareOnElectron) {
      boardToolLeftInCSS = `(100% - ${boardToolWidth}px)`;
    } else {
      boardToolLeftInCSS = `(100% - ${rightSideW}px - ${boardToolWidth}px)`;
    }
    if (document.body.clientHeight - 230 < boardToolHeight) {   // 白板工具栏显示不下
      boardToolScale = ((document.body.clientHeight - 230) / boardToolHeight).toFixed(2);
    }
    promiseArray.push(this.sdk.updateComponent('board-tool-component', {
      top: `calc(50% - ${boardToolHeight / 2}px)`,
      left: `calc(${boardToolLeftInCSS})`,
      width: `${boardToolWidth}px`,
      height: `${boardToolHeight}px`,
      display: boardToolVisible ? 'block' : 'none',
      transform: `scale(${boardToolScale})`,
      transformOrigin: boardToolOrigin,
      style: 'overflow: visible;',  // 由于白板工具栏需要展示tips，需要允许展示超出部分
    }));

    if (this.sdk.isElectron()) {
      // 更新屏幕分享工具栏布局
      const shareToolbarVisible = isScreenShareOnElectron;
      promiseArray.push(this.sdk.updateComponent('share-toolbar-component', {
        top: isScreenShareAdvanceMode ? '50px' : `${headerHeight}px`,  // 屏幕分享简单模式下显示在Header下方
        left: '0',
        width: '100%',
        height: '300px',
        display: shareToolbarVisible ? 'block' : 'none',
      }));

      // 更新屏幕分享视频容器布局
      const screenVideoWrapVisible = isScreenShareOnElectron;
      const screenVideoWidth = 176;
      const screenVideoHeight = boardToolHeight - 12;
      promiseArray.push(this.sdk.updateComponent('screen-videowrap-component', {
        top: `calc(${boardToolTopInCSS} + 6px)`,
        left: `calc(${boardToolLeftInCSS} - 10px - ${screenVideoWidth}px)`,
        width: `${screenVideoWidth}px`,
        height: `${screenVideoHeight}px`,
        display: screenVideoWrapVisible ? 'block' : 'none',
        style: 'overflow: visible;',    // 由于视频容器需要展示控制栏，需要允许展示超出部分
      })
        .then((success) => {
          if (success) {
            const ele = this.sdk.getComponent('screen-videowrap-component');
            if (ele) {
              if (isScreenShareOnElectron) {
                ele.getVueInstance().loadVideoWraper();
              } else {
                ele.getVueInstance().unloadVideoWraper(classLayout);
              }
            }
          }
        }));
    }

    // 更新边栏切换按钮布局
    const sideToggleButtonVisible = !deviceDetecting && !isVideoLayout;
    // footerHeight
    promiseArray.push(this.sdk.updateComponent('side-toggle-button-component', {
      top: `calc(100% - 2.5px - ${footerHeight}px - 40px)`,
      left: `calc(100% - ${rightSideW}px - 20px)`,
      width: '20px',
      height: '40px',
      display: sideToggleButtonVisible ? 'block' : 'none',
    }));

    if (this.sdk.isElectron()) {
      // 更新屏幕分享工具栏布局
      const shareToolbarVisible = isScreenShareOnElectron;
      promiseArray.push(this.sdk.updateComponent('share-toolbar-component', {
        top: isScreenShareAdvanceMode ? '50px' : `${headerHeight}px`,  // 屏幕分享简单模式下显示在Header下方
        left: '0',
        width: '100%',
        height: '300px',
        display: shareToolbarVisible ? 'block' : 'none',
      }));

      // 更新屏幕分享视频容器布局
      const screenVideoWrapVisible = isScreenShareOnElectron;
      const screenVideoWidth = 176;
      const screenVideoHeight = boardToolHeight - 12;
      promiseArray.push(this.sdk.updateComponent('screen-videowrap-component', {
        top: `calc(${boardToolTopInCSS} + 6px)`,
        left: `calc(${boardToolLeftInCSS} - 10px - ${screenVideoWidth}px)`,
        width: `${screenVideoWidth}px`,
        height: `${screenVideoHeight}px`,
        display: screenVideoWrapVisible ? 'block' : 'none',
        style: 'overflow: visible;',    // 由于视频容器需要展示控制栏，需要允许展示超出部分
      })
        .then((success) => {
          if (success) {
            const ele = this.sdk.getComponent('screen-videowrap-component');
            if (ele) {
              if (isScreenShareOnElectron) {
                ele.getVueInstance().loadOneOnOneVideoWraper();
              } else {
                ele.getVueInstance().unloadOneOnOneVideoWraper(classLayout);
              }
            }
          }
        }));
    }

    // 更新计时器组件
    const clockToolScale = autoFitScale;
    promiseArray.push(this.sdk.updateComponent('clock-tool-component', {
      transform: `scale(${clockToolScale})`,
    }));

    // 更新定时器组件
    const timerToolScale = autoFitScale;
    promiseArray.push(this.sdk.updateComponent('timer-tool-component', {
      transform: `scale(${timerToolScale})`,
    }));

    // 更新答题器组件
    const quizScale = this.getFitScaleBasedOnHeight(classLayout, 660);
    promiseArray.push(this.sdk.updateComponent('quiz-component', {
      transform: `scale(${quizScale})`,
    }));

    // 更新平板屏幕分享组件
    if (this.sdk.isPad()) {
      if (0 === screenShare) {  // 正在屏幕分享
        promiseArray.push(this.sdk.loadComponent('share-tips-component', {
          top: `${headerHeight}px`,
          left: '0',
          width: '100%',
          height: '64px',
          display: 'block',
        }));
      } else {
        promiseArray.push(this.sdk.removeComponent('share-tips-component'));
      }
    }
    if (this.sdk.isPad()) {
      promiseArray.push(this.sdk.updateComponent('mobile-im-input-bar-component', {
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        display: 'block',
      }));
    }

    return Promise.all(promiseArray);
  }
  updateLayoutAfterJoinClass() {
    const promiseArray = [];
    const classLayout = this.sdk.getClassLayout();
    const autoFitScale = this.getFitScale(classLayout, false);  // 高度不足以显示的情况下，一些组件需要缩小展示

    const videoWrap = this.sdk.getComponent('videowrap-component');
    if (videoWrap) {
      videoWrap.getVueInstance()
        .setWrapMode('full');
    }

    // 更新计时器组件
    const clockToolScale = autoFitScale;
    const clockWidth = (this.sdk.isTeacher() || this.sdk.isAssistant()) ? 469 : 424;
    const clockHeight = 390;
    promiseArray.push(this.sdk.updateComponent('clock-tool-component', {
      left: `calc(50% - ${clockWidth}px / 2)`,
      top: `calc(50% - ${clockWidth}px / 2)`,
      width: `${clockWidth}px`,
      height: `${clockHeight}px`,
      transform: `scale(${clockToolScale})`,
      style: 'overflow: visible;',
    }));

    // 更新答题器组件
    const quizScale = autoFitScale;
    const quizHeight = (this.sdk.isTeacher() || this.sdk.isAssistant()) ? 551 : 455;
    const quizWidth = (this.sdk.isTeacher() || this.sdk.isAssistant()) ? 400 : 360;
    // const quizScale = this.getFitScaleBasedOnHeight(classLayout, quizHeight);  // 高度不足以显示的情况下，一些组件需要缩小展示
    promiseArray.push(this.sdk.updateComponent('quiz-component', {
      left: `calc(50% - ${quizWidth}px / 2)`,
      top: `calc(50% - ${quizHeight}px / 2)`,
      width: `${quizWidth}px`,
      height: `${quizHeight}px`,
      transform: `scale(${quizScale})`,
    }));
    // 答题组件在缩放的情况下，内部的popover也需要缩放
    Util.addStyle(`.sta-popover { transform: scale(${quizScale}) }`);

    // 更新定时器组件
    const timerToolScale = autoFitScale;
    const timerWidth = 424;
    const timerHeight = 390;
    promiseArray.push(this.sdk.updateComponent('timer-tool-component', {
      left: `calc(50% - ${timerWidth}px / 2)`,
      top: `calc(50% - ${timerWidth}px / 2)`,
      width: `${timerWidth}px`,
      height: `${timerHeight}px`,
      transform: `scale(${timerToolScale})`,
      style: 'overflow: visible;',
    }));


    // 更新进房提醒组件
    promiseArray.push(this.sdk.updateComponent('interact-reminder-component', {
      display: 'block',
    }));

    // 更新公开课进房提醒组件
    promiseArray.push(this.sdk.updateComponent('live-reminder-component', {
      display: 'block',
    }));

    // 政策提示
    promiseArray.push(this.sdk.updateComponent('class-edupolicy-component', {
      display: 'block',
    }));

    // 关闭音频上课提示
    promiseArray.push(this.sdk.updateComponent('class-closeaudio-component', {
      display: 'block',
    }));

    // 滚动消息组建
    promiseArray.push(this.sdk.updateComponent('quickmsg-show-component', {
      display: 'block',
    }));

    // 加载设备检测
    const isTeacher = this.sdk.isTeacher();
    const isSuperVisor = this.sdk.isSupervisor();
    const isPad = this.sdk.isPad();
    // 以下场景无需设备检测
    // 1. 直播课 classType => 'live' 并且不是老师
    // 2. 录制模式下
    // 3. 移动端
    // 4. pad
    if (this.sdk.getState(Constant.TStateRecordMode)
        || isSuperVisor
        || isPad) {
      // 直接完成设备检测
      this.sdk.setState(Constant.TStateDeviceDetect, false);
    } else {
      const loadDevice = this.sdk.checkPermission(TCIC.TResourcePath.Device, TCIC.TPermissionFlag.Read);
      if (loadDevice) {
        promiseArray.push(this.sdk.loadComponent('device-detect-component', {
          width: '100%',
          height: 'calc(100% - 64px)',
          top: '64px',
          display: 'block',
          zIndex: 600,
        })
          .then((dom) => {
            // 大屏无需检测设备
            const devices = ['screen-capture', 'browser', 'microphone', 'camera', 'speaker'];
            dom.getVueInstance()
              .start({
                flag: true,
                devices,
              });
          }));
      } else {
        this.sdk.setState(Constant.TStateDeviceDetect, false);
      }
    }
    return Promise.all(promiseArray);
  }
}
