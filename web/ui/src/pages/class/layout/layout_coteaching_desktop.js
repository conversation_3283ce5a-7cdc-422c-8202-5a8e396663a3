import { LayoutBase } from '@/pages/class/layout/layout_base';
import Constant from '@/util/Constant';

export class LayoutCoTeachingDestop extends LayoutBase {
  constructor() {
    super('LayoutCoTeachingDestop');
    this.videoShrinkedBeforeScreenShare = false;
    this.instance = null;
  }
  static getInstance() {
    if (!this.instance) {
      this.instance = new LayoutCoTeachingDestop();
    }
    return this.instance;
  }
  initLayout() {
    // TStateLocalAVBeforeClassBegin状态需要在VideoWrapComponent加载前设置
    if (this.sdk.isTeacher()) {
      this.sdk.setState(Constant.TStateLocalAVBeforeClassBegin, true);
    } else {
      this.sdk.setState(Constant.TStateLocalAVBeforeClassBegin, false);
    }
    let initialScale = 1.0;
    if (this.sdk.isPad() && this.sdk.isAndroid()) {
      // 如果android pad的逻辑像素小于1280，通过全局scale撑满1280像素
      if (window.screen.width < 1280) {
        initialScale = window.screen.width / 1280;
        this.sdk.setState(Constant.TStateWebScale, initialScale);
      }
    }
    document.querySelector('meta[name=viewport]').
      setAttribute(
        'content',
        `width=device-width, initial-scale=${initialScale},maximum-scale=1,user-scalable=no,viewport-fit=cover`,
      );
    // 初始化机顶盒按键模块
    if (this.sdk.isAndroid() || this.sdk.isTeacher()) {
      window.tbm.init();
      const wid = window.tbm.pushWindow('App');
      window.tbm.pushTarget('header', ['back', 'info', 'menu', 'button', 'subMenu', 'notice', 'muteall', 'layout'], wid);
      window.tbm.pushTarget('classinfo', ['buttons'], wid);
      window.tbm.pushTarget('setting', ['camera', 'cameraSelect', 'cameraMirror', 'mic', 'micSelect', 'micVolume', 'speaker', 'speakerVolumes', 'button'], wid);
      window.tbm.pushTarget('screenshare', ['close', 'screen', 'window', 'option', 'button'], wid);
      window.tbm.pushTarget('screentool', ['menu', 'muteall', 'systemaudio'], wid);
      window.tbm.pushTarget('tips', ['default'], wid);
      window.tbm.pushTarget('notice', ['title', 'content', 'button'], wid);
      window.tbm.pushTarget('memberlist', ['title', 'tab', 'mute', 'students'], wid);
      window.tbm.pushTarget('videowall', ['line0', 'line1', 'line2', 'line3'], wid);
      window.tbm.pushTarget('handup', ['default'], wid);
      window.tbm.pushTarget('pagination', ['default'], wid);
    }
  }
  updateLayout() {
    const promiseArray = [];
    const isShowDeviceDetect = this.sdk.getState(Constant.TStateDeviceDetect, false);
    const screenShare = this.sdk.getState(TCIC.TMainState.Screen_Share, 2);
    const isScreenShareOnElectron = this.sdk.isElectron()
        && (screenShare < 2); // 老师在electron下屏幕分享
    const isScreenShareAdvanceMode = this.sdk.isFeatureAvailable('ScreenShareAdvanceMode');
    const isScreenShareAdvanceModeOnElectron = isScreenShareOnElectron && isScreenShareAdvanceMode;
    const isScreenShareSimpleModeOnElectron = isScreenShareOnElectron && !isScreenShareAdvanceMode;
    if (isScreenShareSimpleModeOnElectron) {
      document.body.style.backgroundColor = 'var(--primary-color, #14181D)';
    } else if (isScreenShareAdvanceModeOnElectron) {
      // 屏幕共享时，背景颜色透明
      document.body.style.backgroundColor = 'transparent';
    }
    promiseArray.push(this.sdk.updateComponent('ct-device-detect-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
      display: isShowDeviceDetect ? 'block' : 'none',
    }));
    const headerHeight = 64;
    promiseArray.push(this.sdk.updateComponent('header-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: `${headerHeight}px`,
      display: 'block',
    }));
    promiseArray.push(this.sdk.updateComponent('ct-toast-component', {
      top: `${headerHeight}px`,
      left: 'calc(50% - 208px)',
      width: 'auto',
      height: 'auto',
      display: 'block',
    }));
    const gridTop = headerHeight;
    promiseArray.push(this.sdk.updateComponent('ct-video-component', {
      top: `${gridTop}px`,
      left: '0',
      width: '100%',
      height: `calc(100% - ${gridTop}px)`,
      display: 'block',
    }));
    promiseArray.push(this.sdk.updateComponent('ct-hand-up-component', {
    }));
    // 是否显示举手列表
    const isCoShowHandupList = this.sdk.getState(Constant.TStateCoTeachingShowHandupList, false);
    promiseArray.push(this.sdk.updateComponent('ct-hand-up-list-component', {
      display: isCoShowHandupList ? 'block' : 'none',
    }));
    if (this.sdk.isElectron()) {
      // 更新屏幕分享工具栏布局
      const shareToolbarVisible = isScreenShareOnElectron;
      promiseArray.push(this.sdk.updateComponent('share-toolbar-component', {
        top: isScreenShareAdvanceMode ? '50px' : `${headerHeight}px`,  // 屏幕分享简单模式下显示在Header下方
        left: '0',
        width: '100%',
        height: '300px',
        display: shareToolbarVisible ? 'block' : 'none',
      }));
    }
    // 更新屏幕分享播放组件位置
    promiseArray.push(this.sdk.updateComponent('screen-player-component', {
      top: `${headerHeight}px`,
    }));
    return Promise.all(promiseArray);
  }
  updateLayoutAfterJoinClass() {
    const promiseArray = [];
    // 更新进房提醒组件
    promiseArray.push(this.sdk.updateComponent('ct-interact-reminder-component', {
      display: 'block',
    }));

    // 加载设备检测
    const isSuperVisor = this.sdk.isSupervisor();
    const isAssistant = this.sdk.isAssistant();
    const isPad = this.sdk.isPad();
    // 以下场景无需设备检测
    if (this.sdk.getState(Constant.TStateRecordMode)
        || isAssistant
        || isSuperVisor
        || isPad) {
      // 直接完成设备检测
      this.sdk.setState(Constant.TStateDeviceDetect, false);
    } else {
      const loadDevice = this.sdk.checkPermission(TCIC.TResourcePath.Device, TCIC.TPermissionFlag.Read);
      if (loadDevice) {
        promiseArray.push(this.sdk.loadComponent('ct-device-detect-component', {
          width: '100%',
          height: 'calc(100% - 45px)',
          top: '45px',
          display: 'block',
          zIndex: 1501,
        })
          .then((dom) => {
            // 大屏无需检测设备
            const devices = ['screen-capture', 'browser', 'microphone', 'camera', 'speaker'];
            dom.getVueInstance()
              .start({
                flag: true,
                devices,
              });
          }));
      } else {
        this.sdk.setState(Constant.TStateDeviceDetect, false);
      }
    }
    return Promise.all(promiseArray);
  }
}
