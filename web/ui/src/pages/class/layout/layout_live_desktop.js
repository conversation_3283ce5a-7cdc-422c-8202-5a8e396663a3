import { LayoutBase } from '@/pages/class/layout/layout_base';
import Constant from '@/util/Constant';
import Util from '@/util/Util';

export class LayoutLiveDesktop extends LayoutBase {
  constructor() {
    super('LayoutLiveDesktop');
    this.videoShrinkedBeforeScreenShare = false;
    this.instance = null;
  }
  static getInstance() {
    if (!this.instance) {
      this.instance = new LayoutLiveDesktop();
    }
    return this.instance;
  }
  initLayout() {
    // TStateLocalAVBeforeClassBegin状态需要在VideoWrapComponent加载前设置
    if (this.sdk.isTeacher()) {
      this.sdk.setState(Constant.TStateLocalAVBeforeClassBegin, true);
    } else {
      this.sdk.setState(Constant.TStateLocalAVBeforeClassBegin, false);
    }

    let initialScale = 1.0;
    if (this.sdk.isPad() && this.sdk.isAndroid()) {
      // 如果android pad的逻辑像素小于1280，通过全局scale撑满1280像素
      if (window.screen.width < 1280) {
        initialScale = window.screen.width / 1280;
        this.sdk.setState(Constant.TStateWebScale, initialScale);
      }
    }
    document.querySelector('meta[name=viewport]').
      setAttribute(
        'content',
        `width=device-width, initial-scale=${initialScale},maximum-scale=1,user-scalable=no,viewport-fit=cover`,
      );
  }
  updateLayout() {
    const promiseArray = [];
    const isTeacher = this.sdk.isTeacher();
    const isAssistant = this.sdk.isAssistant();
    const isSupervisor = this.sdk.isSupervisor();
    const stageCount = this.sdk.getState(TCIC.TMainState.Stage_Count, 0);
    const screenShare = this.sdk.getState(TCIC.TMainState.Screen_Share, 2);
    const vodPlay = this.sdk.getState(TCIC.TMainState.Vod_Play, 2);
    const isShowBoardToolComponent = this.sdk.getState(Constant.TStateIsShowBoardToolComponent, false);
    const isShowFooterComponent = this.sdk.getState(Constant.TStateIsShowFooterComponent, false);
    const deviceDetecting = this.sdk.getState(Constant.TStateDeviceDetect, true);
    const isFullscreen = this.sdk.getState(Constant.TStateFullScreen, false);
    const showThumbnail = this.sdk.getState(Constant.TStateIsShowThumbnailComponent, false);
    const showHeader = this.sdk.getState(Constant.TStateHeaderVisible, true);
    const showScreenPlayer = this.sdk.getState(Constant.TStateScreenPlayerVisible, false);
    const showVodPlayer = this.sdk.getState(Constant.TStateVodPlayerVisible, false);
    const isChatTips = this.sdk.getState(Constant.TStateChatTipsEnable, true);
    const isSubCameraStarted = this.sdk.getState(Constant.TStateStartSubCamera, false);
    const isBigVideoMode = this.sdk.getState(Constant.TStateBigVideoMode, false);
    const isSharingPPt = this.sdk.getState(Constant.TStatePPtSharingState, false);
    const isRecordMode = this.sdk.getState(Constant.TStateRecordMode);

    // 屏幕共享、点播功能、辅助摄像头功能互斥
    const isVodPlayOnElectron = this.sdk.isTeacherOrAssistant()
        && this.sdk.isElectron()
        && (vodPlay < 2);
    let isScreenShareOnELectron = this.sdk.isElectron()
        && (screenShare < 2)
        && !isSubCameraStarted
        && isVodPlayOnElectron; // 老师在electron下屏幕分享
    if (screenShare === 2 && isSharingPPt) {
      isScreenShareOnELectron = true;
    }
    const isScreenShareAdvanceMode = this.sdk.isFeatureAvailable('ScreenShareAdvanceMode');
    const isTeacherScreenShareAdvanceModeOnElectron = isScreenShareOnELectron && isScreenShareAdvanceMode;
    const isTeacherScreenShareSimpleModeOnElectron = isScreenShareOnELectron && !isScreenShareAdvanceMode;

    const classLayout = this.sdk.getClassLayout();

    // 高度不足以显示的情况下，一些组件需要缩小展示
    const autoFitScale = this.getFitScale(classLayout, isScreenShareOnELectron);

    if (isTeacherScreenShareSimpleModeOnElectron) {
      document.body.style.backgroundColor = 'var(--primary-color, #14181D)';
    } else if (isTeacherScreenShareAdvanceModeOnElectron) {
      // 屏幕共享时，背景颜色透明
      document.body.style.backgroundColor = 'transparent';
    }

    // 屏幕分享时，Loading页面背景透明
    this.sdk.getComponent('loading-component')
      .getVueInstance()
      .setBackgroundTransparent(isTeacherScreenShareAdvanceModeOnElectron);

    // 更新导航栏布局
    const headerVisible = showHeader
        && !isTeacherScreenShareAdvanceModeOnElectron
        && !isRecordMode;
    const headerHeight = headerVisible ? 64 : 0;
    promiseArray.push(this.sdk.updateComponent('header-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: `${headerHeight}px`,
      display: headerVisible ? 'block' : 'none',
    }));

    // 更新老师视频布局
    const videoVisible = !deviceDetecting && !isFullscreen && !isTeacherScreenShareAdvanceModeOnElectron;
    const teacherVideoWidth = isFullscreen ? 0 : 286;  // 视频占用空间
    const teacherVideoHeight = (isFullscreen || classLayout === TCIC.TClassLayout.Video) ? 0 : 160;
    const floatLayout = {
      top: `${headerHeight}px`,
      left: `calc(100% - ${teacherVideoWidth}px)`,
      width: `${teacherVideoWidth}px`,
      height: `${teacherVideoHeight}px`,
      display: videoVisible ? 'block' : 'none',
      zIndex: 300,
      style: 'overflow: visible;',
    };
    promiseArray.push(this.sdk.updateComponent('college-video-switch-component', {
      top: `${headerHeight + teacherVideoHeight - 44}px`,
      left: `calc(100% - ${44}px)`,
      width: '44px',
      height: '44px',
      display: videoVisible ? 'block' : 'none',
      zIndex: 310,
      style: 'overflow: visible;',
    }));

    // 更新视频列表布局
    const videoWrapVisible = (!deviceDetecting
        && !isFullscreen
        && stageCount > 0)
        || isTeacherScreenShareAdvanceModeOnElectron;
    let studentVideoWrapHeight = 0;
    if (stageCount > 0) { // 连麦
      studentVideoWrapHeight = isFullscreen ? 0 : 110;
    }
    // 屏幕分享简单模式下不预留视频栏空间
    if (isTeacherScreenShareSimpleModeOnElectron) {
      studentVideoWrapHeight = 0;
    }
    // 更新缩略图布局
    const thumbnailVisible = !deviceDetecting
        && showThumbnail
        && !isScreenShareOnELectron
        && isShowBoardToolComponent
        && !showVodPlayer;
    const thumbnailHeight = thumbnailVisible ? 110 : 0;
    const headerVideoHeight = headerHeight + studentVideoWrapHeight;
    const headerVideoThumbnailHeight = headerVideoHeight + thumbnailHeight;
    promiseArray.push(this.sdk.updateComponent('thumbnail-component', {
      top: `calc(100% - ${thumbnailHeight}px)`,
      left: '0',
      width: `calc(100% - ${teacherVideoWidth}px)`,
      height: `${thumbnailHeight}px`,
      display: thumbnailVisible ? 'block' : 'none',
    }));

    // 更新白板布局
    const boardVisible = !deviceDetecting
        && !isScreenShareOnELectron;
    let videowrapLayout = {};
    let teacherVideoLayout = {};
    let controlDirect = 'left';
    const boardlayout = {
      top: `${headerVideoHeight}px`,
      left: '0',
      width: `calc(100% - ${teacherVideoWidth}px)`,
      height: `calc(100% - ${headerVideoThumbnailHeight}px)`,
      zIndex: 1,
      display: boardVisible && classLayout !== TCIC.TClassLayout.Video ? 'block' : 'none',
      transform: 'scale(1)',
      transformOrigin: 'center',
    };

    if (isBigVideoMode) {
      const width = document.body.clientWidth - teacherVideoWidth;
      const height = width * 9.0 / 16.0;
      const scale = teacherVideoWidth / width;
      boardlayout.top = `${headerHeight}px`;
      boardlayout.left = `${teacherVideoWidth}px`;
      boardlayout.width = `calc(100% - ${teacherVideoWidth}px)`;
      boardlayout.height = `${height}px`;
      // 用scale的方式，解决白板在切换时出现闪烁的问题
      boardlayout.transform = `scale(${scale})`;
      boardlayout.transformOrigin = 'top right';
    }
    promiseArray.push(TCIC.SDK.instance.updateComponent('board-component', boardlayout));
    if (classLayout === TCIC.TClassLayout.Video) {
      // 纯视频模式, 老师+学生在videowrap-component中以视频墙方式展示，没有屏幕共享
      // 纯视频模式没有白板缩略图，没有顶部视频区
      videowrapLayout = {
        top: `${headerHeight}px`,
        left: '0',
        width: `calc(100% - ${teacherVideoWidth}px)`,
        height: `calc(100% - ${headerHeight}px)`,
        zIndex: 1,
        display: boardVisible ? 'block' : 'none',
      };
      controlDirect = 'hide';
    } else {
      if (isScreenShareOnELectron) {
        // 屏幕共享时， videowrap-component作为screen-videowrap-component的子组件显示
        videowrapLayout = {
          top: '0',
          left: '0',
          position: 'relative',
          width: '100%',
          height: '100%',
          display: 'block',
        };
        teacherVideoLayout = {
          top: '0',
          left: '0',
          width: '160px',
          height: '90px',
          zIndex: 1,
          display: 'block',
          position: 'relative',
        };
      } else {
        // 文档+视频， videowrap在左边顶部
        videowrapLayout = {
          top: `${headerHeight}px`,
          left: '0px',
          width: `calc(100% - ${teacherVideoWidth}px)`,
          height: `${studentVideoWrapHeight}px`,
          display: videoWrapVisible ? 'block' : 'none',
          zIndex: 1,
          style: 'overflow: visible;',
          position: 'absolute',
        };
        if (isBigVideoMode) {
          // 老师视屏在主显示区，16：9居中
          const top = headerHeight + studentVideoWrapHeight;
          teacherVideoLayout = {
            top: `calc(${top}px + (100vh - ${top}px) / 2  - (100vw - ${teacherVideoWidth}px) * (9 / 16) / 2)`,
            left: '0',
            width: `calc(100vw - ${teacherVideoWidth}px)`,
            height: `calc((100vw - ${teacherVideoWidth}px) * (9 / 16))`,
            zIndex: 1,
            display: boardVisible ? 'block' : 'none',
          };
        } else {
          teacherVideoLayout = floatLayout;
        }
      }
      controlDirect = 'left';
    }
    if (classLayout !== TCIC.TClassLayout.Video) {
      // 文档+视频
      promiseArray.push(TCIC.SDK.instance.updateComponent('teacher-component', teacherVideoLayout).then((sucess) => {
        const teacherDom = TCIC.SDK.instance.getComponent('teacher-component');
        if (sucess && teacherDom) {
          teacherDom.getVueInstance().setControlDirect(controlDirect);
        }
      }));
    }
    promiseArray.push(this.sdk.updateComponent('videowrap-component', videowrapLayout)
      .then((success) => {
        if (success) {
          const comp = this.sdk.getComponent('videowrap-component');
          if (comp) {
            comp.getVueInstance()
              .setWrapLayout(isScreenShareOnELectron ? 'screen' : classLayout);
            comp.getVueInstance()
              .setWrapMode('full');   // 屏幕分享结束后需恢复视频模式
            if (isScreenShareOnELectron) {  // 屏幕分享不隐藏视频
              if (isFullscreen) {
                comp.getVueInstance()
                  .toggleShow(true);
                this.videoShrinkedBeforeScreenShare = true;
              }
            } else {
              if (this.videoShrinkedBeforeScreenShare) {
                comp.getVueInstance()
                  .toggleShow(false);
                this.videoShrinkedBeforeScreenShare = false;
              }
            }
          }
        }
      }));

    // 连麦提示组件
    const stageListVisible = !isRecordMode && (isTeacher || isAssistant || isSupervisor);
    promiseArray.push(this.sdk.updateComponent('stage-float-list-component', {
      width: `calc(100% - ${teacherVideoWidth}px)`,
      display: stageListVisible ? 'block' : 'none',
    }));

    // 更新提示布局（宽高和白板一致）
    promiseArray.push(this.sdk.updateComponent('live-reminder-component', {
      top: `${headerVideoHeight}px`,
      left: '0',
      width: `calc(100% - ${teacherVideoWidth}px)`,
      height: `calc(100% - ${headerVideoThumbnailHeight}px)`,
      display: 'block',
    }));

    // 更新视频播放器
    const vodPlayerVisible = !deviceDetecting
        && !isScreenShareOnELectron
        && showVodPlayer;
    promiseArray.push(this.sdk.updateComponent('vod-player-component', {
      top: `${headerVideoHeight}px`,
      left: '0',
      width: `calc(100% - ${teacherVideoWidth}px)`,
      height: `calc(100% - ${headerVideoThumbnailHeight}px)`,
      display: vodPlayerVisible ? 'block' : 'none',
    }));

    // 更新网络组件位置
    promiseArray.push(this.sdk.updateComponent('network-tips-component', {
      top: `${headerVideoHeight}px`,
      left: 'calc(50% - 208px)',
      width: '416px',
      height: 'auto',
      display: 'block',
    }));

    // 屏幕分享观看布局和白板一致
    promiseArray.push(this.sdk.updateComponent('screen-player-component', {
      top: `${headerVideoHeight}px`,
      left: '0',
      width: `calc(100% - ${teacherVideoWidth}px)`,
      height: `calc(100% - ${headerVideoThumbnailHeight}px)`,
    }));

    // 更新PPT工具栏布局
    const boardFooterVisible = !deviceDetecting
        && !isRecordMode
        && (isTeacher || isAssistant || isSupervisor)
        && !showScreenPlayer
        && isShowFooterComponent
        && !showVodPlayer
        && classLayout !== TCIC.TClassLayout.Video;
    const footerHeight = 80;
    let footerWidth;
    if (isScreenShareOnELectron) {
      footerWidth = isChatTips ? '500px' : '230px';
    } else {
      footerWidth =  `calc(100% - ${teacherVideoWidth}px)`;
    }
    promiseArray.push(this.sdk.updateComponent('footer-component', {
      left: '0px',
      top: `calc(100% - ${footerHeight}px - ${thumbnailHeight}px)`,
      width: footerWidth,
      display: boardFooterVisible ? 'block' : 'none',
      height: `${footerHeight}px`,
      transformOrigin: 'bottom',
      style: 'overflow: visible;',
    }));

    // 学生端连麦入口
    promiseArray.push(this.sdk.updateComponent('stage-access-component', {
      width: footerWidth,
    }));

    // 视频工具组件的宽高和白板保持一致
    const vodToolVisible = !deviceDetecting
        && isVodPlayOnElectron
        && showVodPlayer;
    const vodToolScale = autoFitScale;
    const vodToolHeight = 72;
    const vodToolBottomMargin = thumbnailHeight + (thumbnailVisible ? 30 : 24);
    promiseArray.push(this.sdk.updateComponent('vod-tool-component', {
      top: `calc(100% - ${vodToolHeight}px - ${vodToolBottomMargin}px)`,
      left: '0',
      width: `calc(100% - ${teacherVideoWidth}px)`,
      height: `${vodToolHeight}px`,
      display: vodToolVisible ? 'block' : 'none',
      transform: `scale(${vodToolScale})`,
      transformOrigin: 'bottom',
      style: 'overflow: visible;',
    }));

    promiseArray.push(this.sdk.updateComponent('image-preview', {
      display: 'none',
    }));

    // 更新边栏聊天组件布局
    const sideIMVisible = !deviceDetecting && !isScreenShareOnELectron && !isFullscreen;
    promiseArray.push(this.sdk.updateComponent('introduction-discuss-component', {
      top: `${headerHeight + teacherVideoHeight}px`,
      left: `calc(100% - ${teacherVideoWidth}px)`,
      width: `${teacherVideoWidth}px`,
      height: `calc(100% - ${headerHeight + teacherVideoHeight}px)`,
      display: sideIMVisible ? 'block' : 'none',
    }));

    // 更新边栏切换按钮布局
    const sideToggleButtonVisible = !deviceDetecting && !isScreenShareOnELectron;
    // footerHeight
    promiseArray.push(this.sdk.updateComponent('side-toggle-button-component', {
      top: `calc(100% - 2.5px - ${footerHeight}px - 40px)`,
      left: `calc(100% - ${teacherVideoWidth}px - 20px)`,
      width: '20px',
      height: '40px',
      display: sideToggleButtonVisible ? 'block' : 'none',
    }));

    // 更新白板工具栏布局
    let boardToolScale = 1;
    const boardToolVisible = !deviceDetecting
        && !isRecordMode
        && (isTeacher || !showScreenPlayer)
        && isShowBoardToolComponent
        && !showVodPlayer
        && classLayout !== TCIC.TClassLayout.Video;
    // 简单模式屏幕分享下、 公开课老师视频全屏显示 不显示白板工具栏（宽度为0）
    const boardToolWidth = isTeacherScreenShareSimpleModeOnElectron ? 0 : 80;
    const boardToolHeight = 718;
    // 计算白板工作左上位置
    // side-toggle-button-component 的高度为40px + 上下margin 5px = 50px
    let boardToolTopInCSS = `calc(100% - ${boardToolHeight}px) - 50px`;
    let boardToolOrigin;
    if (isScreenShareOnELectron) {  // 屏幕分享
      boardToolTopInCSS = `calc(${headerVideoHeight}px + (100% - ${headerVideoThumbnailHeight}px - ${boardToolHeight}px) / 2) - 50px)`;
      boardToolOrigin = 'right';
    } else {  // 顶部布局时留下视频栏空间
      boardToolTopInCSS = `calc(100% - ${footerHeight}px - ${boardToolHeight}px - 50px)`;
      boardToolOrigin = 'bottom right';
    }
    let boardToolLeftInCSS;
    if (isScreenShareOnELectron) {
      boardToolLeftInCSS = `calc(100% - ${boardToolWidth}px)`;
    } else {
      boardToolLeftInCSS = `calc(100% - ${teacherVideoWidth}px - ${boardToolWidth}px)`;
    }
    if (document.body.clientHeight - 230 < boardToolHeight) {   // 白板工具栏显示不下
      boardToolScale = ((document.body.clientHeight - 230) / boardToolHeight).toFixed(2);
    }
    promiseArray.push(this.sdk.updateComponent('board-tool-component', {
      top: boardToolTopInCSS,
      left: boardToolLeftInCSS,
      width: `${boardToolWidth}px`,
      height: `${boardToolHeight}px`,
      display: boardToolVisible ? 'block' : 'none',
      transform: `scale(${boardToolScale})`,
      transformOrigin: boardToolOrigin,
      style: 'overflow: visible;',  // 由于白板工具栏需要展示tips，需要允许展示超出部分
    }));

    if (this.sdk.isElectron()) {
      // 更新屏幕分享工具栏布局
      const shareToolbarVisible = isScreenShareOnELectron;
      promiseArray.push(this.sdk.updateComponent('share-toolbar-component', {
        top: isScreenShareAdvanceMode ? '50px' : `${headerHeight}px`,  // 屏幕分享简单模式下显示在Header下方
        left: '0',
        width: '100%',
        height: '300px',
        display: shareToolbarVisible ? 'block' : 'none',
      }));

      // 更新屏幕分享视频容器布局
      const screenVideoWrapVisible = isScreenShareOnELectron;
      const screenVideoWidth = 176;
      const screenVideoHeight = boardToolHeight - 12;
      promiseArray.push(this.sdk.updateComponent('screen-videowrap-component', {
        top: `calc(${boardToolTopInCSS} + 6px)`,
        left: `calc(${boardToolLeftInCSS} - 10px - ${screenVideoWidth}px)`,
        width: `${screenVideoWidth}px`,
        height: `${screenVideoHeight}px`,
        display: screenVideoWrapVisible ? 'block' : 'none',
        style: 'overflow: visible;',    // 由于视频容器需要展示控制栏，需要允许展示超出部分
      })
        .then((success) => {
          if (success) {
            const ele = this.sdk.getComponent('screen-videowrap-component');
            if (ele) {
              if (isScreenShareOnELectron) {
                ele.getVueInstance().loadVideoWraper();
              } else {
                ele.getVueInstance().unloadVideoWraper(classLayout);
              }
            }
          }
        }));
    }

    // 更新计时器组件
    const clockToolScale = autoFitScale;
    promiseArray.push(this.sdk.updateComponent('clock-tool-component', {
      transform: `scale(${clockToolScale})`,
    }));

    // 更新定时器组件
    const timerToolScale = autoFitScale;
    promiseArray.push(this.sdk.updateComponent('timer-tool-component', {
      transform: `scale(${timerToolScale})`,
    }));

    // 更新答题器组件
    const quizScale = this.getFitScaleBasedOnHeight(classLayout, 660);
    promiseArray.push(this.sdk.updateComponent('quiz-component', {
      transform: `scale(${quizScale})`,
    }));

    // 更新平板屏幕分享组件
    if (this.sdk.isPad()) {
      if (0 === screenShare) {  // 正在屏幕分享
        promiseArray.push(this.sdk.loadComponent('share-tips-component', {
          top: `${headerVideoHeight}px`,
          left: '0',
          width: `calc(100% - ${teacherVideoWidth}px)`,
          height: '64px',
          display: 'block',
        }));
      } else {
        promiseArray.push(this.sdk.removeComponent('share-tips-component'));
      }
    }
    if (this.sdk.isPad()) {
      promiseArray.push(this.sdk.updateComponent('mobile-im-input-bar-component', {
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        display: 'block',
      }));
    }

    // 是否正在邀请我
    const isInvitingMe = this.sdk.getState(Constant.TStateLiveTeacherInvitingMe, false);
    promiseArray.push(this.sdk.updateComponent('invite-stage-dialog-component', {
      display: isInvitingMe ? 'block' : 'none',
    }));

    return Promise.all(promiseArray);
  }
  updateLayoutAfterJoinClass() {
    const promiseArray = [];
    const classLayout = this.sdk.getClassLayout();
    // 直播课
    const comp = this.sdk.getComponent('quick-im-component');
    comp && comp.getVueInstance()
      .toggleActive(false);
    const autoFitScale = this.getFitScale(classLayout, false);  // 高度不足以显示的情况下，一些组件需要缩小展示

    const videoWrap = this.sdk.getComponent('videowrap-component');
    if (videoWrap) {
      videoWrap.getVueInstance()
        .setWrapMode('teacher');
    }

    // 更新计时器组件
    const clockToolScale = autoFitScale;
    const clockWidth = (this.sdk.isTeacher() || this.sdk.isAssistant()) ? 469 : 424;
    const clockHeight = 390;
    promiseArray.push(this.sdk.updateComponent('clock-tool-component', {
      left: `calc(50% - ${clockWidth}px / 2)`,
      top: `calc(50% - ${clockWidth}px / 2)`,
      width: `${clockWidth}px`,
      height: `${clockHeight}px`,
      transform: `scale(${clockToolScale})`,
      style: 'overflow: visible;',
    }));

    // 更新答题器组件
    const quizScale = autoFitScale;
    const quizHeight = (this.sdk.isTeacher() || this.sdk.isAssistant()) ? 551 : 455;
    const quizWidth = (this.sdk.isTeacher() || this.sdk.isAssistant()) ? 400 : 360;
    // const quizScale = this.getFitScaleBasedOnHeight(classLayout, quizHeight);  // 高度不足以显示的情况下，一些组件需要缩小展示
    promiseArray.push(this.sdk.updateComponent('quiz-component', {
      left: `calc(50% - ${quizWidth}px / 2)`,
      top: `calc(50% - ${quizHeight}px / 2)`,
      width: `${quizWidth}px`,
      height: `${quizHeight}px`,
      transform: `scale(${quizScale})`,
    }));
    // 答题组件在缩放的情况下，内部的popover也需要缩放
    Util.addStyle(`.sta-popover { transform: scale(${quizScale}) }`);

    // 更新定时器组件
    const timerToolScale = autoFitScale;
    const timerWidth = 424;
    const timerHeight = 390;
    promiseArray.push(this.sdk.updateComponent('timer-tool-component', {
      left: `calc(50% - ${timerWidth}px / 2)`,
      top: `calc(50% - ${timerWidth}px / 2)`,
      width: `${timerWidth}px`,
      height: `${timerHeight}px`,
      transform: `scale(${timerToolScale})`,
      style: 'overflow: visible;',
    }));


    // 更新进房提醒组件
    promiseArray.push(this.sdk.updateComponent('interact-reminder-component', {
      display: 'block',
    }));

    // 更新公开课进房提醒组件
    promiseArray.push(this.sdk.updateComponent('live-reminder-component', {
      display: 'block',
    }));

    // 政策提示
    promiseArray.push(this.sdk.updateComponent('class-edupolicy-component', {
      display: 'block',
    }));

    // 关闭音频上课提示
    promiseArray.push(this.sdk.updateComponent('class-closeaudio-component', {
      display: 'block',
    }));

    // 加载设备检测
    const isStudent = this.sdk.isStudent();
    const isPad = this.sdk.isPad();
    // 以下场景无需设备检测
    // 1. 直播课 classType => 'live' 并且不是老师
    // 2. 录制模式下
    // 3. 移动端
    // 4. pad
    if (this.sdk.getState(Constant.TStateRecordMode)
        || isStudent
        || isPad) {
      // 直接完成设备检测
      this.sdk.setState(Constant.TStateDeviceDetect, false);
    } else {
      const loadDevice = this.sdk.checkPermission(TCIC.TResourcePath.Device, TCIC.TPermissionFlag.Read);
      if (loadDevice) {
        promiseArray.push(this.sdk.loadComponent('device-detect-component', {
          width: '100%',
          height: 'calc(100% - 64px)',
          top: '64px',
          display: 'block',
          zIndex: 600,
        })
          .then((dom) => {
            // 大屏无需检测设备
            const devices = ['screen-capture', 'browser', 'microphone', 'camera', 'speaker'];
            dom.getVueInstance()
              .start({
                flag: true,
                devices,
              });
          }));
      } else {
        this.sdk.setState(Constant.TStateDeviceDetect, false);
      }
    }

    return Promise.all(promiseArray);
  }
}
