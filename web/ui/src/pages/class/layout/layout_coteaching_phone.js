import { LayoutBase } from '@/pages/class/layout/layout_base';
import Constant from '@/util/Constant';

export class LayoutCoTeachingPhone extends LayoutBase {
  constructor() {
    super('LayoutCoTeachingPhone');
    this.instance = null;
  }
  static getInstance() {
    if (!this.instance) {
      this.instance = new LayoutCoTeachingPhone();
    }
    return this.instance;
  }
  initLayout() {
    // 初始化机顶盒按键模块
    if (TCIC.SDK.instance.isAndroid()) {
      window.tbm.init();
      const wid = window.tbm.pushWindow('App');
      window.tbm.pushTarget('header', ['back', 'info', 'menu', 'button', 'subMenu', 'layout'], wid);
      window.tbm.pushTarget('setting', ['camera', 'cameraSelect', 'cameraMirror', 'mic', 'micSelect', 'micVolume', 'speaker', 'speakerVolumes', 'button'], wid);
      window.tbm.pushTarget('tips', ['default'], wid);
      window.tbm.pushTarget('notice', ['title', 'button'], wid);
      window.tbm.pushTarget('handup', ['default'], wid);
    }
  }
  updateLayout() {
    const promiseArray = [];
    const headerHeight = 45;
    promiseArray.push(this.sdk.updateComponent('header-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: `${headerHeight}px`,
      display: 'block',
    }));
    promiseArray.push(this.sdk.updateComponent('ct-toast-component', {
      top: `${headerHeight}px`,
      left: 'calc(50% - 208px)',
      width: 'auto',
      height: 'auto',
      display: 'block',
    }));
    const gridTop = headerHeight;
    promiseArray.push(this.sdk.updateComponent('ct-video-component', {
      top: `${gridTop}px`,
      left: '0',
      width: '100%',
      height: `calc(100% - ${gridTop}px)`,
      display: 'block',
    }));
    promiseArray.push(this.sdk.updateComponent('ct-hand-up-component', {
    }));
    return Promise.all(promiseArray);
  }
  updateLayoutAfterJoinClass() {
    const promiseArray = [];
    // 更新进房提醒组件
    promiseArray.push(this.sdk.updateComponent('ct-interact-reminder-component', {
      display: 'block',
    }));

    this.sdk.setState(Constant.TStateDeviceDetect, false);
    return Promise.all(promiseArray);
  }
}
