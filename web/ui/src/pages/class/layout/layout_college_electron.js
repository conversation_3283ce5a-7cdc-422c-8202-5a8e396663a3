import { LayoutBase } from '@/pages/class/layout/layout_base';
import Constant from '../../../util/Constant';

export class LayoutCollegeElectron extends LayoutBase {
  constructor() {
    super('LayoutCollegeElectron');
    this.instance = null;
  }
  static getInstance() {
    if (!this.instance) {
      this.instance = new LayoutCollegeElectron();
    }
    return this.instance;
  }
  initLayout() {
    document.body.style.backgroundColor = 'transparent';
  }
  updateLayout() {
    const promiseArray = [];
    promiseArray.push(this.sdk.updateComponent('loading-component', {
      display: 'none',
    }));
    promiseArray.push(this.sdk.updateComponent('teacher-videowrap-component', {}).then((success) => {
      const comp = this.sdk.getComponent('teacher-videowrap-component');
      if (success && comp) {
        comp.getVueInstance().updateLayout();
      }
    }));
    return Promise.all(promiseArray);
  }
  updateLayoutAfterJoinClass() {
    const promiseArray = [];
    // 加载设备检测
    const loadDevice = this.sdk.checkPermission(TCIC.TResourcePath.Device, TCIC.TPermissionFlag.Read);
    if (loadDevice) {
      promiseArray.push(this.sdk.loadComponent('device-detect-component', {
        width: '100%',
        height: '100%',
        display: 'block',
        zIndex: 600,
      })
        .then((dom) => {
          const devices = ['microphone', 'camera', 'speaker'];
          dom.getVueInstance()
            .start({
              flag: true,
              devices,
            });
        }));
    } else {
      this.sdk.setState(Constant.TStateDeviceDetect, false);
    }
    return Promise.all(promiseArray);
  }
}
