export class LayoutBase {
  constructor(name) {
    this.sdk = TCIC.SDK.instance;
    this.name = name || 'UnknownClassLayout';
  }
  initLayout() {
  }
  updateLayout() {
  }
  updateLayoutAfterJoinClass() {
  }
  getFitScale(classLayout, isTeacherScreenShareOnElectron) {
    const bodyRect = this.sdk.getAppClientRect();
    let blankHeight;
    if (isTeacherScreenShareOnElectron) {
      blankHeight = 0;
    } else {
      blankHeight = 45 + 200;  // 右侧布局，高度 = 导航栏 + 上下各55px的空间 + 白板工具栏  V1.5.0增加底部工具栏背景高度有变化
    }
    const planScales = [1, 0.75];
    while (planScales.length > 0) {
      const scale = planScales.shift();
      if (bodyRect.height >= (718 * scale + blankHeight)) {
        return scale;
      }
    }
    return 0.5;  // 最小允许0.5倍
  }
  getFitScaleBasedOnHeight(classLayout, componentHeight, min = 0.5) {
    const isMobilePhone = this.sdk.isMobile() && !this.sdk.isPad();
    const bodyRect = this.sdk.getAppClientRect();
    let blankHeight;
    if (isMobilePhone) {
      blankHeight = 0;
    } else {
      blankHeight = 45 + 200;  // 右侧布局，高度 = 导航栏 + 上下各55px的空间 + 白板工具栏 V1.5.0增加底部工具栏背景高度有变化
    }
    return Math.max(min, Math.min(1, bodyRect.height / (componentHeight + blankHeight) - 0.1));
  }
}
