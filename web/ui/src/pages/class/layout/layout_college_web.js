import { LayoutBase } from '@/pages/class/layout/layout_base';
import Constant from '@/util/Constant';

export class LayoutCollegeWeb extends LayoutBase {
  constructor() {
    super('LayoutCollegeWeb');
    this.instance = null;
  }
  static getInstance() {
    if (!this.instance) {
      this.instance = new LayoutCollegeWeb();
    }
    return this.instance;
  }
  initLayout() {
    // TStateLocalAVBeforeClassBegin状态需要在VideoWrapComponent加载前设置
    this.sdk.setState(Constant.TStateLocalAVBeforeClassBegin, false);
  }
  updateLayout() {
    const promiseArray = [];
    const deviceDetecting = this.sdk.getState(Constant.TStateDeviceDetect, true);
    const isShowTeacherVideoInWhiteBoardArea = this.sdk.getState(Constant.TStateShowTeacherVideoInWhiteBoardArea, false);
    const videoVisible = !deviceDetecting;
    if (deviceDetecting) {
      return Promise.resolve();
    }

    const headerHeight = 45;  // 标题栏高度
    const rightToolWidth = 56; // 预留右侧工具栏宽度
    const teacherVideoWidth = 264;
    const padding = 8;
    const isShowStudentsWrap = this.sdk.getState(Constant.TStateShowStudentsVideoWrap, true);
    const isShowRightColumn = this.sdk.getState(Constant.TStateShowRightColumn, true);
    const rightWidth = isShowRightColumn ? (rightToolWidth + teacherVideoWidth + padding) : rightToolWidth;     // 右侧边框
    promiseArray.push(this.sdk.updateComponent('teacher-videowrap-component', {}).then((success) => {
      const comp = this.sdk.getComponent('teacher-videowrap-component');
      if (success && comp) {
        comp.getVueInstance().updateLayout();
      }
    }));
    // 更新学生视频组件位置
    const studentsWrapHeight = isShowStudentsWrap ? 110 : 0;  // 视频列表高度
    promiseArray.push(this.sdk.updateComponent('students-wrap-component', {
      top: `${headerHeight}px`,
      left: '0px',
      width: `calc(100% - ${rightWidth}px)`,
      height: `${studentsWrapHeight}px`,
      display: videoVisible ? 'block' : 'none',
      style: 'overflow: visible;',
    }));
    if (this.sdk.isTeacher()) {
      return Promise.all(promiseArray);
    }
    // 更新老师视频组件位置
    if (isShowTeacherVideoInWhiteBoardArea) {
      const areaWidth = document.body.clientWidth - rightWidth;
      const areaHeight = document.body.clientHeight - headerHeight - studentsWrapHeight;
      if (areaWidth / areaHeight >= 16 / 9) {   // 高度适配
        const videoWidth = Math.floor(areaHeight * 16 / 9);     // 计算视频宽度
        promiseArray.push(this.sdk.updateComponent('teacher-component', {
          top: `${headerHeight + studentsWrapHeight}px`,
          left: `${(areaWidth - videoWidth) / 2}px`,
          width: `${videoWidth}px`,
          height: `${areaHeight}px`,
          display: 'block',
          style: 'overflow: visible;',
        }));
      } else {  // 宽度适配
        const videoHeight = Math.floor(areaWidth * 9 / 16);
        promiseArray.push(this.sdk.updateComponent('teacher-component', {
          top: `${headerHeight + studentsWrapHeight + (areaHeight - videoHeight) / 2}px`,
          left: '0px',
          width: `${areaWidth}px`,
          height: `${videoHeight}px`,
          display: 'block',
          style: 'overflow: visible;',
        }));
      }
      // 更新屏幕分享播放组件位置
      promiseArray.push(this.sdk.updateComponent('screen-player-component', {
        top: `${headerHeight + padding}px`,
        left: `calc(100% - ${rightWidth - padding}px)`,
        width: `${teacherVideoWidth}px`,
        height: `${Math.floor(teacherVideoWidth * 9 / 16)}px`,
      }));
    } else {
      promiseArray.push(this.sdk.updateComponent('teacher-component', {
        top: `${headerHeight + padding}px`,
        left: `calc(100% - ${rightWidth - padding}px)`,
        width: `${teacherVideoWidth}px`,
        height: `${Math.floor(teacherVideoWidth * 9 / 16)}px`,
        display: isShowRightColumn ? 'block' : 'none',
        style: 'overflow: visible;',
      }));
      // 更新屏幕分享播放组件位置
      promiseArray.push(this.sdk.updateComponent('screen-player-component', {
        top: `${headerHeight + studentsWrapHeight}px`,
        left: '0',
        width: `calc(100% - ${rightWidth}px)`,
        height: `calc(100% - ${headerHeight + studentsWrapHeight}px)`,
      }));
    }
    return Promise.all(promiseArray);
  }
  updateLayoutAfterJoinClass() {
    const promiseArray = [];
    // 加载设备检测
    // 以下场景无需设备检测
    // 1. 直播课 classType => 'live' 并且不是老师
    // 2. 录制模式下
    // 3. 移动端
    if (this.sdk.getState(Constant.TStateRecordMode)) {
      // 直接完成设备检测
      this.sdk.setState(Constant.TStateDeviceDetect, false);
    } else {
      const loadDevice = this.sdk.checkPermission(TCIC.TResourcePath.Device, TCIC.TPermissionFlag.Read);
      if (loadDevice) {
        promiseArray.push(this.sdk.loadComponent('device-detect-component', {
          width: '100%',
          height: 'calc(100% - 45px)',
          top: '64px',
          display: 'block',
          zIndex: 600,
        })
          .then((dom) => {
            const devices = ['microphone', 'camera', 'speaker'];
            dom.getVueInstance()
              .start({
                flag: true,
                devices,
              });
          }));
      } else {
        this.sdk.setState(Constant.TStateDeviceDetect, false);
      }
    }
    return Promise.all(promiseArray);
  }
}
