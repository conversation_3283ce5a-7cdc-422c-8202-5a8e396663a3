import { LayoutBase } from '@/pages/class/layout/layout_base';
import Util from '@/util/Util';
import Constant from '@/util/Constant';

export class LayoutLivePhone extends LayoutBase {
  constructor() {
    super('LayoutLivePhone');
    this.instance = null;
  }
  static getInstance() {
    if (!this.instance) {
      this.instance = new LayoutLivePhone();
    }
    return this.instance;
  }
  initLayout() {
    // TStateLocalAVBeforeClassBegin状态需要在VideoWrapComponent加载前设置
    this.sdk.setState(Constant.TStateLocalAVBeforeClassBegin, false);
  }
  updateLayout() {
    const deviceOrientation = this.sdk.getState(TCIC.TMainState.Device_Orientation, TCIC.TDeviceOrientation.Portrait);
    document.body.style.backgroundColor = 'white';
    if (deviceOrientation === TCIC.TDeviceOrientation.Portrait) {
      return this.updateLayoutPortrait();
    }
    return this.updateLayoutLandscape();
  }
  updateLayoutPortrait() {
    const promiseArray = [];
    this.sdk.setState(Constant.TStateHeaderVisible, true);
    const isBigVideoMode = this.sdk.getState(Constant.TStateBigVideoMode, false);
    const curLayout = this.sdk.getClassLayout();
    const bodyBounds = document.body.getBoundingClientRect();
    const headerHeight = 45;
    let safeTop = '0px';
    let safeBottom = '0px';
    if (this.sdk.isIOS()) {
      safeTop = 'env(safe-area-inset-top)';
      safeBottom = 'env(safe-area-inset-bottom)';
      const versions = this.sdk.getIOSVersion();
      // evn在ios11.2以下的兼容性
      if (versions.length === 2
          && parseInt(versions[0], 10) <= 11
          && parseInt(versions[1], 10) <= 2) {
        safeTop = 'constant(safe-area-inset-top)';
        safeBottom = 'constant(safe-area-inset-bottom)';
      }
    }
    // header
    promiseArray.push(this.sdk.updateComponent('header-component', {
      top: safeTop,
      left: '0',
      width: '100%',
      height: `${headerHeight}px`,
      display: 'block',
    }));
    // 课堂状态
    const boardHeight = bodyBounds.width * 9 / 16;
    promiseArray.push(this.sdk.updateComponent('live-status-component', {
      top: `calc(${safeTop} + ${headerHeight}px)`,
      left: '0',
      width: '100%',
      height: `${boardHeight}px`,
      display: 'block',
    }));

    // 在线人数
    promiseArray.push(this.sdk.updateComponent('live-numbers-component', {
      // width: '40px',
      // height: '40px',
      top: `calc(${safeTop} + ${headerHeight}px)`,
      left: '0px',
      display: 'block',
    }));
    // 旋转按钮
    promiseArray.push(this.sdk.updateComponent('rotate-device-component', {
      width: '40px',
      height: '40px',
      top: `calc(${safeTop} + ${headerHeight + boardHeight - 40 - 9}px)`,
      left: `calc(${bodyBounds.width - 40 - 16}px)`,
      display: 'block',
    }));

    // 主显示窗
    const mainLayout = {
      top: `calc(${safeTop} + ${headerHeight}px)`,
      left: '0',
      width: '100%',
      height: `${boardHeight}px`,
      display: 'block',
      zIndex: 1,
    };
    // 画中画时， 浮窗位于左下角
    const width = 128; const height = 72;
    const top = bodyBounds.width * 9 / 16 - height + headerHeight;
    let floatLayout = {
      top: `calc(${safeTop} + ${top}px)`,
      left: '0px',
      width: `${width}px`,
      height: `${height}px`,
      zIndex: 300, // 视频全屏时需要调整zIndex
      display: 'block',
    };
    let teacherVideoLayout = floatLayout;
    let boardLayout = mainLayout;
    if (curLayout === TCIC.TClassLayout.VideoDoc || curLayout === TCIC.TClassLayout.Video) {
      // 手机文档+视频或纯视频
      floatLayout = {  display: 'none' };
      teacherVideoLayout = mainLayout;
      boardLayout = floatLayout;
    } else {
      if (isBigVideoMode) {
        teacherVideoLayout = mainLayout;
        boardLayout = floatLayout;
      } else {
        teacherVideoLayout = floatLayout;
        boardLayout = mainLayout;
      }
    }
    const classState = this.sdk.getState(window.TCIC.TMainState.Class_Status);
    if (TCIC.TClassStatus.Already_Start !== classState) {
      teacherVideoLayout.display = 'none';
    }
    promiseArray.push(this.sdk.updateComponent('teacher-component', teacherVideoLayout));
    if (curLayout !== TCIC.TClassLayout.VideoDoc) {
      // 手机竖屏时文档+视频布局，白板在tab栏里，所以不需要updateComponent('board-component'）
      promiseArray.push(this.sdk.updateComponent('board-component', boardLayout));
    }

    floatLayout.zIndex = 310;
    floatLayout.style = 'pointer-events: auto;';
    promiseArray.push(this.sdk.updateComponent('big-video-switch-component', floatLayout));
    // 讨论区
    promiseArray.push(this.sdk.updateComponent('introduction-discuss-component', {
      top: `calc(${safeTop} + ${headerHeight + boardHeight}px)`,
      left: '0',
      width: '100%',
      height: `calc(100% - ${headerHeight + boardHeight}px - ${safeBottom} - ${safeTop})`,
      display: 'block',
    }));
    // 屏幕分享观看布局和白板一致
    if (curLayout !== TCIC.TClassLayout.VideoDoc) {
      promiseArray.push(this.sdk.updateComponent('screen-player-component', {
        top: `calc(${safeTop} + ${headerHeight}px)`,
        left: '0',
        width: '100%',
        height: `${boardHeight}px`,
      }));
    }

    promiseArray.push(this.sdk.updateComponent('footer-component', {
      display: 'none',
    }));
    promiseArray.push(this.sdk.updateComponent('stage-video-list-component', {
      top: `calc(${safeTop})`,
      right: '0',
    }));
    promiseArray.push(this.sdk.updateComponent('mobile-im-input-bar-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
      display: 'block',
    }));
    // 是否正在邀请我
    const isInvitingMe = this.sdk.getState(Constant.TStateLiveTeacherInvitingMe, false);
    promiseArray.push(this.sdk.updateComponent('invite-stage-dialog-component', {
      display: isInvitingMe ? 'block' : 'none',
    }));

    return Promise.all(promiseArray);
  }

  updateLayoutLandscape() {
    const promiseArray = [];
    this.sdk.setState(Constant.TStateHeaderVisible, false);
    const isBigVideoMode = this.sdk.getState(Constant.TStateBigVideoMode, false);
    const curLayout = this.sdk.getClassLayout();
    // header
    promiseArray.push(this.sdk.updateComponent('header-component', {
      display: 'none',
    }));
    // 课堂状态
    promiseArray.push(this.sdk.updateComponent('live-status-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
      display: 'block',
    }));
    // 在线人数
    promiseArray.push(this.sdk.updateComponent('live-numbers-component', {
      // width: '40px',
      // height: '40px',
      top: '9px',
      left: '16px',
      display: 'block',
    }));
    promiseArray.push(this.sdk.updateComponent('rotate-device-component', {
      display: 'none',
    }));
    // 讨论区
    promiseArray.push(this.sdk.updateComponent('introduction-discuss-component', {
      display: 'none',
    }));
    // 屏幕分享观看布局和白板一致
    promiseArray.push(this.sdk.updateComponent('screen-player-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
    }));
    const isShowFooterComponent = this.sdk.getState(Constant.TStateIsShowFooterComponent, false);
    // footer
    promiseArray.push(this.sdk.updateComponent('footer-component', {
      width: '100%',
      display: isShowFooterComponent ? 'block' : 'none',
    }));
    // 主显示窗
    const mainLayout = {
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
      display: 'block',
      zIndex: 1,
    };
    const width = 128; const height = 72; const margin = 14;
    const bodyBounds = document.body.getBoundingClientRect();
    const enableStage = this.sdk.getState(TCIC.TMainState.Enable_Stage, false);
    const onStaging = this.sdk.getState(TCIC.TMainState.Stage_Status, false);
    const isLiveClass = this.sdk.isLiveClass();
    const left = 198 + (isLiveClass && (enableStage || onStaging) ? (94 + 10) : 0);
    const top = bodyBounds.height - margin - height;
    let floatLayout = {
      top: `${top}px`,
      left: `${left}px`,
      width: `${width}px`,
      height: `${height}px`,
      zIndex: 300,
      display: 'block',
    };
    if (curLayout === TCIC.TClassLayout.VideoDoc) {
      // App在文档+视频模式下会将board-component移到introduction-discuss-component中，这里需要移回来
      const boardDom = this.sdk.getComponent('board-component');
      const root = document.getElementById('app');
      if (boardDom.parentNode !== root) {
        root.appendChild(boardDom);
      }
      const screenPlayerDom = this.sdk.getComponent('screen-player-component');
      if (screenPlayerDom.parentNode !== root) {
        root.appendChild(screenPlayerDom);
      }
    }
    let teacherVideoLayout = floatLayout;
    let boardLayout = Object.assign({}, mainLayout, { transform: 'scale(1)', transformOrigin: 'center' });
    if (curLayout === TCIC.TClassLayout.Video) {
      // 纯视频
      floatLayout = {  display: 'none' };
      floatLayout.display = 'none';
      teacherVideoLayout = mainLayout;
      boardLayout = floatLayout;
    } else {
      if (isBigVideoMode) {
        teacherVideoLayout = mainLayout;
        boardLayout = Object.assign({}, floatLayout);;
        const scale = width / document.body.clientWidth;
        boardLayout.width = '100%';
        boardLayout.height = '100%';
        // 用scale的方式，解决白板在切换时出现闪烁的问题
        boardLayout.transform = `scale(${scale})`;
        boardLayout.transformOrigin = 'top left';
      } else {
        teacherVideoLayout = floatLayout;
      }
    }
    const classState = this.sdk.getState(window.TCIC.TMainState.Class_Status);
    if (TCIC.TClassStatus.Already_Start !== classState) {
      teacherVideoLayout.display = 'none';
    }
    promiseArray.push(this.sdk.updateComponent('teacher-component', teacherVideoLayout));
    promiseArray.push(this.sdk.updateComponent('board-component', boardLayout));

    floatLayout.zIndex = 310;
    promiseArray.push(this.sdk.updateComponent('big-video-switch-component', floatLayout));

    promiseArray.push(this.sdk.updateComponent('stage-video-list-component', {
      top: '-48px',
      right: '0',
    }));
    promiseArray.push(this.sdk.updateComponent('mobile-im-input-bar-component', {
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
      display: 'block',
    }));
    // 是否正在邀请我
    const isInvitingMe = this.sdk.getState(Constant.TStateLiveTeacherInvitingMe, false);
    promiseArray.push(this.sdk.updateComponent('invite-stage-dialog-component', {
      display: isInvitingMe ? 'block' : 'none',
    }));
    return Promise.all(promiseArray);
  }
  updateLayoutAfterJoinClass() {
    const promiseArray = [];
    const classLayout = this.sdk.getClassLayout();
    const comp = this.sdk.getComponent('quick-im-component');
    comp && comp.getVueInstance()
      .toggleActive(false);
    const autoFitScale = this.getFitScale(classLayout, false);  // 高度不足以显示的情况下，一些组件需要缩小展示

    const videoWrap = this.sdk.getComponent('videowrap-component');
    if (videoWrap) {
      videoWrap.getVueInstance()
        .setWrapMode('full');
    }

    // 更新计时器组件
    const clockToolScale = autoFitScale;
    const clockWidth = (this.sdk.isTeacher() || this.sdk.isAssistant()) ? 469 : 424;
    const clockHeight = 390;
    promiseArray.push(this.sdk.updateComponent('clock-tool-component', {
      left: `calc(50% - ${clockWidth}px / 2)`,
      top: `calc(50% - ${clockWidth}px / 2)`,
      width: `${clockWidth}px`,
      height: `${clockHeight}px`,
      transform: `scale(${clockToolScale})`,
      style: 'overflow: visible;',
    }));

    // 更新答题器组件
    const quizScale = autoFitScale;
    const quizHeight = (this.sdk.isTeacher() || this.sdk.isAssistant()) ? 551 : 455;
    const quizWidth = (this.sdk.isTeacher() || this.sdk.isAssistant()) ? 400 : 360;
    // const quizScale = this.getFitScaleBasedOnHeight(classLayout, quizHeight);  // 高度不足以显示的情况下，一些组件需要缩小展示
    promiseArray.push(this.sdk.updateComponent('quiz-component', {
      left: `calc(50% - ${quizWidth}px / 2)`,
      top: `calc(50% - ${quizHeight}px / 2)`,
      width: `${quizWidth}px`,
      height: `${quizHeight}px`,
      transform: `scale(${quizScale})`,
    }));
    // 答题组件在缩放的情况下，内部的popover也需要缩放
    Util.addStyle(`.sta-popover { transform: scale(${quizScale}) }`);

    // 更新定时器组件
    const timerToolScale = autoFitScale;
    const timerWidth = 424;
    const timerHeight = 390;
    promiseArray.push(this.sdk.updateComponent('timer-tool-component', {
      left: `calc(50% - ${timerWidth}px / 2)`,
      top: `calc(50% - ${timerWidth}px / 2)`,
      width: `${timerWidth}px`,
      height: `${timerHeight}px`,
      transform: `scale(${timerToolScale})`,
      style: 'overflow: visible;',
    }));


    // 更新进房提醒组件
    promiseArray.push(this.sdk.updateComponent('class-reminder-component', {
      display: 'block',
    }));

    // 更新公开课进房提醒组件
    promiseArray.push(this.sdk.updateComponent('liveclass-reminder-component', {
      display: 'block',
    }));

    // 政策提示
    promiseArray.push(this.sdk.updateComponent('class-edupolicy-component', {
      display: 'block',
    }));

    // 关闭音频上课提示
    promiseArray.push(this.sdk.updateComponent('class-closeaudio-component', {
      display: 'block',
    }));

    // 滚动消息组建
    const deviceOrientation = this.sdk.getState(TCIC.TMainState.Device_Orientation, TCIC.TDeviceOrientation.Portrait);
    if (deviceOrientation === TCIC.TDeviceOrientation.Landscape) {
      promiseArray.push(this.sdk.updateComponent('quickmsg-show-component', {
        display: 'block',
      }));
    }
    // 加载设备检测
    // 以下场景无需设备检测
    // 1. 直播课 classType => 'live' 并且不是老师
    // 2. 录制模式下
    // 3. 移动端
    this.sdk.setState(Constant.TStateDeviceDetect, false);
    return Promise.all(promiseArray);
  }
}
