export class Component {
  constructor(name = '', layout = {}) {
    this.name = name;
    this.layout = layout;
  }
}

export class ComponentBase {
  constructor(name) {
    this.name = name || 'UnknownClassComponent';
  }
  getComponents() {
    return [];
  }
  loadComponents() {
    const loadPromises = [];
    this.getComponents().forEach((component) => {
      if (component instanceof Component) {
        loadPromises.push(TCIC.SDK.instance.loadComponent(component.name, component.layout));
      }
    });
    return Promise.all(loadPromises);
  }
}
