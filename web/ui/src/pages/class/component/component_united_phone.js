import { Component, ComponentBase } from '@/pages/class/component/component_base';

import Constant from '../../../util/Constant';

export class ComponentUnitedPhone extends ComponentBase {
  constructor() {
    super('ComponentUnitedPhone');
    this.instance = null;
    this.sdk = TCIC.SDK.instance;
  }
  static getInstance() {
    if (!this.instance) {
      this.instance = new ComponentUnitedPhone();
    }
    return this.instance;
  }
  getComponents() {
    let componentsFunc = '';
    try {
      const isAIClass = TCIC.SDK.instance.isAiRoom();
      if (isAIClass) {
        componentsFunc = 'getAIClassComponents';
        return this.getAIClassComponents();
      }
      if (TCIC.SDK.instance.isCoTeachingClass())  { // 双师课堂
        componentsFunc = 'getCoTeachingComponents';
        return this.getCoTeachingComponents();
      }

      if (TCIC.SDK.instance.isVideoOnlyClass()) { // 纯视频课堂优先级更高，1v1纯视频课堂也走到这里
        // 小班课纯视频竖屏有自己独立的样式，暂时不放到新布局
        const isLandscapeSmallClass = (TCIC.SDK.instance.isOneOnOneClass() || TCIC.SDK.instance.getClassInfo().maxRtcMember <= 1)
          && TCIC.SDK.instance.getParams('defaultDeviceOrientation') === '0';
        if (TCIC.SDK.instance.isBigRoom()
          || isLandscapeSmallClass
        ) {
          componentsFunc = 'getRtcComponents';
          // 纯视频1v1,1v0包含(大班，小班)课走到新布局
          return this.getRtcComponents();
        }
        componentsFunc = 'getVideoOnlyComponents';
        return this.getVideoOnlyComponents();
      }
      if (TCIC.SDK.instance.isOneOnOneClass()) { // 1v1 video doc 小班课堂
        componentsFunc = 'getOneOnOneComponents';
        return this.getOneOnOneComponents();
      }

      if (TCIC.SDK.instance.isUnitedRTCClass() || TCIC.SDK.instance.isTeacherOrAssistant()) {
        componentsFunc = 'getRtcComponents';
        // RTC课堂
        return this.getRtcComponents();
      }
      componentsFunc = 'getLiveComponents';
      // 标准课堂
      return this.getLiveComponents();
    } finally {
      TCIC.SDK.instance.reportLog('ComponentUnitedPhone-getComponents', componentsFunc);
    }
  }

  /**
   * 互动课堂组件初始化( - 同原互动课布局)
   */
  getRtcComponents() {
    const components = [];
    if (!TCIC.SDK.instance.getState(Constant.TStateRecordMode)) {  // 录制模式下不加载header组件
      // 加载导航栏组件
      components.push(new Component('header-component', { zIndex: 1500 }));
    }

    // 轮播组件
    components.push(new Component('carousel-component', { zIndex: 2 }));
    if (TCIC.SDK.instance.isMobile()
      && (TCIC.SDK.instance.isBigRoom()
      || (TCIC.SDK.instance.isVideoOnlyClass() && TCIC.SDK.instance.getClassInfo().maxRtcMember <= 1)) // 大班课和1v1课
    ) {
      // 布局组件
      components.push(new Component('layout-component', { zIndex: 2 }));
    } else {
      // 加载视频列表组件
      components.push(new Component('videowrap-component', { zIndex: 300 }));
    }

    // 加载竖屏IM聊天组件
    components.push(new Component('portrait-im-component', { zIndex: 4 }));
    // 加载边栏IM聊天组件
    components.push(new Component('introduction-discuss-component', { zIndex: 4 }));
    // 加载边栏切换按钮
    components.push(new Component('side-toggle-button-component', { zIndex: 4 }));

    // 各种文档区的组件
    this.getDocAreaComponents(components);

    // 各种和布局无关的工具组件、弹窗、tip等
    this.getClassTools(components);

    return components;
  }


  /**
   * 视频直播课堂组件初始化
   */
  getLiveComponents() {
    const components = [];
    if (!TCIC.SDK.instance.getState(Constant.TStateRecordMode)) {  // 录制模式下不加载header组件
      // 加载导航栏组件
      components.push(new Component('header-component', { zIndex: 1500 }));
    }
    // 先加载奖杯组件
    components.push(new Component('trophy-component', {
      display: 'block',
      zIndex: 452,
    }));

    if (TCIC.SDK.instance.isLiveClass()) {
      // 公开课课堂状态组件
      components.push(new Component('live-status-component', { zIndex: 3 }));
      // 公开课在线人数组件
      components.push(new Component('live-numbers-component', { zIndex: 400 }));
    }

    // 加载课堂人数展示组件
    components.push(new Component('interact-reminder-component', { zIndex: 301 }));
    // 公开课旋转设备组件
    components.push(new Component('rotate-device-component', { zIndex: 400 }));
    // 加载屏幕分享展示组件
    components.push(new Component('screen-player-component', { zIndex: 2 }));
    // 加载视频播放组件
    components.push(new Component('vod-player-component', { zIndex: 6 }));
    // 加载公开课课堂人数展示组件
    components.push(new Component('live-reminder-component', { zIndex: 2 }));
    // 轮播组件
    components.push(new Component('carousel-component', { zIndex: 2 }));
    // 关闭音频上课提示
    components.push(new Component('class-closeaudio-component', { zIndex: 2000 }));
    // 加载PPT缩略图组件
    components.push(new Component('thumbnail-component', { zIndex: 3 }));
    // 加载边栏IM聊天组件
    components.push(new Component('introduction-discuss-component', { zIndex: 4 }));
    components.push(new Component('portrait-im-component', { zIndex: 4 }));
    // 加载边栏切换按钮
    components.push(new Component('side-toggle-button-component', { zIndex: 4 }));
    // 政策提示
    components.push(new Component('class-edupolicy-component', { zIndex: 5 }));
    components.push(new Component('college-video-switch-component', { zIndex: 310 }));
    // 加载拉流组件
    components.push(new Component('live-component', { zIndex: 300 }));
    // 加载计时器组件
    components.push(new Component('clock-tool-component', { zIndex: 350 }));
    // 加载定时器组件
    components.push(new Component('timer-tool-component', { zIndex: 350 }));
    // 加载答題器组件
    components.push(new Component('quiz-component', { zIndex: 350 }));
    // 注意：不加载涉及上台的组件，比如随机选人、抢答器
    // 加载底部工具栏 V1.5.0s
    components.push(new Component('footer-component', { zIndex: 301 }));
    // 加载音频播放器
    components.push(new Component('audio-player-component', { zIndex: 350 }));
    components.push(new Component('board-picture-tool', { zIndex: 2 }));
    // 加载网络提醒组件
    components.push(new Component('network-tips-component', { zIndex: 1500 }));
    // 加载网络检测组件
    components.push(new Component('network-detector-component', { zIndex: 1500 }));
    // components.push(new Component('setting-component', { zIndex: 1501 }));
    // 加载移动端IM输入栏组件
    components.push(new Component('mobile-im-input-bar-component', { zIndex: 40000 }));
    // 只有小屏的移动端才使用mobile的分享
    components.push(new Component('invite-dialog-mobile-component', { zIndex: 451 }));
    // 消息滚动
    components.push(new Component('quickmsg-show-component', { zIndex: 900 }));
    // 加载公告对话框组件
    components.push(new Component('notice-dialog', {}));
    components.push(new Component('image-preview', { zIndex: 2000 }));
    if (TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isSupervisor()) {
      // 非学生且是直播课时加载
      components.push(new Component('stage-float-list-component', { zIndex: 2000 }));
    }
    return components;
  }

  /**
   * 双师课堂组件初始化
   */
  getCoTeachingComponents() {
    const components = [];
    // 加载header组件
    components.push(new Component('header-component', { zIndex: 1500 }));
    // 加载视频宫格组件
    components.push(new Component('ct-video-component', { zIndex: 300 }));
    // 加载举手组件
    components.push(new Component('ct-hand-up-component', { zIndex: 301, display: 'block' }));
    // 加载课堂人数展示组件
    components.push(new Component('ct-interact-reminder-component', { zIndex: 301 }));
    // 加载举手列表组件
    components.push(new Component('ct-hand-up-list-component', { zIndex: 2000 }));
    // 加载toast组件
    components.push(new Component('ct-toast-component', { zIndex: 1500 }));
    // 加载网络检测组件
    components.push(new Component('network-detector-component', { zIndex: 1500 }));
    // 加载公告对话框组件
    components.push(new Component('notice-dialog', {}));
    // 加载屏幕分享展示组件
    components.push(new Component('screen-player-component', { zIndex: 302 }));
    return components;
  }

  /**
   * 1v1课堂组件初始化
   */
  getOneOnOneComponents() {
    const components = [];
    if (!TCIC.SDK.instance.getState(Constant.TStateRecordMode)) {  // 录制模式下不加载header组件
      // 加载header组件
      components.push(new Component('header-component', { zIndex: 1500 }));
    }

    if (TCIC.SDK.instance.isBigRoom() && TCIC.SDK.instance.isMobile()) {
      // 布局组件
      components.push(new Component('layout-component', { zIndex: 2 }));
    } else {
      // 加载1v1课视频组件
      components.push(new Component('ooo-video-wrap-component', { zIndex: 300 }));
    }
    // 加载边栏IM聊天组件
    components.push(new Component('introduction-discuss-component', { zIndex: 4 }));
    // 加载边栏切换按钮
    components.push(new Component('side-toggle-button-component', { zIndex: 4 }));

    // 各种文档区的组件
    this.getDocAreaComponents(components);

    // 各种和布局无关的工具组件、弹窗、tip等
    this.getClassTools(components);

    return components;
  }

  /**
   * 纯视频课堂组件
   */
  getVideoOnlyComponents() {    // 加载RTC组件
    const components = [];
    if (!TCIC.SDK.instance.getState(Constant.TStateRecordMode)) {  // 录制模式下不加载header组件
      // 加载导航栏组件
      components.push(new Component('header-component', { zIndex: 1500 }));
    }

    // 加载竖屏 tabbar 组件
    components.push(new Component('tabbar-component', { zIndex: 1500 }));

    // 轮播组件
    components.push(new Component('carousel-component', { zIndex: 2 }));
    // 加载视频列表组件
    components.push(new Component('video-loader-component', { zIndex: 300 }));
    components.push(new Component('video-wall-component', { zIndex: 1, display: 'block' }));

    // 旋转设备组件
    components.push(new Component('rotate-device-component', { zIndex: 400 }));

    // 各种文档区的组件
    this.getDocAreaComponents(components);

    // 各种和布局无关的工具组件、弹窗、tip等
    this.getClassTools(components);

    return components;
  }

  // 1v1/videoOnly/Rtc 共用，各种文档区的组件
  getDocAreaComponents(components) {
    const isVideoDocClass = this.sdk.isVideoDocClass();

    if (isVideoDocClass) {
      // 加载白板图片工具组件
      components.push(new Component('board-picture-tool', { zIndex: 2 }));
      // 手机不需要 board-tool-component，白板操作入口在 footer 里
      components.push(new Component('board-tool-component', { zIndex: 352 }));
      // 加载PPT缩略图组件
      components.push(new Component('thumbnail-component', { zIndex: 3 }));
    }

    // 加载屏幕分享展示组件
    components.push(new Component('screen-player-component', { zIndex: 2 }));
    // 加载视频播放组件
    components.push(new Component('vod-player-component', { zIndex: 6 }));
    // 加载视频工具组件
    components.push(new Component('vod-tool-component', { zIndex: 299 }));
    // 加载底部工具栏 V1.5.0s
    components.push(new Component('footer-component', { zIndex: 30 }));
    // 消息滚动
    components.push(new Component('quickmsg-show-component', { zIndex: 900 }));
  }

  // 1v1/videoOnly/Rtc 共用，各种和布局无关的工具组件、弹窗、tip等，一般 zIndex > 300
  // 注意要包括layout里 updateClassTools 中的组件
  getClassTools(components) {
    // 政策提示，不需要了
    // components.push(new Component('class-edupolicy-component', { zIndex: 5 }));

    // 奖杯组件
    components.push(new Component('trophy-component', {
      display: 'block',
      zIndex: 452,
    }));

    // 加载课堂人数展示组件
    components.push(new Component('interact-reminder-component', { zIndex: 301 }));

    // 加载网络提醒组件
    components.push(new Component('network-tips-component', { zIndex: 1500 }));
    // 加载网络检测组件
    components.push(new Component('network-detector-component', { zIndex: 1500 }));

    // 关闭音频上课提示
    components.push(new Component('class-closeaudio-component', { zIndex: 2000 }));

    // 设置
    // components.push(new Component('setting-component', { zIndex: 1501 }));

    // 加载计时器组件
    components.push(new Component('clock-tool-component', { zIndex: 350 }));
    // 加载定时器组件
    components.push(new Component('timer-tool-component', { zIndex: 350 }));
    // 加载答題器组件
    components.push(new Component('quiz-component', { zIndex: 350 }));
    // 加载随机选人组件
    // components.push(new Component('random-choose-tool-component', { zIndex: 350 }));
    // 加载抢答器组件
    // components.push(new Component('seize-answer-component', { zIndex: 350 }));

    // 手机肯定不是 Electron
    // if (TCIC.SDK.instance.isElectron()) {
    //   // 加载辅助摄像头组件
    //   components.push(new Component('sub-camera-component', { zIndex: 350 }));
    //   // 加载辅助摄像头弹出框 V1.5.0
    //   components.push(new Component('sub-camera-preview-component', { zIndex: 350 }));
    // }

    // 加载音频播放器
    components.push(new Component('audio-player-component', { zIndex: 350 }));

    // 加载公告对话框组件
    components.push(new Component('notice-dialog', {}));

    // 加载图片预览组件
    components.push(new Component('image-preview', { zIndex: 2000 }));

    // 加载课件上传
    components.push(new Component('document-upload-component', { zIndex: 350 }));

    // 只有小屏的移动端才使用mobile的分享
    components.push(new Component('invite-dialog-mobile-component', { zIndex: 451 }));

    // 加载移动端IM输入栏组件
    components.push(new Component('mobile-im-input-bar-component', { zIndex: 40000 }));

    // 加载 RTMP 推流弹框组件
    components.push(new Component('rtmp-component', {}));

    // 加载外部课件组件
    components.push(new Component('external-courseware-component', { zIndex: 1000 }));
  }

  /**
   * AI 课堂组件
   */
  getAIClassComponents() {
    const components = [];
    if (!TCIC.SDK.instance.getState(Constant.TStateRecordMode)) {  // 录制模式下不加载header组件
      // 加载导航栏组件
      components.push(new Component('header-component', { zIndex: 1500 }));
    }
    components.push(new Component('ai-audio-component', { zIndex: 6 }));
    return components;
  }
}
