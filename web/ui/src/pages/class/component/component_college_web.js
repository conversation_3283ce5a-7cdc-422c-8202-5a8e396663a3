import { ComponentBase, Component } from '@/pages/class/component/component_base';

export class ComponentCollegeWeb extends ComponentBase {
  constructor() {
    super('ComponentCollegeWeb');
    this.instance = null;
  }
  static getInstance() {
    if (!this.instance) {
      this.instance = new ComponentCollegeWeb();
    }
    return this.instance;
  }
  getComponents() {
    const components = [];
    // 加载屏幕分享展示组件
    components.push(new Component.loadComponent('screen-player-component', { zIndex: 2 }));
    // 加载大教学视频控制栏组件
    components.push(new Component.loadComponent('collclass-vod-ctrl-component', { zIndex: 401 }));
    // 加载老师组件
    components.push(new Component.loadComponent('teacher-component', { zIndex: 300 }));
    // 加载学生列表组件
    components.push(new Component.loadComponent('students-wrap-component', { zIndex: 300 }));
    // 加载悬浮按钮
    components.push(new Component.loadComponent('float-tools-component', { zIndex: 400, display: 'block' }));
    return components;
  }
}
