import { Component, ComponentBase } from '@/pages/class/component/component_base';

import Constant from '../../../util/Constant';

export class ComponentInteractPhone extends ComponentBase {
  constructor() {
    super('ComponentInteractPhone');
    this.instance = null;
  }
  static getInstance() {
    if (!this.instance) {
      this.instance = new ComponentInteractPhone();
    }
    return this.instance;
  }
  getComponents() {
    const components = [];
    if (!TCIC.SDK.instance.getState(Constant.TStateRecordMode)) {  // 录制模式下不加载header组件
      // 加载导航栏组件
      components.push(new Component('header-component', { zIndex: 1500 }));
    }
    // 先加载奖杯组件
    components.push(new Component('trophy-component', {
      display: 'block',
      zIndex: 452,
    }));

    // 加载屏幕分享展示组件
    components.push(new Component('screen-player-component', { zIndex: 2 }));
    // 加载视频播放组件
    components.push(new Component('vod-player-component', { zIndex: 6 }));
    // 加载课堂人数展示组件
    components.push(new Component('interact-reminder-component', { zIndex: 301 }));
    // 轮播组件
    components.push(new Component('carousel-component', { zIndex: 2 }));
    // 关闭音频上课提示
    components.push(new Component('class-closeaudio-component', { zIndex: 2000 }));
    // 加载PPT缩略图组件
    components.push(new Component('thumbnail-component', { zIndex: 3 }));
    // 加载边栏IM聊天组件
    components.push(new Component('introduction-discuss-component', { zIndex: 4 }));
    // 加载边栏切换按钮
    components.push(new Component('side-toggle-button-component', { zIndex: 4 }));
    // 政策提示
    components.push(new Component('class-edupolicy-component', { zIndex: 5 }));
    components.push(new Component('college-video-switch-component', { zIndex: 310 }));
    // 加载视频列表组件
    components.push(new Component('videowrap-component', { zIndex: 300 }));
    // 加载视频工具组件
    components.push(new Component('vod-tool-component', { zIndex: 299 }));
    // 加载计时器组件
    components.push(new Component('clock-tool-component', { zIndex: 350 }));
    // 加载定时器组件
    components.push(new Component('timer-tool-component', { zIndex: 350 }));
    // 加载辅助摄像头组件
    components.push(new Component('sub-camera-component', { zIndex: 350 }));
    // 加载辅助摄像头弹出框 V1.5.0
    components.push(new Component('sub-camera-preview-component', { zIndex: 350 }));
    // 加载底部工具栏 V1.5.0s
    components.push(new Component('footer-component', { zIndex: 301 }));
    // 加载答題器组件
    components.push(new Component('quiz-component', { zIndex: 350 }));
    // 加载音频播放器
    components.push(new Component('audio-player-component', { zIndex: 350 }));
    // 加载课件上传
    components.push(new Component('document-upload-component', { zIndex: 350 }));
    // 加载
    components.push(new Component('board-picture-tool', { zIndex: 2 }));
    // 加载网络提醒组件
    components.push(new Component('network-tips-component', { zIndex: 1500 }));
    // 加载网络检测组件
    components.push(new Component('network-detector-component', { zIndex: 1500 }));
    // components.push(new Component('setting-component', { zIndex: 1501 }));
    // 加载移动端IM输入栏组件
    components.push(new Component('mobile-im-input-bar-component', { zIndex: 40000 }));
    // 只有小屏的移动端才使用mobile的分享
    components.push(new Component('invite-dialog-mobile-component', { zIndex: 451 }));
    // 消息滚动
    components.push(new Component('quickmsg-show-component', { zIndex: 900 }));
    // 加载公告对话框组件
    components.push(new Component('notice-dialog', {}));
    components.push(new Component('image-preview', { zIndex: 2000 }));
    return components;
  }
}
