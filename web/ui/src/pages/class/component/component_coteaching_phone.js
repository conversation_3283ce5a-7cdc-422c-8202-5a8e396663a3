import { ComponentBase, Component } from '@/pages/class/component/component_base';

export class ComponentCoTeachingPhone extends ComponentBase {
  constructor() {
    super('ComponentCoTeachingPhone');
    this.instance = null;
  }
  static getInstance() {
    if (!this.instance) {
      this.instance = new ComponentCoTeachingPhone();
    }
    return this.instance;
  }
  getComponents() {
    const components = [];
    // 加载header组件
    components.push(new Component('header-component', { zIndex: 1500 }));
    // 加载视频宫格组件
    components.push(new Component('ct-video-component', { zIndex: 300 }));
    // 加载举手组件
    components.push(new Component('ct-hand-up-component', { zIndex: 301, display: 'block' }));
    // 加载课堂人数展示组件
    components.push(new Component('ct-interact-reminder-component', { zIndex: 301 }));
    // 加载举手列表组件
    components.push(new Component('ct-hand-up-list-component', { zIndex: 2000 }));
    // 加载toast组件
    components.push(new Component('ct-toast-component', { zIndex: 1500 }));
    // 加载网络检测组件
    components.push(new Component('network-detector-component', { zIndex: 1500 }));
    // 加载公告对话框组件
    components.push(new Component('notice-dialog', {}));
    // 加载屏幕分享展示组件
    components.push(new Component('screen-player-component', { zIndex: 302 }));
    return components;
  }
}
