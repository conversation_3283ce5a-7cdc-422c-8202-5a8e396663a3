import { ComponentBase, Component } from '@/pages/class/component/component_base';

export class ComponentCollegeElectron extends ComponentBase {
  constructor() {
    super('ComponentCollegeElectron');
    this.instance = null;
  }
  static getInstance() {
    if (!this.instance) {
      this.instance = new ComponentCollegeElectron();
    }
    return this.instance;
  }
  getComponents() {
    const components = [];
    // 加载屏幕分享展示组件
    components.push(new Component('screen-player-component', { zIndex: 2 }));
    // 加载大教学视频控制栏组件
    components.push(new Component('collclass-vod-ctrl-component', { zIndex: 401 }));
    // 加载老师端视频列表组件
    components.push(new Component('teacher-videowrap-component', { zIndex: 300 }));
    // 加载老师端组合布局组件
    components.push(new Component('video-loader-component', { zIndex: 300 }));
    // 加载视频加载组件
    components.push(new Component('college-combo-layout-component', { zIndex: 300, display: 'block' }));
    components.push(new Component('college-cell-layout-component', { zIndex: 300, display: 'block' }));
    return components;
  }
}
