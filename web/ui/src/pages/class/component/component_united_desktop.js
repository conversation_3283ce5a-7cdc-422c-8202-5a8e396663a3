import { ComponentBase, Component } from '@/pages/class/component/component_base';
import Constant from '../../../util/Constant';

export class ComponentUnitedDesktop extends ComponentBase {
  constructor() {
    super('ComponentUnitedDesktop');
    this.instance = null;
    this.sdk = TCIC.SDK.instance;
    this.isNewLayout = false;
  }
  static getInstance() {
    if (!this.instance) {
      this.instance = new ComponentUnitedDesktop();
    }
    return this.instance;
  }
  getComponents() {
    if (TCIC.SDK.instance.isCoTeachingClass())  { // 双师课堂
      return this.getCoTeachingComponents();
    }
    if (TCIC.SDK.instance.isCollegeClass()) { // 大教学模式
      return this.getCollegeComponents();
    }
    if (TCIC.SDK.instance.isOneOnOneClass() || TCIC.SDK.instance.isBigRoom()) { // 1v1课堂
      return this.getOneOnOneComponents();
    }
    if (TCIC.SDK.instance.isVideoOnlyClass()) { // 纯视频课堂
      return this.getVideoOnlyComponents();
    }
    if (TCIC.SDK.instance.isUnitedRTCClass() || TCIC.SDK.instance.isTeacherOrAssistant()) {
      // RTC课堂
      return this.getRtcComponents();
    }
    // 标准课堂
    return this.getLiveComponents();
  }

  /**
   * 互动课堂组件
   */
  getRtcComponents() {
    const components = [];
    if (!TCIC.SDK.instance.getState(Constant.TStateRecordMode)) {  // 录制模式下不加载header组件
      // 加载导航栏组件
      components.push(new Component('header-component', { zIndex: 1500 }));
    }

    // 轮播组件
    components.push(new Component('carousel-component', { zIndex: 2 }));
    // 加载视频列表组件
    components.push(new Component('videowrap-component', { zIndex: 300 }));
    // 大视频模式切换按钮
    components.push(new Component('college-video-switch-component', { zIndex: 310 }));

    // 加载边栏IM聊天组件
    components.push(new Component('introduction-discuss-component', { zIndex: 4 }));
    // 加载边栏切换按钮
    components.push(new Component('side-toggle-button-component', { zIndex: 4 }));

    // 各种文档区的组件
    this.getDocAreaComponents(components);

    // 各种和布局无关的工具组件、弹窗、tip等
    this.getClassTools(components);

    return components;
  }

  /**
   * 标准课堂组件初始化
   */
  getLiveComponents() {
    const components = [];
    if (!TCIC.SDK.instance.getState(Constant.TStateRecordMode)) {  // 录制模式下不加载header组件
      // 加载导航栏组件
      components.push(new Component('header-component', { zIndex: 1500 }));
    }
    // 先加载奖杯组件
    components.push(new Component('trophy-component', {
      display: 'block',
      zIndex: 452,
    }));
    // 加载课堂人数展示组件
    components.push(new Component('interact-reminder-component', { zIndex: 301 }));
    // 关闭音频上课提示
    components.push(new Component('class-closeaudio-component', { zIndex: 2000 }));
    // 加载视频拉流组件
    components.push(new Component('live-component', { zIndex: 3 }));
    // 加载PPT缩略图组件
    components.push(new Component('thumbnail-component', { zIndex: 3 }));
    // 加载边栏IM聊天组件
    components.push(new Component('introduction-discuss-component', { zIndex: 4 }));
    // 加载边栏切换按钮
    components.push(new Component('side-toggle-button-component', { zIndex: 4 }));
    // 加载计时器组件
    components.push(new Component('clock-tool-component', { zIndex: 350 }));
    // 加载定时器组件
    components.push(new Component('timer-tool-component', { zIndex: 350 }));
    // 加载答題器组件
    components.push(new Component('quiz-component', { zIndex: 350 }));
    // 注意：不加载涉及上台的组件，比如随机选人、抢答器
    if (TCIC.SDK.instance.isPad()) {
      // 加载移动端IM输入栏组件
      components.push(new Component('mobile-im-input-bar-component', { zIndex: 40000 }));
    }
    // 加载公告对话框组件
    components.push(new Component('notice-dialog', {}));
    components.push(new Component('image-preview', { zIndex: 2000 }));
    return components;
  }

  /**
   *  双师课堂组件初始化
   */
  getCoTeachingComponents() {
    const components = [];
    // 加载header组件
    components.push(new Component('header-component', { zIndex: 1500 }));
    // 加载视频宫格组件
    components.push(new Component('ct-video-component', { zIndex: 300 }));
    // 加载举手组件
    components.push(new Component('ct-hand-up-component', { zIndex: 301, display: 'block' }));
    // 加载课堂人数展示组件
    components.push(new Component('ct-interact-reminder-component', { zIndex: 301 }));
    // 加载举手列表组件
    components.push(new Component('ct-hand-up-list-component', { zIndex: 2000 }));
    // 加载toast组件
    components.push(new Component('ct-toast-component', { zIndex: 1500 }));
    // 加载网络检测组件
    components.push(new Component('network-detector-component', { zIndex: 1500 }));
    // 加载公告对话框组件
    components.push(new Component('notice-dialog', {}));
    // 有些组件只在electron平台下需要加载
    if (TCIC.SDK.instance.isElectron()) {
      // 加载屏幕分享视频容器组件
      components.push(new Component('screen-videowrap-component', { zIndex: 350 }));
      // 加载屏幕分享工具栏
      components.push(new Component('share-toolbar-component', { zIndex: 1500 }));
    }
    // 加载屏幕分享展示组件
    components.push(new Component('screen-player-component', { zIndex: 302 }));
    return components;
  }

  /**
   * 1v1课堂组件初始化
   */
  getOneOnOneComponents() {
    console.log('getOneOnOneComponents');
    const components = [];
    if (!TCIC.SDK.instance.getState(Constant.TStateRecordMode)) {  // 录制模式下不加载header组件
      // 加载header组件
      components.push(new Component('header-component', { zIndex: 1500 }));
    }
    if (this.sdk.isBigRoom() && this.sdk.isVideoDocClass()) {
      components.push(new Component('layout-component', { zIndex: 2 }));
      this.isNewLayout = true;
    } else {
      // 加载1v1课视频组件
      components.push(new Component('ooo-video-wrap-component', { zIndex: 300 }));
    }


    // 加载边栏IM聊天组件
    components.push(new Component('introduction-discuss-component', { zIndex: 4 }));
    // 加载边栏切换按钮
    components.push(new Component('side-toggle-button-component', { zIndex: 4 }));

    // 各种文档区的组件
    this.getDocAreaComponents(components);

    // 各种和布局无关的工具组件、弹窗、tip等
    this.getClassTools(components);
    // 即是1v1又是纯视频
    if (TCIC.SDK.instance.isVideoOnlyClass()) { // 纯视频课堂
      // 轮播组件
      components.push(new Component('carousel-component', { zIndex: 2 }));
      // 加载视频列表组件
      components.push(new Component('video-loader-component', { zIndex: 300 }));
      components.push(new Component('video-wall-component', { zIndex: 1, display: 'block' }));
    }

    return components;
  }

  /**
   * 纯视频课堂组件
   */
  getVideoOnlyComponents() {    // 加载RTC组件
    const components = [];
    if (!TCIC.SDK.instance.getState(Constant.TStateRecordMode)) {  // 录制模式下不加载header组件
      // 加载导航栏组件
      components.push(new Component('header-component', { zIndex: 1500 }));
    }

    // 轮播组件
    components.push(new Component('carousel-component', { zIndex: 2 }));
    // 加载视频列表组件
    components.push(new Component('video-loader-component', { zIndex: 300 }));
    components.push(new Component('video-wall-component', { zIndex: 1, display: 'block' }));

    // 加载边栏IM聊天组件
    components.push(new Component('introduction-discuss-component', { zIndex: 4 }));
    // 加载边栏切换按钮
    components.push(new Component('side-toggle-button-component', { zIndex: 4 }));

    // 各种文档区的组件
    this.getDocAreaComponents(components);

    // 各种和布局无关的工具组件、弹窗、tip等
    this.getClassTools(components);

    return components;
  }

  /* 大教学组件 */
  getCollegeComponents() {
    const components = [];
    // 加载屏幕分享展示组件
    components.push(new Component('screen-player-component', { zIndex: 2 }));
    // 加载大教学视频控制栏组件
    components.push(new Component('collclass-vod-ctrl-component', { zIndex: 401 }));
    // 有些组件只在electron平台下需要加载
    if (TCIC.SDK.instance.isTeacher()) {
      // 加载老师端视频列表组件
      components.push(new Component('teacher-videowrap-component', { zIndex: 300 }));
      // 加载老师端组合布局组件
      components.push(new Component('video-loader-component', { zIndex: 300 }));
      // 加载视频加载组件
      components.push(new Component('college-combo-layout-component', { zIndex: 300, display: 'block' }));
      components.push(new Component('college-cell-layout-component', { zIndex: 300, display: 'block' }));
    } else { // web 平台
      // 加载老师组件
      components.push(new Component('teacher-component', { zIndex: 300 }));
      // 加载学生列表组件
      components.push(new Component('students-wrap-component', { zIndex: 300 }));
      // 加载悬浮按钮
      components.push(new Component('float-tools-component', { zIndex: 400, display: 'block' }));
    }

    return components;
  }

  // 1v1/videoOnly/Rtc 共用，各种文档区的组件
  getDocAreaComponents(components) {
    const isVideoDocClass = this.sdk.isVideoDocClass();

    if (isVideoDocClass) {
      // 加载白板图片工具组件
      components.push(new Component('board-picture-tool', { zIndex: 2 }));
      // 加载白板工具组件
      components.push(new Component('board-tool-component', { zIndex: 350 }));
      if (!this.isNewLayout) {
        // 加载PPT缩略图组件
        components.push(new Component('thumbnail-component', { zIndex: 3 }));
      }
    }

    // 加载屏幕分享展示组件
    components.push(new Component('screen-player-component', { zIndex: 2 }));
    // 加载视频播放组件
    components.push(new Component('vod-player-component', { zIndex: 6 }));
    // 加载视频工具组件
    components.push(new Component('vod-tool-component', { zIndex: 302 }));
    // 加载底部工具栏 V1.5.0s
    components.push(new Component('footer-component', { zIndex: 301 }));
    // 互动课助教连麦（入口） V1.7.0
    components.push(new Component('interactive-stage-access-component', { zIndex: 350 }));
    // 消息滚动
    components.push(new Component('quickmsg-show-component', { zIndex: 900 }));
    // 有些组件只在electron平台下需要加载
    if (TCIC.SDK.instance.isElectron()) {
      // 加载屏幕分享视频容器组件
      components.push(new Component('screen-videowrap-component', { zIndex: 350 }));
      // 加载屏幕分享工具栏
      components.push(new Component('share-toolbar-component', { zIndex: 1500 }));
    }
  }

  // 1v1/videoOnly/Rtc 共用，各种和布局无关的工具组件、弹窗、tip等，一般 zIndex > 300
  // 注意要包括layout里 updateClassTools 中的组件
  getClassTools(components) {
    // 政策提示，不需要了
    // components.push(new Component('class-edupolicy-component', { zIndex: 5 }));

    // 奖杯组件
    components.push(new Component('trophy-component', {
      display: 'block',
      zIndex: 452,
    }));

    // 加载课堂人数展示组件
    components.push(new Component('interact-reminder-component', { zIndex: 301 }));

    // 加载网络提醒组件
    components.push(new Component('network-tips-component', { zIndex: 1500 }));
    // 加载网络检测组件
    components.push(new Component('network-detector-component', { zIndex: 1500 }));

    // 关闭音频上课提示
    components.push(new Component('class-closeaudio-component', { zIndex: 2000 }));

    // 加载计时器组件
    components.push(new Component('clock-tool-component', { zIndex: 350 }));
    // 加载定时器组件
    components.push(new Component('timer-tool-component', { zIndex: 350 }));
    // 加载答題器组件
    components.push(new Component('quiz-component', { zIndex: 350 }));
    // 加载随机选人组件
    // components.push(new Component('random-choose-tool-component', { zIndex: 350 }));
    // 加载抢答器组件
    // components.push(new Component('seize-answer-component', { zIndex: 350 }));
    if (TCIC.SDK.instance.isElectron()) {
      // 加载辅助摄像头组件
      components.push(new Component('sub-camera-component', { zIndex: 350 }));
      // 加载辅助摄像头弹出框 V1.5.0
      components.push(new Component('sub-camera-preview-component', { zIndex: 350 }));
    }

    // 加载音频播放器
    components.push(new Component('audio-player-component', { zIndex: 350 }));

    // 加载公告对话框组件
    components.push(new Component('notice-dialog', {}));

    // 加载图片预览组件
    components.push(new Component('image-preview', { zIndex: 2000 }));

    // 加载课件上传
    components.push(new Component('document-upload-component', { zIndex: 350 }));

    // 加载pc和pad分享组件
    components.push(new Component('invite-dialog-pc-component', { zIndex: 451 }));

    if (TCIC.SDK.instance.isPad()) {
      // 加载移动端IM输入栏组件
      components.push(new Component('mobile-im-input-bar-component', { zIndex: 40000 }));
    }

    // 加载 RTMP 推流弹框组件
    components.push(new Component('rtmp-component', {}));

    // 加载外部课件组件
    components.push(new Component('external-courseware-component', { zIndex: 1000 }));
  }
}
