// import semverSatisfies from 'semver/functions/satisfies';

// TODO: usermap, <,=,|| &&支持, trycatch上报sentry, setappinfo relunch?

const tryParseInt = (d, defaultNumber) => {
  if (!d) {
    return 0;
  }
  try {
    return parseInt(d, 10);
  } catch (error) {
    console.error('version seg ', d, ' is not number');
    return defaultNumber;
  }
};

const compareVersionSegment = (d1, d2, cp = '<') => {
  d1 = tryParseInt(d1, 0);
  d2 = tryParseInt(d2, 0);
  return d1 - d2;
  // switch (cp) {
  //   case '':
  //   case '<':
  //     return d1 < d2;
  //   case '>':
  //     return d1 > d2;
  //   case '=':
  //     return d1 === d2;
  //   default:
  //     return d1 < d2;
  // }
};

const ltVersion = (version, newVersion) => {
  const oldVer = version.split('.');
  const newVer = newVersion.split('.');
  const maxLen = Math.max(oldVer.length, newVer.length);
  let i = 0;
  for (; i < maxLen; i++) {
    // 从左到右任何子版本(主/次)依次比较
    const n = compareVersionSegment(oldVer[i], newVer[i]);
    if (n === 0) {
      continue;
    }
    if (n < 0) {
      return true;
    }
    if (n > 0) {
      return false;
    }
  }
  // 所有位 全 === (i === maxLen) {
  return false;
};

const checkSdkApps = {
  3923193: 'prod demo',
  3520371: 'test demo',
  3050810: 'turito',
  3458714: 'charles-test',
  3847989: 'tyler-test',
  1400313729: 'local',
};

async function request(url, payload) {
  const resp = await fetch(`${url}?_t=${Date.now()}`, {
    method: 'GET',
    // body: JSON.stringify(payload),
    // credentials: 'include',
    cache: 'no-store',
    mode: 'cors',
    window: null,
    // headers: {
    //   'Content-Type': 'application/json',
    // },
    // keepalive: true
  }).catch((err) => {
    console.warn('fetch error:', err);
    return {};
  });

  return resp.json().catch((err) => {
    console.warn('resp.json() failed:', err.message);
    return {};
  });
}

const testConfig = {
  title: 'xxx',
  content: '',
  downloadUrl: '',
  conds: [
    {
      appVersionRange: '<1.7.3',
      sdkVersionRange: '<1.7.3',
      inUsers: [],
      inUsersMap: {},
      platforms: [],
    },
  ],
};

const getPlatform = () => {
  const sdk = TCIC.SDK.instance;
  switch (sdk.getPlatformType()) {
    case 3: // TPlatform.Android:
      return 'android';
    case 4: // TPlatform.IOS:
      return 'ios';
    case 6: // TPlatform.H5:
      return 'h5';
    case 7: // TPlatform.Miniprogram:
      return 'miniprogram';
    case 5: // TPlatform.Web:
      return 'web';
    case 2: // TPlatform.Mac:
      return 'mac';
    case 1: // TPlatform.Windows:
      return 'win';
    case 8: // TPlatform.TV:
      return 'tv';
    default:
      return 'unknown';
  }
};

export const agreeDesktopUpgrade = async (config) => {
  // 只在桌面端页面内触发
  // TODO:
  // if (!window.Electron) {
  //   return false;
  // }
  const sdk = TCIC.SDK.instance;
  const sdkAppId = sdk.getSchoolInfo().sdkAppId;
  if (!checkSdkApps[sdkAppId]) {
    return false;
  }
  if (!config) {
    let url = 'https://class.qcloudclass.com/turito/desktop-upgrade.json';
    if (
      process.env.NODE_ENV !== 'production'
      || /test/.exec(window.location.host)
    ) {
      url = 'https://test-class.qcloudclass.com/turito/desktop-upgrade.json';
    }

    console.log('DESKTOP_UPGRADE url', url);
    config = await request(url, {});
  }
  console.log('DESKTOP_UPGRADE', config);
  const conds = config.conds || [];
  const userId = sdk.getUserId();
  console.log('DESKTOP_UPGRADE userId', userId);
  let clientInfo = {};
  let appInfo = { appVersion: '0.0.1' };
  if (window.Electron) {
    console.log('DESKTOP_UPGRADE window.Electron', config);
    if (window.Electron.getAppInfo) {
      appInfo = await window.Electron.getAppInfo();
      console.log('DESKTOP_UPGRADE window.getAppInfo', appInfo);
    }
    clientInfo = window.Electron.getClientInfo();
    console.log('DESKTOP_UPGRADE window.getClientInfo', clientInfo);
  }
  const sdkVersion = clientInfo.sdkVersion || '0.0.1';
  const platform = clientInfo.platform || getPlatform();
  console.warn('conds input:', appInfo, sdkVersion, platform);
  let meetCond = false;
  for (let i = 0; i < conds.length; i++) {
    const cond = conds[i];
    // 任何一个限制条件不满足, 检查下一个cond
    if (cond.platforms && !cond.platforms.includes(platform)) {
      console.log('DESKTOP_UPGRADE window.getClientInfo ！！！！', i, ' not meets cond platforms:', platform);
      continue;
    }
    // inUsers或inUsersMap任何一个满足即可
    if (!((cond.inUsers && cond.inUsers.includes(userId))
    || (cond.inUsersMap && cond.inUsersMap[userId]))
    ) {
      continue;
    }
    if (cond.appVersionRange) {
      /**
       * appVersionRange写目标升级版本
       */
      const appcond = ltVersion(appInfo.appVersion, cond.appVersionRange || '1.7.3.430');
      // semverSatisfies(
      //   appInfo.appVersion, cond.appVersionRange,
      //   { includePrerelease: true, loose: true },
      // );
      console.log('DESKTOP_UPGRADE window.getClientInfo appcond',  appInfo.appVersion, cond.appVersionRange, 'result:', appcond);
      if (!appcond) {
        console.log('DESKTOP_UPGRADE window.getClientInfo not meets cond appVersion', i, ' not meets cond appVersion:', appInfo);
        continue;
      }
    }
    if (cond.sdkVersionRange && !ltVersion(sdkVersion, cond.sdkVersionRange)) {
      console.warn(i, ' not meets cond sdkVersion:', sdkVersion);
      continue;
    }
    console.warn(i, 'st cond meets:', cond, { sdkVersion, appInfo });
    meetCond = true;
    break;
  }
  // 不满足弹窗条件
  if (!meetCond) {
    console.warn('notmeetany cond');
    return false;
  }
  const agree = await new Promise((resolve) => {
    sdk.showMessageBox(
      config.title || 'Version Update Notification',
      config.content || `Step 1: Click on the Agree button to go to the download page. \n
        Step 2: Initiate the download and follow the installation instructions provided. \n
        Step 3: Upon completion, return to the TMS page and rejoin the class."`,
      ['Agree', 'Cancel'], (index, options) => {
        resolve(index === 0);
      },
    );
  });
  if (agree) {
    let open = window.open;
    if (window.Electron) {
      open = window.Electron.openUrl;
    }
    open(config.downloadUrl || 'https://www.turito.com/whiteboard-download');
  }
  return agree;
};

// for test.
window.desktopTest = {
  request,
  ltVersion,
  agreeDesktopUpgrade,
};
