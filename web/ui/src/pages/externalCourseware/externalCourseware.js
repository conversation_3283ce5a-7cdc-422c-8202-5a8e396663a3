import App from './externalCourseware.vue';
import { externalCoursewareParams } from './params';
import './externalCoursewareTask';
import { startIframeTaskDaemon, startPollingTaskDaemon } from './taskDaemon';
import { initI18Next } from './i18n';

initI18Next(externalCoursewareParams.lng);

switch (externalCoursewareParams.pushMode) {
  case 'postMessage':
    startIframeTaskDaemon();
    break;
  case 'polling':
  default:
    startPollingTaskDaemon();
    break;
}

new window.Vue(App).$mount('#app');
