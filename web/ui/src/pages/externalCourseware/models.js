const requestTcicApi = (method, path, query, data) => new Promise((resolve, reject) => {
  const finalQuery = {
    random: Date.now(),
    lang: 'zh-CN',
    ...query,
  };

  const queryString = finalQuery
    ? `?${Object.entries(finalQuery)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&')
    }`
    : '';
  const url = `https://tcic-api.qcloudclass.com/${path}${queryString}`;
  const xhr = new XMLHttpRequest();
  xhr.open(method, url, true);
  xhr.setRequestHeader('Content-Type', 'application/json;charset=UTF-8');

  xhr.onreadystatechange = function () {
    if (xhr.readyState === 4) {
      if (xhr.status === 200) {
        const response = JSON.parse(xhr.responseText);

        if (response.error_code === 0) {
          resolve(response);
        } else {
          reject(response);
        }
      } else {
        reject(new Error(`请求失败 HTTP status=${xhr.status}`));
      }
    }
  };

  xhr.onerror = function () {
    reject(new Error('网络异常'));
  };

  xhr.send(JSON.stringify(data));
});

export const updateTask = (token, classId, taskId, content, duration = -1, onlyCreate = 0, bindingUser = '') => requestTcicApi(
  'POST',
  'v1/class/updateTask',
  {
    token,
  },
  {
    class_id: classId,
    task_id: taskId,
    content,
    duration,
    only_create: onlyCreate,
    binding_user: bindingUser,
  },
);

export const getTasks = (token, classId, seq = 0) => requestTcicApi(
  'POST',
  'v1/class/getTasks',
  {
    token,
  },
  {
    class_id: classId,
    seq,
  },
);

export const stopTask = (token, classId, taskId) => requestTcicApi(
  'POST',
  'v1/class/stopTask',
  {
    token,
  },
  {
    class_id: classId,
    task_id: taskId,
  },
);

