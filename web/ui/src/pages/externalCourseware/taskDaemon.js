import { getTasks } from './models';
import { externalCoursewareParams } from './params';
import { initialize as pmInitialize, registerMethod as pmRegisterMethod, invokeMethod as pmInvokeMethod } from '@tencent/easy-postmessage';

const taskLockMap = {};
const delayedTaskInfoMap = {};

let lastSeq = 0;
const taskSeqMap = {};

const taskUpdateListeners = [];
const loadedListeners = [];

const POLLING_INTERVAL = 2000;

export const startIframeTaskDaemon = () => {
  pmInitialize();
  pmInvokeMethod(window.parent, 'getTasks')
    .then(() => {
      emitLoaded();
    });
  pmRegisterMethod('notifyTaskUpdate', (taskInfo) => {
    emitTaskUpdate(taskInfo);
  });
};

export const startPollingTaskDaemon = () => {
  let isLoaded = false;

  const handlePolling = () => {
    console.log('polling tasks...');
    getTasks(externalCoursewareParams.token, externalCoursewareParams.classId, lastSeq)
      .then((resp) => {
        console.log('polling tasks got', resp);
        resp.tasks.forEach((taskInfo) => {
          emitTaskUpdate(taskInfo);
        });
        lastSeq = resp.last_seq;

        // 首次拉取到 tasks 列表时抛出 loaded 事件
        if (!isLoaded) {
          isLoaded = true;
          emitLoaded();
        }
      })
      .catch((err) => {
        console.error('polling tasks fail', err);
      })
      .then(() => {
        setTimeout(() => {
          handlePolling();
        }, POLLING_INTERVAL);
      });
  };

  handlePolling();
};

export const addTaskUpdateListener = (listener) => {
  taskUpdateListeners.push(listener);

  return () => {
    removeListener(taskUpdateListeners, listener);
  };
};

const broadcastEvent = (name, listeners, ...args) => {
  listeners.forEach((listener) => {
    try {
      listener(...args);
    } catch (error) {
      console.error(`[broadcastEvent: ${name}] listener err`, error);
    }
  });
};

export const emitTaskUpdate = (taskInfo) => {
  console.log('[taskUpdate] ↑ emit', taskInfo.task_id, taskInfo.seq, taskInfo);
  if (taskInfo.task_id in taskSeqMap && taskSeqMap[taskInfo.task_id] >= taskInfo.seq) {
    console.log('[taskUpdate] × skipped', taskInfo.task_id, taskInfo.seq, taskInfo);
    return;
  }

  taskSeqMap[taskInfo.task_id] = taskInfo.seq;

  if (isTaskLocked(taskInfo.task_id)) {
    console.log('[taskUpdate] @ delayed', taskInfo.task_id, taskInfo.seq, taskInfo);
    delayedTaskInfoMap[taskInfo.task_id] = taskInfo;
    return;
  }

  console.log('[taskUpdate] √ accept', taskInfo.task_id, taskInfo.seq, taskInfo);
  broadcastEvent('taskUpdate', taskUpdateListeners, taskInfo);
};

const isTaskLocked = taskId => !!taskLockMap[taskId];

const afterTaskLockReleased = (taskId) => {
  if (delayedTaskInfoMap[taskId]) {
    const taskInfo = delayedTaskInfoMap[taskId];
    console.log('[taskUpdate] @ delayed √ accept', taskInfo.task_id, taskInfo.seq, taskInfo);

    delete delayedTaskInfoMap[taskId];
    broadcastEvent('taskUpdate', taskUpdateListeners, taskInfo);
  }
};

export const lockTaskId = (taskId) => {
  let hasUnlocked = false;

  const unlock = () => {
    if (hasUnlocked) {
      return;
    }

    taskLockMap[taskId] -= 1;
    hasUnlocked = true;

    if (!isTaskLocked(taskId)) {
      afterTaskLockReleased(taskId);
    }
  };

  taskLockMap[taskId] = (taskLockMap[taskId] || 0) + 1;

  return unlock;
};

export const addLoadedListener = (listener) => {
  loadedListeners.push(listener);

  return () => {
    removeListener(loadedListeners, listener);
  };
};

const removeListener = (listeners, listener) => {
  const index = listeners.indexOf(listener);

  if (index !== -1) {
    listeners.splice(index, 1);
  }
};

export const removeLoadedListener = (listener) => {
  removeListener(loadedListeners, listener);
};

export const removeTaskUpdateListener = (listener) => {
  removeListener(taskUpdateListeners, listener);
};

export const emitLoaded = () => {
  broadcastEvent('loaded', loadedListeners);
};
