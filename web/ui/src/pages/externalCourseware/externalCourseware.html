<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 引入vue -->
    <script src="static/libs/vue/vue_2.7.10.js"></script>
    <!-- 引入组件库 -->
    <script src="static/libs/element_ui/element_ui_2.5.4.js"></script>
    <script src="static/libs/element_ui/locale/zh-CN.js"></script>
    <link rel="stylesheet" href="static/libs/element_ui/index.css">
    <title>课件</title>
    <script>
      const params = new URLSearchParams(location.search);
      if (params.has('title')) {
        document.title = params.get('title');
      } else if (params.has('courseware')) {
        document.title = params.get('courseware');
      }
    </script>
</head>
<body>
    <div id="app"></div>
</body>
</html>
