/* eslint-disable no-underscore-dangle */
import { getTasks, stopTask, updateTask } from './models';
import { externalCoursewareParams } from './params';
import { addTaskUpdateListener, emitTaskUpdate, lockTaskId } from './taskDaemon';

/**
 * @typedef {Object} TaskInfo
 * @property {number} seq - 任务序列号，每次修改任务序列号都会更新，序列号单调递增，课堂内全局唯一
 * @property {string} taskId - 任务ID，用于课堂内唯一索引一个任务
 * @property {number} status - 任务状态，0表示已停止，1表示进行中
 * @property {string} content - 任务内容
 * @property {number} createTime - 任务创建的服务器时间，UNIX时间戳
 * @property {number} updateTime - 任务更新的服务器时间，UNIX时间戳
 * @property {number} expireTime - 任务结束的服务器时间，UNIX时间戳，具体值为设置duration的时间点加上duration。如果duration为-1，则本值为0
 * @property {string} bindingUser - 任务绑定的用户ID，未绑定则为空字符串
 */

/**
 * 课堂 Task 机制的面向对象接口
 */
export class TaskAtom {
  /**
   * @param {string} taskId 任务ID
   */
  constructor(taskId) {
    this._taskId = taskId;
    this._taskInfo = null;

    this.subscribe((taskInfo) => {
      this._taskInfo = taskInfo;
    });
  }

  /**
   * 刷新任务信息
   */
  refresh() {
    return getTasks(0)
      .then((result) => {
        const taskInfo = result.tasks.find(task => task.taskId === this._taskId);
        if (taskInfo) {
          emitTaskUpdate(taskInfo);
        }
      })
      .catch((err) => {
        console.error('[TaskAtom] refresh for', this._taskId, 'failed', err);
        return Promise.reject(err);
      });
  }

  /**
   * 获取缓存的任务信息
   *
   * @returns {TaskInfo | null}
   */
  getCachedTaskInfo() {
    return this._taskInfo;
  }

  /**
   * 获取缓存的任务信息中的 content
   *
   * @returns {string | null}
   */
  getCachedContent() {
    return this._taskInfo ? this._taskInfo.content : null;
  }

  /**
   * 更新任务的 content
   *
   * @param {string} content
   * @returns {Promise<void>}
   */
  async setContent(content) {
    const unlock = lockTaskId(this._taskId);
    return updateTask(externalCoursewareParams.token, externalCoursewareParams.classId, this._taskId, content)
      .then((result) => {
        emitTaskUpdate(result.task);
      })
      .catch(() => {})
      .then(() => {
        unlock();
      });
  }

  /**
   * 结束任务（status 置为 0）
   *
   * @returns {Promise<void>}
   */
  async stop() {
    return stopTask(externalCoursewareParams.token, externalCoursewareParams.classId, this._taskId);
  }

  /**
   * 订阅任务更新的事件回调
   *
   * @param {(taskInfo: TaskInfo) => void} callback 回调函数
   * @returns {() => void} 调用返回的函数以取消订阅
   */
  subscribe(callback) {
    const listener = (taskInfo) => {
      if (taskInfo.task_id !== this._taskId) {
        return;
      }

      callback(taskInfo);
    };

    let unsubscribeFn = null;

    const unsubscribe = () => {
      unsubscribeFn();
    };

    unsubscribeFn = addTaskUpdateListener(listener);

    return unsubscribe;
  }
}
