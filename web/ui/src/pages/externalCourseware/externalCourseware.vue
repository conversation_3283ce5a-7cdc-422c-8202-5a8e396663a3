<template>
  <div id="app">
    <el-tabs
      v-if="tabList.length > 0 || showNewTabForm"
      v-model="activeTab"
      class="courseware-tabs"
      type="card"
      :closable="editable"
      :addable="editable"
      @tab-remove="removeTab"
      @tab-add="createNewTabForm"
    >
      <el-tab-pane
        v-for="item in tabList"
        :key="item.name"
        :label="item.title"
        :name="item.name"
      >
        <iframe
          v-if="item.url"
          class="courseware-iframe"
          allowfullscreen="true"
          :src="item.url"
        />
      </el-tab-pane>
      <el-tab-pane
        v-if="showNewTabForm"
        :key="newTabFormName"
        :label="textMap.addCourseware"
        :name="newTabFormName"
      >
        <el-form
          ref="form"
          :model="form"
          label-width="auto"
          class="courseware-form"
        >
          <el-form-item :label="textMap.coursewareTitle">
            <el-input
              v-model="newTabForm.title"
              :placeholder="textMap.defaultCoursewareTitle"
            />
          </el-form-item>
          <el-form-item :label="textMap.coursewareUrl">
            <el-input v-model="newTabForm.url" />
          </el-form-item>
          <el-form-item>
            <el-button @click="submitNewTab">
              {{ textMap.addCoursewareBtn }}
            </el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
    <div v-else>
      <div class="el-empty">
        <div class="el-empty__image" />
        <div class="el-empty__description">
          <p>{{ isLoading ? textMap.loading : textMap.noCourseware }}</p>
        </div>
        <div class="el-empty__bottom">
          <el-button
            v-if="editable && !isLoading"
            type="primary"
            @click="createNewTabForm"
          >
            {{ textMap.addCoursewareBtn }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { externalCoursewareParams } from './params';
import { externalCoursewareTask } from './externalCoursewareTask';
import { addLoadedListener } from './taskDaemon';
import i18next from 'i18next';

export default {
  data() {
    const { coursewareText, editable } = externalCoursewareParams;
    return {
      isLoading: true,
      showNewTabForm: false,
      newTabFormName: '$newTab',
      newTabForm: {
        title: '',
        url: '',
      },
      editable,
      textMap: {
        loading: i18next.t('加载中'),
        defaultCoursewareTitle: coursewareText,
        noCourseware: i18next.t('暂无课件', { courseware: coursewareText }),
        addCoursewareBtn: i18next.t('添加课件按钮'),
        addCourseware: i18next.t('添加课件', { courseware: coursewareText }),
        coursewareTitle: i18next.t('课件标题', { courseware: coursewareText }),
        coursewareUrl: i18next.t('课件网址', { courseware: coursewareText }),
      },
      activeTab: '',
      tabList: [],
    };
  },
  computed: {
  },
  watch: {
    activeTab(value) {
      if (value === this.newTabFormName) {
        this.activeTabLastIndex = -1;
      } else {
        const index = this.tabList.findIndex(tab => tab.name === value);
        if (index !== -1) {
          this.activeTabLastIndex = index;
        }
      }
    },
  },
  created() {
    this.activeTabLastIndex = 0;
    this.cleanupFnList = [];
  },
  mounted() {
    const cachedTaskInfo = externalCoursewareTask.getCachedTaskInfo();
    if (cachedTaskInfo) {
      this.handleTaskUpdate(cachedTaskInfo);
    }

    const unsubscribeTask = externalCoursewareTask.subscribe((taskInfo) => {
      this.handleTaskUpdate(taskInfo);
    });

    const unsubscribeLoaded = addLoadedListener(() => {
      this.isLoading = false;
    });

    this.cleanupFnList.push(unsubscribeTask);
    this.cleanupFnList.push(unsubscribeLoaded);
  },
  beforeDestroy() {
    this.cleanupFnList.forEach((cleanupFn) => {
      cleanupFn();
    });
  },
  methods: {
    handleTaskUpdate(taskInfo) {
      this.loadTabList(JSON.parse(taskInfo.content).tabs);
      this.isLoading = false;
    },
    createNewTabForm() {
      this.newTabForm.title = '';
      this.newTabForm.url = '';
      this.showNewTabForm = true;
      this.activeTab = this.newTabFormName;
    },
    deleteNewTabForm() {
      this.showNewTabForm = false;
      this.refreshActiveTab();
    },
    refreshActiveTab() {
      if (this.activeTab === this.newTabFormName && this.showNewTabForm) {
        return;
      }

      if (this.tabList.length === 0) {
        return;
      }

      if (this.tabList.findIndex(tab => tab.name === this.activeTab) === -1) {
        let nextIndex = this.activeTabLastIndex;
        if (nextIndex === -1) {
          nextIndex = this.tabList.length - 1;
        }
        nextIndex = Math.max(0, Math.min(this.tabList.length - 1, nextIndex));
        this.activeTab = this.tabList[nextIndex].name;
      }
    },
    submitNewTab() {
      this.deleteNewTabForm();
      const newTabName = `${Date.now()}`;
      this.tabList.push({
        name: newTabName,
        title: this.newTabForm.title || this.textMap.defaultCoursewareTitle,
        url: this.newTabForm.url,
      });
      this.activeTab = newTabName;
      this.saveTabList();
    },
    removeTab(targetName) {
      if (targetName === this.newTabFormName) {
        this.deleteNewTabForm();
        return;
      }

      const tabs = this.tabList;
      let activeTab = this.activeTab;
      if (activeTab === targetName) {
        tabs.forEach((tab, index) => {
          if (tab.name === targetName) {
            const nextTab = tabs[index + 1] || tabs[index - 1];
            if (nextTab) {
              activeTab = nextTab.name;
            }
          }
        });
      }
      this.activeTab = activeTab;
      this.tabList = tabs.filter(tab => tab.name !== targetName);
      this.saveTabList();
    },
    loadTabList(tabList) {
      this.tabList.splice(0, this.tabList.length);
      this.tabList.push(...tabList.map(tab => ({
        name: tab.name,
        title: tab.title,
        url: tab.url,
      })));
      this.refreshActiveTab();
    },
    saveTabList() {
      externalCoursewareTask.setContent(JSON.stringify({
        tabs: this.tabList
          .map(tab => ({
            name: tab.name,
            title: tab.title,
            url: tab.url,
          })),
      }));
    },
  },
};
</script>

<style>
@import './element-empty.css';
body {
  margin: 0;
  padding: 0;
  height: 100vh;
}

#app {
  height: 100%;
  box-sizing: border-box;
}

.courseware-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.courseware-tabs .el-tabs__header {
  padding: 8px 12px 0;
  margin-bottom: 0;
}

.courseware-tabs .el-tabs__content {
  flex-grow: 1;
}

.courseware-tabs .el-tab-pane {
  height: 100%;
}

.courseware-tabs .el-tabs__new-tab {
  width: auto;
  font-size: 18px;
  height: auto;
  padding: 4px;
  margin-top: 4px;
  color: #000;
}

.courseware-iframe {
  border: none;
  width: 100%;
  height: 100%;
  flex: 1
}

.courseware-form {
  padding: 16px;
}
</style>
