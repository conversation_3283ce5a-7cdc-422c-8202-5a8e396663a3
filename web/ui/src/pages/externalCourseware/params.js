let coursewareText = '课件';
let userId = '';
let token = '';
let classId = '';
let editable = false;
let pushMode = 'polling'; // polling / postMessage
let lng = 'en';

const searchParams = new URLSearchParams(location.search);
coursewareText = searchParams.get('courseware') || coursewareText;
userId = searchParams.get('userId') || userId;
token = searchParams.get('token') || token;
classId = parseInt(searchParams.get('classId') || classId, 10);
editable = searchParams.get('editable') === '1';
pushMode = searchParams.get('pushMode') || pushMode;
lng = searchParams.get('lng') || lng;

export const externalCoursewareParams = {
  coursewareText,
  userId,
  token,
  classId,
  editable,
  pushMode,
  lng,
};

console.log('externalCoursewareParams', externalCoursewareParams);
