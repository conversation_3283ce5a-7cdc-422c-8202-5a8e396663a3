import i18next from 'i18next';
import zh from './locales/zh.json';
import zhTW from './locales/zh-TW.json';
import en from './locales/en.json';
import ko from './locales/ko.json';
import ja from './locales/ja.json';

export const initI18Next = (lng) => {
  i18next.init({
    lng,
    resources: {
      zh: { translation: zh },
      'zh-TW': { translation: zhTW },
      en: { translation: en },
      ko: { translation: ko },
      ja: { translation: ja },
    },
    fallbackLng: 'en',
    load: 'currentOnly',
    keySeparator: false,
    nsSeparator: false,
  });
};
