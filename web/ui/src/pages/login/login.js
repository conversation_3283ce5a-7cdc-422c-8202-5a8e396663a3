import loginConfig from './config.js';
import moment from 'moment';
import i18next from 'i18next';
import zh from './locales/zh.json';
import zhTW from './locales/zh-TW.json';
import en from './locales/en.json';
import ko from './locales/ko.json';
import ja from './locales/ja.json';
import vi from './locales/vi.json';
import ar from './locales/ar.json';
import App from './login.vue';

i18next.init({
  lng: 'en',
  resources: {
    zh: { translation: zh },
    'zh-TW': { translation: zhTW },
    en: { translation: en },
    ko: { translation: ko },
    ja: { translation: ja },
    vi: { translation: vi },
    ar: { translation: ar },
  },
  fallbackLng: 'en',
  load: 'currentOnly',
  keySeparator: false,
  nsSeparator: false,
});

window.loginConfig = loginConfig;
window.moment = moment;
window.i18next = i18next;
window.$tt = i18next.t;

// 为什么不直接 import Vue ...
window.Vue.prototype.$t = i18next.t;

new window.Vue(App).$mount('#app');
