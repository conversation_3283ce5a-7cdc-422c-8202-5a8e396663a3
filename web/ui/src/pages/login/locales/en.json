{"实时互动-教育版": "Low-Code Interactive Class", "TCIC 版本": "TCIC Version", "课堂列表": "Class List", "课堂状态": "Class Status", "网络检测": "Network Test", "未开始": "Not started", "进行中": "In progress", "已结束": "Finished", "已过期": "Expired", "加入课堂": "Join Class", "查看回放": "View Playback", "暂无课堂，请先创建课堂": "No class, please create first", "获取课堂列表失败，点击重试": "Failed to get class list, click to retry", "创建课堂": "Create Class", "进入课堂": "Enter Class", "课堂ID": "Class ID", "请输入课堂ID": "Please input Class ID", "课堂ID输入不正确": "Class ID error", "课堂名称": "Class name", "请输入课堂名称": "Please input class name", "角色类型": "Role type", "老师": "teacher", "助教": "assistant", "学生": "student", "巡课": "<PERSON><PERSON><PERSON>er", "老师昵称": "Teacher name", "请输入老师昵称": "Please input teacher name", "助教昵称": "TA name", "请输入助教昵称": "Please input assistant name", "学生昵称": "Student name", "请输入学生昵称": "Please input student name", "巡课昵称": "Training teacher name", "请输入巡课昵称": "Please input training teacher name", "角色昵称": "Role name", "请输入角色昵称": "Please input role name", "随机": "Random", "上课时间": "Class time", "请选择上课时间": "Please select class time", "开始时间": "Start time", "课堂时长": "Class duration", "请选择课堂时长": "Please select class duration", "15分钟": "15 Minutes", "30分钟": "30 Minutes", "1小时": "1 Hour", "2小时": "2 Hours", "3小时": "3 Hours", "4小时": "4 Hours", "5小时": "5 Hours", "班型选择": "Class type", "小班课": "Small Classroom", "大班课": "Lecture Hall", "最大上台人数": "Max on-stage limit", "请选择最大上台人数": "Please select max on-stage limit", "0人": "0", "1人": "1", "2~6人": "2~6", "7~12人": "7~12", "13~16人": "13~16", "教室布局": "Layout", "视频+文档布局": "Camera + PPT", "纯视频布局": "Camera only", "横屏(视频+白板)": "Landscape (video + whiteboard)", "横屏(纯视频)": "Landscape (video only)", "竖屏(纯视频)": "Portrait (video only)", "上台设置": "On-stage mode", "允许学生自动上台": "Auto on-stage", "音质模式": "Audio quality", "标准音质": "Standard quality", "高音质": "High quality", "课堂分辨率": "Resolution", "标清": "SD", "高清": "HD", "全高清": "Full HD", "课后评价": "Post-class Evaluation", "老师控制权限": "Teacher control authority", "无需获得学生授权": "No student authorization required", "开启扬声器": "Enable speaker", "开启麦克风": "Enable microphone", "开启摄像头": "Enable camera", "开启录制": "Enable record", "录制布局": "Record layout", "互动模式": "Interaction mode", "观看所有参与者的音视频": "Subscribe to All", "仅看老师和助教": "Subscribe to Teacher & TA Only", "取消": "Cancel", "更多配置": "More Config", "课堂环境": "Environment", "正式环境": "Formal environment", "测试环境": "Test environment", "请输入SdkAppId": "Please input SdkAppId", "旗舰版": "Ultimate", "轻量版": "Light", "Uin列表": "Uin list", "课堂URL": "Class URL", "登录": "<PERSON><PERSON>", "设备检测": "Device Detection", "完成": "Complete", "课堂信息": "Room Info", "课堂设置": "Room Setting", "课堂创建成功": "Class created successfully", "YYYY年 MM月DD日": "YYYY-MM-DD", "课堂地址": "Classroom URL", "全部复制": "Copy", "复制": "Copy", "复制成功": "Copy successfully", "复制失败": "Co<PERSON> failed", "立即进入": "Enter Class", "暂不进入": "Back", "重新创建": "Recreate", "设备设置": "<PERSON><PERSON>", "限时特惠": "/", "更多 Demo 体验": "More Platforms", "请求失败": "Request failed", "获取课堂信息失败": "Fail to get class info", "InternalError": "Internal error", "InvalidParameter": "Invalid parameter", "InvalidParameter.SdkAppId": "Invalid parameter: SdkAppId", "InvalidParameter.StartTime": "The start time cannot be earlier than the current time", "InvalidParameter.EndTime": "The end time cannot be earlier than the start time", "ResourceNotFound.Room": "Class does not exist", "ResourceNotFound.User": "User does not exist", "推流方式": "Streaming Mode", "互动小班课": "Small Classroom", "直播大班课（公开课）": "Lecture Hall（public class）", "直播大班课": "Lecture Hall", "课堂模式": "Classroom mode", "纯音视频": "Camera only", "音视频+白板": "Camera + PPT", "展开更多课堂配置": "Expand more", "15分钟快速上线自有品牌的互动教学平台": "Launch your own interactive teaching platform in 15 minutes - Free trial", "免费试用": "Free trial available", "集成指南": "Integration guide", "企微咨询": "Enterprise WeChat consultation", "欢迎加入": "Welcome to join us", "技术服务交流群": "Technical Service Exchange Group", "基于实时音视频TRTC技术，支持17人同时上台实时音视频互动。": "Based on TRTC, supporting 17 people to interact on stage simultaneously", "老师对多达万名学生进行直播教学，学生可以实时申请上台与老师进行音视频互动。": "Teachers can conduct  teaching for up to 10,000 students, and students can apply in real time to interact with teachers on stage. ", "竖屏(视频+白板)": "Portrait (video + whiteboard)", "体验AI课堂": "Experience AI Classroom", "需学生授权，开启摄像头和麦克风": "Students' authorization is required to turn on the camera and microphone.", "无须学生授权，开启摄像头和麦克风": "There is no need for students' authorization to turn on the camera and microphone.", "需学生授权，开启麦克风（摄像头无须授权）": "Students' authorization is required to turn on the microphone (while no authorization is needed for the camera).", "需学生授权，开启摄像头（麦克风无须授权）": "Students' authorization is required to turn on the camera (while no authorization is needed for the microphone).", "支持 <span>音视频连麦、互动白板、屏幕共享、云端录制和直播</span> 等功能。老师和学生可以在学习软件上预约课程，并通过该学习工具（实时互动教育版）进入课堂进行学习。": "It supports  <span>audio and video live connections, interactive whiteboards, screen sharing, cloud recording and live broadcasting</span> and other functions. Teachers and students can reserve courses on the learning software and enter the classroom to study through this learning tool (Real-time Interactive Education Edition)."}