{"实时互动-教育版": "实时互动-教育版", "TCIC 版本": "TCIC 版本", "课堂列表": "课堂列表", "课堂状态": "课堂状态", "网络检测": "网络检测", "未开始": "未开始", "进行中": "进行中", "已结束": "已结束", "已过期": "已过期", "加入课堂": "加入课堂", "查看回放": "查看回放", "暂无课堂，请先创建课堂": "暂无课堂，请先创建课堂", "获取课堂列表失败，点击重试": "获取课堂列表失败，点击重试", "创建课堂": "创建课堂", "进入课堂": "进入课堂", "课堂ID": "课堂ID", "请输入课堂ID": "请输入课堂ID", "课堂ID输入不正确": "课堂ID输入不正确", "课堂名称": "课堂名称", "请输入课堂名称": "请输入课堂名称", "角色类型": "角色类型", "老师": "老师", "助教": "助教", "学生": "学生", "巡课": "巡课", "老师昵称": "老师昵称", "请输入老师昵称": "请输入老师昵称", "助教昵称": "助教昵称", "请输入助教昵称": "请输入助教昵称", "学生昵称": "学生昵称", "请输入学生昵称": "请输入学生昵称", "巡课昵称": "巡课昵称", "请输入巡课昵称": "请输入巡课昵称", "角色昵称": "角色昵称", "请输入角色昵称": "请输入角色昵称", "随机": "随机", "上课时间": "上课时间", "请选择上课时间": "请选择上课时间", "开始时间": "开始时间", "课堂时长": "课堂时长", "请选择课堂时长": "请选择课堂时长", "15分钟": "15分钟", "30分钟": "30分钟", "1小时": "1小时", "2小时": "2小时", "3小时": "3小时", "4小时": "4小时", "5小时": "5小时", "班型选择": "班型选择", "小班课": "小班课", "大班课": "大班课", "最大上台人数": "最大上台人数", "请选择最大上台人数": "请选择最大上台人数", "0人": "0人", "1人": "1人", "2~6人": "2~6人", "7~12人": "7~12人", "13~16人": "13~16人", "教室布局": "教室布局", "视频+文档布局": "视频+文档布局", "纯视频布局": "纯视频布局", "横屏(视频+白板)": "横屏(视频+白板)", "横屏(纯视频)": "横屏(纯视频)", "竖屏(纯视频)": "竖屏(纯视频)", "上台设置": "上台设置", "允许学生自动上台": "允许学生自动上台", "音质模式": "音质模式", "标准音质": "标准音质", "高音质": "高音质", "课堂分辨率": "课堂分辨率", "标清": "标清", "高清": "高清", "全高清": "全高清", "课后评价": "课后评价", "老师控制权限": "老师控制权限", "无需获得学生授权": "无需获得学生授权", "开启扬声器": "开启扬声器", "开启麦克风": "开启麦克风", "开启摄像头": "开启摄像头", "开启录制": "开启录制", "录制布局": "录制布局", "互动模式": "互动模式", "观看所有参与者的音视频": "观看所有参与者的音视频", "仅看老师和助教": "仅看老师和助教", "取消": "取消", "更多配置": "更多配置", "课堂环境": "课堂环境", "正式环境": "正式环境", "测试环境": "测试环境", "请输入SdkAppId": "请输入SdkAppId", "旗舰版": "旗舰版", "轻量版": "轻量版", "Uin列表": "Uin列表", "课堂URL": "课堂URL", "登录": "登录", "设备检测": "设备检测", "完成": "完成", "课堂信息": "课堂信息", "课堂设置": "课堂设置", "课堂创建成功": "课堂创建成功", "YYYY年 MM月DD日": "YYYY年 MM月DD日", "课堂地址": "课堂地址", "全部复制": "全部复制", "复制": "复制", "复制成功": "复制成功", "复制失败": "复制失败", "立即进入": "立即进入", "暂不进入": "暂不进入", "重新创建": "重新创建", "设备设置": "设备设置", "限时特惠": "限时特惠", "更多 Demo 体验": "更多 Demo 体验", "请求失败": "请求失败", "获取课堂信息失败": "获取课堂信息失败", "InternalError": "内部错误", "InvalidParameter": "参数错误", "InvalidParameter.SdkAppId": "参数错误: SdkAppId", "InvalidParameter.StartTime": "开始时间不能早于当前时间", "InvalidParameter.EndTime": "结束时间不能早于开始时间", "ResourceNotFound.Room": "课堂不存在", "ResourceNotFound.User": "用户不存在", "推流方式": "推流方式", "互动小班课": "互动小班课", "直播大班课": "直播大班课", "直播大班课（公开课）": "直播大班课（公开课）", "课堂模式": "课堂模式", "纯音视频": "纯音视频", "音视频+白板": "音视频+白板", "展开更多课堂配置": "展开更多课堂配置", "15分钟快速上线自有品牌的互动教学平台": "15分钟快速上线自有品牌的互动教学平台", "免费试用": "免费试用", "集成指南": "集成指南", "企微咨询": "企微咨询", "欢迎加入": "欢迎加入", "技术服务交流群": "技术服务交流群", "基于实时音视频TRTC技术，支持17人同时上台实时音视频互动。": "基于实时音视频TRTC技术，支持17人同时上台实时音视频互动。", "老师对多达万名学生进行直播教学，学生可以实时申请上台与老师进行音视频互动。": "老师对多达万名学生进行直播教学，学生可以实时申请上台与老师进行音视频互动。", "竖屏(视频+白板)": "竖屏(视频+白板)", "体验AI课堂": "体验AI课堂", "需学生授权，开启摄像头和麦克风": "需学生授权，开启摄像头和麦克风", "无须学生授权，开启摄像头和麦克风": "无须学生授权，开启摄像头和麦克风", "需学生授权，开启麦克风（摄像头无须授权）": "需学生授权，开启麦克风（摄像头无须授权）", "需学生授权，开启摄像头（麦克风无须授权）": "需学生授权，开启摄像头（麦克风无须授权）", "支持 <span>音视频连麦、互动白板、屏幕共享、云端录制和直播</span> 等功能。老师和学生可以在学习软件上预约课程，并通过该学习工具（实时互动教育版）进入课堂进行学习。": "支持 <span>音视频连麦、互动白板、屏幕共享、云端录制和直播</span> 等功能。老师和学生可以在学习软件上预约课程，并通过该学习工具（实时互动教育版）进入课堂进行学习。"}