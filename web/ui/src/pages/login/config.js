/**
 * login页配置
 * 本文件中 title|label|message|formatErrorMessage 字段的值会被扫描到语言包配置文件里，新增词条时请注意检查
 * 如需特殊处理可以修改 web/ui/i18next-scanner-login.config.js
 */

// 语言配置
const languageConfig = {
  zh: {
    value: 'zh',
    label: '中文(简体)',
    elementLang: 'zhCN',
    logoText: '实时互动-教育版',
    logoTextStyle: 'font-size: 35px; line-height: 58px; vertical-align: 5%;',
  },
  'zh-TW': {
    value: 'zh-TW',
    label: '中文(繁体)',
    elementLang: 'zhTW',
    logoText: '實時互動-教育版',
    logoTextStyle: 'font-size: 35px; line-height: 58px; vertical-align: 5%;',
  },
  en: {
    value: 'en',
    label: 'English',
    isWideText: true,
    logoText: ['Low-Code Interactive', 'Class'],
    logoTextStyle: 'font-size: 24px; line-height: 28px;',
  },
  ko: {
    value: 'ko',
    label: '한국인',
    isWideText: true,
    logoText: '로우코드 상호 작용 수업',
    logoTextStyle: 'font-size: 26px; line-height: 62px;',
  },
  ja: {
    value: 'ja',
    label: '日本語',
    isWideText: true,
    logoText: ['低コードインタラクティブ', 'クラスルーム'],
    logoTextStyle: 'font-size: 21px; line-height: 28px;',
  },
  vi: {
    value: 'vi',
    label: 'Tiếng Việt',
    isWideText: true,
    logoText: 'Lớp học tương tác',
    logoTextStyle: 'font-size: 26px; line-height: 62px;',
  },
  ar: {
    value: 'ar',
    label: 'العربية',
    isWideText: true,
    logoText: 'مدرس تفاعل',
    logoTextStyle: 'font-size: 26px; line-height: 62px;',
  },
};


// innerLayout变化后要更新的roomConfig，参数说明 https://cloud.tencent.com/document/product/1639/80942
const innerLayoutConfig = {
  videodoc: {
    SubType: 'videodoc',
    VideoOrientation: 0,
  },
  video: {
    SubType: 'video',
    VideoOrientation: 0,
  },
  videoPortrait: {
    SubType: 'video',
    VideoOrientation: 1,
  },
  videodocPortrait: {
    SubType: 'videodoc',
    VideoOrientation: 1,
  },
};

/**
 * 房间表单配置，以pc版为准，phone如果不一样在phoneConfig里覆盖
 * label 字段名，用户可见
 * required 是否必填，必填会在label后显示红色星号
 * type 控件类型
 * message 数据为空的提示
 * name 对应的字段名，text类型对应 idInputs.xxx，其他对应 roomConfig.xxx
 * visibleName/visibleValue 字段可见条件，没指定 visibleName 时不限制，指定 visibleName 时 roomConfig[visibleName] === visibleValue 时可见
 * enableName/enableValue 字段可用条件，没指定 enableName 时不限制，指定 enableName 时 roomConfig[enableName] === enableValue 时可见
 * options 选项map，selector/radioGroup/switch 类型用
 * optionsVisibleName 选项可见条件，没指定时不限制，指定时 roomConfig[optionsVisibleName] 中的选项可见
 * optionsEnableName 选项可选条件，没指定时不限制，指定时 roomConfig[optionsEnableName] 中的选项可选
 */
const roomFormConfig = {
  // create start
  // 参数说明见 https://cloud.tencent.com/document/product/1639/80942
  roomName: {
    label: '课堂名称',
    required: true,
    type: 'text',
    name: 'className', // idInputs.xxx
    maxlength: 30,
    remindEmpty: true,
    message: '请输入课堂名称',
  },
  teacherName: {
    label: '老师昵称',
    required: true,
    type: 'text',
    name: 'teacherName', // idInputs.xxx
    maxlength: 30,
    randomNum: true,
    randomNumPrefix: 'teacher',
    remindEmpty: true,
    message: '请输入老师昵称',
  },
  assistantName: {
    label: '助教昵称',
    required: true,
    type: 'text',
    name: 'assistantName', // idInputs.xxx
    maxlength: 30,
    randomNum: true,
    randomNumPrefix: 'assistant',
    remindEmpty: true,
    message: '请输入助教昵称',
  },
  startTime: {
    label: '上课时间',
    type: 'datetimePicker',
    name: 'PickerTime', // roomConfig.xxx
    message: '请选择上课时间',
  },
  period: {
    label: '课堂时长',
    type: 'selector',
    name: 'Period', // roomConfig.xxx
    message: '请选择课堂时长',
    options: {
      // 15: {
      //   value: 15,
      //   label: '15分钟',
      // },
      30: {
        value: 30,
        label: '30分钟',
      },
      60: {
        value: 60,
        label: '1小时',
      },
      120: {
        value: 120,
        label: '2小时',
      },
      180: {
        value: 180,
        label: '3小时',
      },
      240: {
        value: 240,
        label: '4小时',
      },
      300: {
        value: 300,
        label: '5小时',
      },
    },
  },
  roomType: {
    label: '班型选择',
    type: 'radioGroup',
    name: 'RoomType',
    areaStyle: 'height:auto',
    options: {
      0: {
        value: 0,
        label: '互动小班课',
        desc: '基于实时音视频TRTC技术，支持17人同时上台实时音视频互动。',
      },
      1: {
        value: 1,
        label: '直播大班课（公开课）',
        desc: '老师对多达万名学生进行直播教学，学生可以实时申请上台与老师进行音视频互动。',
      },
    },
  },
  mobileRoomType: {
    label: '班型选择',
    type: 'selector',
    name: 'RoomType',
    options: {
      0: {
        value: 0,
        label: '互动小班课',
      },
      1: {
        value: 1,
        label: '直播大班课',
      },
    },
  },
  maxMacNumber: {
    label: '最大上台人数',
    type: 'selector',
    name: 'MaxMicNumber', // roomConfig.xxx
    message: '请选择最大上台人数',
    enableName: 'MaxMicNumberEnable',
    optionsVisibleName: 'MaxMicNumberVisibleOptions',
    options: {
      0: {
        value: 0,
        label: '0人',
      },
      1: {
        value: 1,
        label: '1人',
      },
      6: {
        value: 6,
        label: '2~6人',
      },
      12: {
        value: 12,
        label: '7~12人',
      },
      16: {
        value: 16,
        label: '13~16人',
      },
    },
  },
  layout: {
    label: '课堂模式',
    type: 'radioGroup',
    name: 'InnerLayout', // 新增选项需要同时更新 innerLayoutConfig
    options: {
      video: {
        value: 'video',
        label: '纯音视频',
      },
      videodoc: {
        value: 'videodoc',
        label: '音视频+白板',
      },
    },
  },
  autoMic: {
    label: '上台设置',
    type: 'switch',
    name: 'AutoMic', // roomConfig.xxx
    enableName: 'AutoMicEnable',
    options: {
      active: {
        value: 1,
        label: '允许学生自动上台',
      },
      inactive: {
        value: 0,
      },
    },
  },
  audioQuality: {
    label: '音质模式',
    type: 'radioGroup',
    name: 'AudioQuality', // roomConfig.xxx
    options: {
      0: {
        value: 0,
        label: '标准音质',
      },
      1: {
        value: 1,
        label: '高音质',
      },
    },
  },
  resolution: {
    label: '课堂分辨率',
    type: 'radioGroup',
    name: 'Resolution', // roomConfig.xxx
    optionsEnableName: 'ResolutionEnableOptions', // roomConfig.xxx
    options: {
      1: {
        value: 1,
        label: '标清',
      },
      2: {
        value: 2,
        label: '高清',
      },
      3: {
        value: 3,
        label: '全高清',
      },
    },
  },
  gradingAfterClass: {
    label: '课后评价',
    type: 'switch',
    name: 'IsGradingRequiredPostClass',
    options: {
      active: {
        value: 1,
      },
      inactive: {
        value: 0,
      },
    },
  },
  enableDirectControl: {
    label: '老师控制权限',
    type: 'switch',
    name: 'EnableDirectControl', // roomConfig.xxx
    options: {
      active: {
        value: 1,
        label: '无需获得学生授权',
      },
      inactive: {
        value: 0,
      },
    },
  },
  // create in others
  interactionMode: {
    label: '互动模式',
    type: 'radioGroup',
    name: 'InteractionMode',
    options: {
      0: {
        value: 0,
        label: '观看所有参与者的音视频',
      },
      1: {
        value: 1,
        label: '仅看老师和助教',
      },
    },
  },
  enableRecord: {
    label: '开启录制',
    type: 'switch',
    name: 'EnableRecord',
    options: {
      active: {
        value: 1,
      },
      inactive: {
        value: 0,
      },
    },
  },
  // 自定义录制布局，为空表示根据教室布局自动填写
  customRecordLayout: {
    label: '录制布局',
    type: 'text',
    name: 'CustomRecordLayout',
  },
  // create end
  // enter start
  roomId: {
    label: '课堂ID',
    required: true,
    type: 'number',
    name: 'classId', // idInputs.xxx
    maxlength: 9,
    remindEmpty: true,
    message: '请输入课堂ID',
    formatErrorMessage: '课堂ID输入不正确',
  },
  roleType: {
    label: '角色类型',
    required: true,
    type: 'radioGroup',
    name: 'RoleType', // roomConfig.xxx
    optionsVisibleName: 'RoleTypeVisibleOptions', // roomConfig.xxx
    options: {
      teacher: {
        label: '老师',
        value: 'teacher',
      },
      assistant: {
        label: '助教',
        value: 'assistant',
      },
      student: {
        label: '学生',
        value: 'student',
      },
      supervisor: {
        label: '巡课',
        value: 'supervisor',
      },
    },
  },
  studentName: {
    label: '学生昵称',
    required: true,
    type: 'text',
    name: 'studentName', // idInputs.xxx
    visibleName: 'RoleType', // roomConfig.xxx
    visibleValue: 'student',
    maxlength: 30,
    randomNum: true,
    randomNumPrefix: 'student',
    remindEmpty: true,
    message: '请输入学生昵称',
  },
  trainingTeacherName: {
    label: '巡课昵称',
    required: true,
    type: 'text',
    name: 'trainingTeacherName', // idInputs.xxx
    visibleName: 'RoleType', // roomConfig.xxx
    visibleValue: 'supervisor',
    maxlength: 30,
    randomNum: true,
    randomNumPrefix: 'trainingTeacher',
    remindEmpty: true,
    message: '请输入巡课昵称',
  },
  enableSpeaker: {
    label: '开启扬声器',
    type: 'switch',
    name: 'Speaker',
    options: {
      active: {
        value: 1,
      },
      inactive: {
        value: 0,
      },
    },
  },
  enableMic: {
    label: '开启麦克风',
    type: 'switch',
    name: 'Mic',
    options: {
      active: {
        value: 1,
      },
      inactive: {
        value: 0,
      },
    },
  },
  enableCamera: {
    label: '开启摄像头',
    type: 'switch',
    name: 'Camera',
    options: {
      active: {
        value: 1,
      },
      inactive: {
        value: 0,
      },
    },
  },
  // enter end
};

export default {
  // eslint-disable-next-line no-undef
  sdkVersion: VERSION_CONFIG.mainVersion,
  languageConfig,
  innerLayoutConfig,
  roomFormConfig,
  // 登录各页面标题、按钮等
  loginPageConfig: {
    // 登录选择
    login: {
      title: '登录',
      roomList: {
        roomStatus: {
          options: {
            0: {
              value: 0,
              label: '未开始',
              sortPriority: 'p2', // 越小的排在越前
            },
            1: {
              value: 1,
              label: '进行中',
              sortPriority: 'p1',
            },
            2: {
              value: 2,
              label: '已结束',
              sortPriority: 'p3',
            },
            3: {
              value: 3,
              label: '已过期',
              sortPriority: 'p4',
            },
          },
        },
        roomMaxMicNumber: {
          options: {
            0: {
              value: 0,
              label: '0人',
            },
            1: {
              value: 1,
              label: '1人',
            },
            6: {
              value: 6,
              label: '2~6人',
            },
            12: {
              value: 12,
              label: '7~12人',
            },
            16: {
              value: 16,
              label: '13~16人',
            },
          },
        },
      },
    },
    // 设备检测
    detection: {
      title: '设备检测',
    },
    // 创建课堂房间
    create: {
      title: '创建课堂',
      roomInfo: {
        formConfig: [
          roomFormConfig.roomName,
          roomFormConfig.teacherName,
          // roomFormConfig.assistantName,
        ],
      },
      roomSetting: {
        formConfigMain: [
          roomFormConfig.startTime,
          roomFormConfig.period,
          roomFormConfig.layout,
          roomFormConfig.roomType,
        ],
        formConfigDetail: [
          roomFormConfig.maxMacNumber,
          roomFormConfig.autoMic,
          roomFormConfig.audioQuality,
          roomFormConfig.resolution,
          roomFormConfig.gradingAfterClass,
          roomFormConfig.enableDirectControl],
      },
    },
    // 进入课堂房间
    enter: {
      title: '进入课堂',
      roomInfo: {
        formConfig: [
          roomFormConfig.roomId,
          roomFormConfig.roleType,
          roomFormConfig.studentName,
          roomFormConfig.trainingTeacherName,
        ],
      },
      deviceSetting: {
        formConfig: [
          roomFormConfig.enableSpeaker,
          roomFormConfig.enableMic,
          roomFormConfig.enableCamera,
        ],
      },
    },
    enterAi: {
      title: '进入课堂',
      roomInfo: {
        formConfig: [
          roomFormConfig.roomId,
          roomFormConfig.roleType,
          roomFormConfig.studentName,
          roomFormConfig.trainingTeacherName,
        ],
      },
      deviceSetting: {
        formConfig: [
          roomFormConfig.enableSpeaker,
          roomFormConfig.enableMic,
          roomFormConfig.enableCamera,
        ],
      },
    },
    // 跳转链接
    links: [
      // {
      //   type: 'primary',
      //   zh: {
      //     text: '限时特惠',
      //     url: 'https://cloud.tencent.com/act/pro/Lcic?from=19529',
      //   },
      //   'zh-TW': {
      //     text: '限時特惠',
      //     url: 'https://cloud.tencent.com/act/pro/Lcic?from=19529',
      //   },
      //   en: null,
      // },
      // {
      //   zh: {
      //     text: '更多 Demo 体验',
      //     url: 'https://cloud.tencent.com/document/product/1639/81110',
      //   },
      //   'zh-TW': {
      //     text: '更多 Demo 體驗',
      //     url: 'https://cloud.tencent.com/document/product/1639/81110',
      //   },
      //   en: {
      //     text: 'More Platforms',
      //     url: 'https://www.tencentcloud.com/document/product/1168/53674',
      //   },
      // },
    ],
    // 手机端配置
    phoneConfig: {
      create: {
        roomInfo: {
          formConfig: [
            roomFormConfig.roomName,
            roomFormConfig.teacherName,
          ],
        },
        basicSetting: {
          formConfig: [
            roomFormConfig.startTime,
            roomFormConfig.period,
            roomFormConfig.mobileRoomType,
            roomFormConfig.maxMacNumber,
            {
              ...roomFormConfig.layout,
              type: 'selector',
              optionsVisibleName: 'InnerLayoutVisibleOptions',
              options: {
                videodoc: {
                  value: 'videodoc',
                  label: '横屏(视频+白板)',
                },
                video: {
                  value: 'video',
                  label: '横屏(纯视频)',
                },
                videoPortrait: {
                  value: 'videoPortrait',
                  label: '竖屏(纯视频)',
                },
                // videodocPortrait: {
                //   value: 'videodocPortrait',
                //   label: '竖屏(视频+白板)',
                // },
              },
            },
          ],
        },
        superSetting: {
          formConfig: [
            {
              ...roomFormConfig.audioQuality,
              type: 'selector',
            },
            {
              ...roomFormConfig.resolution,
              type: 'selector',
            },
          ],
        },
      },
      enter: {
        roomInfo: {
          roomId: roomFormConfig.roomId,
          roleType: {
            ...roomFormConfig.roleType,
            type: 'selector',
          },
          roleName: {
            label: '角色昵称',
            name: 'roleName',
            message: '请输入角色昵称',
          },
        },
        basicSetting: {
          formConfig: [
            roomFormConfig.enableSpeaker,
            roomFormConfig.enableMic,
            roomFormConfig.enableCamera,
          ],
        },
      },
      enterAi: {
        roomInfo: {
          roomId: roomFormConfig.roomId,
          roleName: {
            label: '角色昵称',
            name: 'roleName',
            message: '请输入角色昵称',
          },
        },
        basicSetting: {
          formConfig: [
            roomFormConfig.enableMic,
          ],
        },
      },
    },
  },
};
