<template>
  <div class="login-form">
    <div
      v-for="(item, index) in formConfig"
      v-show="isItemVisible(item)"
      :key="index"
      class="input-item"
    >
      <div class="label">
        <span>{{ $t(item.label) }}</span><span
          v-show="item.required"
          style="color:red;margin-left:10px;"
        >*</span>
      </div>
      <div class="input-area">
        <input
          v-if="item.type==='number'"
          v-model="formDataInner[item.name]"
          type="number"
          class="input-control numberInput"
          style="ime-mode: disabled"
          pattern="[0-9]*"
          oninput="value=value.replace(/[^\\d]/g,'')"
          :maxlength="item.maxlength"
          :placeholder="$t(item.message)"
          @dblclick="selectValue($event)"
        >
        <input
          v-if="item.type==='text'"
          v-model="formDataInner[item.name]"
          type="text"
          class="input-control"
          :maxlength="item.maxlength"
          :placeholder="$t(item.message)"
          @dblclick="selectValue($event)"
        >
        <div
          v-if="item.type==='text'"
          v-show="item.randomNum"
          class="random"
          @click="$emit('click-random', item)"
        >
          {{ $t('随机') }}
        </div>
        <el-date-picker
          v-if="item.type==='datetimePicker'"
          v-model="formDataInner[item.name]"
          type="datetime"
          size="small"
          :editable="false"
          :placeholder="$t(item.message)"
          prefix-icon="el-icon-date"
          format="yyyy-MM-dd HH:mm"
        />
        <el-select
          v-if="item.type==='selector'"
          v-model="formDataInner[item.name]"
          size="small"
          :placeholder="$t(item.message)"
          :disabled="!isItemEnable(item)"
        >
          <el-option
            v-for="option in item.options"
            :key="option.value"
            :label="$t(option.label)"
            :value="option.value"
          />
        </el-select>
        <el-radio-group
          v-if="item.type==='radioGroup'"
          v-model="formDataInner[item.name]"
          :disabled="!isItemEnable(item)"
        >
          <el-radio
            v-for="option in item.options"
            v-show="!item.optionsVisibleName || formDataInner[item.optionsVisibleName][option.value]"
            :key="option.value"
            :label="option.value"
            :value="option.value"
            :disabled="item.optionsEnableName && !formDataInner[item.optionsEnableName][option.value]"
          >
            {{ $t(option.label) }}
          </el-radio>
        </el-radio-group>
        <el-switch
          v-if="item.type==='switch'"
          v-model="formDataInner[item.name]"
          size="small"
          active-color="#006cff"
          inactive-color="#959595"
          :active-text="getSwitchText(item, true)"
          :active-value="getSwitchValue(item, true)"
          :inactive-value="getSwitchValue(item, false)"
          :disabled="!isItemEnable(item)"
        />
      </div>
      <div
        v-if="item.remindEmpty"
        class="err-remind"
      >
        <span v-if="!formDataInner[item.name]">{{ $t(item.message) }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import i18next from 'i18next';

export default {
  props: {
    lng: {
      type: String,
      default: 'zh',
      required: true,
    },
    formConfig: {
      type: Array,
      required: true,
    },
    formData: {
      type: Object,
      required: true,
    },
  },
  emits: ['click-random'],
  data() {
    return {
      formDataInner: this.formData,
    };
  },
  methods: {
    selectValue(e) {
      e && e.currentTarget.select();
    },
    isItemVisible(item) {
      if (!item.visibleName) {
        // 不限制
        return true;
      }
      return item.visibleValue !== undefined
        ? this.formDataInner[item.visibleName] === item.visibleValue
        : this.formDataInner[item.visibleName];
    },
    isItemEnable(item) {
      if (!item.enableName) {
        // 不限制
        return true;
      }
      return item.enableValue !== undefined
        ? this.formDataInner[item.enableName] === item.enableValue
        : this.formDataInner[item.enableName];
    },
    getSwitchValue(item, isActive) {
      const obj = item.options ? item.options[isActive ? 'active' : 'inactive'] : null;
      return obj ? obj.value : !!isActive;
    },
    getSwitchText(item, isActive) {
      const obj = item.options ? item.options[isActive ? 'active' : 'inactive'] : null;
      return obj && obj.label ? i18next.t(obj.label) : '';
    },
    getOptionLabel(item, index) {
      const obj = item.options ? item.options[this.roomConfig[item.name]] : this.roomConfig[item.name];
      return i18next.t(obj.label);
    },
  },
};
</script>
