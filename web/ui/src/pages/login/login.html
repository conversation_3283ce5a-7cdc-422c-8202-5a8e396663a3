<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="/static/livelogo.png" />
    <title>低代码互动课堂LCIC_在线教育_职业教育_直播大班（公开课）_互动小班_圆桌会议_研讨会_素质教育</title>
    <meta name="description" content="腾讯云实时互动-教育版（原低代码互动课堂）是一款低代码开发产品，集成音视频连麦、互动白板、屏幕共享、云端录制和直播等核心功能，提供1v1教学、小班互动、直播大班课、圆桌会议（研讨会）、拍摄直播、伪直播、课件直播、录屏直播、AI课堂等丰富的直播教学场景。产品降低了在线教育平台的90%开发工作，帮助教育、医疗、金融等行业快速搭建稳定可靠的在线直播系统。">
    <meta name="keywords" content="互动课堂，在线教育，职业教育，实时互动教学方案，实时音视频教育解决方案，1v1课堂，AI课堂，互动白板，企业直播，医疗直播，教育直播">
    
    <!-- 补充Open Graph协议增强社交媒体分享 -->
    <meta property="og:title" content="低代码互动课堂LCIC - 腾讯云实时互动教育解决方案">
    <meta property="og:description" content="腾讯云低代码互动课堂，快速搭建在线教育直播系统，支持1v1教学/小班课/大班课/研讨会等多种教学场景">
    <meta property="og:image" content="/static/livelogo.png">
    <!-- 引入vue -->
    <script src="static/libs/vue/vue_2.7.10.js"></script>
    <!-- 引入组件库 -->
    <script src="static/libs/element_ui/element_ui_2.5.4.js"></script>
    <script src="static/libs/element_ui/locale/zh-CN.js"></script>
    <script src="static/libs/element_ui/locale/zh-TW.js"></script>
    <script src="static/libs/element_ui/locale/en.js"></script>
    <script src="static/libs/element_ui/locale/ko.js"></script>
    <script src="static/libs/element_ui/locale/ja.js"></script>
    <link rel="stylesheet" href="static/libs/element_ui/index.css">
    <!-- 引入webview -->
    <script type="text/javascript" src="static/libs/jweixin/jweixin_1.6.0.js"></script>
    <!-- 字体不打包到css -->
    <style>
      @font-face {
        font-family: 'TencentSans';
        src: url('static/assets/font/TencentSans-W7.ttf') format('woff');
      }
    </style>
    <script>
      /**
       * 加个构建版本
       **/
      window.__TCIC_UI_BUILD_VERSION = '{TCIC_UI_BUILD_VERSION}';
      console.log('TCIC_UI_BUILD_VERSION', window.__TCIC_UI_BUILD_VERSION);
    </script>
</head>

<body>
    <div id="loading" style="display: none;">
        <div class="loader"></div>
    </div>
    <!-- 新登录页面 -->
    <div id="app"></div>
</body>
</html>
