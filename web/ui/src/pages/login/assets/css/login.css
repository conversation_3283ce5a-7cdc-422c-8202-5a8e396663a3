html,
body {
  margin: 0;
}

body {
  background-color: #14181d;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 14px;
}
a, a:active {
  text-decoration: none;
  color: #fff;
}

select {
  height: 100%;
  flex: 1;
}
/* todo 调试*/
#app1 {
  display: none;
}

/*在Chrome下移除input[number]的上下箭头*/
.numberInput::-webkit-outer-spin-button,
.numberInput::-webkit-inner-spin-button {
  margin: 0;
  -webkit-appearance: none !important;
}

.numberInput input[type='number']::-webkit-outer-spin-button,
.numberInput input[type='number']::-webkit-inner-spin-button {
  margin: 0;
  -webkit-appearance: none !important;
}

/*在firefox下移除input[number]的上下箭头*/
.numberInput {
  -moz-appearance: textfield;
}

.numberInput input[type='number'] {
  -moz-appearance: textfield;
}

#loading {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #14181d88;
  z-index: 1000;
}

.loader {
  border: 0.5rem solid #f3f3f3;
  /* Light grey */
  border-top: 8px solid #3498db;
  /* Blue */
  border-radius: 50%;
  width: 3.75rem;
  height: 3.75rem;
  animation: spin 2s linear infinite;
}
/* 新css of login */
#app {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.main {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
	justify-content: space-between;
  overflow-x: hidden;
}

.init-hide {
  display: none;
}

.main .intro-wrap {
  flex: 1;
  width: 1%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.intro-wrap .intro-pic {
  width: 100%;
  height: 100%;
  max-width: 753px;
  background: url('../img/login-bg.png') center center no-repeat;
  background-size: contain;
}
/* .intro-wrap .intro-pic img {
  width: 100%;
} */

.main .setting-wrap {
  width: 50%;
  min-width: 320px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.setting {
  position: relative;
  width: 100%;
  max-width: 500px;
  min-width: 320px;
  height: 90%;
  max-height: 750px;
  min-height: 100px;
  overflow-y: auto;
  border-radius: 8px;
  box-shadow: 0px 0px 5px #2b4b96;
  z-index: 2;
  background: #1d2233;
}
.setting > .full {
  height: 100%;
  overflow-y: auto;
}
.scroll {
  overflow-y: auto;
}
.scroll::-webkit-scrollbar {
  display: none;
}
.more-env {
  padding: 0 5%;
  display: none;
}
.more-env.active {
  display: block;
}
.env-dot {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: orange;
}

/* 垂直flex */
.flex-column {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
}
.flex-column > div {
  width: 100%;
}

/* svg icon，默认 20x20 */
.svg-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}
.icon-detection {
  width: 20px;
  height: 20px;
  background: url('../img/icon-detection.svg') center center no-repeat;
}
.icon-lang {
  width: 20px;
  height: 20px;
  background: url('../img/icon-lang.svg') center center no-repeat;
}
.icon-create {
  width: 24px;
  height: 24px;
  background: url('../img/icon-create.svg') center center no-repeat;
}
.icon-create-ai {
  width: 24px;
  height: 24px;
  background: url('../img/icon-create-ai.svg') center center no-repeat;
}
.icon-enter {
  width: 24px;
  height: 24px;
  background: url('../img/icon-enter.svg') center center no-repeat;
}
.icon-success {
  width: 32px;
  height: 32px;
  background: url('../img/icon-success.svg') center center no-repeat;
}
.icon-copy {
  width: 24px;
  height: 24px;
  background: url('../img/icon-copy.svg') center center no-repeat;
}
.icon-back {
  width: 16px;
  height: 16px;
  background: url('../img/icon-back.svg') center center no-repeat;
}
.icon-back-phone {
  width: 20px;
  height: 20px;
  background: url('../img/icon-back-phone.svg') center center no-repeat;
}
.icon-right-arrow {
  width: 6px;
  height: 10px;
  background: url('../img/icon-right-arrow.svg') center center no-repeat;
}
.icon-switch {
  width: 44px;
  height: 24px;
  background: url('../img/icon-switch-close.svg') center center no-repeat;
}
.icon-switch.active {
  background: url('../img/icon-switch-open.svg') center center no-repeat;
}

/* switch 切换界面 */
.config {
  box-sizing: border-box;
  text-align: right;
  display: block;
  width: 100%;
  height: 60px;
  padding: 20px 10px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
}
.config .config-item {
  position: relative;
  display: inline-block;
  margin-left: 10px;
  cursor: pointer;
}
.config-item .config-btn {
  position: relative;
  display: flex;
  height: 20px;
  vertical-align: middle;
  color: rgba(255, 255, 255, 0.9);
}
.config-item .icon {
  display: inline-block;
  color: rgba(255, 255, 255, 0.9);
  width: 20px;
  height: 20px;
  margin-right: 6px;
}
.config-item .text {
  display: inline-block;
}
.config-item .config-btn.dropdown-btn {
  padding-right: 20px;
}
.config-item .icon-dropdown {
  position: absolute;
  top: 8px;
  right: 5px;
  width: 0px;
  height: 0px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #fff;
  transition:transform 0.5s;
}
.config-item.active .icon-dropdown {
  transform: rotate(180deg);
}
.config-item .dropdown-content {
  position: absolute;
  display: none;
  top: 28px;
  left: auto;
  right: 0;
  z-index: 5;
  width: 90%;
  height: auto;
  padding: 5px 0;
  overflow-y: auto;
  background: #fff;
  color:#000;
  max-width: 100px;
  box-shadow: 0px 8px 16px 0px rgb(0 0 0 / 20%);
  border-radius: 4px;
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.config-item .dropdown-content::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}
.dropdown-content span {
  cursor: pointer;
  text-align: center;
  width: 100%;
  height: 25px;
  font-size: 14px;
  display: block;
  border-bottom: 1px solid #eee;
}
.dropdown-content span:hover {
  background-color: #ccc;
  color: #007fff;
}
/* .active .dropdown-content span {
  color: #007fff;
} */
.config .active .dropdown-content {
  display: block;
}
.brand {
  box-sizing: border-box;
  width: 100%;
  height: 150px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.brand .brand-logo {
  max-width: 90%;
  margin: 0 auto 10px auto;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.brand .brand-logo .logo-wrap {
  width: 320px;
  height: 64px;
  display: flex;
  flex-direction: row;
}
.brand .brand-logo .logo-wrap .logo-icon {
  width: 52px;
  background: url('../img/icon-logo.svg') center center no-repeat;
  background-size: contain;
  flex-shrink: 0;
}
.brand .brand-logo .logo-wrap .logo-text {
  flex: 1;
  font-family: 'TencentSans';
  font-weight: 700;
  font-size: 35px;
  margin-left: 15px;
  white-space: nowrap;
  display: flex;
  flex-direction: column;
}
.brand .brand-text {
  font-size: 14px;
  line-height: 1.5;
  color:#7E7E7E;
  margin: 0 auto;
}
.brand .brand-text.version {
  font-weight: bold;
}
.brand .brand-text.env {
  color: orange;
}
.room-list-wrap {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  padding: 0 5%;
  flex: 1;
  overflow-y: auto;
}
.room-list-empty {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  cursor: pointer;
}
.room-list {
  width: 100%;
  padding: 0;
  margin: 0;
}
.room-item {
  display: flex;
  justify-content: space-between;
  border-radius: 10px;
  padding: 20px;
  background-color: #2F3443;
  color: #fff;
  font-size: 16px;
  margin-bottom: 8px;
}
.room-item:last-child {
  margin-bottom: 0;
}
.room-item .small {
  font-size: 14px;
}
.room-item .info {
  transform-origin: left;
}
.room-item .op {
  transform-origin: right;
}
.room-item .info > div {
  margin-bottom: 6px;
}
.room-item .info > div:last-child {
  margin-bottom: 0;
}
.room-item .info .info-other .other-item {
  display: inline-flex;
  align-items: center;
  margin-right: 30px;
}
.room-item .info .info-other .other-item:last-child {
  margin-right: 0;
}
.room-item .info .info-other .other-item > span {
  display: inline-block;
}
.room-item .info .info-other .other-item .icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 16px;
  height: 16px;
  margin-right: 8px;
}
.room-item .info .info-other .other-item .icon.icon-room-status {
  background: url('../img/icon-room-status.svg') center center no-repeat;
}
.room-item .info .info-other .other-item .icon.icon-room-status.status-1 {
  background: url('../img/icon-room-status-started.svg') center center no-repeat;
}
.room-item .info .info-other .other-item .txt.status-1 {
  color: #0ABF5B;
}
.room-item .info .info-other .other-item .txt.status-2 {
  color: #969EB4;
}
.room-item .info .info-other .other-item .txt.status-3 {
  color: #969EB4;
}
.room-item .info .info-other .other-item .icon.icon-room-max-mic {
  background: url('../img/icon-room-max-mic.svg') center center no-repeat;
}
.room-item .op {
  display: flex;
  justify-content: center;
  align-items: center;
}
.room-item .op .btn {
  box-sizing: border-box;
  width: 9em;
  height: 3em;
  border: 2px solid #006eff;
  border-radius: 8px;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.room-item .op .btn:hover {
  color: #006eff;
}
.room-item .op .btn.disabled {
  color: #969EB4;
  border-color: #969EB4;
  cursor: auto;
}
.room-item .op .btn.disabled:hover {
  color: #969EB4;
}
.room-item .op .op .btn > span {
  display: inline-block;
}
.room-item .op .btn .btn-full {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.room-item .op .btn .btn-txt {
  height: 22px;
  line-height: 22px;
}
.room-item .op .btn .btn-line {
  height: 16px;
  border-left: 2px solid #eee;
  opacity: 0.2;
  margin: 0 8px;
}
.room-item .op .btn .btn-icon {
  width: 24px;
  height: 24px;
}

.login-button-group {
  box-sizing: border-box;
  position: relative;
  text-align: center;
  padding: 0 5%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.login-button-group.large {
  flex: 1;
  justify-content: center;
  flex-direction: column;
}
.login-button-group.large .login-button {
  width: 60%;
  margin: 10px 0;
}
.login-button-group.small {
  height: 120px;
  justify-content: space-between;
}
.login-button-group.small .login-button {
  width: 48%;
}
.login-button {
  border: 2px solid #006eff;
  background: #181b22;
  color: #fff;
  font-size: 22px;
  height: 3em;
  text-align: center;
  display: inline-flex;
  border-radius: 8px;
  text-decoration: none;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.login-button:hover {
  color: #006eff;
}
.login-button.primary {
  background: linear-gradient(#0a5ef5, #0468fb) ;
}
.login-button.primary:hover {
  color: #fff;
}
.login-button .btn-icon {
  /* vertical-align: bottom; */
  /* line-height: 34px; */
  margin-right: 10px;
  display: flex;
  align-items: center;
  width: 24px;
  height: 24px;
}

/* pc右下角推广链接区域 */
.pc-link-list {
  position: fixed;
  right: 20px;
  bottom: 0px;
  z-index: 20;
}
.pc-link-list .pc-link-item {
  padding: 10px 5px;
  margin-bottom: 20px;
  writing-mode: vertical-lr;
  -webkit-writing-mode: vertical-rl;
  border: 1px solid #f3f3f3;
  border-radius: 16px;
  background-color: #fff;
  color: #1d2233;
}
.pc-link-list .pc-link-item a {
  color: #1d2233;
}
.pc-link-list .pc-link-item.primary {
  background-color: #006cff;
  color: #fff;
}
.pc-link-list .pc-link-item.primary a {
  color: #fff;
}

/* create 创建界面*/
.pc-detail {
  box-sizing: border-box;
  width: 100%;
  padding: 0 5%;
}
.tab-head {
  box-sizing: border-box;
  position: relative;
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #474b58;
  flex-shrink: 0;
}
.tab-head .back {
  position: absolute;
  left: 0;
  cursor: pointer;
}
.tab-head .title {
  font-size: 16px;
  margin: 0;
}
.tab-head .env-dot {
  position: absolute;
  right: 0;
}

.tab-body {
  box-sizing: border-box;
  width: 100%;
  padding: 0;
  margin: 0;
  overflow: hidden;
}
.input-type {
  color: #969FB4;
  font-size: 14px;
  margin: 12px 0;
}
.input-item {
  width: 100%;
  position: relative;
  display: inline-flex;
  margin: 8px 0;
}
.input-item .label {
  flex: 1;
  min-width: 80px;
  max-width: 120px;
  line-height: 30px;
  text-align: left;
  display: inline-block;
}

.input-item .input-area {
  flex:2;
  width: calc(100% - 120px);
  height: 30px;
  line-height: 30px;
  float: right;
  color: #fff;
  position: relative;
}
.input-area > input {
  width: 100%;
  height: 30px;
  float: right;
  color: #fff;
  position: relative;
}
.input-area .radiogroup {
  vertical-align: sub;
}
.input-item input.input-control {
  text-indent: 6px;
  position: relative;
  background: #2f3443;
  color: #fff;
  border-radius: 4px;
  border: none;
  outline: none;
}
.input-item input.input-control:hover {
  outline: 1px solid #0c68f3;
}

.input-item .random {
  position: absolute;
  right: 6px;
  top: 0;
  font-size: 14px;
  color: #0C68f3;
  cursor: pointer;
}

.input-item .random:hover {
  color: #006cff;
}

.input-item .op-wrap {
  position: absolute;
  right: 6px;
  top: 0;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: end;
}

.input-item .op-wrap .op-item {
  color: #0C68f3;
  cursor: pointer;
  margin-left: 6px;
}

.input-item .op-wrap .op-item:hover {
  color: #006cff;
}

.input-item .single-line-text {
  white-space: nowrap;
  overflow-x: auto;
}

.input-item .el-select {
  width: 100%
}

.input-item .el-input {
  width: 100%
}

.input-item .el-input .el-input__inner {
  background-color: #2f3443;
  border: none;
  color: #fff;
}
.input-item .el-input.is-disabled .el-input__inner {
  color: #666;
}

.input-item .el-switch.is-disabled {
  opacity: 0.4;
}
.input-item .el-switch .el-switch__label {
  color: #fff;
}
.input-item .el-switch .el-switch__label.is-active {
  color: #006cff;
}

.input-item .el-radio {
  color: #fff;
  font-weight: 500;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 15px;
}
.input-item .el-radio.is-disabled {
  opacity: 0.4;
}
.input-item .el-radio .el-radio__inner {
  width: 16px;
  height: 16px;
  background: none;
}
.input-item .el-radio.is-checked .el-radio__inner {
  background-color: #006cff;
  border-color: #006cff;
}
.input-item .el-radio .el-radio__inner:after {
  width: 8px;
  height: 8px;
  background-color: #000;
}
.input-item .el-radio .el-radio__input.is-disabled+span.el-radio__label {
  color: #fff;
  cursor: not-allowed;
}

.pc-detail.full .pc-section {
  width: 100%;
  flex: 1;
}
.pc-detail.full .pc-section .tab-body {
  height: 100%;
}
.pc-detail.full .pc-section .tab-body .pc-form-wrap {
  flex: 1;
}
.pc-detail.full .pc-section .tab-body .pc-button-wrap {
  height: 70px;
  flex-direction: row;
  justify-content: space-around;
}
.pc-detail.full .pc-section .tab-body .pc-button-wrap .main-button,.other-button-wrap{
  width: 170px;
}
.pc-detail.full .pc-section .tab-body.tab-success .pc-form-wrap {
  justify-content: center;
}
.pc-detail.full .pc-section .tab-body.tab-success .pc-button-wrap {
  height: 150px;
}
.pc-button-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.pc-button-wrap .main-button {
  width: 100%;
  height: 40px;
  line-height: 40px;
  border-radius: 6px;
  overflow: hidden;
  font-size: 16px;
  text-align: center;
  cursor: pointer;
  background-color: #006cff;
}
.pc-button-wrap .other-button-wrap {
  font-size: 16px;
  height: 40px;
  line-height: 40px;
  border-radius: 6px;
  border: 1px solid #1c66e5;
  overflow: hidden;
  font-size: 16px;
  text-align: center;
  cursor: pointer;
}
.pc-button-wrap .other-button-wrap a {
  color: #fff;
}

.pc-button-wrap .other-button-wrap:hover a {
  color: #006cff;
}

.create-success {
  margin: 30px auto;
  display: flex;
  justify-content: center;
}
.create-success .icon-success {
  width: 32px;
  height: 32px;
}
.create-success .text-success {
  font-size: 30px;
  line-height: 32px;
  color: #fff;
  padding-left: 15px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.room-info {
  width: 90%;
  padding: 0px 5%;
  background: #2F3443;
  text-align: center;
  border-radius: 6px;
  color: #fff;
}
.room-info > p {
  margin: 0 0 10px 0;
}
.room-info .name {
  font-size: 20px;
  text-align: left;
}
.room-info .time {
  font-size: 12px;
}
.room-info .room-id {
  font-size: 12px;
  user-select: text;
}
.room-info .icon-copy {
  margin-left: 8px;
  cursor: pointer;
  display: inline-block;
  height: 24px;
}
/* element ui 修复*/
.el-radio__input.is-checked .el-radio__inner {
  border-color: #006cff;
  background: #006cff;
}
.el-radio__input.is-checked+.el-radio__label {
  color: #006cff;
}
.el-date-editor--datetimerange.el-input, .el-date-editor--datetimerange.el-input__inner {
  width:220px;
  max-width: 100%;
}
.el-radio-button__inner, .el-radio-group {
  vertical-align: sub;
}

.err-remind {
  margin-left: 120px;
  color: red;
  height: auto;
  line-height: 20px;
}

/* pc 效果 end */






/* mobile 效果 start */
#loading {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #14181D88;
  z-index: 1000;
}

.loader {
  border: .5rem solid #f3f3f3;
  /* Light grey */
  border-top: 8px solid #3498db;
  /* Blue */
  border-radius: 50%;
  width: 3.75rem;
  height: 3.75rem;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% {
      transform: rotate(0deg);
  }

  100% {
      transform: rotate(360deg);
  }
}

  .phone-detail {
    width: 100%;
    /* height: 100%; */
    display: none;
  }
  .phone-card {
    background: #2A2D38;
    border-radius: 10px;
    margin: 20px;
    padding: 5px 10px;
  }
  ::placeholder {
    font-size: 14px;
  }
  .phone-detail-main {
    opacity: 1;
    color: rgba(255, 255, 255, 1);
    font-size: 16px;
    font-weight: 700;
    font-family: "SF Pro";
    text-align: center;
    margin-top: 30px;

  }

  .phone-detail-head {
    position: relative;
    display: flex;
    align-items: center;
    font-family: 'PingFang SC';
    font-style: normal;
    font-size: 17px;
    line-height: 22px;
    text-align: center;
    height: 50px;
    color: #D1D9EC;
    margin: 0 20px;
  }
  .phone-detail-head .back {
    position: absolute;
    left: 0;
  }
  .phone-detail-head .title {
    display: inline-block;
    margin: 0 auto;
  }
  .phone-detail-head .env-dot {
    position: absolute;
    right: 0;
  }

  .dialog-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
  }
  .dialog-input {
    height: 2.3rem;
    padding: .4rem;
    border-bottom: 0.5px solid #605c5c
  }
  .dialog-input:last-child {
    border: 0;
  }
  .dialog-input .label {
    font-family: 'PingFang SC';
    font-style: normal;
    font-size: 1rem;
    line-height: 1.375rem;
    color: #DEE7FC;
    width: 7.5rem;
  }
  .class-info .dialog-input .label {
    width: 4rem;
  }
  .class-info.wide-text .dialog-input .label {
    width: 7.5rem;
  }

  .dialog-input input {
    height: 2.25rem;
    padding: 0;
    border: 0px;
    border-radius: .25rem;
    outline: none;
    background: none;
    font-family: 'PingFang SC';
    font-style: normal;
    font-size: 1rem;
    color: #969FB4;
    text-align: right;
  }
  .el-input--prefix .el-input__inner {
    padding-right: 0;
  }
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
    background: rgba(255, 255, 255, 0.03);
    border: .0625rem solid rgba(255, 255, 255, 0.1);
    border-radius: .625rem;
  }
  .phone-demo-class{
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 10%;
    float: left;
    margin-left: 25px;
    margin-bottom: 20px;
  }
  .phone-demo-button {
      width: 45%;
      padding: .75rem .75rem;
      border-radius: .7rem;
      letter-spacing: .0625rem;
      cursor: pointer;
      font-size: 1rem;
      user-select: none;
      text-align: center;
      margin-bottom: 20px;
      background: linear-gradient(315deg, #006EFF 0%, #0C59F2 98.81%);
      box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
      display: flex;
      justify-content: center;
      align-items: center;
      line-height: 2rem;
      text-decoration: none;
  }
  .phone-demo-button .btn-text {
    text-decoration: none;
    cursor: none;
    color: white;
  }
  .role-info-value {
    font-weight: 500;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: #969FB4;
  }
  .role-info-value input {
    text-align: left;
  }
  .el-input__inner {
    /* background: none; */
    /* color: #ffffff; */
    /* opacity: 0.8; */
    /* border: 0.0625rem solid rgba(255, 255, 255, 0.2); */
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: .875rem;
    line-height: 1.375rem;
}
  .enter-role-value {
    position: absolute;
    left: 35%;
  }

  .dialog-input .label {
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: .875rem;
    line-height: 1.375rem;
    opacity: 0.8;
    margin-right: 1.875rem;
  }
  .info-value, .phone-detail-select {
    display: block;
    position: absolute;
    right: 10%;
    color: #959595;
    font-size: 16px;
  }
  .info-value {
    right: 13%;
    overflow: hidden;
    white-space: nowrap;
    max-width: 40%;
    text-overflow: ellipsis;
    color: #969FB4;
  }
  .random {
    margin-left: 17px;
    color: #266FE8;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    cursor: pointer;
  }

  .phone-detail-footer {
    display: none;
    position: fixed;
    bottom: 3%;
    width: 100%;
  }
  .phone-detail-button-list {
      display: flex;
      flex-direction: row;
      align-items: flex-end;
      justify-content: center;
      flex-wrap: wrap;
  }

  .phone-detail-button {
      width: 85%;
      padding: .75rem .75rem;
      border-radius: .5rem;
      letter-spacing: .0625rem;
      cursor: pointer;
      font-size: 1rem;
      user-select: none;
      text-align: center;
      margin-bottom: 20px;
      background: linear-gradient(315deg, #006EFF 0%, #0C59F2 98.81%);
      box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
      display: flex;
      justify-content: center;
      align-items: center;
      line-height: 1.5rem;
  }
  .phone-detail-svg-create {
    margin: 0 5px;
    display: flex;
    align-items: center;
  }

  .phone-detail-icon {
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      color: #FFFFFF;
      opacity: 1;
  }

  .phone-detail-input {
      background: none;
      outline: none;
      border: 0px;
      color: #fff;
      width: 200px;
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      color: #FFFFFF;

  }
  .phone-detail-selector {
    bottom: 0;
    display: block;
    position: fixed;
    width: 100%;
    background: #2c2c2c;
    z-index: 1;
    border-radius: 20px 20px 0px 0px;
    font-size: 17px;
  }
  .phone-detail-mask {
    display: block;
    position: fixed;
    left: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.7);
    opacity: .8;
    width: 100%;
    height: 100%;
  }
  .phone-detail-li {
    background: #2A2D38;
    height: 3rem;
    line-height: 3rem;
    color: #DEE7FC;
    padding: 5px;
    text-align: center;
    border-bottom: 0.5px solid #605c5c
  }
  .phone-detail-li:first-child{
    border-radius: 20px 20px 0px 0px;
  }
  .selector-cancel {
    height: 3.6rem;
    color: #D42E4A;
  }

  .phone-detail-switch {
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      color: #BBD3FB;
  }

  .phone-detail-icon {
      width: 70px;
  }

  .sdk-version {
      margin-top: 20px;
      color: #fff;
      text-align: center;
      font-size: 12px;
      opacity: 0.4;
  }
  .el-input{
    width: auto;

  }
  .active1 {
    display: none;
  }

  .active {
      display: block;
  }

/* phone demo detail */
  .select {
    width: 100%;
  }
  .select .el-input.el-input--suffix {
    width: 100%;
  }

/* 手机端初始状态 */

.phone-demo {display: none;}

.phone-demo-button:hover {
  background-color: #2f7fff;
}


.create{
  padding-top:15px;
}
.intro-footer{
  position: fixed;
  font-size: 12px;
  bottom: 15px;
  z-index: 2;
}
.intro-footer span{
    color: #A3AEC7;
}
.intro-footer a{
  color: #CFD4E6;
}
.intro-footer strong{
  color: #A3AEC7;
  margin: 0px 10px;
}
.intro-footer .group-qr{
  cursor: default;
  position:relative;
}
.intro-footer .qr-img-wrapper{
  width: 160px;
  padding: 5px;
  background: #fff;
  border-radius: 3px;
  box-shadow: 1px 1px 3px rgba(32, 77, 141, 0.03);
  display: none;
  position: absolute;
  bottom: 20px;
  left: -50px;
}
.intro-footer .qr-img-wrapper p{
  margin: 0px;
  color: #4F586B;
  padding-bottom: 5px;
  text-align: center;
}
.intro-footer .qr-img-wrapper p strong{
  color: #4791FF;
  font-weight: 400;
  margin: 0px;
}
.intro-footer .qr-img-wrapper img{
  width: 100%;
}
.group-qr:hover .qr-img-wrapper{
  display: block;
}
.group-qr:hover .qr-img-wrapper img{
  margin: 0px;
}
.intro-footer img{
  vertical-align: bottom;
  margin:0px 5px;
}
.more-info{
  color: #4791FF;
  cursor: pointer;
}
.room-type-wrap{
  border: 2px #4791FF  solid;
}
.room-wrapper{
  padding: 15px 10px;
  display: block;
  border-radius: 4px;
  background: #2f3443;
  border: 2px solid #fff0;
  margin-bottom: 10px;
}
.room-wrapper.active{
  border: 2px solid #4791FF !important;
}
.room-wrapper.active .el-radio__label{
  color: #CFD4E5 !important;
}
.room-wrapper .room-desc{
  font-size: 12px;
  color: #A3AEC7;
  line-height: 1.5em;
  margin-left: 2.5em;
  white-space: initial;
}

.ai-room-create-btn {
  box-sizing: border-box;
  padding: 0 5%;
  margin-top: 10px;
}

.ai-create {
  width: 100%;
}

/* .detail-wrap{
  display: none;
} */

/* 平板版本 */
@media (max-width: 800px) {
  .intro-footer{
    display: none;
  }
  .phone-demo {display: none;}

  body {
    background-color: #1d2233;
  }
  .main .intro-wrap {
    display: none;
  }
  .main .setting-wrap {
    width: 100%;
  }
  .main .pc-link-list {
    display: none;
  }
  .setting {
    width: 100%;
    max-width: 800px;
    height: 100%;
    max-height: 100%;
    overflow-y: auto;
    box-shadow: none;
  }
  .more-env .tab-body {
    margin: 20px 0 100px 0;
  }
}
/* 手机版 */
@media (max-width: 500px) {
  .phone-detail,
  .phone-detail-footer {
    display: block;
  }
  .main .pc-detail, .main .pc-area {
    display: none;
  }
  .login-button {
    font-size: 16px;
  }
  .room-item {
    padding: 16px;
    font-size: 14px;
  }
  .room-item .small {
    font-size: 12px;
  }
  .brand .brand-logo .logo-wrap {
    transform: scale(0.9);
  }
}
/* table 版本 */
@media (min-width: 800px) and (max-width: 980px) {
  .setting {
    max-width: 450px;
    height: 82%;
  }
  .brand {
    height: 120px;
  }
  .login-button {
    font-size: 18px;
  }
  .room-item {
    padding: 16px;
    font-size: 14px;
  }
  .room-item .small {
    font-size: 12px;
  }
}

@media (min-width: 980px) and (max-width: 1280px) {
  .setting {
    max-width: 450px;
  }
  .login-button {
    font-size: 20px;
  }
  .room-item {
    padding: 18px;
    font-size: 15px;
  }
  .room-item .small {
    font-size: 13px;
  }
}

@media screen and (orientation:portrait) {
  /* @media (max-width: 530px) {
    .main {display: none;}
    .phone-demo {display: block;}
  }
  @media (min-width: 530px)  and (max-width: 980px)  {
    .main {display: block;}
    .phone-demo { display: none; }

  } */

}

/** 横屏模式使用手机端央视 */
@media screen and (orientation:landscape) {
  /* .main {
    display: none;
  }
  .phone-demo {
    display: block;
  }
  @media (max-width: 980px) {
    .phone-demo {
      display: block;
    }
    .phone-demo-main {
      position: relative;
    }
    .phone-demo-language {
      margin: 0;
      position: absolute;
      left: 0;
      top: 20px;
    }
    .phone-demo-logo {
      margin-top: 25px;
    }
    .phone-demo-logo,
    .phone-demo-title {
      text-align: left;
      margin-left: 0px;
      width: 150px;
    }
    .phone-demo-logo svg,
    .phone-demo-title svg
    {
      width: 100%;
    }
    .phone-demo-footer {
      margin: 0;
      text-align: center;
      width: auto;
      right: 5%;
      width: 220px;
      top: 25%;
      bottom: 0;
    }
    .phone-demo-button {
      padding: 0.2rem;
    }
  }

  @media (min-width: 980px) {
    .phone-demo {
      display: none;
    }
  } */

}

.room-info .room-info-container {
  margin: auto 5%
}

.room-info .straight-line {
  margin: 14px auto 20px;
  height: 1px;
  border-top: 1px solid #a5afc2;
  opacity: .2
}

.room-info .straight-line.bottom {
  margin: 14px auto
}

.room-info .room-info-container .read-only-form>div {
  margin: 12px auto
}

.room-info .room-info-container .title {
  display: inline-block;
  width: 25%;
  font-size: 14px;
  font-weight: 500;
  text-align: left;
}

.room-info .room-info-container .content {
  display: inline-flex;
  align-items: center;
  width: 72%;
  min-width: 72%;
  font-size: 14px;
  color: #fff;
}

.room-info .room-info-container .class-url span:first-child{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 100%;
}

.room-info .room-info-container .copy-all-info {
  text-align: center;
  margin: 20px auto;
}

.room-info .room-info-container .copy-all-info .copy-all-button {
  font-size: 16px;
  color: #1c66e5;
  cursor: pointer;
  font-weight: 500
}

.describtion-text {
  height: 50%;
  width: calc(100% - 94px)!important;
  font-size: 18px;
  line-height: 30px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.85);
  text-align: center;
  word-break: break-all;
}

.describtion-text > span {
  color: #FFFFFF;
}
