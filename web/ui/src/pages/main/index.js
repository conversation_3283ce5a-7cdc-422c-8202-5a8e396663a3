import { Base64 } from 'js-base64';

function decodeSign(sign) {
  const info = JSON.parse(Base64.decode(sign.split('.')[1]));
  localStorage.setItem('token', sign);
  localStorage.setItem('userid', info.user_id);
  localStorage.setItem(info.user_id, sign);
  if (info.scene) {
    localStorage.setItem('scene', info.scene);
  }
  return {
    classId: info.class_id,
    uid: info.uid,
    cid: info.cid,
    role: info.role,
  };
}

function signLogin(sign) {
  const { classId, cid, uid, role } = decodeSign(sign);
  const paramsMap = new Map();
  const query = window.location.search.substring(1);
  const vars = query.split('&');
  for (let i = 0; i < vars.length; i++) {
    const pair = vars[i].split('=');
    if (pair[0] !== 'sign' && pair[0] !== 'env') {
      paramsMap.set(pair[0], pair[1]);
    }
  }
  // 环境检测(未指定环境具域名断定为非正式环境连接测试后台)
  if (!getQuery('env') && (location.host.startsWith('dev-') || location.host.startsWith('test-'))) {
    if (location.host.startsWith('dev-')) {
      paramsMap.set('env', 'dev');
    } else if (location.host.startsWith('test-')) {
      paramsMap.set('env', 'test');
    }
  } else if (getQuery('env')) {
    paramsMap.set('env', getQuery('env'));
  }

  if (role && role === 3) {
    paramsMap.set('role', 'supervisor');
  }

  // 存储当前页加载时间
  localStorage.setItem('webloadstamp', new Date().getTime());
  localStorage.setItem('originurl', window.location.href);

  let newUrl = `class.html?classid=${classId}&cid=${cid}&uid=${uid}`;
  paramsMap.forEach((value, key) => {
    newUrl += `&${key}=${value}`;
  });
  newUrl += `&webloadstamp=${new Date().getTime()}`;
  console.log('signLogin newUrl', newUrl);

  window.location.replace(newUrl);
}


function login() {
  const query = window.location.search.substring(1);
  const saveParams = ['token', 'uid', 'cid', 'globalrandom'];
  const paramsMap = new Map();
  const vars = query.split('&');
  let classId = '';
  let cid = '';
  let uid = '';
  let userid = '';
  for (let i = 0; i < vars.length; i ++) {
    const pair = vars[i].split('=');
    if (pair[0] === 'classid' || pair[0] === 'cid' || pair[0] === 'uid' || pair[0] === 'userid') {
      switch (pair[0]) {
        case 'classid':
          classId = pair[1];
          break;
        case 'cid':
          cid = pair[1];
          break;
        case 'uid':
          uid = pair[1];
          break;
        case 'userid':
          userid = pair[1];
      }
      localStorage.setItem(pair[0], pair[1]);
    } else if (saveParams.indexOf(pair[0]) !== - 1) {
      localStorage.setItem(pair[0], pair[1]);
    } else {
      paramsMap.set(pair[0], pair[1]);
    }
  }
  // 环境检测(未指定环境具域名断定为非正式环境连接测试后台)
  if (!paramsMap.has('env') && (location.host.startsWith('dev-') || location.host.startsWith('test-'))) {
    if (location.host.startsWith('dev-')) {
      paramsMap.set('env', 'dev');
    } else if (location.host.startsWith('test-')) {
      paramsMap.set('env', 'test');
    }
  }

  // 存储当前页加载时间
  localStorage.setItem('webloadstamp', new Date().getTime());
  localStorage.setItem('originurl', window.location.href);
  localStorage.setItem(userid, localStorage.getItem('token'));

  let newUrl = `class.html?classid=${classId}&cid=${cid}&uid=${uid}&userid=${userid}`;
  paramsMap.forEach((value, key) => {
    newUrl += `&${key}=${value}`;
  });
  newUrl += `&webloadstamp=${new Date().getTime()}`;
  console.log('login newUrl', newUrl);
  window.location.replace(newUrl);
}


function getQuery(n, search) {
  console.log('getQuery');
  const m = (search || window.location.search).match(new RegExp(`(\\?|&)${n}=([^&]*)(&|$)`));
  return !m ? '' : decodeURIComponent(m[2]);
}

if (getQuery('sign')) {
  signLogin(getQuery('sign'));
} else {
  login();
}

