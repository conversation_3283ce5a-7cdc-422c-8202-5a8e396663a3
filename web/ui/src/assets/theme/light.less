.light{
  @import 'base/index.less';
  @import 'base/common.less';
  --primary-color: #EBF3FF;
  --video-bg-color: #F4F7FF;
  --primary-color-light: #e7e7f9; 
  --icon-color: rgba(0,0,0,0.9);
  --btn--secondary--text-color: rgba(0,0,0,0.55);
  --text-color: #2B2C30;
  --pop-bg-color: #fff;
  --pop-text-color: #2B2C30;
  --input-bg-color: #C3CAD833;

  --switch-bg-color: #e3e3e3;
  --switch-border-color: #e3e3e3;

  --ui-box-background: #fff;
  .introduction-discuss-component{
    --bg-color: #FCFDFF;
    --text-color: black;
    --bubble-bg: rgba(182, 188, 201, 0.3);
  }
  .member-list-component{
    --text-color: black;
  }
  .el-slider__button {
    border: 1px solid #006eff;
  }

  .float-im-component{
     --bg-color: #FCFDFF;
     --ui-box-background: #FCFDFF;
  }
  .im-component {
    --color: black;
    --text-color: black;
    --bubble-bg: rgba(182, 188, 201, 0.3);
  }
  .el-tabs{
    --active-color: #006eff;
  }
  .document-drawer{
    --bg-color: #fff;
  }

  .header-pc-component{
    background-color: #fff;
    color: var(--text-color);
  }
  .header-mobile-component.mobile-layout{
    background-color: #fff;
  }
  .el-popover.submenu-popover .header-more-box .header-menu-li{
    border-bottom: 1px solid rgba(0,0,0,0.1);
  }
  .more-popover.el-popper{
    background-color: #fff;
  }
  .el-popper.header-component-popover{
    background-color: #fff;
  }
  .footer-component{
    background-color: #fff;
    background-image: none;
     .filter-blur-bg{
       display: none;
     }
  }
  .el-icon-close:before {
    content: "\e6db"
  }
  .header-close{
    i{
      color: #D5D5D5;
    }
  }
  .el-button--primary.is-disabled,
  .el-button--primary.is-disabled:active,
  .el-button--primary.is-disabled:focus,
  .el-button--primary.is-disabled:hover {
    color: #FFF;
    background-color: rgb(160, 207, 255);
    border-color: rgb(160, 207, 255);
  }
}