/**
  ----------------------------------------------------------------------------------------------------
  - RESET                                                                                      -
  ----------------------------------------------------------------------------------------------------
*/
.el-input.is-disabled .el-input__inner {
  background-color: transparent;
}

@import "text.less";
/**
  ----------------------------------------------------------------------------------------------------
  - 通用组件                                                                                      -
  ----------------------------------------------------------------------------------------------------
*/
@component-header-height: var(--component-header-height, 64px);
@component-header-height-small-screen: var(--component-header-height-small-screen, 40px);
@component-header-dialog-height: var(--component-header-height, 40px);
@component-header-dialog-height-small-screen: var(--component-header-height-small-screen, 30px);
@component-header-title-font-size: var(--component-header-title-font-size, 20px);
@component-header-title-font-size-small-screen: var(--component-header-title-font-size-small-screen, 16px);

@ui-box-title-height: var(--ui-box-title-height, 60px);
// 头部菜单弹出框 - 头部标题统一
.component-header {
  //cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: @component-header-height;
  padding: 0 16px;
  &.dialog {
    height: @component-header-dialog-height;
    padding: 0;
  }

  .header-title {
    font-size: @component-header-title-font-size;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #8A9099;
    line-height: 20px;
  }

  .header-close {
    width: 24px;
    height: 24px;
    border-radius: 3px;

    display: flex;
    align-items: center;
    justify-content: center;
    &.mini {
      width: 20px;
      height: 20px;
    }


    i {
      display: flex;
      opacity: 0.8;
      font-size: 20px;
    }

    &:hover {
      opacity: 1;

      i {
        opacity: 1;
      }
    }

    &:active {
      background-color: #2E68E1;
      opacity: 1;
      color:#fff;
    }
  }
}

.small-screen {
  .component-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 55px;
    padding: 0px 16px;

    &.dialog {
      height: @component-header-dialog-height-small-screen;
      padding: 0;
    }

    .header-title {
      font-size: 14px;
    }
  }
}

.component-close-button {
  width: 32px;
  height: 32px;
  border-radius: 2px;
  background-color: #030910;
  display: flex;
  align-items: center;
  justify-content: center;

  &.mini {
    width: 20px;
    height: 20px;
  }

  i {
    display: flex;
    opacity: 0.8;
    font-size: 20px;
    color:#fff;
  }

  &:hover {
    opacity: 1;

    i {
      opacity: 1;
    }
  }

  &:active {
    background-color: #2E68E1;
    opacity: 1;
    color:#fff;
  }
}

.component-content {
  padding: 0 16px;

  .el-form-item__label {
    font-size: 16px;
    line-height: 50px;
  }
  .el-form-item__content {
    .el-select > .el-input {
      border: none;
      border-radius: 4px;
      background: rgba(#000, 0.15);

      .el-input__inner {
        display: -webkit-box;
        height: 50px;
        overflow: hidden;
        font-size: 16px;
        text-overflow: ellipsis;
        word-break: break-all;
        -webkit-line-clamp: 1; //显示多少行
        -webkit-box-orient: vertical;
      }

      .el-select__caret {
        font-size: 24px;
        font-weight: bold;
      }

      .el-input__suffix {
        right: 16px;
      }
    }
  }
}


.header__button {
  &[disabled] {
    opacity: .5;
  }
}

:focus {
  outline: none;
}

// 工具栏 - 面板模糊背景  ----  父类div需要加样式 position: relative; overflow: hidden; 才不会有背景溢出
.filter-blur-bg {
  position: absolute;
  top: -5px;
  right: -10px;
  bottom: -5px;
  left: -10px;
  background: linear-gradient(141deg, rgba(21, 27, 48, 0.2) 0%, rgba(28, 33, 49, 0.4) 100%);
  filter: blur(10px);
}

.board-back-bg {
  position: relative;
  background: linear-gradient(141deg, rgba(21, 27, 48, 0.7) 0%, rgba(28, 33, 49, 0.9) 100%);
  box-shadow: 0 0 3px 0 rgba(32, 32, 32, 0.4);
  border-radius: 4px;
  border: none;
  overflow: hidden;
}

// 视频/音频 - 进度
.el-slider__runway .el-slider__button-wrapper .el-slider__button {
  width: 16px !important;
  height: 16px !important;
  background: #FFFFFF !important;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.1);
}

// pre样式
pre {
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
  text-align: justify;
  text-justify: inter-ideograph;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "\5FAE\8F6F\96C5\9ED1", Arial, sans-serif;
}

// V1.3.4暂时自适应处理 -- pc
@media screen and(min-width: 1920px) {
  //1920   //2048   //2560   //3840
}

@media screen and(min-width: 1600px) and (max-width: 1919px) {
  .volume-popover,
  .audio-player-component {
    transform: scale(0.95)
  }
}

@media screen and(min-width: 1400px) and (max-width: 1599px) {
  .volume-popover,
  .audio-player-component {
    transform: scale(0.9)
  }
}

@media screen and(min-width: 1360px) and (max-width: 1439px) {
  .volume-popover,
  .audio-player-component {
    transform: scale(0.85)
  }
}

@media screen and(max-width: 1359px) {
  .volume-popover,
  .audio-player-component {
    transform: scale(0.75)
  }
}


// V1.3.4暂时自适应处理 -- 手机&iPad
@media screen and(min-width: 960px) and (max-width: 1199px) {
  //.header-pc-component .header__sub-operation {
  //  flex: auto;
  //}
}

@media screen and(min-width: 768px) and (max-width: 959px) {
  .header-component-popover {
    transform: scale(0.8)
  }
}

@media screen and(min-width: 480px) and (max-width: 767px) {
  .header-component-popover {
    transform: scale(0.7)
  }
}

@media screen and (max-width: 479px) {
  .header-component-popover {
    transform: scale(0.6)
  }
}

@media screen and (max-width: 959px) {
  .setting-component.fixed {
    max-width: 100vw;
  }
}

/* common magic css rules */

.pt10 {
  padding-top: 10px;
}

.pb10 {
  padding-bottom: 10px;
}

.ml10 {
  margin-left: 10px;
}

.mr10 {
  margin-right: 10px;
}

.ml5 {
  margin-left: 5px;
}

.mr5 {
  margin-right: 5px;
}
