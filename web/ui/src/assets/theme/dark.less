.dark {
  @import 'base/index.less';
  @import 'base/common.less';

  @background-color: #14181d;
  @font-color: #fff;
  @primary-color: #006EFF;
  @show-green: #13A449;
  @show-warning: #FA6400;
  @show-gray: #969FB4;

  /* 通用组件 */

  /* Form */

  .el-form-item__content {
  }

  /* Button */
  .el-slider__button {
    border: none;
  }
  .el-button {
    &.is-plain {
      background: transparent;

      &:focus {
        background: transparent;
        border-color: #fff;
        color: #fff;
        opacity: .7;
      }

      &:hover {
        background: transparent;
        border-color: #fff;
        color: #fff;
        opacity: .8;
      }
    }
    &.el-button--mini {
      span {
        vertical-align: text-top;
      }
    }
  }

  /* Input */

  .el-input {
  }


  /* Select */

  .el-select {
    flex: 1;

    .el-input__inner {
      background: transparent;
      border-radius: 0;
      border: 0;
      color: #fff;
    }

    & > .el-input {
      border: 1px solid #999999;
    }

  }


  /* Tooltip */

  .el-tooltip__popper.is-light {
    background: #fff;
    border: 1px solid #fff;
    box-shadow: 2px 2px 6px rgba(0, 0, 0, .3);
  }

  .el-tooltip__popper.is-light[x-placement^=top] .popper__arrow {
    border-top-color: #fff
  }

  .el-tooltip__popper.is-light[x-placement^=top] .popper__arrow::after {
    border-top-color: #fff
  }

  .el-tooltip__popper.is-light[x-placement^=bottom] .popper__arrow {
    border-bottom-color: #fff
  }

  .el-tooltip__popper.is-light[x-placement^=bottom] .popper__arrow::after {
    border-bottom-color: #fff
  }

  .el-tooltip__popper.is-light[x-placement^=left] .popper__arrow {
    border-left-color: #fff
  }

  .el-tooltip__popper.is-light[x-placement^=left] .popper__arrow::after {
    border-left-color: #fff
  }

  .el-tooltip__popper.is-light[x-placement^=right] .popper__arrow {
    border-right-color: #fff
  }

  .el-tooltip__popper.is-light[x-placement^=right] .popper__arrow::after {
    border-right-color: #fff
  }

  .el-tooltip__popper[x-placement^=top] .popper__arrow {
    border-top-color: #222;
  }

  .el-tooltip__popper[x-placement^=bottom] .popper__arrow {
    border-bottom-color: #222
  }

  .el-tooltip__popper[x-placement^=right] .popper__arrow {
    border-right-color: #222;
  }

  .el-tooltip__popper[x-placement^=left] .popper__arrow {
    border-left-color: #222
  }

  .el-tooltip:focus:hover, .el-tooltip:focus:not(.focusing), .el-popover:focus:hover, .el-popover:focus:not(.focusing) {
    outline: 0;
    outline: none;
  }

  /* Popover */

  .el-popper[x-placement^=top] .popper__arrow:after {
    border-top-color: #000;
  }

  .el-popper[x-placement^=bottom] .popper__arrow:after {
    border-bottom-color: #000
  }

  .el-popper[x-placement^=right] .popper__arrow:after {
    border-right-color: #000;
  }

  .el-popper[x-placement^=left] .popper__arrow:after {
    border-left-color: #000
  }

  .el-popper.network-detect-error {
    background-color: #FFE1E1;
    color: #8F2B3D;
  }

  // popper-class 配置  light, 使用白色
  .el-popper.light {
    background: #fff;
    border-color: #fff;

    &[x-placement^=top] {
      .popper__arrow {
        border-top-color: #fff;

        &:after {
          border-top-color: #fff
        }
      }
    }

    &[x-placement^=bottom] {
      .popper__arrow {
        border-bottom-color: #fff;

        &:after {
          border-bottom-color: #fff
        }
      }
    }

    &[x-placement^=right] {
      .popper__arrow {
        border-right-color: #fff;

        &:after {
          border-right-color: #fff
        }
      }
    }

    &[x-placement^=left] {
      .popper__arrow {
        border-left-color: #fff;

        &:after {
          border-left-color: #fff
        }
      }
    }
  }

  .el-popconfirm__main {
    padding-bottom: 10px;
  }


  /* DropDown */

  .el-select-dropdown {
    background: #222;
    color: #fff;
    border: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    
    .el-select-dropdown__item {
      &.select, &.hover, &:hover {
        background: #444;
      }
    }
  }


  // dropdown  配置 popper-class = light, 使用白色
  .el-select-dropdown {
    &.light {
      background: transparent !important;
      border-color: transparent !important;

      .el-select-dropdown__item {
        &.select, &.hover, &:hover {
          background: #fff !important;
          color: #006eff;
        }
      }

      .el-select-dropdown__wrap {
        background: #fff !important;
      }

      &[x-placement^=bottom] {
        .popper__arrow:after {
          border-bottom-color: #fff !important;
        }
      }

      .popper__arrow,
      .popper__arrow:after {
        border-bottom-color: #fff !important;
      }
    }
  }


  .el-message {

    &.el-message--warning {
      color: #0D4C9F;
      background: #E6F1FF;
      border-color: #0D4C9F;

      .el-message__content {
        color: #0D4C9F;
      }
    }

    .el-icon-warning {
      color: #006EFF;

      &:before {
        font-size: 20px;
      }
    }
  }


  .el-scrollbar {
    > .el-scrollbar__bar {
      opacity: 1 !important;
    }
  }


  /* badge */

  .badge {
    .el-badge__content {
      &.is-fixed {
        right: 10px;
        top: 2px;
        font-family: Arial;
        &.is-dot {
          right: 5px;
        }
      }
    }
  }

  /* scrollbar */

  .el-scrollbar__wrap {
    overflow: auto;
  }


  .el-loading-mask {
    max-width: 100vw;
    max-height: 100vh;
  }


  .el-switch__core {
    background: transparent;
  }

  .el-switch.is-checked .el-switch__core {
    border-color: rgba(19, 164, 73, 1);
    background-color: rgba(19, 164, 73, 1)
  }

  /* radio */
  .el-radio {
    &__inner {
      width: 16px;
      height: 16px;
      &:after {
        width: 8px;
        height: 8px;
        background: @primary-color;
      }
    }
    &__input {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      &.is-checked {
        .el-radio__inner {
          width: 20px;
          height: 20px;
          background: #fff;
          border: 2px solid @primary-color;
        }
      }
    }
  }
  .el-icon-close:before {
    content: "\e6db"
  }
  .header-close{
    background-color: #030910;
    i{
      color: #fff;
    }
  }
}
