<template>
  <Box
    class="beauty-config-component"
    @hide="hide"
  >
    <template #title>
      {{ $t('美颜设置') }}
    </template>
    <template #content>
      <div class="beauty-content">
        <div class="beauty-item">
          <span class="beauty-item-name">
            {{ $t('美颜') }}
          </span>
          <el-slider
            v-model="beautyLevel"
            class="beauty-slider"
            :step="1"
            :max="10"
            :show-tooltip="false"
          />
          <span class="beauty-item-value">
            {{ beautyLevel }}
          </span>
        </div>
        <div class="beauty-item">
          <span class="beauty-item-name">
            {{ $t('美白') }}
          </span>
          <el-slider
            v-model="whitenessLevel"
            class="beauty-slider"
            :step="1"
            :max="10"
            :show-tooltip="false"
          />
          <span class="beauty-item-value">
            {{ whitenessLevel }}
          </span>
        </div>
        <div
          v-if="showRuddyConfig"
          class="beauty-item"
        >
          <span class="beauty-item-name">
            {{ $t('红润') }}
          </span>
          <el-slider
            v-model="ruddyLevel"
            class="beauty-slider"
            :step="1"
            :max="10"
            :show-tooltip="false"
          />
          <span class="beauty-item-value">
            {{ ruddyLevel }}
          </span>
        </div>
      </div>
    </template>
  </Box>
</template>


<script>
import Lodash from 'lodash';
import BaseComponent from '@core/BaseComponent';
import Constant from '@/util/Constant';
import Box from '@/component/ui-component/box-component/Box';

export default {
  components: { Box },
  extends: BaseComponent,
  props: {
  },
  data() {
    return {
      beautyLevel: 0,
      whitenessLevel: 0,
      ruddyLevel: 0,
      showRuddyConfig: true,    // 是否显示红润配置
    };
  },
  watch: {
    beautyLevel(val) {
      this.onBeautyConfigUpdate();
    },
    whitenessLevel(val) {
      this.onBeautyConfigUpdate();
    },
    ruddyLevel(val) {
      this.onBeautyConfigUpdate();
    },
  },
  mounted() {
    this.initBeautyConfig();
    // Windows Electron端不支持红润配置，接口文档 https://web.sdk.qcloud.com/trtc/electron/doc/zh-cn/trtc_electron_sdk/TRTCCloud.html#setBeautyStyle
    this.showRuddyConfig = !(TCIC.SDK.instance.isElectron() && TCIC.SDK.instance.isWindows());
  },
  methods: {
    onComponentVisibilityChange(visible) {
      if (visible) this.initBeautyConfig();
    },
    onBeautyConfigUpdate: Lodash.throttle(function () {
      this.saveBeautyConfig();
    }, 500, {
      leading: false,
      trailing: true,
    }),
    initBeautyConfig() {  // 初始化美颜配置
      const beautyConfig = TCIC.SDK.instance.getState(Constant.TStateVideoBeautyConfig, 0);
      this.beautyLevel = beautyConfig & 0xF;
      this.whitenessLevel = (beautyConfig >> 4) & 0xF;
      this.ruddyLevel = (beautyConfig >> 8) & 0xF;
      this.onBeautyConfigUpdate();
    },
    getBeautyConfig() {
      return (this.ruddyLevel << 8) | (this.whitenessLevel << 4) | this.beautyLevel;
    },
    saveBeautyConfig() {
      TCIC.SDK.instance.setState(Constant.TStateVideoBeautyConfig, this.getBeautyConfig());
      TCIC.SDK.instance.setBeautyParam(this.beautyLevel, this.whitenessLevel, this.ruddyLevel);
    },
  },
};
</script>
<style lang="less">
.beauty-config-component {
  position: absolute;
  width: 450px;
  height: 220px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  .beauty-content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 100%;
    width: 100%;
    .beauty-item {
      padding: 0 40px;
      margin-bottom: 20px;
      .beauty-item-name {
        font-size: 14px;
        line-height: 22px;
        color: #ffffff;
        width: 80px;
        display: inline-block;
        text-align: left;
      }
      .beauty-slider {
        display: inline-block;
        width: 240px;
        margin-right: 15px;
        height: 16px;
        padding-top: 3px;
      }
      .beauty-item-value {
        display: inline-block;
        width: 20px;
        font-size: 14px;
        line-height: 22px;
        color: #ffffff;
        opacity: 0.5;
      }
    }
  }
}
</style>
