<template>
  <div class="raw-virtual-background-select-component">
    <div class="virtual-background-content">
      <div
        v-for="(item, index) in [...imgArr]"
        :key="item.name"
        :class="['virtual-background-item', { 'active': curIdx === index }, item.class]"
        :style="item.url ? { backgroundImage: 'url(' + item.url + ')' } : null"
        @click="onSelect(index)"
      >
        <span
          v-if="item.title"
          class="virtual-background-item-title"
        >
          {{ item.title }}
        </span>
        <div
          v-if="showLoading && curIdx === index"
          class="el-loading-spinner"
        >
          <svg
            viewBox="25 25 50 50"
            class="circular"
          ><circle
            cx="50"
            cy="50"
            r="20"
            fill="none"
            class="path"
          /></svg>
        </div>
        <span class="virtual-background-item-name">
          {{ item.name }}
        </span>
      </div>
    </div>
  </div>
</template>


<script>
import BaseComponent from '@core/BaseComponent';
import i18next from 'i18next';

export default {
  extends: BaseComponent,
  props: {},
  data() {
    return {
      imgArr: [
        // 默认背景
        {
          title: i18next.t('无'),
          name: i18next.t('无'),
          disable: true,
          url: '',
          sceneKey: '',
        },
        {
          title: i18next.t('背景虚化'),
          name: i18next.t('背景虚化'),
          class: 'blur',
          url: '',
          sceneKey: 'blur',
        },
        // this.imgArr.push({
        //   name: i18next.t('自定义'),
        //   url: './static/assets/virtual_background.png',
        // });
        // this.imgArr.push({
        //   name: i18next.t('办公室'),
        //   url: './static/assets/office.png',
        // });
        {
          name: i18next.t('教室'),
          url: './static/assets/classroom.png',
          sceneKey: '黑板',
        },
        // {
        //   name: 'Turito black',
        //   url: './static/assets/classroom.png',
        //   sceneKey: 'turito_dark',
        // },
        // {
        //   name: 'Turito white',
        //   url: './static/assets/classroom.png',
        //   sceneKey: 'turito_white',
        // },
        {
          name: i18next.t('图书馆'),
          url: './static/assets/library.png',
          sceneKey: '舒适轻松的会议室',
        },
        {
          name: i18next.t('阅读室'),
          url: './static/assets/readingroom.png',
          sceneKey: '简洁明亮的会议室',
        },
        {
          name: i18next.t('自习室'),
          url: './static/assets/studyroom.png',
          sceneKey: '复古书架',
        },
        // {
        //   title: i18next.t('空白'),
        //   name: i18next.t('空白'),
        //   url: '',
        //   sceneKey: 'empty',
        // },
      ],
      curIdx: 0,
      showLoading: false,
    };
  },
  mounted() {
    const electronSdk = window?.Electron?.beautySdk;
    if (electronSdk?.sceneKeyToLocalImgUrl && electronSdk?.getSceneKeys) {
      const sceneKeyToLocalImgUrl = electronSdk.sceneKeyToLocalImgUrl;
      const keys = electronSdk.getSceneKeys();
      console.log('%c [ keys ]-120', 'font-size:13px; background:pink; color:#bf2c9f;', keys);
      if (typeof sceneKeyToLocalImgUrl === 'function') {
        this.imgArr = [{
          title: i18next.t('无'),
          name: i18next.t('无'),
          disable: true,
          url: '',
          sceneKey: '',
        }];

        for (const key of keys) {
          const fileUrl = sceneKeyToLocalImgUrl(key);
          if (fileUrl) {
            console.log('%c [ fileUrl ]-140', 'font-size:13px; background:pink; color:#bf2c9f;', fileUrl);
            this.imgArr.push({
              title: i18next.t(key),
              name: i18next.t(key),
              url: encodeURI(fileUrl),
              sceneKey: key,
            });
          }
        }
      }
    }


    // 初始化虚拟背景
    // console.debug('virtual background init', this.imgArr[this.curIdx]);
    // if (this.imgArr.length > 0  && this.imgArr[this.curIdx]) {
    // this.onSelect(this.curIdx);
    // }
  },
  methods: {
    async onSelect(index) {
      this.curIdx = index;
      if (this.showLoading) {
        return;
      }
      try {
        this.showLoading = true;
        const imgInfo = this.imgArr[index];
        if (imgInfo) {
          await TCIC.SDK.instance.setVirtualImg(!imgInfo.disable, imgInfo.url, imgInfo.sceneKey);
        } else {
          await TCIC.SDK.instance.setVirtualImg(!imgInfo.disable);
        }
        this.showLoading = false;
      } catch (e) {
        console.error(e);
        this.showLoading = false;
      }
    },
  },
};
</script>
<style lang="less">
.raw-virtual-background-select-component {
  .virtual-background-content {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin-top: -8px;

    .virtual-background-item {
      position: relative;
      display: inline-block;
      width: calc(33.3% - 8px);
      height: 67px;
      margin: 4px;
      border-radius: 4px;
      border: solid 1px transparent;
      background: #353535;
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center;

      &.blur {
        background-image: url('./assets/virtual.png') !important;
      }

      &.active {
        border: solid 2px #4787F0
      }

      &:hover {
        border: solid 1px #96BBF8;

        .virtual-background-item-name {
          opacity: 1;
        }
      }

      .virtual-background-item-title {
        position: absolute;
        display: block;
        font-size: 16px;
        text-align: center;
        top: 50%;
        left: 50%;
        width: 64px;
        transform: translate(-50%, -50%);
      }

      .virtual-background-item-name {
        position: absolute;
        bottom: 0px;
        width: 100%;
        height: 16px;
        background: rgba(0, 0, 0, 0.8);
        text-align: center;
        font-size: 12px;
        opacity: 0;
      }
    }
  }
}
</style>
