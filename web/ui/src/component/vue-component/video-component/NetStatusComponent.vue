<template>
  <div
    v-if="status !== 'break'"
    class="net-state-component"
  >
    <i
      v-for="n in 5"
      :key="n"
      class="network-quality-status"
      :class="[n <= 6 - networkQuality && networkQuality !== 0 ? 'active' : 'inactive', GetNetworkTips(status).icon]"
      :style="{ height: `${n * 20}%` }"
    />
  </div>
  <i
    v-else-if="status === 'break'"
    class="class-netstatus-nosignal"
  />
</template>

<script>
import BaseComponent from '@/component/core/BaseComponent';
import { NetworkMap, GetNetworkTips } from '@/util/NetworkQualityDefinition';

export default {
  name: 'NetstatusComponent',
  components: {},
  extends: BaseComponent,
  props: {
    networkQuality: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
    };
  },
  computed: {
    status() {
      return NetworkMap[this.networkQuality];
    },
  },
  methods: {
    GetNetworkTips(status) {
      return GetNetworkTips(status);
    },
  },
};
</script>
<style lang="less">
.net-state-component {
  -webkit-app-region: no-drag;
  display: block;
  height: 12px;
  margin-right: 6px;
  flex-shrink: 0;

  .network-quality-status {
    width: 2px;
    margin-right: 2px;
    display: inline-block;
    vertical-align: bottom;
  }

  .icon-break {
    position: absolute;
    width: 12px;
    height: 12px;
    background-image: url(./assets/icon-break.svg);
    background-repeat: no-repeat;
    background-size: contain;
    bottom: -2px;
    left: 8px;
  }

  .active {
    background: rgb(39, 190, 76);

    &.poor {
      background: rgb(250, 100, 0);
    }

    &.good {
      background: rgb(39, 190, 76);
    }

    &.break {
      background: #fff;
    }
  }

  .inactive {
    background: rgb(138, 144, 153);
  }
}

.class-netstatus-nosignal {
  position: relative;
  background-position: center;
  background-size: contain !important;
  height: 24px;
  width: 24px;
  margin-bottom: 1px;
  background: url('./assets/ic_signal_break.svg') no-repeat center;
  margin-right: 6px;
  bottom: 1px;
}
</style>
