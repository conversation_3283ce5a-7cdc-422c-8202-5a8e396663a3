<template>
  <div
    ref="component"
    :class="['video-ctrl-component', {
      'mobile': isMobile,
      'is-student': isStudent && isPortrait,
      'portrait-class': isPortraitClass,
    }]"
    @mouseover="onMouseOver"
    @mouseleave="onMouseLeave"
  >
    <i
      v-if="stageStatus != 'hide' && !isMobile"
      :data-user-event="`VideoCtrl-stage_${stageStatus}`"
      :class="['video__icon', 'vid_stage_'+stageStatus, {'hangup' : isLiveClass, 'small-screen': isMobile}]"
      @click="toggleStage"
    />
    <el-tooltip
      placement="bottom"
      effect="dark"
      :disabled="!micStatusTip || (micStatus === 'error' && isAndroid)"
      :content="micStatusTip"
      :hide-after="micStatus==='error' ? 10000: 2000"
      :popper-class="micStatus==='error' ? 'detect-error-tps': ''"
    >
      <div
        class="mobile-wrap"
        :data-user-event="`VideoCtrl-mic_${micStatusReport}`"
        @click="toggleMic"
      >
        <i
          :class="['video__icon', 'vid_mic_'+micStatus, { 'vid_loading': !!loadingMicState, 'vid_error': micStatus === 'error' }]"
        />
      </div>
    </el-tooltip>
    <el-tooltip
      placement="bottom"
      effect="dark"
      :disabled="!cameraStatusTip || (cameraStatus === 'error' && isAndroid)"
      :content="cameraStatusTip"
      :hide-after="cameraStatus === 'error' ? 10000 : 2000"
      :popper-class="cameraStatus === 'error' ? 'detect-error-tps' : ''"
    >
      <div
        class="mobile-wrap"
        :data-user-event="`VideoCtrl-camera_${cameraStatus}`"
        @click="toggleCamera"
      >
        <i
          :class="['video__icon', 'vid_camera_'+cameraStatus, { 'vid_loading': !!loadingCameraState, 'vid_error': cameraStatus === 'error' }]"
        />
      </div>
    </el-tooltip>
    <div
      v-if="isMobile && isSelf"
      class="mobile-wrap"
      data-user-event="VideoCtrl-switchCamera"
      @click="switchCamera"
    >
      <i
        class="video__icon switch_camera"
      />
    </div>
    <i
      v-if="boardStatus !== 'hide'"
      :data-user-event="`VideoCtrl-board_${boardStatus}`"
      :class="['video__icon', 'vid_board_'+boardStatus]"
      @click="toggleBoard"
    />
    <i
      v-if="showReset"
      data-user-event="VideoCtrl-vid_drag_reset"
      class="video__icon vid_drag_reset"
      @click="resetDrag"
    />
    <i
      v-if="videoWallStatus !== 'hide' && !isSmallScreen"
      :data-user-event="`VideoCtrl-vid_videowall${videoWallStatus}`"
      :class="['video__icon', 'vid_videowall', {'active': videoWallStatus === 'on'}]"
      @click="toggleVideoWall"
    />
    <div
      v-if="isMobile && isSelf"
      class="mobile-wrap"
      data-user-event="VideoCtrl-rotate90"
      @click="rotate90"
    >
      <i
        class="video__icon video_rotate"
      />
    </div>
  </div>
</template>


<script>
import i18next from 'i18next';
import Constant from '@/util/Constant';
import Util from '@util/Util';
import DetectUtil from '@vueComponent/device-detect-component/DetectUtil';
import { VideoCtrlBaseComponent } from './VideoCtrlBase';
import './VideoCtrlIcon.less';

export default {
  extends: VideoCtrlBaseComponent,
  data() {
    return {
      micStatus: 'close',
      cameraStatus: 'close',
      boardStatus: 'hide',
      stageStatus: 'hide',
      videoWallStatus: 'hide',
      showReset: false,
      isVideoHover: false,
      isCtrlHover: false,
      innerToggleMic: null,
      innerToggleCamera: null,
      innerToggleBoard: null,
      innerToggleStage: null,
      innerResetDrag: null,
      innerToggleVideoWall: null,
      innerHoverTimer: null,
      isShow: true,
      isLiveClass: false,
      isMobile: false,
      cameras: [],
      camera: '',
      isTeacher: false,
      isStudent: false,
      isPortrait: false,
      isPortraitClass: TCIC.SDK.instance.isPortraitClass(),
      userId: '',
      isSelf: false,
      isAndroid: false,
    };
  },
  computed: {
    micStatusReport() {
      let status = this.micStatus;
      if (!this.isSelf) {
        return status;
      }
      if (status === 'error') {
        status = 'error';
      } else {
        status = status === 'close' ? 'open' : 'close';
      }
      return status;
    },
  },
  watch: {
    micStatus(val) {
      console.warn('change mic status', val);
      this.$EventBus.$emit('mic-status-change', val);
    },
  },
  mounted() {
    this.isMobile = TCIC.SDK.instance.isMobile();
    this.isTeacher = TCIC.SDK.instance.isTeacher();
    this.isStudent = TCIC.SDK.instance.isStudent();
    this.isAndroid = TCIC.SDK.instance.isAndroid();
    this.userId = this.$el.parentNode.getAttribute('label');
    this.isSelf = this.userId === TCIC.SDK.instance.getUserId();
    TCIC.SDK.instance.subscribeState(Constant.TStateVideoCtrlUserId, (userId) => {
      if (this.isShow) {
        // 有新的悬浮窗显示时，隐藏自己
        if (userId !== this.userId) {
          this.hideCtrlComponent();
        }
      }
    });
    // todo
    this.isLiveClass = TCIC.SDK.instance.isLiveClass();
    if (this.isMobile) {
      this.getCameras();
    }
    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.isPortrait = orientation === TCIC.TDeviceOrientation.Portrait;
    });
    this.initLoadingEventListener(this.userId, 'VideoCtrl');
  },
  beforeDestroy() {
    document.removeEventListener('click', this.hideCtrlComponent);
  },
  methods: {
    toggleMic() {
      if (this.micStatus === 'disable') {
        return;
      }
      if (this.innerToggleMic) {
        this.innerToggleMic(this.micStatus === 'open' ? 'close' : 'open');
      }
    },
    rotate90() {
      if (this.innerRotate) {
        this.innerRotate();
      }
    },
    toggleCamera() {
      if (this.cameraStatus === 'disable') {
        return;
      }
      if (this.innerToggleCamera) {
        this.innerToggleCamera(this.cameraStatus === 'open' ? 'close' : 'open');
      }
    },
    toggleBoard() {
      if (this.innerToggleBoard) {
        this.innerToggleBoard();
      }
    },
    toggleStage() {
      if (this.innerToggleStage) {
        this.innerToggleStage();
      }
    },
    toggleVideoWall() {
      if (this.innerToggleVideoWall) {
        this.innerToggleVideoWall();
      }
    },
    resetDrag() {
      if (this.innerResetDrag) {
        this.innerResetDrag();
      }
    },
    // 设置消息处理回调
    setToggleMicListener(listener) {
      this.innerToggleMic = listener;
    },
    setToggleCameraListener(listener) {
      this.innerToggleCamera = listener;
    },
    setToggleBoardListener(listener) {
      this.innerToggleBoard = listener;
    },
    setToggleStageListener(listener) {
      this.innerToggleStage = listener;
    },
    setResetDragListener(listener) {
      this.innerResetDrag = listener;
    },
    setToggleVideoWallListener(listener) {
      this.innerToggleVideoWall = listener;
    },
    setInnerRotate(listener) {
      this.innerRotate = listener;
    },
    // 更新状态
    // 修改设备状态的都移到 VideoCtrlBase
    // updateMicStatus(status) {
    //   this.micStatus = status;
    // },
    // updateCameraStatus(status) {
    //   this.cameraStatus = status;
    // },
    updateBoardStatus(status) {
      this.boardStatus = status;
    },
    updateStageStatus(status) {
      this.stageStatus = status;
    },
    updateEnableReset(flag) {
      this.showReset = flag && !Util.isScreenShare() && !this.isSmallScreen;
    },
    updateVideoWallStatus(status) {
      console.warn(`updateVideoWallStatus: ${status}`);
      this.videoWallStatus = status;
    },
    updateVideoHover(flag, force) {
      this.isVideoHover = flag;
      this.checkMouseStatus();
    },
    updateFullScreen(fullscreen) {
      if (this.isFullScreen !== fullscreen) {
        this.isFullScreen = fullscreen;
        // 全屏状态变化隐藏自己
        this.hideCtrlComponent();
      }
    },
    // 鼠标事件
    onMouseOver() {
      // console.log("VideoCtrlComponent::mouse over=>", this.isCtrlHover, this.isVideoHover);
      if (!TCIC.SDK.instance.isMobile()) {  // 移动端忽略
        this.isCtrlHover = true;
        this.checkMouseStatus();
        window?.Electron?.screenShareClickAreaMouseIn?.();
      }
    },
    onMouseLeave() {
      // console.log("VideoCtrlComponent::mouse leave=>", this.isCtrlHover, this.isVideoHover);
      if (!TCIC.SDK.instance.isMobile()) {  // 移动端忽略
        this.isCtrlHover = false;
        this.checkMouseStatus();
        window?.Electron?.screenShareClickAreaMouseOut?.();
      }
    },
    checkMouseStatus() {
      if (!this.isCtrlHover && !this.isVideoHover) {
        // console.log("VideoCtrlComponent::hide=>", this.isCtrlHover, this.isVideoHover);
        this.innerHideSelf();
      } else {
        this.isShow = true;
        // console.log("VideoCtrlComponent::cancel=>", this.isCtrlHover, this.isVideoHover);
        this.clearHoverTimer();
      }
    },
    updateShowVideoWall() {
      const isShowPlayer = TCIC.SDK.instance.getState(Constant.TStateVodPlayerVisible);
      this.showVideoWall = !isShowPlayer && !Util.isScreenShare();
    },
    showInTime(fullScreen) {
      this.isShow = true;
      // 3s后隐藏自己
      this.innerHideSelf();
    },
    // 隐藏自己
    innerHideSelf() {
      const hideTime = TCIC.SDK.instance.isMobile() ? 15000 : 500;
      this.clearHoverTimer();
      // console.log(`VideoCtrlComponent::setTimeout, innerHideSelf after ${hideTime}`);
      this.innerHoverTimer = setTimeout(() => {
        this.hideCtrlComponent();
      }, hideTime);
    },
    // 隐藏自己
    hideCtrlComponent() {
      // console.log(`VideoCtrlComponent::hideCtrlComponent, isShow ${this.isShow}`);
      this.clearHoverTimer();
      this.isShow = false;
      TCIC.SDK.instance.updateComponent(this.$el.parentNode.tagName.toLowerCase(), {
        display: 'none',
      }, this.userId).then(() => {
        const videoShade = document.getElementById(`video_shade_${this.userId}`);
        if (videoShade) {
          videoShade.style.display = 'none';
        }
      });
    },
    clearHoverTimer() {
      if (this.innerHoverTimer) {
        // console.log('VideoCtrlComponent::clearTimeout');
        clearTimeout(this.innerHoverTimer);
        this.innerHoverTimer = null;
      }
    },
    getIsShow() {
      return this.isShow;
    },
    async getCameras(init = false) {
      try {
        if (this.isMobile) {
          const devices = await DetectUtil.getCameras();
          if (Util.isArray(devices) && devices.length) {
            this.cameras = devices;
            if (!this.camera) {
              DetectUtil.getCameraDeviceId().then((deviceId) => {
                this.camera = devices.find(item => item.deviceId === deviceId);
              });
            }
          } else {
            this.cameras = [];
            this.camera = null;
          }
        }
      } catch (e) {
        console.error(e);
      }
    },
    /**
     * 切换摄像头
     */
    async switchCamera() {
      try {
        const newDevice = this.cameras?.find(item => item.deviceId !== this.camera?.deviceId);
        console.log(`switchCamera, ${this.camera?.deviceId}(${this.camera?.deviceName}) -> ${newDevice?.deviceId}(${newDevice?.deviceName})`);
        if (!newDevice) {
          this.$message.error(i18next.t('切换摄像头失败，请重试'));
          return;
        }
        this.camera = newDevice;
        await DetectUtil.switchCamera(this.camera.deviceId);
      } catch (e) {
        console.error('switch camera error', e);
        this.$message.error(i18next.t('切换摄像头失败，请重试'));
      }
    },
  },
};

</script>

<style lang="less">
.video-ctrl-component {
  width: 100%;
  height: 100%;
  display: flex;
  transition: visibility 2s;
  align-items: center;
  justify-content: space-around;
  border-radius: 4px;
  background-color: #34363b;
  opacity: .8;
  cursor: default;

  &.mobile {
    background: none;
    .mobile-wrap{
      background: rgba(0, 0, 0, 0.6);
      border-radius: 30px;
      padding: 5px;
      width: 35px;
      height: 35px;
    }
    &.is-student {
      width: 60%;
      float: right;
      margin-right: 10px;
      &.portrait-class{
        float: none;
        margin: 0 auto;
      }
    }
  }

  // .video__icon 相关移到 VideoCtrlIcon.less，VodFooterCtrl 复用
}
</style>
