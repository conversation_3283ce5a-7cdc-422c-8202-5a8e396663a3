<template>
  <div
    :class="['video-icon-list', { 'is-narrowable': !isFullscreen && hasBoardPermission }]"
  >
    <el-tooltip
      placement="bottom"
      effect="dark"
      :disabled="!micStatusTip"
      :content="micStatusTip"
      :hide-after="2000"
    >
      <i
        :class="[
          `video-icon video-mic-icon icon-${micStatus}`,
          { 'icon-loading': !!loadingMicState },
        ]"
        @click="toggleMic()"
      />
    </el-tooltip>

    <div
      :class="[
        `video-icon video-camera-icon icon-${cameraStatus}`,
        { 'icon-loading': !!loadingCameraState }
      ]"
      @click="changeShowCameraPopup"
    />
    <div
      v-if="showCameraPopup"
      class="video-camera-icon-list"
    >
      <div
        class="video-icon video-switch-camera"
        @click="switchCamera"
      />
      <el-tooltip
        placement="bottom"
        effect="dark"
        :disabled="!cameraStatusTip"
        :content="cameraStatusTip"
        :hide-after="2000"
      >
        <div
          :class="[
            `video-icon video-camera-icon icon-${cameraStatus}`,
            { 'icon-loading': !!loadingCameraState }
          ]"
          @click="toggleCamera()"
        />
      </el-tooltip>
      <div
        v-if="isMobile"
        class="video-icon video-rotate"
        @click="rotate90"
      />
    </div>
  </div>
</template>

<script>
import i18next from 'i18next';
import Util from '@util/Util';
import DetectUtil from '@vueComponent/device-detect-component/DetectUtil';
import Constant from '@/util/Constant';
import { VideoCtrlBaseComponent } from './VideoCtrlBase';

export default {
  extends: VideoCtrlBaseComponent,

  data() {
    return {
      ownerTcicComponentName: '',
      micStatus: 'close',
      cameraStatus: 'close',
      showCameraPopup: false,
      isFrontCamera: true,
      isFullscreen: false,
      hasBoardPermission: false,
      cameras: [],
      camera: '',
      userId: '',
      isMobile: false,
    };
  },
  mounted() {
    this.isMobile = TCIC.SDK.instance.isMobile();
    const ownerTcicComponent = this.getOwnerTCICComponent();
    this.ownerTcicComponentName = ownerTcicComponent?.theTCICComponentName || '';
    // console.log(`[${this.ownerTcicComponentName}][CameraFoot] mounted`);
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Joined_Class, true).then(() => {
      this.getCameras();
    });
    this.userId = TCIC.SDK.instance.getUserId();
    this.addLifecycleTCICStateListener(Constant.TStateFullScreen, (isFullscreen) => {
      this.isFullscreen = isFullscreen;
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Board_Permission, (hasBoardPermission) => {
      this.hasBoardPermission = hasBoardPermission;
    });
    this.initLoadingEventListener(this.userId, `${this.ownerTcicComponentName}-CameraFoot`);

    this.$EventBus.$on('self-mic-ctrl-status', this.updateMicStatus);
    this.$EventBus.$on('self-camera-ctrl-status', this.updateCameraStatus);
  },
  beforeDestroy() {
    // console.log(`[${this.ownerTcicComponentName}][CameraFoot] beforeDestroy`);
    this.$EventBus.$off('self-mic-ctrl-status', this.updateMicStatus);
    this.$EventBus.$off('self-camera-ctrl-status', this.updateCameraStatus);
  },
  methods: {
    // 更新状态
    // 修改设备状态的都移到 VideoCtrlBase
    // updateMicStatus(status) {
    //   this.micStatus = status;
    // },
    // updateCameraStatus(status) {
    //   this.cameraStatus = status;
    // },
    rotate90() {
      TCIC.SDK.instance.rotate90(this.userId, TCIC.TStreamType.Main);
    },
    toggleMic() {
      // || this.micStatus === 'error'
      if (this.micStatus === 'disable') {
        return;
      }
      this.$EventBus.$emit('self-toggle-mic');
    },
    toggleCamera() {
      // || this.cameraStatus === 'error'
      if (this.cameraStatus === 'disable') {
        return;
      }
      this.$EventBus.$emit('self-toggle-camera');
    },
    /**
     * 检测摄像头
     */
    async getCameras() {
      try {
        const devices = await DetectUtil.getCameras();
        console.warn('%c [ devices ]-145', 'font-size:13px; background:pink; color:#bf2c9f;', devices);
        if (Util.isArray(devices) && devices.filter(d => !!d?.deviceId).length) {
          this.cameras = devices;
          console.warn('%c [ cameras ]-148', 'font-size:13px; background:pink; color:#bf2c9f;', devices);
          if (!this.camera) {
            const deviceId = await DetectUtil.getCameraDeviceId();
            this.camera = devices.find(item => item.deviceId === deviceId);
          }
        } else {
          console.warn('%c [ empty cameras ]-155', 'font-size:13px; background:pink; color:#bf2c9f;');
          this.cameras = [];
          this.camera = null;
        }
      } catch (e) {
        console.error('%c [ e ]-159', 'font-size:13px; background:pink; color:#bf2c9f;', e);
      }
    },
    /**
     * 切换摄像头
     */
    async switchCamera() {
      try {
        if (!(this.cameras?.length)) {
          await this.getCameras();
        }
        console.warn('%c [ this.cameras ]-166', 'font-size:13px; background:pink; color:#bf2c9f;', this.cameras, this.camera);
        const newDevice = this.cameras?.find(item => item.deviceId !== this.camera?.deviceId);
        console.warn(`switchCamera, ${this.camera?.deviceId}(${this.camera?.deviceName}) -> ${newDevice?.deviceId}(${newDevice?.deviceName})`);
        if (!newDevice) {
          this.$message.error(i18next.t('摄像头切换失败'));
          return;
        }
        this.camera = newDevice;
        await DetectUtil.switchCamera(this.camera.deviceId);
      } catch (e) {
        console.error('switch camera error', e);
        this.$message.error(i18next.t('切换摄像头失败，请重试'));
      }
    },
    changeShowCameraPopup() {
      this.showCameraPopup = !this.showCameraPopup;
    },
  },
};
</script>

<style lang="less">

.video-icon-list {
  padding: 10px;
  width: 30%;
  height: 115%;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: rgba(120, 120, 122, 0.5);
  border-radius: 20px;
  pointer-events: auto !important;

  .video-icon {
    pointer-events: auto !important;
    background-position: 50%;
    width: 26px;
    height: 26px;
    background-size: 100%;
    background-repeat: no-repeat;
  }

  .video-camera-icon-list {
    position: absolute;
    bottom: 60px;
    background: rgb(89, 87, 87, 50%);
    width: 40px;
    border-radius: 23px;
    padding: 7px 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
  }

  .video-mic-icon {
    background-image: url('./assets/video-ctrl/mobile/mic-close.svg');
    &.icon-open {
      background-image: url('./assets/video-ctrl/mobile/mic-open.svg');
    }
  }
  .video-camera-icon {
    background-image: url('./assets/video-ctrl/mobile/camera-close.svg');
    &.icon-open {
      background-image: url('./assets/video-ctrl/mobile/camera-open.svg');
    }
  }
  .video-switch-camera {
    background-image: url('./assets/video-ctrl/mobile/switch-camera.svg');
  }
  .video-rotate {
    background-image: url('./assets/video-ctrl/mobile/rotate90.svg');
  }
  .hang-up-icon {
    background-image: url('./assets/video-ctrl/mobile/hang-up.svg');
  }
  .icon-loading {
    background-image: url('./assets/icon_loading.gif');
  }
  .icon-error {
    opacity: .5;
  }
  &.quick-im {
    width: 110px;
    height: 100%;
    position: absolute;
    right: 20px;
    border-radius: 8px;
    background-color: rgba(120, 120, 122, 0.5);
    pointer-events: auto;

    &.is-board-full {
      right: 40px;
    }

    .video-camera-icon-list {
      position: absolute;
      bottom: 55px;
      background: rgba(120, 120, 122, 0.5);
      width: 35px;
      padding: 7px 0;
      border-radius: 10px;
    }
  }

  @media screen and (max-width: 690px) {
    &.quick-im.is-narrowable {
      width: 100px;
      .hang-up-icon {
        display: none;
      }
    }
  }
}
</style>
