<svg width="112" height="112" viewBox="0 0 112 112" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_3068_909)">
<path d="M28.3955 75.5837C28.8423 74.0316 30.2623 72.9627 31.8774 72.9627H81.1842C82.7993 72.9627 84.2193 74.0316 84.6661 75.5837L91.7069 100.041C92.3737 102.357 90.6353 104.667 88.225 104.667H24.8366C22.4263 104.667 20.6879 102.357 21.3547 100.041L28.3955 75.5837Z" fill="#4E5461"/>
</g>
<path d="M28.5301 75.6224C28.9597 74.1304 30.3248 73.1029 31.8774 73.1029H81.1842C82.7368 73.1029 84.1019 74.1304 84.5314 75.6224L91.5722 100.08C92.2132 102.307 90.542 104.527 88.225 104.527H24.8366C22.5195 104.527 20.8484 102.307 21.4894 100.08L28.5301 75.6224Z" stroke="#727A8A" stroke-width="0.280302"/>
<circle cx="56.3044" cy="49.6375" r="47.7826" fill="#22262E"/>
<g filter="url(#filter1_ii_3068_909)">
<circle cx="56.3043" cy="49.6375" r="42.3476" fill="#4E5461"/>
</g>
<circle cx="56.3043" cy="49.6375" r="42.2075" stroke="#727A8A" stroke-width="0.280302"/>
<circle cx="56.3043" cy="49.6375" r="28.7601" fill="#22262E"/>
<g filter="url(#filter2_ii_3068_909)">
<circle cx="56.3043" cy="49.6375" r="22.4193" fill="#4E5461"/>
</g>
<circle cx="56.3043" cy="49.6375" r="22.2792" stroke="#727A8A" stroke-width="0.280302"/>
<circle cx="56.3043" cy="49.6375" r="10.6435" fill="#22262E"/>
<circle cx="83.6956" cy="83.6956" r="28.3043" fill="#22262E"/>
<circle cx="83.3913" cy="83.3913" r="18.2609" stroke="#783939" stroke-width="6.08696"/>
<path d="M71.2174 95.8798L95.8798 71.2174" stroke="#783939" stroke-width="6.08696"/>
<defs>
<filter id="filter0_ii_3068_909" x="20.9307" y="72.4021" width="71.2001" height="32.8253" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.280302" dy="-0.560604"/>
<feGaussianBlur stdDeviation="0.420453"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.447059 0 0 0 0 0.478431 0 0 0 0 0.541176 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3068_909"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.280302" dy="0.560604"/>
<feGaussianBlur stdDeviation="0.420453"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.447059 0 0 0 0 0.478431 0 0 0 0 0.541176 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_3068_909" result="effect2_innerShadow_3068_909"/>
</filter>
<filter id="filter1_ii_3068_909" x="13.6764" y="6.72931" width="85.2559" height="85.8165" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.280302" dy="-0.560604"/>
<feGaussianBlur stdDeviation="0.420453"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.447059 0 0 0 0 0.478431 0 0 0 0 0.541176 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3068_909"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.280302" dy="0.560604"/>
<feGaussianBlur stdDeviation="0.420453"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.447059 0 0 0 0 0.478431 0 0 0 0 0.541176 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_3068_909" result="effect2_innerShadow_3068_909"/>
</filter>
<filter id="filter2_ii_3068_909" x="33.6047" y="26.6576" width="45.3993" height="45.9599" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.280302" dy="-0.560604"/>
<feGaussianBlur stdDeviation="0.420453"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.447059 0 0 0 0 0.478431 0 0 0 0 0.541176 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3068_909"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.280302" dy="0.560604"/>
<feGaussianBlur stdDeviation="0.420453"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.447059 0 0 0 0 0.478431 0 0 0 0 0.541176 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_3068_909" result="effect2_innerShadow_3068_909"/>
</filter>
</defs>
</svg>
