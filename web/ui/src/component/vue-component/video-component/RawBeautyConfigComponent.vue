<template>
  <div class="raw-beauty-config-component">
    <div class="beauty-content">
      <div class="beauty-item">
        <span class="beauty-item-name">
          {{ isWeb ? $t('美白'): $t('美颜') }}
        </span>
        <el-slider
          v-model="beautyLevel"
          class="beauty-slider"
          :step="1"
          :max="isWeb?100:10"
          :show-tooltip="false"
        />
        <span class="beauty-item-value">
          {{ beautyLevel }}
        </span>
      </div>
      <div class="beauty-item">
        <span class="beauty-item-name">
          {{ isWeb ? $t('瘦脸'): $t('美白') }}
        </span>
        <el-slider
          v-model="whitenessLevel"
          class="beauty-slider"
          :step="1"
          :max="isWeb?100:10"
          :show-tooltip="false"
        />
        <span class="beauty-item-value">
          {{ whitenessLevel }}
        </span>
      </div>
      <div
        v-if="showRuddyConfig"
        class="beauty-item"
      >
        <span class="beauty-item-name">
          {{ isWeb ? $t('大眼'): $t('红润') }}
        </span>
        <el-slider
          v-model="ruddyLevel"
          class="beauty-slider"
          :step="1"
          :max="isWeb?100:10"
          :show-tooltip="false"
        />
        <span class="beauty-item-value">
          {{ ruddyLevel }}
        </span>
      </div>
    </div>
  </div>
</template>


<script>
import Lodash from 'lodash';
import BaseComponent from '@core/BaseComponent';
import Constant from '@/util/Constant';

export default {
  extends: BaseComponent,
  props: {
  },
  data() {
    return {
      beautyLevel: 0,
      whitenessLevel: 0,
      ruddyLevel: 0,
      showRuddyConfig: true,    // 是否显示红润配置
      isWeb: false,
    };
  },
  watch: {
    beautyLevel(val) {
      this.onBeautyConfigUpdate();
    },
    whitenessLevel(val) {
      this.onBeautyConfigUpdate();
    },
    ruddyLevel(val) {
      this.onBeautyConfigUpdate();
    },
  },
  mounted() {
    // Windows Electron端不支持红润配置
    this.showRuddyConfig = !(TCIC.SDK.instance.isElectron() && TCIC.SDK.instance.isWindows());
    this.isWeb = !!TCIC.SDK.instance.isWeb();
    this.initBeautyConfig();
  },
  methods: {
    onBeautyConfigUpdate: Lodash.throttle(function () {
      this.saveBeautyConfig();
    }, 100, {
      leading: false,
      trailing: true,
    }),
    initBeautyConfig() {  // 初始化美颜配置
      let beautyConfig = TCIC.SDK.instance.getState(Constant.TStateVideoBeautyConfig, 0);
      if (!this.isWeb) {
        this.beautyLevel = beautyConfig & 0xF;
        this.whitenessLevel = (beautyConfig >> 4) & 0xF;
        this.ruddyLevel = (beautyConfig >> 8) & 0xF;
      } else {
        beautyConfig = beautyConfig || [0, 0, 0];
        this.beautyLevel = beautyConfig[0] || 0;
        this.whitenessLevel = beautyConfig[1] || 0;
        this.ruddyLevel = beautyConfig[2] || 0;
      }
      this.onBeautyConfigUpdate();
    },
    getBeautyConfig() {
      if (this.isWeb) {
        return [this.beautyLevel, this.whitenessLevel, this.ruddyLevel];
      }
      return (this.ruddyLevel << 8) | (this.whitenessLevel << 4) | this.beautyLevel;
    },
    saveBeautyConfig() {
      TCIC.SDK.instance.setState(Constant.TStateVideoBeautyConfig, this.getBeautyConfig());
      TCIC.SDK.instance.setBeautyParam(this.beautyLevel, this.whitenessLevel, this.ruddyLevel);
    },
  },
};
</script>
<style lang="less">
.raw-beauty-config-component {
  .beauty-content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 100%;
    width: 100%;

    .beauty-item {
      margin-bottom: 20px;
      display: flex;
      justify-content: stretch;
      width: 100%;

      .beauty-item-name {
        font-size: 14px;
        line-height: 22px;
        color: var(--text-color, #fff);
        display: inline-block;
        text-align: left;
        width: 90px;
      }

      .beauty-slider {
        display: inline-block;
        width: 240px;
        margin: 0 15px;
        height: 16px;
        padding-top: 3px;
        flex: 1;
      }

      .beauty-item-value {
        font-size: 14px;
        line-height: 22px;
        color: var(--text-color, #fff);
        opacity: 0.5;
        width: 32px;
        text-align: right;
      }
    }
  }
}
</style>
