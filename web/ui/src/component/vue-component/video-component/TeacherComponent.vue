<template>
  <div
    ref="component"
    class="teacher-component"
    data-user-event="TeacherVideo-click"
    :style="style"
    @click="handleClick"
    @dblclick="toggleFullScreen"
  >
    <TeacherCollegeCoverComponent
      v-if="isCollegeClass && !isTeacher && !isClassStarted"
      class="teacher-cover"
    />
    <TeacherInteractCoverComponent
      v-if="!isCollegeClass && !isTeacher && canTeacherJoin && !isTeacherHere"
      class="teacher-cover"
    />
    <VideoComponent
      ref="videoControl"
      class="teacher-video"
      :user-role="roleInfo.teacher"
      :user-id="teacherId"
      :is-disable="isDisable"
      :network-quality="netQuality"
      :enable-show-ctrl-component="!disableCtrl"
      :enable-show-mic-border="enableShowMicBorder"
    />
  </div>
</template>


<script>
import Lodash from 'lodash';
import BaseComponent from '@core/BaseComponent';
import Util from '@util/Util';
import VideoComponent from './VideoComponent.vue';
import TeacherCollegeCoverComponent from '@/component/vue-component/video-component/cover/TeacherCollegeCoverComponent';
import TeacherInteractCoverComponent from '@/component/vue-component/video-component/cover/TeacherInteractCoverComponent';

export default {
  components: {
    TeacherCollegeCoverComponent,
    TeacherInteractCoverComponent,
    VideoComponent,
  },
  extends: BaseComponent,
  props: {},
  data() {
    return {
      teacherId: '',
      isDisable: false,
      disableCtrl: false,
      enableShowMicBorder: true,
      isSmallMode: false,
      netQuality: 0, // 网络质量
      isLiveClass: false,  // 是否公开课
      isTeacher: false,
      isAssistant: false,
      isSupervisor: false,
      isTeacherHere: false,
      isCollegeClass: false,    // 是否大教学模式(没有讲师标志)
      classStatus: -1,
      roleInfo: {},
      style: {},
    };
  },

  computed: {
    isClassStarted() {
      return this.classStatus === TCIC.TClassStatus.Already_Start;
    },
    canTeacherJoin() {
      return this.classStatus === TCIC.TClassStatus.Not_Start || this.classStatus === TCIC.TClassStatus.Already_Start;
    },
  },

  mounted() {
    this.roleInfo = TCIC.SDK.instance.getRoleInfo().roleInfo;
    this.makeSureClassJoined(this.onJoinClass);
    this.addLifecycleTCICEventListener(TCIC.TStatisticsEvent.Remote_Network_Statistics, this.networkStatisticsHandler);
    this.addLifecycleTCICEventListener(TCIC.TStatisticsEvent.Network_Statistics, this.networkStatisticsHandler);
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Permission_Update, this.onPermissionUpdate);
    this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, status => this.classStatus = status);
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start)
      .then(() => {
        // !正常情况是课前老师约课，学生进入课程不会出现teacherId为空;由于大教学测试直接用web学生端创建课程导致此bug
        if (this.teacherId === '' && !this.isTeacher) {
          this.onJoinClass();
        }
      });
  },
  beforeDestroy() {
  },
  methods: {
    hideRadius() {
      this.$refs.videoControl.showBoardRadius = false;
    },
    handleClick: Lodash.throttle(function () {
      // 1v1课堂小窗口切换成大窗口（类似于微信视频聊天）
      if (TCIC.SDK.instance.isOneOnOneClass() &&  this.disableCtrl) {
        this.$EventBus.$emit('toggle-video-size', { name: 'teacher-component', label: this.userId });
      }
    }, 500, {
      leading: true,
      trailing: false,
    }),
    toggleFullScreen() {
      if (TCIC.SDK.instance.isVideoOnlyClass()) {
        this.$EventBus.$emit('video-fullscreen', this.teacherId);
      }
    },
    onJoinClass() {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      this.teacherId = classInfo.teacherId;
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      this.isSupervisor = TCIC.SDK.instance.isSupervisor();
      this.isDisable = Util.getBooleanParams('disableInnerTeacher');
      this.isLiveClass = TCIC.SDK.instance.isLiveClass();
      this.isCollegeClass = TCIC.SDK.instance.isCollegeClass();

      const permissionList = TCIC.SDK.instance.getPermissionList();
      if (permissionList.length > 0) {
        this.onPermissionUpdate(permissionList);
      }

      if (this.isLiveClass && this.teacherId !== TCIC.SDK.instance.getUserId()) {
        // 手机上才处理：公开课学生端, 横屏时隐藏video__status
        if (!TCIC.SDK.instance.isPad()) {
          this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
            this.enableSmallMode(orientation === TCIC.TDeviceOrientation.Landscape);
          });
        }
      }
    },
    onPermissionUpdate(permissionList) {
      TCIC.SDK.instance.promiseState(TCIC.TMainState.Joined_Class, true).then(() => {
        const teacherPermission = permissionList.find(permission => permission.userId === this.teacherId);
        this.isTeacherHere = !!teacherPermission;
      });
    },
    setControlDirect(direct) {
      this.$refs.videoControl.updateControl(direct);
    },
    setDbListener(listener) {
      this.$refs.videoControl.setDbListener(listener);
    },
    onLeaveVideoWrap(flag) {
      if (this.isTeacher || this.isAssistant || this.isSupervisor) {  // 是老师才展示复位按钮
        this.$refs.videoControl.updateShowReset(flag);
      }
      // 设置用户视频清晰度
      TCIC.SDK.instance.setVideoEncodeQuality(this.teacherId, flag);
    },
    toggleScreenCapture() {
      this.$refs.videoControl.toggleScreenShare();
    },
    disableScreenShare() {
      this.$refs.videoControl.disableScreenShare();
    },
    // 视频暂停/恢复
    onPauseRender() {
      return this.$refs.videoControl.onPauseRender();
    },
    onResumeRender() {
      return this.$refs.videoControl.onResumeRender();
    },
    onResetRender() {
      return this.$refs.videoControl.onResetRender();
    },
    // 设置缩小模式
    enableSmallMode(flag) {
      this.isSmallMode = flag;
      this.$refs.videoControl.enableSmallMode(flag);
    },
    networkStatisticsHandler(netStatus) {
      const targetUserId = netStatus?.userId;
      // 本人
      if (this.teacherId === TCIC.SDK.instance.getUserId() && !targetUserId) {
        this.translateQualityToStatus(netStatus.networkQuality);
      } else {
        // 公开课或大教学不需要显示网络状态
        if (TCIC.SDK.instance.isLiveClass() || TCIC.SDK.instance.isCollegeClass()) {
          return;
        }
        // 非本人
        if (targetUserId && this.teacherId === targetUserId) {
          this.translateQualityToStatus(netStatus.networkQuality);
        }
        // netStatus.remoteStatisticsArray.forEach((item) => {
        //   if (this.teacherId === item.userId) {
        //     this.translateQualityToStatus(item.networkQuality);
        //   }
        // });
      }
    },
    translateQualityToStatus(networkQuality) {
      if (networkQuality >= 3) {
        this.netQuality = networkQuality;
      } else {
        this.netQuality = 0;
      }
    },
    setFooterCtrlCom(com) {
      // 公开课pc/pad设置VodFooterCtrlComponent为视频控制栏
      this.$refs.videoControl.setFooterCtrlCom(com);
    },
    videoVueInstance() {
      return this.$refs.videoControl;
    },
  },
};

</script>

<style lang="less">
.teacher-component {
  width: 100%;
  height: 100%;

  i.video-teacher {
    position: absolute;
    bottom: 3px;
    left: 2px;
    padding: 4px 3px;
    font-size: 12px;
    border-radius: 2px;
    color: #fff;
    background-image: linear-gradient(153deg, #00A7FF, 0%, #006EFF 95%);
    transform: scale(0.7);
  }

  .teacher-video {
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 1;
  }

  .teacher-cover {
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 2;
    background: black;
  }
}
</style>
