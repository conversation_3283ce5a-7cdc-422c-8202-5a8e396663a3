<template>
  <div
    ref="component"
    class="footer-vod-ctrl-component"
  >
    <i
      :class="['video__icon', 'vid_mic_'+micStatus]"
      @click="toggleMic"
    />
    <span style="width:10px;" />
    <i
      :class="['video__icon', 'vid_camera_'+cameraStatus]"
      @click="toggleCamera"
    />
  </div>
</template>

<script>
import BaseComponent from '@core/BaseComponent';
import './VideoCtrlIcon.less';

export default {
  name: 'VodFooterCtrlComponent',
  extends: BaseComponent,
  data() {
    return {
      micStatus: 'close',
      cameraStatus: 'close',
      innerToggleMic: null,
      innerToggleCamera: null,
      isShow: true,
    };
  },

  mounted() {
    // 监听加入课堂事件
    this.makeSureClassJoined(this.onJoinClass);
  },
  beforeDestroy() {

  },
  methods: {
    toggleMic() {
      if (this.innerToggleMic) {
        this.innerToggleMic();
      }
    },
    toggleCamera() {
      if (this.innerToggleCamera) {
        this.innerToggleCamera();
      }
    },
    toggleBoard() {
    },
    toggleStage() {
    },
    resetDrag() {
    },
    hideCtrlComponent() {
    },
    // 设置消息处理回调
    setToggleMicListener(listener) {
      this.innerToggleMic = listener;
    },
    setToggleCameraListener(listener) {
      this.innerToggleCamera = listener;
    },
    setToggleBoardListener(listener) {
    },
    setToggleStageListener(listener) {
    },
    setToggleVideoWallListener(listener) {
    },
    setResetDragListener(listener) {
    },
    // 更新状态
    updateMicStatus(status) {
      this.micStatus = status;
    },
    updateCameraStatus(status) {
      this.cameraStatus = status;
    },
    updateBoardStatus(status) {
    },
    updateStageStatus(status) {
    },
    updateVideoWallStatus(status) {
    },
    updateEnableReset(flag) {
    },
    updateVideoHover(flag, force) {
    },
    onJoinClass() {
    },
  },
};
</script>
<style lang="less">
.footer-vod-ctrl-component {
  width: 100%;
  height: 100%;
  display: flex;
  transition: visibility 2s;
  align-items: center;
  justify-content: center;
  cursor: default;

  .video__icon {
    width: 28px;
    height: 28px;
    cursor: pointer;
  }
}
</style>
