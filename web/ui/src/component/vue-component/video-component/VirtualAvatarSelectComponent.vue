<template>
  <div class="raw-virtual-background-select-component">
    <div class="virtual-background-content">
      <div
        v-for="(item, index) in imgArr"
        :key="index"
        :class="['virtual-background-item', { 'active': curIdx === index }, item.class]"
        :style="item.icon ? { backgroundImage: 'url(' + item.icon + ')' }: null"
        @click="onSelect(index)"
      >
        <span
          v-if="item.title"
          class="virtual-background-item-title"
        >
          {{ item.title }}
        </span>
        <div
          v-if="showLoading && curIdx === index"
          class="el-loading-spinner"
        >
          <svg
            viewBox="25 25 50 50"
            class="circular"
          ><circle
            cx="50"
            cy="50"
            r="20"
            fill="none"
            class="path"
          /></svg>
        </div>
        <span class="virtual-background-item-name">
          {{ item.name }}
        </span>
      </div>
    </div>
  </div>
</template>


<script>
import BaseComponent from '@core/BaseComponent';
import i18next from 'i18next';

export default {
  extends: BaseComponent,
  props: {},
  data() {
    return {
      imgArr: [],
      curIdx: 0,
      showLoading: false,
    };
  },
  mounted() {
    // 默认背景
    this.imgArr.push({
      title: i18next.t('无'),
      name: i18next.t('无'),
      flag: true,
      url: '',
    });
    this.imgArr.push({
      name: i18next.t('男性'),
      effectId: 'A5A78182F8FE4567',
      icon: './static/assets/avatar-man-1.png',
      url: './static/assets/avatar-bg1.jpeg',
    });
    this.imgArr.push({
      name: i18next.t('女性'),
      effectId: '4525E182F8DD2BDA',
      icon: './static/assets/avatar-woman-1.png',
      url: './static/assets/avatar-bg1.jpeg',
    });
  },
  methods: {
    async onSelect(index) {
      this.curIdx = index;
      const imgInfo = this.imgArr[index];
      if (this.showLoading) {
        return;
      }
      this.showLoading = true;
      if (imgInfo) { // 图片需要执行加载背景资源
        try {
          await TCIC.SDK.instance.setAvatar(imgInfo.effectId, imgInfo.url);
          this.showLoading = false;
        } catch (e) {
          console.error(e);
          this.showLoading = false;
        }
      }
    },
  },
};
</script>

