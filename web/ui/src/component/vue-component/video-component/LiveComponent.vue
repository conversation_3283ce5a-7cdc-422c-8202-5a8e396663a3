<template>
  <div
    ref="component"
    class="live-component"
  >
    <div
      ref="video"
      data-dom-name="live-video-dom"
      class="live-video"
    />
    <div
      v-if="needIM"
      ref="mixFrame"
      class="mix-frame"
    >
      <div
        ref="imFrame"
        class="mix-frame-im introduction-discuss-component client-black"
      >
        <Discuss ref="im" />
      </div>
    </div>
  </div>
</template>

<script>
import Constant from '@/util/Constant';
import BaseComponent from '@core/BaseComponent';
import Discuss from '../introduction-discuss-component/Discuss';

export default {
  components: {
    Discuss,
  },
  extends: BaseComponent,

  data() {
    const rightSideWidth = 286; // 三分布局右侧栏宽度
    const mixFrameConfig = {
      total: {
        width: 1920,
        height: 1080,
      },
      im: {
        width: 480,
        height: 810,
      },
    };
    return {
      sequence: 0,
      isClassStarted: false,    // 是否开启上课
      viewInited: false,    // 组件高宽初始化完成
      needRender: false,    // 需要渲染
      remoteVideoUser: 'mix', // 混流
      remoteVideoStreamType: TCIC.TTrtcVideoStreamType.Big,
      needIM: false,
      mixFrameConfig,
      imNormalSize: {
        width: rightSideWidth,
        height: rightSideWidth * mixFrameConfig.im.height / mixFrameConfig.im.width,
      },
    };
  },
  computed: {
  },

  mounted() {
    this.makeSureClassJoined(() => {
      // 大班课用快直播
      if (TCIC.SDK.instance.isBigRoom()) {
        this.remoteVideoStreamType = TCIC.TTrtcVideoStreamType.QLive;
        if (!this.isSmallScreen) {
          this.needIM = true;
          this.$nextTick(() => {
            // nextTick 才有 this.$refs.mixFrame 等等
            this.updateIMLayout();
            this.$refs.im.notifyVisibilityChange(this.isVisible);
          });
        }
      }
      this.startRenderLive();
    });
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start).then(() => {
      this.isClassStarted = true;
    });
    this.addLifecycleTCICStateListener(Constant.TStateRecordMode, (recordMode) => {
      this.isRecordMode = recordMode;
    });
    const resizeObserver = new ResizeObserver((entries) => {
      this.onBoundingClientRectChange(entries[0].contentRect);
    });
    resizeObserver.observe(this.$el);
  },

  beforeDestroy() {
    TCIC.SDK.instance.unbindRemoteVideoDom(this.remoteVideoUser, this.remoteVideoStreamType, this.sequence);
  },

  methods: {
    onBoundingClientRectChange(rect) {
      console.info(`LiveComponent::onBoundingClientRectChange=>${JSON.stringify(rect)}, needRender ${this.needRender}, viewInited ${this.viewInited}`);
      if (rect && rect.width > 0 && rect.height > 0) {
        if (!this.viewInited && this.needRender) {
          this.$nextTick(() => {
            this.sequence = TCIC.SDK.instance.bindRemoteVideoDom(this.remoteVideoUser, this.remoteVideoStreamType, this.$refs.video, true);
          });
        } else {
          // this.onResize();
        }
        this.viewInited = true;
        this.updateIMLayout();
      } else {
        this.viewInited = false;
      }
    },
    onResize() {
      console.info(`LiveComponent::onResize=>needRender ${this.needRender}, viewInited ${this.viewInited}`);
      if (this.viewInited && this.needRender) {    // 公开课尺寸变更时需要重新拉流
        TCIC.SDK.instance.unbindRemoteVideoDom(this.remoteVideoUser, this.remoteVideoStreamTypeTCIC.TTrtcVideoStreamType.Big, this.sequence);
        this.$nextTick(() => {
          this.sequence = TCIC.SDK.instance.bindRemoteVideoDom(this.remoteVideoUser, this.remoteVideoStreamType, this.$refs.video, true);
        });
      }
    },
    startRenderLive() {
      this.needRender = true;
      console.info(`LiveComponent::startRenderLive=>viewInited ${this.viewInited}`);
      if (this.viewInited) {
        this.sequence = TCIC.SDK.instance.bindRemoteVideoDom(this.remoteVideoUser, this.remoteVideoStreamType, this.$refs.video, true);
      }
    },
    stopRenderLive() {
      this.needRender = false;
      console.info(`LiveComponent::stopRenderLive=>viewInited ${this.viewInited}`);
      TCIC.SDK.instance.unbindRemoteVideoDom(this.remoteVideoUser, this.remoteVideoStreamType, this.sequence);
    },
    updateIMLayout() {
      if (!this.viewInited || !this.needIM || !this.$refs.mixFrame) {
        return;
      }
      const compWidth = this.$el.clientWidth;
      const compHeight = this.$el.clientHeight;
      // console.info(`updateIMLayout comp ${compWidth} ${compHeight}`);
      if (!compWidth || !compHeight) {
        return;
      }
      let mixWidth = 0;
      let mixHeight = 0;
      if (compWidth * 9 / 16 > compHeight) {
        mixWidth = compHeight * 16 / 9;
        mixHeight = compHeight;
      } else {
        mixWidth = compWidth;
        mixHeight = mixWidth * 9 / 16;
      }
      // console.info(`updateIMLayout mixFrame ${mixWidth} ${mixHeight}`);
      this.$refs.mixFrame.style = `width:${mixWidth}px; height:${mixHeight}px; left:${(compWidth - mixWidth) / 2}px; top: ${(compHeight - mixHeight) / 2}px;`;

      const imShowWidth = mixWidth * this.mixFrameConfig.im.width / this.mixFrameConfig.total.width;
      if (imShowWidth > this.imNormalSize.width) {
        // 放大，直接改 width/height
        const imShowHeight = imShowWidth * this.imNormalSize.height / this.imNormalSize.width;
        this.$refs.imFrame.style = `width:${imShowWidth}px; height:${imShowHeight}px; transform: scale(1);`;
      } else {
        // 缩小，用 scale
        const imScale = imShowWidth / this.imNormalSize.width;
        this.$refs.imFrame.style = `width:${this.imNormalSize.width}px; height:${this.imNormalSize.height}px; transform: scale(${imScale});`;
      }
    },
  },
};
</script>

<style lang="less">
@import '../introduction-discuss-component/IntroductionDiscussPC.less';
@import '../introduction-discuss-component/IntroductionDiscussMobile.less';

.live-component {
  position: relative;
  width: 100%;
  height: 100%;

  .live-video {
    width: 100%;
    height: 100%;
    background: var(--primary-color, #14181D);

    .vcp-player {
      width: 100% !important;
      height: 100% !important;
      video {
        width: 100% !important;
        height: 100% !important;
      }
    }
    pointer-events: none;
  }

  .teacher-cover {
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 2;
  }

  .mix-frame {
    pointer-events: none;
    position: absolute;
    width: 0;
    height: 0;
    // border: 1px solid #366EF4;

    // 根据混流布局调整
    .mix-frame-im {
      pointer-events: initial;
      position: absolute;
      width: 0;
      height: 0;
      right: 0;
      bottom: 0;
      transform-origin: bottom right;
      overflow: hidden;
      // border: 1px solid #366EF4;
    }
  }
}


</style>
