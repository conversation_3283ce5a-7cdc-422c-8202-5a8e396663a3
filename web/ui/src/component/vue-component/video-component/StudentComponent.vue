<template>
  <div
    :class="['student-component', {'small-screen': isSmallScreen, 'one-on-one': isOneOnOneClass}]"
    data-user-event="StudentVideo-click"
    :style="style"
    @dblclick="toggleFullScreen"
    @click="handleClick"
  >
    <StudentInteractCoverComponent
      v-if="!isSmallMode && !isCollegeClass && !isLiveClass"
      class="student-cover"
      :user-id="userId"
    />
    <VideoComponent
      ref="videoControl"
      :class="{ 'student-video': true, 'small-screen': isSmallScreen }"
      :user-role="showAssistantIcon ? $t(roleInfo.assistant) : ''"
      :user-id="userId"
      :is-disable="isDisable"
      :network-quality="netQuality"
      :enable-show-ctrl-component="enableShowCtrlComponent"
      :enable-show-mic-border="enableShowMicBorder"
    />
  </div>
</template>


<script>
import Lodash from 'lodash';
import BaseComponent from '@core/BaseComponent';
import Util from '@util/Util';
import VideoComponent from './VideoComponent.vue';
import StudentInteractCoverComponent from '@/component/vue-component/video-component/cover/StudentInteractCoverComponent';

export default {
  components: {
    StudentInteractCoverComponent,
    VideoComponent,
  },
  extends: BaseComponent,
  props: {},
  data() {
    return {
      userId: '',
      isTeacher: false,
      enableShowMicBorder: true,
      isAssistant: false,
      isSupervisor: false,
      showAssistantIcon: false, // 助教展示标识
      isDisable: false,
      isSmallMode: false,       // 是否缩小模式(缩小模式下仅展示视频，不显示奖杯)
      netQuality: 0, // 网络质量
      isCollegeClass: false,    // 是否大教学模式(没有奖杯)
      isLiveClass: false,    // 是否直播课(没有奖杯)
      roleInfo: null,
      isOneOnOneClass: false,
      disableCtrl: false,
      isVideoOnlyClass: false,
      style: {},
    };
  },
  computed: {
    enableShowCtrlComponent() {
      if (this.disableCtrl) {
        return false;
      }
      return !this.isSmallScreen || this.isTeacher || this.isAssistant || this.isVideoOnlyClass;
    },
  },

  mounted() {
    this.roleInfo = TCIC.SDK.instance.getRoleInfo().roleInfo;
    this.userId = this.$el.parentNode.attributes.label.value;
    this.isDisable = Util.getBooleanParams('disableInnerStudent');
    this.isVideoOnlyClass = TCIC.SDK.instance.isVideoOnlyClass();
    // 监听加入课堂事件
    this.makeSureClassJoined(this.onJoinClass);

    this.addLifecycleTCICEventListener(TCIC.TStatisticsEvent.Remote_Network_Statistics, this.networkStatisticsHandler);
    this.addLifecycleTCICEventListener(TCIC.TStatisticsEvent.Network_Statistics, this.networkStatisticsHandler);
    // this.translateQualityToStatus(5);
    // 保证componentsMap有组件key.
    const dom = TCIC.SDK.instance.getComponent('student-component', this.userId);
  },
  beforeDestroy() {
  },
  methods: {
    hideRadius() {
      this.$refs.videoControl.showBoardRadius = false;
    },
    handleClick: Lodash.throttle(function () {
      // 1v1课堂小窗口切换成大窗口（类似于微信视频聊天）
      if (this.isOneOnOneClass &&  this.disableCtrl) {
        this.$EventBus.$emit('toggle-video-size', { name: 'student-component', label: this.userId });
      }
    }, 500, {
      leading: true,
      trailing: false,
    }),
    toggleFullScreen() {
      if (TCIC.SDK.instance.isVideoOnlyClass()) {
        this.$EventBus.$emit('video-fullscreen', this.userId);
      }
    },
    onJoinClass() {
      this.showAssistantIcon = TCIC.SDK.instance.getClassInfo().assistants.includes(this.userId);
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      this.isSupervisor = TCIC.SDK.instance.isSupervisor();
      this.isCollegeClass = TCIC.SDK.instance.isCollegeClass();
      this.isLiveClass = TCIC.SDK.instance.isLiveClass();
      this.isCoTeachingClass = TCIC.SDK.instance.isCoTeachingClass();
      this.isOneOnOneClass = TCIC.SDK.instance.isOneOnOneClass();
    },
    setControlDirect(direct) {
      this.$refs.videoControl.updateControl(direct);
    },
    setDbListener(listener) {
      this.$refs.videoControl.setDbListener(listener);
    },
    // 是否在脱离视频栏
    onLeaveVideoWrap(flag) {
      if (this.isTeacher || this.isAssistant || this.isSupervisor) {   // 是老师则展示复位按钮
        this.$refs.videoControl.updateShowReset(flag);
      }
      // 设置用户视频清晰度
      TCIC.SDK.instance.setVideoEncodeQuality(this.userId, flag);
    },
    // 视频暂停/恢复
    onPauseRender() {
      return this.$refs.videoControl.onPauseRender();
    },
    onResumeRender() {
      return this.$refs.videoControl.onResumeRender();
    },
    onResetRender() {
      return this.$refs.videoControl.onResetRender();
    },
    // 设置缩小模式
    enableSmallMode(flag) {
      this.isSmallMode = flag;
      this.$refs.videoControl.enableSmallMode(flag);
    },
    // 操作麦克风
    enableMic(flag) {
      return this.$refs.videoControl.enableMic(flag);
    },
    // 操作摄像头
    enableCamera(flag) {
      return this.$refs.videoControl.enableCamera(flag);
    },
    toggleScreenCapture() {
      this.$refs.videoControl.toggleScreenShare();
    },
    // 打开屏幕分享
    enableScreenShare() {
      return this.$refs.videoControl.enableScreenShare();
    },
    // 操作摄像头
    disableScreenShare() {
      this.$refs.videoControl.disableScreenShare();
    },
    networkStatisticsHandler(netStatus) {
      // 公开课不需要显示网络状态
      if (TCIC.SDK.instance.isLiveClass()) {
        return;
      }
      const targetUserId = netStatus?.userId;
      // 本人
      if (this.userId === TCIC.SDK.instance.getUserId() && !targetUserId) {
        this.translateQualityToStatus(netStatus.networkQuality);
      } else {
        // 其他用户
        if (targetUserId && this.userId === targetUserId) {
          this.translateQualityToStatus(netStatus.networkQuality);
        }
        // netStatus.remoteStatisticsArray.forEach((item) => {
        //   if (this.userId === item.userId) {
        //     this.translateQualityToStatus(item.networkQuality);
        //   }
        // });
      }
    },
    translateQualityToStatus(networkQuality) {
      if (networkQuality >= 3) {
        this.netQuality = networkQuality;
      } else {
        this.netQuality = 0;
      }
    },
  },
};

</script>

<style lang="less">
.student-component {
  width: 100%;
  height: 100%;
  &.small-screen {
    width: auto;
  }
  .one-on-one {
    margin: 0;
  }

  .student-video {
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 1;
  }

  .student-cover {
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 2;
  }

}
</style>
