.video__icon {
  width: 25px;
  height: 25px;
  cursor: pointer;
}

i.vid_mic_open {
  opacity: 1;
  content: url('./assets/video-ctrl/mic_on.png');
}

i.vid_mic_close {
  content: url('./assets/video-ctrl/mic_off.png');
}

i.vid_mic_error {
  content: url('./assets/video-ctrl/mic_error.png');
}

i.vid_mic_disable {
  padding: 3px;
  content: url('./assets/video-ctrl/mic_disable.png');
}

i.vid_camera_open {
  opacity: 1;
  content: url('./assets/video-ctrl/camera_on.png');
}

i.vid_camera_close {
  content: url('./assets/video-ctrl/camera_off.png');
}

i.vid_camera_error {
  content: url('./assets/video-ctrl/camera_error.png');
}

i.vid_camera_disable {
  padding: 3px;
  content: url('./assets/video-ctrl/camera_disable.png');
}

i.vid_stage_on {
  opacity: 1;
  content: url('./assets/video-ctrl/stage_on.png');
  &.hangup {
    content: url('./assets/video-ctrl/hangup_stage.png');
  }
}

i.vid_stage_off {
  content: url('./assets/video-ctrl/stage_off.png');
}

i.vid_videowall {
  opacity: 1;
  content: url('./assets/video-ctrl/ic_videowall_off.png');
  &.active {
    content: url('./assets/video-ctrl/ic_videowall_on.png');
  }
}

i.vid_board_enable {
  opacity: 1;
  content: url('../member-list-component/assets/member-list__board-on.svg');
}

i.vid_board_disable {
  content: url('../member-list-component/assets/member-list__board-off.svg');
}

i.vid_drag_reset {
  content: url('./assets/video-ctrl/drag_reset.png');
}

i.vid_loading {
  content: url('./assets/icon_loading.gif');
}

i.vid_error {
  opacity: .5;
}

.mobile {
  i.vid_mic_open {
    content: url('./assets/video-ctrl/mobile/mic-open.svg');
  }
  i.vid_mic_close, i.vid_mic_error {
    content: url('./assets/video-ctrl/mobile/mic-close.svg');
  }
  i.video_rotate {
    opacity: 1;
    content: url('./assets/video-ctrl/mobile/rotate90.svg');
  }
  i.switch_camera {
    opacity: 1;
    content: url('./assets/video-ctrl/mobile/switch-camera.svg');
  }
  i.vid_camera_open {
    content: url('./assets/video-ctrl/mobile/camera-open.svg');
  }
  i.vid_camera_close, i.vid_camera_error {
    content: url('./assets/video-ctrl/mobile/camera-close.svg');
  }

  i.vid_board_enable {
    opacity: 1;
    content: url('./assets/video-ctrl/mobile/mb-board-on.svg');
  }
  i.vid_board_disable {
    opacity: .6;
    content: url('./assets/video-ctrl/mobile/mb-board-off.svg');
  }

  i.vid_loading {
    content: url('./assets/icon_loading.gif');
  }
  
  i.vid_error {
    opacity: .5;
  }
}