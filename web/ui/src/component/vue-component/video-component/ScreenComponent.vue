<template>
  <div
    :class="['screen-component']"
  >
    <div
      v-show="!isScreenShareOpen"
      ref="preivewShare"
      class="screen-share-preview"
    >
      <video ref="screenPreviewer" />
    </div>
    <div
      :id="'screen' + userId"
      ref="screen"
      class="screen-share-dom"
    >
      <div
        v-if="isScreenShareOpen && isWeb"
        class="screen-share-overlay"
      >
        <p>{{ $t('屏幕共享中') }}</p>
        <p class="stop">
          <button
            class="el-button header__button--start el-button--warning"
            @click="disableScreenShare"
          >
            {{ $t('停止共享') }}
          </button>
        </p>
      </div>
    </div>
  </div>
</template>


<script>
import BaseComponent from '@core/BaseComponent';
import i18next from 'i18next';
export default {
  name: 'ScreenComponent',
  extends: BaseComponent,
  props: {},
  data() {
    return {
      isTeacher: false,
      isAssistant: false,
      isScreenShareOpen: false,   // 屏幕分享是否开启
      isSelf: false,
      userId: '',
      isClassStart: false,
      isWeb: false,
      previewStream: null,
    };
  },

  mounted() {
    this.isTeacher = TCIC.SDK.instance.isTeacher();
    this.isAssistant = TCIC.SDK.instance.isAssistant();
    this.isWeb = TCIC.SDK.instance.isWeb();
    const classInfo = TCIC.SDK.instance.getClassInfo();
    if (this.isTeacher) {
      this.userId = classInfo.teacherId;
    }  else  {
      this.userId = TCIC.SDK.instance.getUserId();
    }
    this.isSelf = this.userId === TCIC.SDK.instance.getUserId();
    this.initLayout();
    // 本地流监听屏幕分享状态变化
    this.addLifecycleTCICStateListener(TCIC.TMainState.Screen_Share, (status) => {
      console.log('[ScreenComponent] on Screen_Share', status);
      const isShareOpening = (status < 2); // 0，1 开始状态
      // 通过操作系统提供的结束共享按钮，不会出发toggle ，所以做个状态检测
      if (isShareOpening !== this.isScreenShareOpen) {
        this.isScreenShareOpen = isShareOpening;
        this.setScreenShareLayer();
      }
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Stage_Status, (status) => {
      console.log('[ScreenComponent] on Stage_Status', status);
      // 下台那就关闭屏幕分享
      if (this.isScreenShareOpen && !status) {
        this.disableScreenShare();
      }
    });
    /**
     * 监听是否已上课的状态
     * 触发时机有点奇怪...但是还是会触发，点屏幕共享时才会触发
     */
    this.addLifecycleTCICStateListener(TCIC.TMainState.Joined_TRTC, (status) => {
      console.log('[ScreenComponent]classstatus change::: in Screen', status);

      this.isClassStart = status === true;
      /**
       * 如果已经在预览状态则触发一次分享
       */
      if (this.previewStream) {
        console.log('[ScreenComponent] will start screenShare');
        this.enableScreenShare();
      }
    });

    if (this.isWeb) {
      this.addLifecycleTCICEventListener(TCIC.TStatisticsEvent.Network_Statistics, (status) => {
        const { networkQuality }  = status;
        if (networkQuality === TCIC.TNetworkQuality.Down) {
          if (this.isScreenShareOpen && this.isTeacher) {
            this.handleNetworkDownWhenScreenShare();
          }
        }
      });
    }
  },


  methods: {
    async handleNetworkDownWhenScreenShare() {
      if (this.screenStopMsgBoxOppen) {
        return;
      }
      const stream = TCIC.SDK.instance.getScreenShareStream();
      if (stream) {
        stream.stop();
      }

      if (Notification.permission === 'granted') {
        const notification = new Notification(i18next.t('屏幕共享失败'), {
            body: i18next.t('网络中断，建议退出重新进入'),
            icon: 'https://tcic-prod-1257307760.qcloudclass.com/customcontent/3923193/default_1708934507_default.png',
          });
          notification.onclick = () => {
            window.focus();
            notification.close();
          };
      } else if (Notification.permission !== 'denied') {
        const notification = new Notification(i18next.t('屏幕共享失败'), {
            body: i18next.t('网络中断，建议退出重新进入'),
            icon: 'https://tcic-prod-1257307760.qcloudclass.com/customcontent/3923193/default_1708934507_default.png',
          });
          notification.onclick = () => {
            window.focus();
            notification.close();
          };
      }
      this.screenStopMsgBoxOppen = true;
    },
    // 切换屏幕分享状态
    toggleScreenShare() {
      console.log('[ScreenComponent] toggleScreenShare');
      if (!this.isScreenShareOpen) {
        this.enableScreenShare();
      } else {
        this.disableScreenShare();
      }
    },
    initLayout() {
      if (TCIC.SDK.instance.getComponent('layout-component')) {
        this.$EventBus.$emit('screen-component-ready');
        return;
      }
      const classLayout = TCIC.SDK.instance.getClassLayout();
      const classMainFrame = TCIC.SDK.instance.getClassLayoutMainFrame(classLayout);
      let rectCom;
      if (classMainFrame.board) {
        rectCom = TCIC.SDK.instance.getComponent('board-component');
      } else if (classMainFrame.videoWall) {
        rectCom = TCIC.SDK.instance.getComponent('video-wall-component');
      }
      if (rectCom) {
        const rect = rectCom.getBoundingClientRect();
        const screenLayout = {
          left: `${rect.left}px`,
          top: `${rect.top}px`,
          width: `${rect.width}px`,
          height: `${rect.height}px`,
        };
        // console.log('[ScreenComponent] initLayout, updateComponent screen-component', screenLayout);
        TCIC.SDK.instance.updateComponent('screen-component', screenLayout);
      }
    },
    disableBoardToolBar(disable = true) {
      const btCom = TCIC.SDK.instance.getComponent('board-tool-component');
      // 纯视频模式这个component不存在
      if (btCom && btCom.getVueInstance) {
        btCom.getVueInstance().hideToolbar = disable;
      }
    },
    setScreenShareLayer() {
      console.log('[ScreenComponent] setScreenShareLayer', this.isScreenShareOpen);
      TCIC.SDK.instance.reportLog('setScreenShareLayer', `[ScreenComponent] setScreenShareLayer ${this.isScreenShareOpen}`);
      if (this.isScreenShareOpen) {
        this.show();
      } else {
        this.hide();
      }
    },
    // 关闭屏幕分享
    async disableScreenShare() {
      if (this.isScreenShareOpen) {
        try {
          console.log('[ScreenComponent] stopScreenShare');
          TCIC.SDK.instance.reportLog('stopScreenShare', '[ScreenComponent] disableScreenShare');
          await TCIC.SDK.instance.stopScreenShare();
          this.setScreenShareLayer();
          this.disableBoardToolBar(false);
        } catch (err) {
          console.log('[ScreenComponent] stopScreenShare error', err);
          throw err;
        }
      }
    },
    // 开启屏幕分享
    async enableScreenShare() { // todo 屏幕分享
      // Web端屏幕分享，将采集到的视频渲染到老师视频节点上
      console.log('[ScreenComponent] isScreenShareOpen', this.isScreenShareOpen);
      if (!this.isScreenShareOpen) {
        try {
          console.log('[ScreenComponent] startScreenShare');
          TCIC.SDK.instance.reportLog('startScreenShare', '[ScreenComponent] enableScreenShare');
          /**
           * 如果是web，先展示分享预览窗口
           */
          if (this.isWeb) {
            /**
             * 如果还没上课，可以预览分享画面，已经上课直接分享画面
             */
            console.log('[ScreenComponent] isClassStarted:', this.isClassStart);
            if (!this.isClassStart) {
              if (this.previewStream) {
                return;
              }
              if (navigator.mediaDevices.getDisplayMedia) {
                const stream = await navigator.mediaDevices.getDisplayMedia({ video: true });
                this.previewStream = stream;
                const videoEl = this.$refs.screenPreviewer;
                videoEl.srcObject = stream;
                const vTrack = stream.getVideoTracks();
                if (vTrack.length > 0) {
                  vTrack[0].addEventListener('ended', () => {
                    console.log('[ScreenComponent] isClassStarted: ended');
                    this.previewStream = null;
                    this.disableBoardToolBar(false);
                    this.disableScreenShare();
                    this.hide();
                  });
                }
                videoEl.play();
              }
              this.show();
              /**
               * 预览屏幕分享前，隐藏画笔工具
               */
              this.disableBoardToolBar();
            } else {
              this.disableBoardToolBar(false);
              const opts = {};
              if (this.previewStream) {
                const videoSource = this.previewStream.getVideoTracks();
                if (videoSource.length > 0) {
                  if (videoSource[0].readyState !== 'ended') {
                    opts.videoSource = videoSource[0];
                  }
                }
              }
              console.log('[ScreenComponent][SDK]::K.instance.startScreenShare', opts);
              await TCIC.SDK.instance.startScreenShare(this.$refs.screen, opts);
              this.resetVideoWrap();
              this.setScreenShareLayer();
            }
          } else {
            await TCIC.SDK.instance.startScreenShare(this.$refs.screen);
            this.resetVideoWrap();
            this.setScreenShareLayer();
          }
        } catch (err) {  // 打开失败或者取消后，回退
          console.log('[ScreenComponent] startScreenShare error', err.errorCode, err);
          // throw err;
          if (this.isWeb) {
            /**
             * 不用提示的错误码：
             * 0: 用户取消
             * -2: 超时，web端在用户选择分享窗口前就开始计时，很容易超时，不用提示
             */
            if (err.errorCode === 0 || err.errorCode === -2) {
              return;
            }
            window.showToast(err.errorMsg || i18next.t('打开屏幕共享遇到一些问题'));
            return;
          }
          throw err;
        }
      } else {
        if (this.isCollegeClass) {
          window.showToast(i18next.t('你有正在共享的内容，如有需要，请先停止共享'));
        }
      }
    },
    resetVideoWrap() {
      if (TCIC.SDK.instance.isClassLayoutHasDoc()) {
        const videoWrapComp = TCIC.SDK.instance.getComponent('videowrap-component');
        if (videoWrapComp) {
          const instance = videoWrapComp.getVueInstance();
          instance.resetAllDrag();
        }
      }
    },
  },
};

</script>

<style lang="less">
.screen-component {
  width: 100%;
  height: 100%;
  .screen-share-preview{
    width: 100%;
    height: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    background: rgba(0,0,0,.85);
    transform: translate(-50%,-50%);
    video{
      width: 100%;
      height: 100%;
    }
  }
  .screen-share-dom {
    width: 100%;
    height: 100%;
    .screen-share-overlay {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background: rgba(0,0,0,0.85);
      z-index: 2;
      display: flex;
      align-items: center;
      align-content: center;
      justify-content: center;
      flex-direction: column;
      color: rgba(255,255,255, .75);
      font-size: 20px;
      .stop {
        margin-top: 20px;
        font-size: 16px;
      }
    }
  }
}
</style>
