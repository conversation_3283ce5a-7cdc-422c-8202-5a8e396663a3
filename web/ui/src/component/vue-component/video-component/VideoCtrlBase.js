import i18next from 'i18next';
import BaseComponent from '@core/BaseComponent';
import Constant from '@/util/Constant';

export const VideoCtrlBaseComponent = {
  extends: BaseComponent,
  data() {
    return {
      selfUserId: '',
      ctrlUserId: '',
      ownerCompName: '',
      cameraStatus: 'close',
      micStatus: 'close',
      loadingCameraState: null,
      loadingMicState: null,
      roleInfo: {},
    };
  },
  computed: {
    cameraStatusTip() {
      if (this.cameraStatus === 'disable') {
        return i18next.t('摄像头权限已被{{arg_0}}禁用', { arg_0: this.roleInfo.teacher });
      }
      if (this.cameraStatus === 'error') {
        let tip = i18next.t('设备不可用（损坏、未授权等）');
        const errCode = TCIC.SDK.instance.getState(TCIC.TMainState.Video_Device_Status);
        switch (errCode) {
          case -1001: // 打开失败
            tip = i18next.t('摄像头打开失败，请检查系统设置');
            break;
          case -1002: // 找不到设备
            tip = i18next.t('找不到可用摄像头');
            break;
          case -1003: // 未授权
            tip = i18next.t('摄像头权限未开启，请前往开启');
            break;
          case -1004: // 设备被占用
            tip = i18next.t('摄像头被占用，请关闭其他软件');
            break;
          case 2: // 已关闭
            tip = i18next.t('摄像头已关闭，请开启');
            break;
          default:
            break;
        }
        return tip;
      }
      return '';
    },
    micStatusTip() {
      if (this.micStatus === 'disable') {
        return i18next.t('麦克风权限已被{{arg_0}}禁用', { arg_0: this.roleInfo.teacher });
      }
      if (this.micStatus === 'error') {
        let tip = i18next.t('设备不可用（损坏、未授权等）');
        const errCode = TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Device_Status);
        switch (errCode) {
          case -1001: // 打开失败
            tip = i18next.t('麦克风打开失败，请检查系统设置');
            break;
          case -1002: // 找不到设备
            tip = i18next.t('找不到可用麦克风');
            break;
          case -1003: // 未授权
            tip = i18next.t('麦克风权限未开启，请前往开启');
            break;
          case -1004: // 设备被占用
            tip = i18next.t('麦克风被占用，请关闭其他软件');
            break;
          case 2: // 已关闭
            tip = i18next.t('麦克风已关闭，请开启');
            break;
          default:
            break;
        }
        return tip;
      }
      return '';
    },
  },
  mounted() {
    this.roleInfo = TCIC.SDK.instance.getRoleInfo().roleInfo;
    this.selfUserId = TCIC.SDK.instance.getUserId();
  },
  methods: {
    initLoadingEventListener(ctrlUserId, ownerCompName) {
      this.ctrlUserId = ctrlUserId;
      this.ownerCompName = ownerCompName;
      if (this.ctrlUserId !== this.selfUserId) {
        return;
      }
      console.log(`[${this.ownerCompName}][SelfVideoCtrl] initLoadingEventListener`);

      // 自己才监听这些
      this.addLifecycleTCICEventListener(Constant.TStateLoadingCameraState, ({ userId, type, value }) => {
        if (userId !== this.selfUserId) {
          return;
        }
        if (type === 'loadingOwnCamera') {
          this.loadingCameraState = value?.loading ? value : null;
          if (typeof value.open === 'boolean') {
            const deviceStatus = TCIC.SDK.instance.getState(TCIC.TMainState.Video_Device_Status);
            // eslint-disable-next-line vue/no-mutating-props, no-nested-ternary
            const cameraCtrlStatus = value.open ? 'open' : (
              TCIC.SDK.instance.isDeviceAbnormal(deviceStatus) ? 'error' : 'close'
            );
            this.updateCameraStatus(cameraCtrlStatus, 'loadingOwnCamera end');
          }
        }
      });
      this.addLifecycleTCICEventListener(Constant.TStateLoadingMicState, ({ userId, type, value }) => {
        if (userId !== this.selfUserId) {
          return;
        }
        if (type === 'loadingOwnMic') {
          this.loadingMicState = value?.loading ? value : null;
          if (typeof value.open === 'boolean') {
            const deviceStatus = TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Device_Status);
            // eslint-disable-next-line vue/no-mutating-props, no-nested-ternary
            const micCtrlStatus = value.open ? 'open' : (
              TCIC.SDK.instance.isDeviceAbnormal(deviceStatus) ? 'error' : 'close'
            );
            this.updateMicStatus(micCtrlStatus, 'loadingOwnMic end');
          }
        }
      });
    },
    // 更新状态
    updateCameraStatus(status, reason) {
      if (status === this.cameraStatus) {
        return;
      }
      this.cameraStatus = status;
      if (this.ctrlUserId === this.selfUserId) {
        console.log(`[${this.ownerCompName}][SelfVideoCtrl] updateCameraStatus ${status}, reason ${reason}`);
      }
    },
    updateMicStatus(status, reason) {
      if (status === this.micStatus) {
        return;
      }
      this.micStatus = status;
      if (this.ctrlUserId === this.selfUserId) {
        console.log(`[${this.ownerCompName}][SelfVideoCtrl] updateMicStatus ${status}, reason ${reason}`);
      }
    },
  },
};
