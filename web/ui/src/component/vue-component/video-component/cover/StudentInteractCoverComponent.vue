<template>
  <div class="student-interact-cover">
    <div
      v-if="isShowTrophy"
      :class="['video-trophy', {'available': isTeacher || isAssistant || isSupervisor}]"
      @click.stop.prevent="sendTrophy"
    >
      <img class="video-trophy-icon">
      <span
        class="video-trophy-num"
      >
        {{ trophyNum }}
      </span>
    </div>
    <div :class="['animation-handup__wrap', {'show' : isShowHandUpMovie}]">
      <div class="star-icon" />
      <div class="body-icon" />
      <div class="arm-icon" />
    </div>
  </div>
</template>


<script>
import BaseComponent from '@core/BaseComponent';

export default {
  extends: BaseComponent,
  props: {
    userId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      isTeacher: false,
      isAssistant: false,
      isShowHandUpMovie: false,
      isShowTrophy: false,
      isSupervisor: false,
      animationTask: null,
      trophyTaskId: 'trophy',
      trophyNum: 0,
      isSendingTrophy: 0,
      isCollegeClass: false,    // 是否大教学模式(没有奖杯)
      isLiveClass: false,    // 是否直播课(没有奖杯)
    };
  },

  mounted() {
    // 监听加入课堂事件
    this.makeSureClassJoined(this.onJoinClass);
  },
  beforeDestroy() {
  },
  methods: {
    onJoinClass() {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      this.isSupervisor = TCIC.SDK.instance.isSupervisor();
      this.isCollegeClass = TCIC.SDK.instance.isCollegeClass();
      this.isLiveClass = TCIC.SDK.instance.isLiveClass();
      // 监听奖杯发放事件
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Task_Updated, this.onTaskUpdate);
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Member_Hand_Up, ({ userId }) => {
        if (userId === this.userId) {
          // 首先清空动画
          if (this.animationTask) {
            clearTimeout(this.animationTask);
          }
          this.isShowHandUpMovie = true; // 记录当前举手状态
          // 如果是举手状态，则设置定时隐藏举手动画
          this.animationTask = setTimeout(() => {
            this.isShowHandUpMovie = false;
          }, 6000);
        }
      });
      this.$nextTick(() => {
        this.isShowTrophy = !classInfo.assistants.includes(this.userId);
        this.initTrophyCount();
      });
    },
    sendTrophy() {
      if (this.$hasRole(['teacher', 'assistant', 'supervisor'])) {
        const trophyComponent = TCIC.SDK.instance.getComponent('trophy-component');
        trophyComponent.getVueInstance().distributeWithThrottle(this.userId);
      }
    },
    initTrophyCount() {
      const trophyComponent = TCIC.SDK.instance.getComponent('trophy-component');
      if (trophyComponent) {
        this.trophyNum = trophyComponent.getVueInstance().getCountByUserId(this.userId);
      } else {
        console.warn('trophy-component unloaded');
      }
    },
    onTaskUpdate(taskInfo) {
      if (taskInfo.taskId === this.trophyTaskId) {
        const trophyComponent = TCIC.SDK.instance.getComponent('trophy-component');
        if (trophyComponent) {
          this.$nextTick(() => {
            this.trophyNum = trophyComponent.getVueInstance().getCountByUserId(this.userId);
          });
        }
      }
    },
  },
};

</script>

<style lang="less">
.student-interact-cover {
  width: 100%;
  height: 100%;
  pointer-events: none !important;
  .video-trophy {
    position: absolute;
    top: 6px;
    left: 6px;
    width: 35px;
    height: 24px;
    opacity: .8;

    &.vistor:hover {
      opacity: .8;
    }

    &:hover {
      opacity: 1;
    }

    &.available {
      cursor: pointer;
      pointer-events: auto;
    }

    .video-trophy-icon {
      width: 25px;
      height: 25px;
      background: #222;
      padding: 3px;
      border-radius: 12px;
      vertical-align: middle;
      content: url('../assets/trophy.svg');
    }

    .video-trophy-num {
      display: block;
      position: absolute;
      left: 21px;
      top: 5px;
      background: #222;
      padding: 0 5px 0 2px;
      border-radius: 6px;
      color: #fff;
      white-space: nowrap;
    }
  }

  .animation-handup__wrap {
    pointer-events: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;

    > * {
      pointer-events: none;
    }

    &.show {
      .body-icon {
        animation: handup-video-enter 400ms linear forwards;
      }

      .arm-icon {
        animation: handup-video-enter 400ms linear forwards, handup-video-arm 600ms linear 400ms infinite alternate;
      }

      .star-icon {
        animation: handup-video-star 300ms linear both infinite alternate;
      }
    }

    .body-icon {
      pointer-events: none;
      position: absolute;
      bottom: 0;
      right: 6px;
      width: 41px;
      height: 48px;
      z-index: 99;
      display: block;
      background-size: contain;
      background: url('../assets/handup-animation/ic_body.svg') no-repeat center bottom;
      transform: translateY(100%);
    }

    .arm-icon {
      position: absolute;
      right: 22px;
      bottom: 0px;
      width: 30px;
      height: 50px;
      background-size: contain;
      background: url('../assets/handup-animation/ic_arm.svg') no-repeat left top;
      transform-origin: bottom left;
      transform: translateY(100%);
      opacity: 1;
    }

    .star-icon {
      position: absolute;
      right: 6px;
      bottom: 42px;
      width: 45px;
      height: 27px;
      background: url('../assets/handup-animation/ic_star.svg') no-repeat center center;
      background-size: contain;
      opacity: 0;
    }
  }
}

@keyframes handup-video-star {
  0% {
    opacity: 0.1;
  }
  100% {
    opacity: 1;
  }
}

@keyframes handup-video-enter {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes handup-video-arm {
  0% {
    opacity: 1;
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(-12deg);
  }

  100% {
    opacity: 1;
    transform: rotate(0deg);
  }
}
</style>
