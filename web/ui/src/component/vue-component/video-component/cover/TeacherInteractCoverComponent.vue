<template>
  <div class="teacher-interact-cover">
    <div class="background">
      <IconNoTeacher />
      <div class="text-wrap">
        <span
          ref="text"
          class="text"
        >
          {{ translateTip.teacherOffline }}
        </span>
      </div>
    </div>
  </div>
</template>
<script>
import i18next from 'i18next';
import IconNoTeacher from '../assets/svg-component/no-teacher.svg';
import BaseComponent from '@/component/core/BaseComponent';

export default {
  components: {
    IconNoTeacher,
  },
  extends: BaseComponent,
  data() {
    return {
      roleInfo: {},
    };
  },
  computed: {
    translateTip() {
      return {
        teacherOffline: i18next.t('{{teacher}}即将进入课堂，请稍后', this.roleInfo),
      };
    },
  },
  mounted() {
    const { roleInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    const resizeObserver = new ResizeObserver((entries) => {
      if (!this.$refs.text) {
        return;
      }
      const rectWidth = entries[0].contentRect.width;
      const fontSize = Math.round(rectWidth / 160 * 10); // 原始svg里，图片 160*90，字号 10
      if (fontSize >= 10) {
        this.$refs.text.style = `font-size: ${Math.min(fontSize, 30)}px;`;
      } else {
        this.$refs.text.style = `font-size: 10px; transform: scale(${rectWidth / 160})`;
      }
    });
    resizeObserver.observe(this.$el);
  },
  beforeDestroy() {
  },
  methods: {
  },
};
</script>
<style lang="less">
.light {
  .teacher-interact-cover {
    --icon-color: #848FB7;
    --video-bg-color: #F4F7FF;
  }
}
.teacher-interact-cover {
  position: absolute;
  z-index: 2;
  width: 100%;
  height: 100%;
  .background {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: var(--video-bg-color, #2D354A);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    svg {
      width: 22.5%;
      margin-bottom: 3%;
    }
    .text-wrap {
      width: 96%;
      text-align: center;
      color: var(--text-color, #fff);
      .text {
        display: inline-block;
        width: auto;
        margin: auto;
      }
    }
  }
}
</style>
