<template>
  <div class="vod-player__wrap">
    <!-- 视频播放 - 老师端 -->
    <div
      class="vod-player center vod-player__blur"
    >
      <div class="filter-blur-bg" />
      <button
        v-if="currentStatus === Status.Play"
        @click="pauseVideo"
      >
        <i class="ic_video_pause" />
      </button>
      <button
        v-else
        @click="playVideo"
      >
        <i class="ic_video_play" />
      </button>
      <div
        class="vod-player-progress"
        @mousedown="isMouseUp = false"
        @mouseup="isMouseUp = true"
      >
        <label class="video-time">{{ currentTimeString }}</label>
        <el-slider
          v-model="currentTime"
          :max="duration"
          class="video-slider"
          :show-tooltip="false"
          @change="seekVideo"
        />
        <label class="video-time">{{ durationString }}</label>
      </div>
      <el-popover
        popper-class="board-back-bg volume-popover"
        placement="bottom"
        trigger="click"
      >
        <button
          slot="reference"
          :class="isVolumeActive? 'active':''"
          @click="toggleVolume"
        >
          <i class="ic_video_volume" />
        </button>
        <el-slider
          v-model="currentVolume"
          class="video-slider-vertical"
          :show-tooltip="false"
          vertical
          height="80px"
          @change="setVideoVolume"
        />
      </el-popover>
      <button>
        <i
          :class="!isFullscreen ? 'ppt-tool__board-full' : 'ppt-tool__board-shrink'"
          @click="toggleFullscreen"
        />
      </button>
    </div>
    <div
      class="vod-player vod-close vod-player__blur"
    >
      <button @click="stopVideo">
        <i class="ic_video_close" />
      </button>
    </div>
    <!-- 视频播放 - 学生端 -->
    <div
      v-if="false"
      class="vod-player center vod-player__blur vod-player-student"
    >
      <div class="filter-blur-bg" />
      <div class="vod-player-progress">
        <label class="video-time">{{ currentTimeString }}</label>
        <el-slider
          v-model="currentTime"
          :max="duration"
          class="video-slider"
          :show-tooltip="false"
          disabled
        />
        <label class="video-time">{{ durationString }}</label>
      </div>
    </div>
  </div>
</template>

<script>
import BaseComponent from '@core/BaseComponent';
import Constant from '@/util/Constant';

export default {
  extends: BaseComponent,
  props: {},
  data() {
    return {
      bounds: {
        left: 0,
        top: 0,
        width: 0,
        height: 0,
      },
      isTeacher: false,
      isAssistant: false,
      currentVolume: 20,
      currentTime: 0,
      duration: 0,
      currentTimeString: '00:00',
      durationString: '00:00',
      isFullscreen: false,
      isVolumeActive: false,
      taskId: 'class_vod',
      lastTask: null,
      updateTimer: 0,
      syncInterval: 5000,
      lastSyncTimestamp: 0,
      Status: {
        Stop: 0,
        Play: 1,
        Pause: 2,
        Loaded: 3,
      },
      currentStatus: 0,
      isMouseUp: true,
      liveEventArr: [],
    };
  },
  mounted() {
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Joined_Class, true).then(() => {
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
    });
    // 全屏回调
    this.addLifecycleTCICStateListener(Constant.TStateFullScreen, (isFullscreen) => {
      this.isFullscreen = isFullscreen;
    });
    // 播放进度回调
    this.addLifecycleTCICStateListener(TCIC.TMainState.Vod_Time_Update, (progress) => {
      if (!this.isMouseUp) {
        return;
      }
      this.currentTime = progress.currentTime;
      this.duration = progress.duration;
      this.currentTimeString = this.formatTime(this.currentTime);
      this.durationString = this.formatTime(this.duration);
      if (this.currentTime === this.duration) {
        // 播放完成
        this.currentStatus = this.Status.Stop;
      }
      // 定时同步进度
      const currentTimestamp = TCIC.SDK.instance.getServerTimestamp();
      if (currentTimestamp - this.lastSyncTimestamp > this.syncInterval) {
        if (this.currentStatus !== this.Status.Stop) {
          this.updateTask(this.currentStatus, this.duration, this.currentTime);
        }
        this.lastSyncTimestamp = currentTimestamp;
      }
    }, {
      noEmitWhileSubscribe: true,
      noEmitWhileRegister: true,
    });
    // 播放进度跳转
    this.addLifecycleTCICStateListener(TCIC.TMainState.Vod_Time_Seeked, (progress) => {
      this.currentTime = progress.currentTime;
      this.duration = progress.duration;
      this.currentTimeString = this.formatTime(this.currentTime);
      this.durationString = this.formatTime(this.duration);
      // 定时同步进度
      const currentTimestamp = TCIC.SDK.instance.getServerTimestamp();
      this.updateTask(this.currentStatus, this.duration, this.currentTime);
      this.lastSyncTimestamp = currentTimestamp;
    }, {
      noEmitWhileSubscribe: true,
      noEmitWhileRegister: true,
    });
    // 任务更新
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Task_Updated, this.onTaskUpdate);
    // 添加触屏样式
    if (TCIC.SDK.instance.isMobile()) {
      // HTMLCollection 没有 forEach 方法
      const domList = document.getElementsByClassName('vod-player__blur');
      for (const dom of domList) {
        this.addLiveEventListener(dom, 'touchstart', () => {
          dom.classList.add('active');
        });
        this.addLiveEventListener(dom, 'touchend', () => {
          dom.classList.remove('active');
        });
      }
    }
    // 防止拖动到slider外部区域进度不动的问题
    document.addEventListener('mouseup', () => {
      this.isMouseUp = true;
    });
  },
  beforeDestroy() {
    this.liveEventArr.forEach((info) => {
      info.dom.removeEventListener(info.event, info.func);
    });
  },
  methods: {
    loadVideo(url) {
      this.stopVideo().then(() => {
        const playerComponent = TCIC.SDK.instance.getComponent('vod-player-component');
        const playerElement = playerComponent.getVueInstance().getPlayerElement();
        TCIC.SDK.instance.loadVideo(playerElement, url).then(() => {
          this.currentStatus = this.Status.Loaded;
          TCIC.SDK.instance.setState(Constant.TStateVodPlayerVisible, true);
          this.updateTask(this.currentStatus, this.duration, this.currentTime);
          this.$nextTick(() => {
            this.playVideo();
          });
        });
      });
    },
    addLiveEventListener(dom, event, func) {
      dom.addEventListener(event, func);
      this.liveEventArr.push({
        dom,
        event,
        func,
      });
    },
    playVideo() {
      TCIC.SDK.instance.playVideo().then(() => {
        this.currentStatus = this.Status.Play;
        this.updateTask(this.currentStatus, this.duration, this.currentTime);
      });
    },
    stopVideo() {
      this.currentStatus = this.Status.Stop;
      TCIC.SDK.instance.setState(Constant.TStateVodPlayerVisible, false);
      TCIC.SDK.instance.stopTask(this.taskId)
        .catch((error) => {

        });
      return TCIC.SDK.instance.stopVideo();
    },
    pauseVideo() {
      TCIC.SDK.instance.pauseVideo().then(() => {
        this.currentStatus = this.Status.Pause;
        this.updateTask(this.currentStatus, this.duration, this.currentTime);
      });
    },
    seekVideo(value) {
      TCIC.SDK.instance.seekVideo(value).then(() => {
      });
    },
    setVideoVolume() {
      TCIC.SDK.instance.setVideoVolume(this.currentVolume / 100).then(() => {
      });
    },
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen;
      TCIC.SDK.instance.setState(Constant.TStateFullScreen, this.isFullscreen);
    },
    toggleVolume() {
      this.isVolumeActive = !this.isVolumeActive;
      if (this.isVolumeActive) {
        this.addHideVolumeEvent();
      } else {
        this.removeHideVolumeEvent();
      }
    },
    addHideVolumeEvent() {
      // 点击空白区域，取消声音按钮的高亮状态
      document.addEventListener('mousedown', this.toggleVolume, true);
      document.addEventListener('touchstart', this.toggleVolume, true);
    },
    removeHideVolumeEvent() {
      document.removeEventListener('mousedown', this.toggleVolume, true);
      document.removeEventListener('touchstart', this.toggleVolume, true);
    },
    formatTime(time) {
      // 格式化为 00:00 格式
      const minutes = Math.floor(time / 60);
      const seconds = Math.floor(time % 60);
      const minutesString = minutes > 9 ? `${minutes}` : `0${minutes}`;
      const secondString = seconds > 9 ? `${seconds}` : `0${seconds}`;
      return `${minutesString}:${secondString}`;
    },
    onTaskUpdate(task) {
      TCIC.SDK.instance.promiseState(TCIC.TMainState.Joined_Class, true).then(() => {
        const vodPlay = TCIC.SDK.instance.getState(TCIC.TMainState.Vod_Play, 2);
        const isVodPlayOnElectron = TCIC.SDK.instance.isTeacherOrAssistant()
          && TCIC.SDK.instance.isElectron()
          && (vodPlay < 2);
        if (isVodPlayOnElectron) {  // 正在播放视频课件的人忽略
          return;
        }
        if (task === null) {
          // interval更新
          task = this.lastTask;
        }
        if (task.taskId !== this.taskId) {
          // 不属于播放任务
          return;
        }
        if (this.lastTask && task.seq < this.lastTask.seq) {
          // 乱序任务包
          return;
        }
        if (task.status === 0) {
          // 任务停止
          if (this.updateTimer) {
            clearInterval(this.updateTimer);
            this.updateTimer = 0;
          }
          TCIC.SDK.instance.setState(Constant.TStateVodPlayerVisible, false);
          return;
        }
        const content = JSON.parse(task.content);
        if (content.status === this.Status.Stop) {
          // 任务停止
          TCIC.SDK.instance.setState(Constant.TStateVodPlayerVisible, false);
          if (this.updateTimer) {
            clearInterval(this.updateTimer);
            this.updateTimer = 0;
          }
          return;
        }
        TCIC.SDK.instance.setState(Constant.TStateVodPlayerVisible, true);
        this.lastTask = task;
        /* status
         *  0: stop
         *  1: play
         *  2: pause
         *  3: pause
         */
        this.currentStatus = content.status;
        this.duration = content.duration;
        if (this.currentStatus === this.Status.Play) {
          // 如果是播放状态，矫正播放时间
          // eslint-disable-next-line max-len
          this.currentTime = Math.floor((TCIC.SDK.instance.getServerTimestamp() - content.timestamp) / 1000) + content.currentTime;
          TCIC.SDK.instance.reportLog(
            'vodPlayer',
            `server:${TCIC.SDK.instance.getServerTimestamp()}, current:${this.currentTime}, content:${JSON.stringify(content)}`,
          );
          // 不超过总时长
          if (this.currentTime >= this.duration) {
            this.currentTime = this.duration;
            this.currentStatus = this.Status.Stop;
          }
        } else {
          this.currentTime = content.currentTime;
        }
        this.currentTimeString = this.formatTime(this.currentTime);
        this.durationString = this.formatTime(this.duration);
        if (!this.updateTimer) {
          this.updateTimer = setInterval(() => {
            this.onTaskUpdate(null);
          }, 1000);
        }
      });
    },
    updateTask(status, duration, currentTime) {
      const userId = TCIC.SDK.instance.getUserId();
      TCIC.SDK.instance.updateTask(this.taskId, JSON.stringify({
        userId,
        status,
        currentTime,
        duration,
        timestamp: TCIC.SDK.instance.getServerTimestamp(),
      }), -1, false, userId);
    },
  },
};

</script>

<style lang="less">

@--color-primary: #006EFF;
@--color-public: #14181D;
@--color-disable: #b9bbbc;
@--color-back: #1C2131;

.ic_video_pause {
  background: url('./assets/ic_video_pause.svg') no-repeat center;

  &:hover {
    background: url('./assets/ic_video_pause_hover.svg') no-repeat center;
  }
}
.ic_video_play {
  background: url('./assets/ic_video_play.svg') no-repeat center;

  &:hover {
    background: url('./assets/ic_video_play_hover.svg') no-repeat center;
  }
}
.ic_video_volume {
  background: url('./assets/ic_video_volume.svg') no-repeat center;

  &:hover {
    background: url('./assets/ic_video_volume_hover.svg') no-repeat center;
  }
}
.ic_video_close {
  background: url('./assets/ic_video_close.svg') no-repeat center;

  &:hover {
    background: url('./assets/ic_video_close_hover.svg') no-repeat center;
  }
}

/*工具内容选择面板*/
.el-popover.vod-player__back-popper {
  padding: 0;
  background: transparent;
  border: none;
  box-shadow: none;
}

.vod-player__back-popper {
  .vod-player__board-drop {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    background: @--color-back;
    color: #fff;
  }

  .vod-player__list-popper {
    margin-bottom: 10px;
    overflow: hidden;

    .el-scrollbar__wrap {
      max-height: 255px;
      margin-bottom: 0px !important;
    }

    ul {
      li {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: 56px;
        padding-left: 16px;
        padding-right: 16px;
        border-bottom: 1px solid rgba(#eee, .1);
        font-size: 16px;
        color: #fff;

        &:last-child {
          border-bottom: 0
        }

        &:hover {
          label {
            color: @--color-primary;
          }
        }

        &.vod-player__li-active {
          background: @--color-primary;

          &:hover {
            label {
              color: #fff;
            }
          }
        }

        label {
          flex: 1;
          max-width: 176px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        i {
          margin-right: 10px;
          width: 24px;
          height: 24px;
        }

        i.vod-player__btn-close {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 20px;
          height: 20px;
          margin-left: 10px;
          border-radius: 4px;
          opacity: 0.3;
          background: rgba(0, 0, 0, .05);

          &:hover {
            opacity: 1
          }
        }
      }
    }
  }
}

.vod-player__wrap {
  width: 100%;
  height: 100%;

  .vod-player {
    display: flex;
    align-items: center;
    width: 496px;
    position: absolute;
    height: 100%;
    padding: 6px 10px;
    border-radius: 10px;
    //background: rgba(0,0,0,0.3); // V1.3.3更改之前
    //filter: blur(2px); // 模糊效果，预期是模糊图层下面的块，暂时用.filter-blur-bg
    opacity: 0.5; // 后期应该是会去掉渐隐这个效果
    background: linear-gradient(141deg, rgba(21, 27, 48, 0.7) 0%, rgba(28, 33, 49, 0.9) 100%);
    box-shadow: 0 0 3px 0 rgba(32, 32, 32, 0.4);
    overflow: hidden;
    pointer-events: auto;

    &.vod-close {
      position: absolute;
      right: 48px;
      width: 80px;
    }

    &.vod-player__blur {
      transition: opacity 1s;
      transition-delay: 3s;

      &.active {
        opacity: 1;
        transition: all 0s;
      }

      &:hover {
        opacity: 1;
        transition: all 0s;
      }
    }

    &.close {
      width: 72px;
      height: 72px;
      top: 24px;
      right: 24px;
      padding: 6px;
    }

    &.right {
      left: 80%;
      width: 140px;
    }

    &.center {
      right: 136px;

      &.vod-player-student {
        left: 50%;
        right: auto;
        transform: translateX(-50%);
      }

      button.vod-player__btn-board-list {
        position: relative;
        display: flex;
        align-items: center;
        margin-left: 20px;

        &:before {
          position: absolute;
          left: -8px;
          width: 1px;
          height: 24px;
          opacity: 0.2;
          border-left: 1px solid #fff;
          background: #fff;
          content: "";
        }
      }
    }

    &.vod-player-student {
      display: flex;
      align-items: center;
      width: 304px;
      padding: 22px 0;
      .vod-player-progress {
        position: relative;
        z-index: 9;
        background: none;
        .el-slider {
          &__button-wrapper {
            display: none;
          }
        }
        .el-slider__runway {
          &:before {
            cursor: default;
          }
        }
        .el-slider__runway.disabled .el-slider__bar {
          background: @--color-primary;
        }
      }
    }

    button {
      position: relative;
      z-index: 9;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      width: 60px;
      height: 60px;
      border-radius: 4px;
      padding: 0;
      background: none;
      outline: none;

      &.active {
        transition: all .4s;
        background: @--color-primary;
        &:hover {
          .ic_video_volume {
            background: url('./assets/ic_video_volume.svg') no-repeat center;
          }
        }
      }

      i {
        display: flex;
        width: 40px;
        height: 40px;
      }
    }

    // 视频工具栏
    .vod-player-progress {
      display: flex;
      align-items: center;
      width: 303px;
      height: 40px;
      padding: 0 16px;
      margin: 0 6px;
      border-radius: 4px;
      background: rgba(0, 0, 0, 0.2);
      .video-slider {
        flex: 1;
        margin: 0 10px;
        .el-slider__runway .el-slider__bar,
        .el-slider__runway:before {
          height: 6px;
        }
      }
      .video-time {
        font-size: 20px;
        color: #fff;
        flex-shrink: 0;
      }
    }
  }
}

// 视频工具栏 - 音量
.volume-popover {
  min-width: inherit !important;
  width: 60px !important;
  height: 112px;
  .video-slider-vertical {
    .el-slider__runway {
      background: #dfdfdf;
      margin: 0;
      &:before {
        top: inherit;
      }
      .el-slider__bar {
        top: inherit;
      }
    }
  }
}
</style>
