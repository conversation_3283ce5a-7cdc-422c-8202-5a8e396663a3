<template>
  <div
    :class="[ isMac ? 'share-sources__is-mac' : '' ]"
    class="inComponent"
  >
    <div class="component-header">
      <div class="header-title">
        {{ $t('选择共享的窗口') }}
      </div>
      <div
        ref="closeBtn"
        class="header-close"
        @click="onClose"
      >
        <i class="el-icon-close icon" />
      </div>
    </div>
    <div class="component-content">
      <div class="share-sources__source-wrap">
        <template v-if="!fetchingSources">
          <h1
            v-if="screenSources.length"
            class="share-sources__category-title"
          >
            {{ $t('桌面') }}
          </h1>
          <div class="share-sources__source-list">
            <div
              v-for="(item, index) in screenSources"
              :key="item.sourceId"
              :class="[ 'share-sources__source-item', selectedSources.includes(item) ? 'share-sources__source-active' : '' ]"
              @click.exact="onClickSource(item)"
              @click.ctrl.exact="onClickSource(item, true)"
              @click.meta.exact="onClickSource(item, true)"
            >
              <canvas :ref="`share-sources__source-${item.sourceId}`" />
              <p>{{ $t('桌面') }}{{ index+1 }}</p>
            </div>
          </div>
          <h1
            v-if="windowSources.length"
            class="share-sources__category-title"
          >
            {{ $t('应用') }}
          </h1>
          <div class="share-sources__source-list">
            <div
              v-for="item in windowSources"
              :key="item.sourceId"
              :class="['share-sources__source-item', selectedSources.includes(item) ? 'share-sources__source-active' : '' ]"
              @click.exact="onClickSource(item)"
              @click.ctrl.exact="onClickSource(item, true)"
              @click.meta.exact="onClickSource(item, true)"
            >
              <canvas :ref="`share-sources__source-${item.sourceId}`" />
              <p>{{ item.sourceName }}</p>
            </div>
          </div>
        </template>
      </div>
      <div class="share-sources__footer">
        <div>
          <el-checkbox
            v-model="enableSystemAudioLoopback"
            @change="onEnableSystemAudioLoopback"
          >
            {{ $t('同时共享电脑声音') }}
          </el-checkbox>
          <el-tooltip
            :content="translateTip.screenTip"
            placement="top"
          >
            <span class="share-sources__loopback-tip" />
          </el-tooltip>
        </div>
        <div v-if="canUseAdvanceMode">
          <el-checkbox
            v-model="isSimpleMode"
            @change="onScreenModeChanged"
          >
            {{ $t('隐藏画笔工具栏') }}
          </el-checkbox>
          <el-tooltip
            :content="$t('演示PPT、PDF时，可以使用PPT、PDF工具自带的画布涂鸦')"
            placement="top"
          >
            <span class="share-sources__loopback-tip" />
          </el-tooltip>
        </div>
        <div>
          <el-button
            size="small"
            @click="onClose"
          >
            {{ $t('取消') }}
          </el-button>
          <el-button
            size="small"
            type="primary"
            :loading="fetchingSources"
            @click="onShare"
          >
            {{ $t('共享') }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18next from 'i18next';
import BaseComponent from '@/component/core/BaseComponent';
import Constant from '@/util/Constant';
import Util from '@/util/Util';

export default {
  extends: BaseComponent,

  data() {
    return {
      screenCaptureSources: [],
      screenSources: [],
      windowSources: [],
      selectedSources: [],
      enableSystemAudioLoopback: false,
      isMac: false,
      fetchingSources: false,
      isSimpleMode: false,
      oldSelectItem: null,
      selectItem: null,
      isSharePPt: false,
      isPPtPlaying: false,
      playingTimer: null,
      isCloseByMyself: false,
      beginFindPPtShare: false,
      findPPtShareCount: 0,
      shareTimer: null,
      isMicroSoftOffice: false,
      isWpsOffice: false,
      isFindingWindows: false,
      pptPlayingName: '', // 演讲者和展台模式
      pptPlayingNameViewer: '', // 观众模式
      pptShareName: '', // 一般模式
      isCoteachingClass: false,   // 是否双师课堂
      isVideoOnlyClass: false,   // 是否纯视频课堂
      canUseAdvanceMode: false,
      roleInfo: {},
    };
  },
  computed: {
    translateTip() {
      return {
        screenTip: i18next.t('共享屏幕的同时，{{arg_0}}将听到您电脑上的声音', { arg_0: this.roleInfo.student }),
      };
    },
  },
  mounted() {
    this.roleInfo = TCIC.SDK.instance.getRoleInfo().roleInfo;
    this.isMac = TCIC.SDK.instance.isMac();
    this.initState();
    this.addLifecycleTCICStateListener(TCIC.TMainState.Screen_Share, (value) => {
      // 停止了屏幕分享
      if ((value === 2 && this.isCloseByMyself) || TCIC.SDK.instance.getState(Constant.TEventShutDownScreenShare)) {
        this.initData();
        this.isFindingWindows = false;
        this.oldSelectItem = null;
        TCIC.SDK.instance.setState(Constant.TEventShutDownScreenShare, false);
      }
    });
    this.addLifecycleTCICStateListener(Constant.TEventShutDownScreenShare, (flag) => {
      this.isCloseByMyself = flag;
    });
    this.makeSureClassJoined(() => {
      this.isCoteachingClass = TCIC.SDK.instance.isCoTeachingClass();
      this.isVideoOnlyClass = TCIC.SDK.instance.isVideoOnlyClass();
      this.canUseAdvanceMode = !this.isCoteachingClass && !this.isVideoOnlyClass;
    });
  },
  methods: {
    initData() {
      clearInterval(this.playingTimer);
      clearInterval(this.shareTimer);
      this.shareTimer = null,
      this.playingTimer = null;
      this.selectItem = null;
      this.isSharePPt = false;
      this.isPPtPlaying = false;
      this.isCloseByMyself = false;
      this.selectedSources = [];
      this.beginFindPPtShare = false;
      this.findPPtShareCount = 0;
      this.isMicroSoftOffice = false;
      this.isWpsOffice = false;
      this.pptPlayingName = '';
      this.pptShareName = '';
      this.pptPlayingNameViewer = '';
    },
    isOtherScreenSharing() {
      const sdk = TCIC.SDK.instance;
      // 检查当前是否有其他有正在分享
      const selfId = sdk.getUserId();
      const permissionList = sdk.getPermissionList();
      const shareItem = permissionList.find(suer =>  suer.screen !== 0 && suer.screenState < 2);
      return shareItem && shareItem.userId !== selfId;
    },
    // 检查是不是分享ppt
    checkPPtIsShared() {
      if (this.selectItem === null) {
        return;
      }
      this.isSharePPt = false;
      this.isPPtPlaying = false;
      if (TCIC.SDK.instance.isWindows()) {
        if (this.selectItem.sourceName.indexOf('.ppt') !== -1
        && this.selectItem.sourceName.indexOf('PowerPoint') !== -1) {
          this.isSharePPt = true;
          this.isMicroSoftOffice = true;
          this.isWpsOffice = false;
          if (this.selectItem.sourceName.indexOf(i18next.t('幻灯片放映')) !== -1) {
            this.isPPtPlaying = true;
          }
        } else {
          if (this.selectItem.sourceName.indexOf('.ppt') !== -1
          && this.selectItem.sourceName.indexOf('WPS Office') !== -1) {
            this.isSharePPt = true;
            this.isPPtPlaying = false;
            this.isMicroSoftOffice = false;
            this.isWpsOffice = true;
          }
          if (this.selectItem.sourceName.indexOf('.ppt') !== -1
          && this.selectItem.sourceName.indexOf('WPS') !== -1 && this.selectItem.sourceName.indexOf(i18next.t('幻灯片放映')) !== -1) {
            this.isSharePPt = true;
            this.isPPtPlaying = true;
            this.isMicroSoftOffice = false;
            this.isWpsOffice = true;
          }
        }
      }
      if (TCIC.SDK.instance.isMac()) {
        if (this.selectItem.sourceName.indexOf('-Microsoft PowerPoint(') !== -1) {
          this.isMicroSoftOffice = true;
          this.isWpsOffice = false;
          this.isSharePPt = true;
          if (this.selectItem.sourceName.indexOf(i18next.t('幻灯片放映')) !== -1) {
            this.isPPtPlaying = true;
          }
        } else {
          if (this.selectItem.sourceName.indexOf('.ppt') !== -1
          && this.selectItem.sourceName.indexOf('-WPS Office(') !== -1) {
            this.isMicroSoftOffice = false;
            this.isWpsOffice = true;
            this.isSharePPt = true;
            this.isPPtPlaying = false;
          } else {
            if (this.selectItem.sourceName.indexOf('WPS Office') !== -1
            && this.selectItem.sourceName.indexOf('.ppt') === -1) {
              this.isMicroSoftOffice = false;
              this.isWpsOffice = true;
              this.isSharePPt = true;
              this.isPPtPlaying = true;
            }
          }
        }
      }
      if (this.isSharePPt) {
        if (this.oldSelectItem === null) {
          this.oldSelectItem = this.selectItem;
        }
        this.getFindingPPTName();
      }
    },
    // 获取需要寻找的ppt名字
    getFindingPPTName() {
      if (this.isMicroSoftOffice) {
        if (this.isPPtPlaying) {
          if (TCIC.SDK.instance.isWindows()) {
            this.pptPlayingName = this.selectItem.sourceName;
            this.pptShareName = this.oldSelectItem.sourceName;
          } else if (TCIC.SDK.instance.isMac()) {
            this.pptPlayingName = this.selectItem.sourceName;
            let selectPPtName = '';
            const indexBegin = this.pptPlayingName.indexOf('[');
            const indexEnd = this.pptPlayingName.lastIndexOf(']');
            selectPPtName = this.pptPlayingName.substring(indexBegin + 1, indexEnd);
            const arrayStr = this.pptPlayingName.split('-');
            if (arrayStr.length > 0) {
              this.pptShareName = selectPPtName;
              // this.pptShareName += selectPPtNamEnd;
              const selectPPtNamEnd = arrayStr[arrayStr.length - 1];
              const indexFindEndBegin = this.pptPlayingName.indexOf(selectPPtNamEnd);
              this.pptPlayingName = this.pptPlayingName.substring(0, indexFindEndBegin - 1);
            }
          }
        } else {
          if (TCIC.SDK.instance.isWindows()) {
            this.pptShareName = this.selectItem.sourceName;
            let selectPPtName = '';
            const arrayStr = this.pptShareName.split('-');
            if (arrayStr.length > 0) {
              const selectPPtNamEnd = arrayStr[arrayStr.length - 1];
              const indexFind = this.pptShareName.indexOf(selectPPtNamEnd);
              selectPPtName = this.pptShareName.substring(0, indexFind - 1).trim();
            }
            this.pptPlayingName = i18next.t('PowerPoint 幻灯片放映');
            this.pptPlayingName += '  -  ';
            this.pptPlayingName += selectPPtName;
            // this.pptPlayingName += '|@#|';
            this.pptPlayingNameViewer = this.pptPlayingName;
            this.pptPlayingNameViewer += ' - PowerPoint';
          } else if (TCIC.SDK.instance.isMac()) {
            let selectPPtName = '';
            this.pptShareName = this.selectItem.sourceName;
            const arrayStr = this.pptShareName.split('-');
            if (arrayStr.length > 0) {
              const selectPPtNamEnd = arrayStr[arrayStr.length - 1];
              const indexFind = this.pptShareName.indexOf(selectPPtNamEnd);
              selectPPtName = this.pptShareName.substring(0, indexFind - 1);
              this.pptPlayingName = i18next.t('PowerPoint 幻灯片放映');
              this.pptPlayingName += ' - ';
              this.pptPlayingName += '[';
              this.pptPlayingName += selectPPtName;
              this.pptPlayingName += ']';
              // this.pptPlayingName += '-';
              // this.pptPlayingName += selectPPtNamEnd;
            }
          }
        }
      } else if (this.isWpsOffice) {
        if (this.isPPtPlaying) {
          if (TCIC.SDK.instance.isWindows()) {
            this.pptPlayingName = this.selectItem.sourceName;
            this.pptShareName = this.oldSelectItem.sourceName;
          } else if (TCIC.SDK.instance.isMac()) {

          }
        } else {
          if (TCIC.SDK.instance.isWindows()) {
            this.pptShareName = this.selectItem.sourceName;
            let selectPPtName = '';
            const arrayStr = this.pptShareName.split('-');
            if (arrayStr.length > 0) {
              selectPPtName = arrayStr[0].trim();
            }
            this.pptPlayingName = i18next.t('WPS演示 幻灯片放映');
            this.pptPlayingName += ' - ';
            this.pptPlayingName += '[';
            this.pptPlayingName += selectPPtName;
            this.pptPlayingName += ']';
          } else if (TCIC.SDK.instance.isMac()) {
            this.pptShareName = this.selectItem.sourceName;
            const arrayStr = this.pptShareName.split('-');
            if (arrayStr.length > 0) {
              const selectPPtNamEnd = arrayStr[arrayStr.length - 1];
              // this.pptPlayingName = selectPPtNamEnd;
            }
          }
        }
      }
    },
    // 检查是不是播放ppt
    checkPPtIsPlaying() {
      const interval = Util.getProperIntervalByCPUUsage(800, 2000);
      // 分享了ppt应用，并没有分享ppt播放，查找是不是在播放ppt了
      if (this.isSharePPt && !this.isPPtPlaying) {
        clearInterval(this.playingTimer);
        this.playingTimer = setInterval(this.findPalyingPPt, interval);
      }
      // 分享了ppt应用，且ppt播放，查找是不是在播放ppt了
      if (this.isSharePPt && this.isPPtPlaying) {
        clearInterval(this.shareTimer);
        this.shareTimer = setInterval(this.findPalyingToSharePPt, interval);
      }
    },
    // 从ppt分享到播放ppt
    getPlayingPPtwindows() {
      TCIC.SDK.instance.getScreenCaptureSources().then((sources) => {
        this.isFindingWindows = false;
        this.screenCaptureSources = sources;
        // 过滤窗口列表
        this.windowSources = this.screenCaptureSources.filter(item => item.type === TCIC.TScreenCaptureSourceType.Window);
        let selectPPtName = '';
        const arrayStr = this.selectItem.sourceName.split('-');
        if (arrayStr.length > 0) {
          selectPPtName = arrayStr[0].trim();
          // 可能在分享的过程中修改了ppt
          if (selectPPtName.substring(selectPPtName.length - 1, selectPPtName.length) === '*') {
            selectPPtName = selectPPtName.substring(0, selectPPtName.length - 2);
          }
        }
        let index = -1;
        // wps 在mac下 切换为幻灯片播放模式 会主动关闭窗口 暂时先不支持
        if (!(TCIC.SDK.instance.isMac() && this.isWpsOffice)) {
          index =  this.windowSources.findIndex(item => item.sourceName.indexOf(i18next.t('幻灯片放映')) !== -1 && item.sourceName.indexOf(selectPPtName) !== -1);
        }
        if (index !== -1) {
          this.selectedSources = [];
          this.isPPtPlaying = true;
          this.beginFindPPtShare = false;
          this.selectItem = this.windowSources[index];
          // 正在播放幻灯片 切换为播放的幻灯片
          TCIC.SDK.instance.setState(Constant.TStatePPtSharingState, true);
          // !先默认一个时间差来同步TStatePPtSharingState，可能会有同步的隐患
          setTimeout(() => {
            TCIC.SDK.instance.reportLog('stopScreenShare', '[ShareSources] getPlayingPPtwindows, found ppt, change share source');
            TCIC.SDK.instance.stopScreenShare();
            setTimeout(() => {
              this.onClickSource(this.windowSources[index]);
              this.onShare();
            }, 1500);
          }, 800);
          if (this.playingTimer) {
            clearInterval(this.playingTimer);
            this.playingTimer = null;
          }
        }
        this.isFindingWindows = false;
      });
    },
    findPalyingPPt() {
      const pptName = [];
      pptName.push(this.pptPlayingName);
      pptName.push(this.pptPlayingNameViewer);
      const pptArrShare = [];
      pptArrShare.push(this.pptShareName);
      const params = {
        pptArrName: pptName,
        pptArrShareName: pptArrShare,
        bToShare: false,
      };
      TCIC.SDK.instance.getDesktopWindows(params).then(() => {
        // !!!先利用native方法检测窗口，设置分享状态，再用electron方法拉去窗口，利用这个时间差来同步TStatePPtSharingState，可能会有同步的隐患
        // TCIC.SDK.instance.setState(Constant.TStatePPtSharingState, true);
        if (this.playingTimer) {
          clearInterval(this.playingTimer);
          this.playingTimer = null;
        }
        this.getPlayingPPtwindows();
      });
      return;
    },
    // 从播放ppt到分享ppt
    getSharePPtWindows() {
      TCIC.SDK.instance.getScreenCaptureSources().then((sources) => {
        this.isFindingWindows = false;
        this.screenCaptureSources = sources;
        // 过滤窗口列表
        this.windowSources = this.screenCaptureSources.filter(item => item.type === TCIC.TScreenCaptureSourceType.Window);
        let indexPlaying = -1;
        if (TCIC.SDK.instance.isMac() && this.isWpsOffice) {
          indexPlaying =  this.windowSources.findIndex(item => item.sourceName.indexOf('WPS Office') !== -1 && item.sourceName.indexOf('.ppt') === -1);
        } else {
          indexPlaying =  this.windowSources.findIndex(item => item.sourceName.indexOf(i18next.t('幻灯片放映')) !== -1 && item.sourceName === this.selectItem.sourceName);
        }
        if (indexPlaying !== -1) {
          this.isFindingWindow = false;
          return;
        }
        if (this.oldSelectItem !== null) {
          // 一开始就是分享的 放映模式 则不用寻找一般模式
          if (TCIC.SDK.instance.isMac() && this.isWpsOffice) {
            if (this.oldSelectItem.sourceName.indexOf('WPS Office') !== -1
            && this.oldSelectItem.sourceName.indexOf('.ppt') === -1) {
              this.isFindingWindow = false;
              if (this.shareTimer) {
                clearInterval(this.shareTimer);
                this.shareTimer = null;
              }
              // TCIC.SDK.instance.shareScreenFromPPt(false);
              TCIC.SDK.instance.setState(Constant.TStatePPtSharingState, false);
              this.isCloseByMyself = true;
              TCIC.SDK.instance.reportLog('stopScreenShare', `[ShareSources] getSharePPtWindows, oldSelectItem ${this.oldSelectItem.sourceName}`);
              TCIC.SDK.instance.stopScreenShare();
              return;
            }
          } else {
            if (this.oldSelectItem.sourceName.indexOf(i18next.t('幻灯片放映')) !== -1) {
              this.isFindingWindow = false;
              if (this.shareTimer) {
                clearInterval(this.shareTimer);
                this.shareTimer = null;
              }
              // TCIC.SDK.instance.shareScreenFromPPt(false);
              TCIC.SDK.instance.setState(Constant.TStatePPtSharingState, false);
              this.isCloseByMyself = true;
              TCIC.SDK.instance.reportLog('stopScreenShare', `[ShareSources] getSharePPtWindows, oldSelectItem ${this.oldSelectItem.sourceName}`);
              TCIC.SDK.instance.stopScreenShare();
              return;
            }
          }
          const index =  this.windowSources.findIndex(item => item.sourceName === this.oldSelectItem.sourceName);
          if (index !== -1) {
            if (this.shareTimer) {
              clearInterval(this.shareTimer);
              this.shareTimer = null;
            }
            this.selectedSources = [];
            this.isPPtPlaying = false;
            this.beginFindPPtShare = true;
            this.selectItem = this.windowSources[index];
            TCIC.SDK.instance.setState(Constant.TStatePPtSharingState, true);
            // !先默认一个时间差来同步TStatePPtSharingState，可能会有同步的隐患
            setTimeout(() => {
              TCIC.SDK.instance.reportLog('stopScreenShare', '[ShareSources] getSharePPtWindows, found ppt, change share source');
              TCIC.SDK.instance.stopScreenShare();
              setTimeout(() => {
                this.onClickSource(this.windowSources[index]);
                this.onShare();
              }, 500);
            }, 800);
          }
          this.isFindingWindow = false;
        }
        this.isFindingWindow = false;
      });
    },
    findPalyingToSharePPt() {
      const pptName = [];
      pptName.push(this.pptPlayingName);
      pptName.push(this.pptPlayingNameViewer);
      const pptArrShare = [];
      pptArrShare.push(this.pptShareName);
      const params = {
        pptArrName: pptName,
        pptArrShareName: pptArrShare,
        bToShare: true,
      };
      TCIC.SDK.instance.getDesktopWindows(params).then(() => {
        // !!!先利用native方法检测窗口，设置分享状态，再用electron方法拉去窗口，利用这个时间差来同步TStatePPtSharingState，可能会有同步的隐患
        // TCIC.SDK.instance.setState(Constant.TStatePPtSharingState, true);
        if (this.shareTimer) {
          clearInterval(this.shareTimer);
          this.shareTimer = null;
        }
        this.getSharePPtWindows();
      });
      return;
    },
    onComponentVisibilityChange(visible) {
      if (visible) {
        this.initSources();
      }
    },
    onClose() {
      TCIC.SDK.instance.setState(Constant.TStateShowSelectShareWindowComponent, false);
      this.hide();
      this.$emit('hide');
      window.tbm.clearTarget('screenshare');
    },
    /**
     * 这里有BUG，有时候分享屏幕不是选中的
     * todo: 待修复
     * @param {*} clickItem
     * @param {*} multiSelect
     */
    onClickSource(clickItem, multiSelect = false) {
      this.initData();
      // 非多选模式下，或者点击目标为屏幕时，永远只选中一个目标
      if (!multiSelect || clickItem.type === TCIC.TScreenCaptureSourceType.Screen) {
        this.selectedSources = [clickItem];
      } else {  // 多选模式下，根据目标的选中状态做反向操作
        const index = this.selectedSources.findIndex(item => item === clickItem);
        if (index === -1) {
          // 最多允许同时选中三个目标
          if (this.selectedSources.length >= 3) return;
          // 如果存在已选中的桌面目标，直接清空
          if (this.selectedSources.some(item => item.type === TCIC.TScreenCaptureSourceType.Screen)) {
            this.selectedSources = [clickItem];
          } else {
            this.selectedSources.push(clickItem);
          }
        } else if (this.selectedSources.length > 1) {
          this.selectedSources.splice(index, 1);
        }
      }
      this.screenSources = this.screenSources.splice(0);
      this.windowSources = this.windowSources.splice(0);
      this.selectItem = clickItem;
      this.checkPPtIsShared();
    },
    onShare() {
      if (this.isOtherScreenSharing()) {
        window.showToast(i18next.t('其他人正在进行屏幕共享，请稍候再试'));
        return;
      }
      if (TCIC.SDK.instance.getState(TCIC.TMainState.Sub_Camera) === 0) {
        window.showToast(i18next.t('请先关闭辅助摄像头后再使用屏幕共享'));
      } else if (this.selectedSources.length > 0) {
        // 触发关闭
        this.$refs.closeBtn.click();
        document.dispatchEvent(new Event('reload-header'));
        // 需要在一次event触发之后再调用屏幕分享逻辑
        setTimeout(async () => {
          await this.selectScreenCaptureTarget();
          document.dispatchEvent(new Event('reload-header'));
        }, 100);
      } else {
        window.showToast(i18next.t('选择要分享的视频'), 'info');
      }
    },
    onEnableSystemAudioLoopback(enable) {
      // 选中状态变更后通知状态变化
      TCIC.SDK.instance.setState(Constant.TStateEnableSystemAudioLoopback, enable);
    },
    onScreenModeChanged(mode) {
      this.isSimpleMode = mode;
    },
    async selectScreenCaptureTarget() {
      if (this.selectedSources.length === 0) {
        return false;  // 没有选中任何目标
      }
      const sdk = TCIC.SDK.instance;
      try {
        const mics = await sdk.getMics().catch((err) => {
          console.error('sdk getMics error:', err);
          sdk.reportEvent('getMics', err, -1);
          return [];
        });
        if (mics.length === 0) {
          // 老师没有打开麦克风，学生端将听不到屏幕共享声音
          window.showToast(i18next.t('检测到当前麦克风未打开，{{arg_0}}将听不到桌面共享的声音', { arg_0: this.roleInfo.student }));
        }

        await sdk.enableSystemAudioLoopback(this.enableSystemAudioLoopback);
        await sdk.setFeatureAvailable('ScreenShareAdvanceMode', !this.isSimpleMode);
        // trtc 先调用selectScreenCaptureTarget再异步调用startScreenShare，会没有startScreenShare的回调，所以将startScreenShare调用放到前面
        let dom;
        if (this.isSimpleMode) {
          dom = await sdk.loadComponent('screen-preview-component');
        }
        await sdk.selectScreenCaptureTarget(this.selectedSources);
        TCIC.SDK.instance.reportLog('startScreenShare', '[ShareSources] selectScreenCaptureTarget');
        await sdk.startScreenShare(dom);
        if (this.isSharePPt) {
          this.checkPPtIsPlaying();
        }

        if (this.selectItem) {
          sdk.reportEvent('screen_share_source', { sourceName: this.selectItem.sourceName });
        }
      } catch (error) {
        console.error('selectScreenCaptureTarget error:', error);
        window.showToast(i18next.t('获取{{arg_0}}信息失败!', { arg_0: 'selectScreenCaptureTarget' }));
        sdk.reportEvent('selectScreenCaptureTarget', error, -1);
      }
      return true;
    },
    handleEnableSystemAudioLoopback(enable) {
      // 处理状态变更
      this.enableSystemAudioLoopback = enable;
    },
    initState() {
      // 注册状态
      TCIC.SDK.instance.registerState(Constant.TStateEnableSystemAudioLoopback, '屏幕共享是否混合系统声音', false);
      TCIC.SDK.instance.registerState(Constant.TStateShowSelectShareWindowComponent, '选择共享窗口组件是否打开', false);
      // 订阅状态
      this.addLifecycleTCICStateListener(Constant.TStateEnableSystemAudioLoopback, this.handleEnableSystemAudioLoopback);
      // 状态更新
      this.enableSystemAudioLoopback = TCIC.SDK.instance.getState(Constant.TStateEnableSystemAudioLoopback, false);
      TCIC.SDK.instance.setState(Constant.TStateShowSelectShareWindowComponent, true);
    },
    initSources() {
      // 获取屏幕分享的窗口列表
      this.fetchingSources = true;
      this.updateTbmButtons();
      setTimeout(() => {
        TCIC.SDK.instance.getScreenCaptureSources().then((sources) => {
          this.fetchingSources = false;
          this.screenCaptureSources = sources;
          // 过滤屏幕列表
          this.screenSources = this.screenCaptureSources.filter(item => item.type === TCIC.TScreenCaptureSourceType.Screen);
          // 过滤窗口列表
          this.windowSources = this.screenCaptureSources.filter(item => item.type === TCIC.TScreenCaptureSourceType.Window);
          this.$nextTick(() => {
            // 默认选择第一个桌面分享源
            this.onClickSource(this.screenSources[0]);
            // 需要等待canvas生成后再渲染缩略图，所以需要使用nextTick
            this.initCanvas();
            this.updateTbmButtons();
          });
        });
      }, 100);
    },
    initCanvas() {
      this.screenCaptureSources.forEach((item) => {
        const canvas = this.$refs[`share-sources__source-${item.sourceId}`][0];
        canvas.width = 160;
        canvas.height = 120;
        if (!item.thumbBGRA.buffer || 0 === item.thumbBGRA.width || 0 === item.thumbBGRA.height) {
          canvas.getContext('2d').fillStyle = '#000';
          canvas.getContext('2d').fillRect(0, 0, 320, 240);
        } else {
          canvas.width = item.thumbBGRA.width;
          canvas.height = item.thumbBGRA.height;
          canvas.getContext('2d').putImageData(new ImageData(new Uint8ClampedArray(item.thumbBGRA.buffer), item.thumbBGRA.width, item.thumbBGRA.height), 0, 0);
        }
      });
    },
    updateTbmButtons() {
      // 添加返回按钮
      window.tbm.updateTarget('screenshare', [window.tbm.generateNode(this.$refs.closeBtn)], 'close');
      // 添加窗口选择
      const lists = Array.from(this.$el.querySelectorAll('.share-sources__source-list'));
      // 桌面
      window.tbm.updateTarget(
        'screenshare',
        Array.from(lists[0].querySelectorAll('.share-sources__source-item'))
          .map(item => window.tbm.generateNode(item)),
        'screen',
      );
      // 窗口
      window.tbm.updateTarget(
        'screenshare',
        Array.from(lists[1].querySelectorAll('.share-sources__source-item'))
          .map(item => window.tbm.generateNode(item)),
        'window',
      );
      // 添加选项配置
      window.tbm.updateTarget(
        'screenshare',
        Array.from(this.$el.querySelectorAll('.el-checkbox'))
          .map(item => window.tbm.generateNode(item)),
        'option',
      );
      // 添加按钮
      window.tbm.updateTarget(
        'screenshare',
        Array.from(this.$el.querySelectorAll('button'))
          .map(item => window.tbm.generateNode(item)),
        'button',
      );
    },
  },
};
</script>
<style lang="less">
.share-sources-default {
  background-color: rgba(0,0,0,0.7);
  box-shadow: 0 0 40px 0 rgba(0, 0, 0, 0.5);
  top: 45px;
  right: 296px;
  z-index: 99;
  position: absolute;
  text-align: initial;
  @media screen and (max-height: 768px) {
    width: 675px;
    height: 535px;
  }
  @media screen and (min-height: 769px) {
    width: 875px;
    height: 692px;
  }
}
.share-sources__is-mac {
  .share-sources__source-wrap {
    .share-sources__source-list {
      .share-sources__source-item {
        canvas {
          width: 120px;
          height: 90px;
        }
      }
    }
  }
}
.share-sources__title {
  position: relative;
  top: 24px;
  left: 23px;
  font-size: 14px;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 20px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.share-sources__source-wrap{
  overflow: auto;
  color: #A1A8B4;
  @media screen and (max-height: 768px) {
    min-width: 443px;
    min-height: 435px;
    max-height: 435px;
  }
  @media screen and (min-height: 769px) {
    min-height: 595px;
    max-height: 595px;
  }
  .share-sources__category-title {
    font-size: 14px;
    color: #A1A8B4;
    line-height: 20px;
    margin-top: 15px;
    font-weight: 400;
    position: sticky;
    top: 0;
    background: #1c2131;
  }
  .share-sources__source-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    @media screen and (max-height: 768px) {
      max-height: 200px;
    }
    @media screen and (min-height: 769px) {
      max-height: 300px;
    }
    .share-sources__source-item {
      margin: 8px;
      width: calc(25% - 16px);
      background: rgba(255, 255, 255, 0.1);
      text-align: center;
      padding: 12px;
      border-radius: 2px;
      &.share-sources__source-active {
        background: #006EFF;
      }
      &:hover{
        cursor: pointer;
        background: #006EFF;
      }
      canvas {
        width: 120px;
        height: 120px;
      }
      p {
        white-space: nowrap;
        text-overflow:ellipsis;
        overflow:hidden;
        margin: 10px 15px 0;
        color: #fff;
      }
    }
  }
}
.share-sources__source-wrap::-webkit-scrollbar, body::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: transparent;
  border-radius: 8px;
}
.share-sources__source-wrap::-webkit-scrollbar-thumb, body::-webkit-scrollbar-thumb {
  background: rgba(138,144,153,1);
  border-radius: 8px;
}
.share-sources__source-wrap::-webkit-scrollbar-track, body::-webkit-scrollbar-track {
  border-radius: 8px;
}
.share-sources__footer{
  display: flex;
  justify-content: space-between;
  padding-right: 25px;
  padding-left: 25px;
  margin: 10px 0 20px;
  .share-sources__loopback-tip {
    width: 20px;
    height: 20px;
    background: url("~@/assets/info.png") no-repeat;
    background-size: 100% 100%;
    margin-left: 10px;
    cursor: pointer;
  }
  div {
    display: flex;
  }
}
</style>
