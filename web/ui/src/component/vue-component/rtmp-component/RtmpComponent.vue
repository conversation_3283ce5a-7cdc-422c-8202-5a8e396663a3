<template>
  <!-- “互动课 + 公开课的PC/Pad端 + 巡课” 弹出的通知对话框-->
  <el-dialog
    :visible="visible"
    custom-class="rtmp-dialog"
    :modal-append-to-body="true"
    :append-to-body="true"
    :close-on-click-modal="false"
    :show-close="false"
    :center="true"
  >
    <div
      slot="title"
    >
      <div class="component-header dialog">
        <div class="header-title">
          {{ $t('RTMP 推流') }}
        </div>
        <div
          class="header-close"
          @click="closeDialog"
        >
          <i class="el-icon-close icon" />
        </div>
      </div>
    </div>
    <table class="rtmp-streaming-table">
      <tr>
        <td>
          {{ seperate ? $t('推流服务器') : $t('推流地址') }}
        </td>
        <td
          class="rtmp-url"
        >
          {{ seperate ? rtmpInfo.server : rtmpInfo.url }}
          <br>
          <a
            v-clipboard:copy="seperate ? rtmpInfo.server : rtmpInfo.url"
            v-clipboard:success="onCopySuccess"
            v-clipboard:error="onCopyError"
            href="#"
            class="copy-btn"
          >{{ $t('复制') }}</a>
          <a
            href="#"
            class="copy-btn"
            @click="seperate = !seperate"
          >{{ $t('推流:切换格式') }}</a>
        </td>
      </tr>
      <tr v-if="seperate">
        <td>
          {{ $t('推流码') }}
        </td>
        <td
          class="rtmp-url"
        >
          {{ rtmpInfo.key }}
          <br>
          <a
            v-clipboard:copy="rtmpInfo.key"
            v-clipboard:success="onCopySuccess"
            v-clipboard:error="onCopyError"
            href="#"
            class="copy-btn"
          >{{ $t('复制') }}</a>
        </td>
      </tr>
      <tr>
        <td>{{ $t('推流开关') }}</td>
        <td :style="{ color: isStreaming ? '#60b238' : '#f56c6c' }">
          {{ isStreaming ? $t('推流:开启') : $t('推流:关闭') }}
        </td>
      </tr>
    </table>
    <div
      slot="footer"
    >
      <el-button
        v-if="isStreaming"
        type="primary"
        class="footer-button"
        :loading="submitting"
        @click="stopStreaming"
      >
        {{ $t('停止推流') }}
      </el-button>
      <el-button
        v-else
        type="primary"
        class="footer-button"
        :loading="submitting"
        @click="startStreaming"
      >
        {{ $t('开始推流') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import i18next from 'i18next';
import BaseComponent from '@core/BaseComponent';
import Menu from '../header-component/Menu';

export default {
  name: 'RtmpComponent',
  extends: BaseComponent,
  data() {
    return {
      menu: null,
      localComponent: this.component,
      rtmpInfo: {
        url: '',
        server: '',
        key: '',
      },
      seperate: false,
      visible: false,
      stage: false,
      audioPublish: false,
      videoPublish: false,
      submitting: false,
    };
  },
  computed: {
    isStreaming() {
      return this.stage && this.audioPublish && this.videoPublish;
    },
  },
  mounted() {
    this.menu = Menu();
    this.addLifecycleTCICStateListener(TCIC.TMainState.Stage_Status, (stage) => {
      this.stage = stage;
    });

    this.addLifecycleTCICStateListener(TCIC.TMainState.Audio_Publish, (audioPublish) => {
      this.audioPublish = audioPublish;
    });

    this.addLifecycleTCICStateListener(TCIC.TMainState.Video_Publish, (videoPublish) => {
      this.videoPublish = videoPublish;
    });

    this.makeSureClassJoined(() => {
      const schoolInfo = TCIC.SDK.instance.getSchoolInfo();
      const classInfo = TCIC.SDK.instance.getClassInfo();
      Object.assign(this.rtmpInfo, this.generateRtmpUrl(
        schoolInfo.sdkAppId,
        classInfo.classId,
        TCIC.SDK.instance.getUserId(),
        TCIC.SDK.instance.getTrtcUserSig(),
      ));
    });

    const menuItem = this.menu.find(item => item.name === 'rtmp');
    if (menuItem) {
      menuItem.label = i18next.t('推流');
    }
  },
  methods: {
    generateRtmpUrl(sdkAppId, roomId, userId, userSig, options = {}) {
      const params = [
        `sdkappid=${encodeURIComponent(sdkAppId)}`,
        `userid=${encodeURIComponent(userId)}`,
        `usersig=${encodeURIComponent(userSig)}`,
      ];

      if (!options.useStringRoomId) {
        params.push('use_number_room_id=1');
      }

      const server = 'rtmp://rtmp.rtc.qq.com/push/';
      const key = `${encodeURIComponent(String(roomId))}?${params.join('&')}`;

      return {
        server,
        key,
        url: `${server}${key}`,
      };
    },
    toggle() {
      this.visible = !this.visible;
    },
    closeDialog() {
      this.visible = false;
    },
    startStreaming() {
      let promise;
      if (!this.stage) {
        if (!TCIC.SDK.instance.isTeacher() && !TCIC.SDK.instance.isAssistant()) {
          window.showToast(i18next.t('你不在台上，可举手申请上台，打开音视频互动~'));
          return;
        }
        const classInfo = TCIC.SDK.instance.getClassInfo();

        promise = TCIC.SDK.instance.memberAction({
          classId: classInfo.classId,
          classType: classInfo.classType,
          userId: TCIC.SDK.instance.getUserId(),
          actionType: TCIC.TMemberActionType.Stage_Up,
        });
      } else {
        promise = Promise.resolve();
      }

      promise = promise
        .then(() => Promise.all([
          TCIC.SDK.instance.startLocalAudio(),
          TCIC.SDK.instance.startLocalVideo(),
          TCIC.SDK.instance.promiseState(TCIC.TMainState.Stage_Status, true),
        ]));

      this.setSubmitting(promise);

      promise
        .then(() => {
          window.showToast(i18next.t('已开启推流'));
        })
        .catch((error) => {
          console.error(error);
          window.showToast(i18next.t('开启推流失败'));
        });
    },

    stopStreaming() {
      let promise;
      if (TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isAssistant()) {
        const classInfo = TCIC.SDK.instance.getClassInfo();

        promise = TCIC.SDK.instance.memberAction({
          classId: classInfo.classId,
          classType: classInfo.classType,
          userId: TCIC.SDK.instance.getUserId(),
          actionType: TCIC.TMemberActionType.Stage_Down,
        })
          .then(() => TCIC.SDK.instance.promiseState(TCIC.TMainState.Stage_Status, false));
      } else {
        promise = Promise.all([
          TCIC.SDK.instance.startLocalAudio(),
          TCIC.SDK.instance.startLocalVideo(),
        ]);
      }

      this.setSubmitting(promise);

      promise
        .then(() => {
          window.showToast(i18next.t('已停止推流'));
        })
        .catch((error) => {
          console.error(error);
          window.showToast(i18next.t('停止推流失败'));
        });
    },

    setSubmitting(promise) {
      this.submitting = true;
      promise.then(() => {
        this.submitting = false;
      }).catch(() => {
        this.submitting = false;
      });
    },

    onCopySuccess() {
      window.showToast(i18next.t('复制成功'), 'success');
    },

    onCopyError() {
      window.showToast(i18next.t('复制失败请重试'));
    },
  },
};
</script>
<style lang="less">
.rtmp-dialog {
  background: #1C2131 !important;
  width: 600px !important;
  max-width: 60%;

  .rtmp-streaming-table {
    td {
      padding: 8px;
      vertical-align: top;
      &:first-child {
        min-width: 100px;
        word-break: break-word;
      }
      &:last-child {
        color: #fff;
      }
    }
  }

  .rtmp-url {
    user-select: text;
  }

  // 覆盖样式
  .el-dialog__body {
    padding: 0 16px 24px 16px !important;
  }
  // 覆盖样式
  .el-dialog__header {
    text-align: left;
    padding: 24px 16px 16px 16px;
  }

  .el-dialog__footer {
    padding: 0 16px 24px 16px;
    .footer-button {
      padding: 5px 22px;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;

      &.el-button--primary {
        background: #006EFF;
        color: #FFF;
      }

      &.el-button--default {
        background: #FFF;
        color: #006EFF;
      }
    }
    .footer-button__disabled {
      opacity: 0.5;
      border: 0;
    }
  }

  .copy-btn {
    color: #006EFF;
    display: inline-block;
    margin-top: 8px;

    & + .copy-btn {
      margin-left: 16px;
    }
  }
}
@media (max-width: 530px) {
  .rtmp-dialog {
    max-width: 100%;
  }
}
</style>
