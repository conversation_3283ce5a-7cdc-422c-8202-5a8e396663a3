import i18next from 'i18next';
import BaseComponent from '@core/BaseComponent';
import Constant from '@/util/Constant';
import Util from '@util/Util';
import { parseEmoji } from './parseEmoji';
import { MsgImageState } from './MsgBase';
import Lodash from 'lodash';

let imBaseSingletonInit = false;

const VALID_IMG_TYPES = ['JPG', 'JPEG', 'PNG', 'GIF', 'BMP'];

export const IMBaseComponent = {
  extends: BaseComponent,

  data() {
    return {
      isMobile: false,            // 是否移动端设备
      chatEnabled: true,          // 当前用户是否被允许发言
      silenceAll: false,          // 当前是否启用了全员禁言
      msgSending: false,          // 是否正在发送消息
      imgSending: false,          // 是否正在发送图片
      fileSending: false,          // 是否正在发送文件
      imgPercent: '',             // 图片发送进度
      imgUrl: '',                 // 当前发送图片文件的 ObjectURL
      acceptExt: '',
      failSendFileMap: new Map(),
      replyMsg: null,             // 要回复的消息
      isTeacher: false,
      isAssistant: false,
      isStudent: false,
      isSupervisor: false,
      roleInfo: TCIC.SDK.instance.getRoleInfo().roleInfo,
      // 消息列表相关
      msgList: [],
      showMsgList: [],
      maxMsgCount: 1000,
      showMsgCount: 100,
      pageSize: 100,
      memeberListPageSize: 50,
      isWeb: TCIC.SDK.instance.isWeb(),
      // 预览图片相关
      openedImgSeq: 0,
      isSendToServer: false,
      previewImgUrl: '',
      privateChatReceiverList: [],
      currentPrivateChatReceiver: null,
      isLoading: false,
      hasMore: true,
      pageIndex: 1,
      pageCount: 1,
      memberList: [],
      supportPrivateChat: false,
      silenceModeList: [],
      currentSilenceMode: 0,
    };
  },

  computed: {
    msgDataExt() {
      const result = {};

      result.IsPrivateMsg = !!this.currentPrivateChatReceiver?.userId ? 1 : 0;
      if (this.currentPrivateChatReceiver?.userId) {
        const currentUserID = TCIC.SDK.instance.getUserId();
        const currentUser = this.memberList.find(item => item.userId === currentUserID);
        const currentUserName = currentUser ? currentUser.userName : currentUserID;

        result.PrivateInfo = {
          From: {
            ID: currentUserID,
            Name: currentUserName,
          },
          To: {
            ID: this.currentPrivateChatReceiver?.userId,
            Name: this.currentPrivateChatReceiver?.userName,
          },
        };
      }

      if (this.replyMsg) {
        const { time, seq, convType, msgType, from, to, data, nickname, avatar, preview1, preview2, desc, ext } = this.replyMsg;
        const overrideData = this.replyMsg.localImgSeq && this.replyMsg.msgType === 'img_message' ? { data: preview1 } : {};
        result.replyMsg = {
          time,
          seq,
          convType,
          msgType,
          from,
          to,
          data,
          nickname,
          avatar,
          preview1,
          preview2,
          desc,
          ext,
          ...overrideData,
        };
      }

      return Object.keys(result).length > 0 ? result : null;
    },
  },
  mounted() {
    this.isMobile = TCIC.SDK.instance.isMobile();
    this.acceptExt = VALID_IMG_TYPES.map(type => `.${type}`).join(',');
    if (!imBaseSingletonInit) {
      imBaseSingletonInit = true;
      // 发完消息移动端隐藏输入框
      this.addLifecycleTCICEventListener(TCIC.TIMEvent.Send_Msg, () => {
        if (this.isMobile) {
          TCIC.SDK.instance.getComponent('mobile-im-input-bar-component')
            .getVueInstance()
            .hide();
        }
      });
    }
    this.addLifecycleTCICStateListener(TCIC.TMainState.Member_List_Page_Count, (pageCount) => {
      this.pageCount = pageCount;
    });
    this.makeSureClassJoined(() => {
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      this.isStudent = TCIC.SDK.instance.isStudent();
      this.isSupervisor = TCIC.SDK.instance.isSupervisor();
      this.pageIndex = 1;
      this.getMemberList();
      this.updateSilenceModeList();

      this.supportPrivateChat = TCIC.SDK.instance.isFeatureAvailable('PrivateChat');
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Chat_Permission, (chatEnabled) => {
      this.chatEnabled = chatEnabled;
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Silence_All, (silenceAll) => {
      this.silenceAll = silenceAll;
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Silence_Mode, (silenceMode) => {
      this.currentSilenceMode = silenceMode;
      this.doUpdatePrivateChatReceiver();
    });

    this.addLifecycleTCICEventListener(TCIC.TIMEvent.Recv_Msg, this.onRecvMsg.bind(this));
    this.addLifecycleTCICEventListener(TCIC.TIMEvent.Revoked_Msg, this.onRevokedMsg.bind(this));
    this.addLifecycleTCICEventListener(TCIC.TIMEvent.Send_Msg, this.onSendMsg.bind(this));

    // 目前各个IM组件的状态数据各自维护，简单点，layout变化时清理replyMsg
    this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Layout, (layout) => {
      this.replyMsg = null;
    });

    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Member_Join, (userId) => {
      this.updateUser(userId);
    });
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Member_Exit, (userId) => {
      const index = this.memberList.findIndex(item => item.userId === userId);
      if (index !== -1) {
        this.memberList.splice(index, 1);
      }
      this.doUpdatePrivateChatReceiver();
    });
    this.$EventBus.$on('set-reply-msg', this.setReplyMsg);
  },

  methods: {
    getMember(userId) {
      return TCIC.SDK.instance.getUserProfile(userId, false).then((res) => {
        console.log('[memberList] getMember', res);
        const memberInfo = new TCIC.TMemberInfo();
        memberInfo.deserialize(res);
        return memberInfo;
      });
    },
    updateUser(userId) {
      this.getMember(userId).then((user) => {
        console.log(`[MemberList] member update ${userId}`, user);
        const index = this.memberList.findIndex(item => item.userId === userId);
        if (index === -1) {
          this.memberList.push(user);
        } else {
          this.memberList.splice(index, 1, user);
        }
        this.doUpdatePrivateChatReceiver();
      });
    },
    async validateImageByLoading(file) {
      return new Promise((resolve) => {
        const img = new Image();
        img.src = URL.createObjectURL(file);

        img.onload = () => {
          resolve(true);
        };

        img.onerror = () => {
          window.showToast(i18next.t('仅支持发送图片'), { type: 'error' });
          resolve(false);
        };
      });
    },
    async validateImageByLoadingWithoutToast(file) {
      return new Promise((resolve) => {
        const validImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp'];
        if (!validImageTypes.includes(file.type)) {
          return resolve(false);
        }

        const img = new Image();
        img.src = URL.createObjectURL(file);

        img.onload = () => {
          resolve(true);
        };

        img.onerror = () => {
          resolve(false);
        };
      });
    },
    onRecvMsg(msg) {

    },
    loadMore() {
      if (this.isLoading || !this.hasMore) {
        console.log('loadMore', this.isLoading, this.hasMore);
        return;
      }
      this.pageIndex += 1;
      this.getMemberList();
    },
    updateSilenceModeList() {
      const supportPrivateChat = TCIC.SDK.instance.isFeatureAvailable('PrivateChat');
      if (supportPrivateChat) {
        this.silenceModeList = [
          {
            label: i18next.t('自由聊天'),
            id: 0,
          },
          {
            label: i18next.t('仅允许公开聊天'),
            id: 1,
          },
          {
            label: i18next.t('仅允许私聊'),
            id: 2,
          },
          {
            label: i18next.t('全员禁言'),
            id: 3,
          },
        ];
      } else {
        this.silenceModeList = [
          {
            label: i18next.t('自由聊天'),
            id: 0,
          },
          {
            label: i18next.t('全员禁言'),
            id: 3,
          },
        ];
      }
    },
    getMemberList() {
      this.isLoading = true;
      const filterType = TCIC.TMemberType.All;
      // 构建查询参数
      const filter = {
        page: this.pageIndex,
        limit: this.memeberListPageSize,
        type: filterType,
      };
      TCIC.SDK.instance.getClassMemberList(filter).then(async (res) => {
        console.log('getMemberList', res);
        for (const member of res.members) {
          const index = this.memberList.findIndex(item => item.userId === member.userId);
          if (index !== -1) {
            this.memberList[index] = member;
          } else {
            this.memberList.push(member);
          }
        }
        this.isLoading = false;

        if (true) { // todo: 这里临时改成不管拉没拉完, 都只拉一次, 所以写死true. 原来是: (this.memberList.length >= res.total)
          const classInfo = TCIC.SDK.instance.getClassInfo();
          const teacherId = classInfo.teacherId;
          const alreadyInList = this.memberList.some(item => item.userId === teacherId);
          if (!alreadyInList) {
            const user = await this.getMember(teacherId);
            this.memberList.push(user);
          }
          console.log('getMemberListForPrivate', this.memberList.map(i => i.userId), res.total);
          TCIC.SDK.instance.reportLog('getMemberListFinished', `memberList = ${this.memberList.map(i => i.userId)}, 
      total = ${res.total},
      `);
          this.hasMore = false;
          this.doUpdatePrivateChatReceiver();
        } else {
          // setTimeout(() => this.loadMore(), 300);
        }
      });
    },
    doUpdatePrivateChatReceiver() {
      Lodash.throttle(() => {
        this.updatePrivateChatReceiver();
      }, 500)();
    },
    updatePrivateChatReceiver() {
      const isAvailable = TCIC.SDK.instance.isFeatureAvailable('PrivateChat');
      if (!isAvailable) {
        this.privateChatReceiverList = [];
        return;
      }

      if (this.currentSilenceMode === TCIC.TClassSilenceMode.Public_Only && this.isStudent && !this.isAssistant) {
        this.privateChatReceiverList = [{
          userName: i18next.t('所有人'),
          userId: null,
          role: '',
        }];
        this.currentPrivateChatReceiver = this.privateChatReceiverList[0];
        TCIC.SDK.instance.reportLog('updatePrivateChatReceiver', `currentSilenceMode = ${this.currentSilenceMode}, 
      currentPrivateChatReceiver = ${JSON.stringify(this.currentPrivateChatReceiver)},
      `);
        return;
      }

      const isTeacherAndAssistant = this.isAssistant || this.isTeacher;
      const selfUserID = TCIC.SDK.instance.getUserId();
      const classInfo = TCIC.SDK.instance.getClassInfo();
      const newMemberList = this.memberList.map((member) => {
        const renderMember = { ...member };
        renderMember.colorName = `${member.userName}`;
        switch (true) {
          case classInfo.assistants.includes(member.userId):
            renderMember.role = 'assistant';
            renderMember.showTrophyIcon = false;
            break;
          case member.userId === classInfo.teacherId:
            renderMember.role = 'teacher';
            renderMember.showTrophyIcon = false;
            break;
          case member.role === 3:
            renderMember.role = 'supervisor';
            renderMember.showTrophyIcon = false;
            break;
          default:
            renderMember.role = 'student';
            renderMember.showTrophyIcon = true;
            break;
        }
        return renderMember;
      });
      let tempPrivateChatReceiverList = [];
      if (isTeacherAndAssistant) {
        if (TCIC.SDK.instance.isBigRoom()) {
          tempPrivateChatReceiverList = newMemberList.filter(member => member.role !== 'student' && member.userId !== selfUserID && member.currentStatus === TCIC.TMemberStatus.Online);
        } else {
          tempPrivateChatReceiverList = newMemberList.filter(member => member.userId !== selfUserID && member.currentStatus === TCIC.TMemberStatus.Online);
        }
      } else {
        tempPrivateChatReceiverList = newMemberList.filter(member => (member.role === 'assistant' || member.role === 'teacher') && member.userId !== selfUserID && member.currentStatus === TCIC.TMemberStatus.Online);
      }
      if (this.currentSilenceMode !== TCIC.TClassSilenceMode.Private_Only || isTeacherAndAssistant) {
        tempPrivateChatReceiverList.unshift({
          userName: i18next.t('所有人'),
          userId: null,
          role: '',
        });
        this.currentPrivateChatReceiver = {
          userName: i18next.t('所有人'),
          userId: null,
          role: '',
        };
      } else {
        if (!this.currentPrivateChatReceiver?.userId) {
          this.currentPrivateChatReceiver = tempPrivateChatReceiverList[0];
        }
      }
      this.privateChatReceiverList = tempPrivateChatReceiverList;
      TCIC.SDK.instance.reportLog('updatePrivateChatReceiver', `currentSilenceMode = ${this.currentSilenceMode}, 
      currentPrivateChatReceiver = ${JSON.stringify(this.currentPrivateChatReceiver)},
      `);
    },
    onRevokedMsg(msg) {

    },

    onSendMsg(msg) {

    },

    parseTipsMsg(msg) {
      let msgData = '';
      if (msg.msgType === TCIC.TIMMsgType.HandUpTips) {
        if (TCIC.SDK.instance.isInteractClass() || TCIC.SDK.instance.isUnitedClass()) {
          msgData = this.tipsHandUp;
        }
      } else {
        msgData = msg.data;
      }
      return msgData;
    },

    parseMsg(msg, inDataExt) {
      if (!msg.parsedTips) {
        msg.parsedTips = this.parseTipsMsg(msg);
      }
      if (!msg.parsedTime) {
        msg.parsedTime = Util.formatTime(msg.time, 'HH:mm');
      }
      if (!msg.parsedEmoji) {
        const contents = parseEmoji(msg.data);
        if (contents.length > 0) {
          msg.parsedEmoji = contents[0];
        } else {
          msg.parsedEmoji = {
            type: 1,
            content: msg.data,
          };
        }
      }
      // 解析 dataExt 里的回复消息
      if (!inDataExt && msg.dataExt?.replyMsg) {
        this.parseMsg(msg.dataExt?.replyMsg, true);
      }
    },

    // 各组件自己处理过滤逻辑，这里是添加过滤后的消息
    appendMsgs(msgs) {
      if (!Array.isArray(msgs)) {
        return;
      }

      msgs.forEach((msg) => {
        this.parseMsg(msg);
        this.msgList.push(msg);
      });
      if (this.msgList.length > this.maxMsgCount) {
        this.msgList = this.msgList.slice(-this.maxMsgCount);
      }

      const processMsgList = (list) => {
        const seqMap = new Map();
        const finalList = [];

        list.forEach((msg) => {
          if (msg.seq !== undefined) {
            const existingMsg = seqMap.get(msg.seq);

            if (!existingMsg) {
              seqMap.set(msg.seq, msg);
              finalList.push(msg);
            } else {
              if (existingMsg.id && msg.id && existingMsg.id !== msg.id) {
                finalList.push(msg);
              }
            }
          } else {
            finalList.push(msg);
          }
        });

        // 根据 time、seq 排序
        return finalList.sort((a, b) => a.time - b.time || (a.seq || 0) - (b.seq || 0));
      };


      if (this.showMsgList.length + msgs.length < this.pageSize * 2) {
        // 小量新增时合并新旧列表处理
        const combinedList = [...this.showMsgList, ...msgs];
        // this.showMsgList = processMsgList(combinedList);
        this.showMsgList = combinedList;
      } else {
        // 大量数据时处理整个msgList
        // this.showMsgList = processMsgList(this.msgList)
        //     .slice(-this.showMsgCount);
        this.showMsgList = this.msgList
            .slice(-this.showMsgCount);
      }
      // 新增代码结束 ====================================

      if (this.replayMsg) {
        const newReplyMsg = this.msgList.find(item => item.seq === this.replayMsg.seq);
        if (newReplyMsg) {
          this.replayMsg = newReplyMsg;
        } else {
          // 没找到也不用删 replayMsg，可能是之前的数据刷没了
        }
      }

      const imagePreviewInstance = TCIC.SDK.instance.getComponent('image-preview')?.getVueInstance();
      if (imagePreviewInstance?.getServerImgId() === this.openedImgSeq && imagePreviewInstance?.getComponentShowState()) {
        let index = -1;
        if (this.isSendToServer) {
          index = this.msgList.findIndex(item => item.seq === this.openedImgSeq);
        } else {
          index = this.msgList.findIndex(item => item.localImgSeq === this.openedImgSeq);
        }
        if (index === -1) {
          imagePreviewInstance.close();
          window.showToast(i18next.t('图片已被管理员删除'), 'error');
        }
      }
    },

    deleteMsgs(msgs) {
      if (!Array.isArray(msgs)) {
        return;
      }
      msgs.forEach((msg) => {
        let index = this.msgList.findIndex(item => item.key === msg.key);
        if (index !== -1) {
          this.msgList.splice(index, 1);
        }
        index = this.showMsgList.findIndex(item => item.key === msg.key);
        if (index !== -1) {
          this.showMsgList.splice(index, 1);
        }
      });
    },

    switchAllSilence() {
      TCIC.SDK.instance.silenceAll(!this.silenceAll)
        .catch((error) => {
          window.showToast(i18next.t('{{arg_0}}失败: {{arg_1}} {{arg_2}}', {
            arg_0: this.silenceAll ? i18next.t('解除全员禁言') : i18next.t('设置全员禁言'),
            arg_1: error.errorCode, arg_2: error.errorMsg,
          }), 'error');
        });
    },
    escapeDangerousTags(str) {
      if (!str) return '';

      return str
          .replace(/<(\/?)(script|iframe|object|embed|style|link|meta|img|xxwss)[^>]*?>/gi, match => match
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;'))
          .replace(/\s(on\w+)=/gi, ' data-$1=');
    },
    sendMsg(inputText) {
      return new Promise((resolve, reject) => {
        if (!inputText || !inputText.trim()) {
          // window.showToast(i18next.t('发送消息不能为空'), 'warning');
          reject();
          return;
        }

        if (TCIC.SDK.instance.isStudent()) {
          if (!this.chatEnabled) {
            window.showToast(i18next.t('你已被禁言'), 'warning');
            reject();
            return;
          }
          switch (this.currentSilenceMode) {
            case TCIC.TClassSilenceMode.Public_Only:
              this.currentPrivateChatReceiver = {
                userName: i18next.t('所有人'),
                userId: null,
                role: '',
              };
              break;
            case TCIC.TClassSilenceMode.Private_Only:
              if (!this.currentPrivateChatReceiver || !this.currentPrivateChatReceiver.userId) {
                window.showToast(i18next.t('仅允许私聊'), 'warning');
                reject();
                return;
              }
              break;
            case TCIC.TClassSilenceMode.All_Mute:
              window.showToast(i18next.t('{{arg_0}}已设置全员禁言', { arg_0: this.roleInfo.teacher }), 'warning');
              reject();
              return;
          }
        }
        if (this.msgSending) {
          reject();
          return;
        }
        this.msgSending = true;
        TCIC.SDK.instance.reportLog('sendMessageAction', `currentSilenceMode = ${this.currentSilenceMode}, 
      currentPrivateChatReceiver = ${JSON.stringify(this.currentPrivateChatReceiver)},
      message = ${this.escapeDangerousTags(inputText)},
      type = file,
      replyMsg = ${JSON.stringify(this.replyMsg)},
      `);
        TCIC.SDK.instance
          .sendGroupTextMessage(this.escapeDangerousTags(inputText), this.msgDataExt)
          .then((res) => {
            this.msgSending = false;
            this.setReplyMsg(null);
            resolve();
          })
          .catch((error) => {
            this.msgSending = false;
            TCIC.SDK.instance.reportEvent('sendGroupTextMessage', `fail, code: ${error?.code}, error: ${JSON.stringify(error)}`, (error?.code || -1), +new Date());
            if (error && error.code) {
              switch (error.code) {
                case 10017:
                  window.showToast(i18next.t('你已被禁言'), 'warning');
                  break;
                case 80001: // 敏感字
                  window.showToast(i18next.t('请文明发言'), 'warning');
                  break;
                default:
                  console.error('sendMsg error', error);
                  window.showToast(i18next.t('消息发送失败，请稍后再试'), 'warning');
              }
            }
            reject();
          });
      });
    },

    sendFileMsg(file, fileUrl, msg) {
      return new Promise((resolve, reject) => {
        if (TCIC.SDK.instance.isStudent()) {
          if (!this.chatEnabled) {
            window.showToast(i18next.t('你已被禁言'), 'warning');
            reject();
            return;
          }
          switch (this.currentSilenceMode) {
            case TCIC.TClassSilenceMode.Public_Only:
              this.currentPrivateChatReceiver = {
                userName: i18next.t('所有人'),
                userId: null,
                role: '',
              };
              break;
            case TCIC.TClassSilenceMode.Private_Only:
              if (!this.currentPrivateChatReceiver || !this.currentPrivateChatReceiver.userId) {
                window.showToast(i18next.t('仅允许私聊'), 'warning');
                reject();
                return;
              }
              break;
            case TCIC.TClassSilenceMode.All_Mute:
              window.showToast(i18next.t('{{arg_0}}已设置全员禁言', { arg_0: this.roleInfo.teacher }), 'warning');
              reject();
              return;
          }
        }
        if (this.msgSending) {
          reject();
          return;
        }
        this.msgSending = true;
        this.fileSending = true;   //  发送消息前就应该设置发送状态，避免连发
        TCIC.SDK.instance.reportLog('sendMessageAction', `currentSilenceMode = ${this.currentSilenceMode}, 
      currentPrivateChatReceiver = ${JSON.stringify(this.currentPrivateChatReceiver)},
      message = ${fileUrl},
      type = file,
      replyMsg = ${JSON.stringify(this.replyMsg)},
      `);
        TCIC.SDK.instance.sendGroupFileMessage(
            file,
            fileUrl,
            async (imgID, percent) => {
              console.log(`===>>> : debug file percent: ${imgID}, ${percent}`);
              this.imgPercent = parseInt(percent * 100, 10);
              this.imgPercent += '%';
              TCIC.SDK.instance.setState(Constant.TStateFileSendStatus, {
                currentID: imgID,
                per: this.imgPercent,
                imgState: MsgImageState.Sending,
              });
            },
            this.msgDataExt,
        )
            .then((res) => {
              this.msgSending = false;
              this.setReplyMsg(null);
              const imgId = TCIC.SDK.instance.getCurrnetImgId();
              TCIC.SDK.instance.reportLog('sendGroupImgMessage', `success, imgId: ${imgId}, res: ${JSON.stringify(res)}`);
              resolve();
              this.imgPercent = '100%';
              TCIC.SDK.instance.setState(Constant.TStateFileSendStatus, {
                currentID: imgId,
                per: this.imgPercent,
                imgState: MsgImageState.Success,
              });
              this.fileSending = false;
              // 测试失败
              // TCIC.SDK.instance.setState(Constant.TStateImgSendStatus, {
              //   currentID: TCIC.SDK.instance.getCurrnetImgId(),
              //   per: '',
              //   imgState: MsgImageState.Fail,
              // });
            })
            .catch((error) => {
              this.msgSending = false;
              const imgId = TCIC.SDK.instance.getCurrnetImgId();
              TCIC.SDK.instance.reportLog('sendGroupImgMessage', `fail, imgId: ${imgId}, code: ${error?.code}, error: ${JSON.stringify(error)}`);
              if (error && error.code) {
                switch (error.code) {
                  case 2253:
                    window.showToast(i18next.t('图片大小超过20M，无法发送'), 'warning');
                    break;
                  case 10016: // 敏感字
                    window.showToast(i18next.t('敏感信息，请谨慎发送'), 'warning');
                    break;
                  default:
                    console.error('sendImgMsg error', error);
                    window.showToast(i18next.t('消息发送失败，请稍后再试'), 'warning');
                }
              }
              this.imgPercent = '';
              TCIC.SDK.instance.setState(Constant.TStateFileSendStatus, {
                currentID: imgId,
                per: '',
                imgState: MsgImageState.Fail,
              });
              this.fileSending = false;
              reject();
            });
      });
    },

    sendImgMsg(imgFile, imgUrl, msg) {
      return new Promise((resolve, reject) => {
        if (TCIC.SDK.instance.isStudent()) {
          if (!this.chatEnabled) {
            window.showToast(i18next.t('你已被禁言'), 'warning');
            reject();
            return;
          }
          switch (this.currentSilenceMode) {
            case TCIC.TClassSilenceMode.Public_Only:
              this.currentPrivateChatReceiver = {
                userName: i18next.t('所有人'),
                userId: null,
                role: '',
              };
              break;
            case TCIC.TClassSilenceMode.Private_Only:
              if (!this.currentPrivateChatReceiver || !this.currentPrivateChatReceiver.userId) {
                window.showToast(i18next.t('仅允许私聊'), 'warning');
                reject();
                return;
              }
              break;
            case TCIC.TClassSilenceMode.All_Mute:
              window.showToast(i18next.t('{{arg_0}}已设置全员禁言', { arg_0: this.roleInfo.teacher }), 'warning');
              reject();
              return;
          }
        }
        if (this.msgSending) {
          reject();
          return;
        }
        this.msgSending = true;
        this.imgSending = true;   //  发送消息前就应该设置发送状态，避免连发
        TCIC.SDK.instance.reportLog('sendMessageAction', `currentSilenceMode = ${this.currentSilenceMode}, 
      currentPrivateChatReceiver = ${JSON.stringify(this.currentPrivateChatReceiver)},
      message = ${imgUrl},
      type = image,
      replyMsg = ${JSON.stringify(this.replyMsg)},
      `);
        TCIC.SDK.instance.sendGroupImgMessage(
          imgFile,
          imgUrl,
          msg,
          async (imgID, percent) => {
            console.log(`===>>> : debug img percent: ${imgID}, ${percent}`);
            this.imgPercent = parseInt(percent * 100, 10);
            this.imgPercent += '%';
            TCIC.SDK.instance.setState(Constant.TStateImgSendStatus, {
              currentID: imgID,
              per: this.imgPercent,
              imgState: MsgImageState.Sending,
            });
          },
          this.msgDataExt,
        )
          .then((res) => {
            this.msgSending = false;
            this.setReplyMsg(null);
            const imgId = TCIC.SDK.instance.getCurrnetImgId();
            TCIC.SDK.instance.reportLog('sendGroupImgMessage', `success, imgId: ${imgId}, res: ${JSON.stringify(res)}`);
            resolve();
            this.imgPercent = '100%';
            TCIC.SDK.instance.setState(Constant.TStateImgSendStatus, {
              currentID: imgId,
              per: this.imgPercent,
              imgState: MsgImageState.Success,
            });
            this.imgSending = false;
            // 测试失败
            // TCIC.SDK.instance.setState(Constant.TStateImgSendStatus, {
            //   currentID: TCIC.SDK.instance.getCurrnetImgId(),
            //   per: '',
            //   imgState: MsgImageState.Fail,
            // });
          })
          .catch((error) => {
            this.msgSending = false;
            const imgId = TCIC.SDK.instance.getCurrnetImgId();
            TCIC.SDK.instance.reportLog('sendGroupImgMessage', `fail, imgId: ${imgId}, code: ${error?.code}, error: ${JSON.stringify(error)}`);
            if (error && error.code) {
              switch (error.code) {
                case 2253:
                  window.showToast(i18next.t('图片大小超过20M，无法发送'), 'warning');
                  break;
                case 10016: // 敏感字
                  window.showToast(i18next.t('敏感信息，请谨慎发送'), 'warning');
                  break;
                default:
                  console.error('sendImgMsg error', error);
                  window.showToast(i18next.t('消息发送失败，请稍后再试'), 'warning');
              }
            }
            this.imgPercent = '';
            TCIC.SDK.instance.setState(Constant.TStateImgSendStatus, {
              currentID: imgId,
              per: '',
              imgState: MsgImageState.Fail,
            });
            this.imgSending = false;
            reject();
          });
      });
    },

    reSendImgMsg(msg) {
      if (this.imgSending) {
        window.showToast(i18next.t('正在重新发送图片请稍后'));
        return;
      }
      this.imgSending = true;
      if (this.failSendFileMap.has(msg.localImgSeq)) {
        const file = this.failSendFileMap.get(msg.localImgSeq);
        if (typeof file === 'object' && file instanceof File) {
          this.imgUrl = window.URL.createObjectURL(file);
        } else {
          this.imgUrl = window.URL.createObjectURL(file.files[0]);
        }
        this.sendImgMsg(file, this.imgUrl, msg);
      } else {
        window.showToast(i18next.t('图片发送失败'));
        this.imgSending = false;
      }
    },

    // 解析是否是发送失败的图片
    parseReSendImgSeq(seq) {
      if (this.failSendFileMap.has(seq)) {
        return true;
      }
      return false;
    },

    handleImgSendState(postMsg, file) {
      if (postMsg?.imgState === MsgImageState.Sending || postMsg?.imgState === MsgImageState.Fail) {
        if (!this.failSendFileMap.has(postMsg.currentID)) {
          this.failSendFileMap.set(postMsg.currentID, file);
        }
      } else if (postMsg.imgState === MsgImageState.Success) {
        if (this.failSendFileMap.has(postMsg.currentID)) {
          this.failSendFileMap.delete(postMsg.currentID);
        }
      }
    },

    openPreviewImg(msg) {
      // 释放前一个
      window.URL.revokeObjectURL(this.previewImgUrl);

      if (TCIC.SDK.instance.isElectron()) {
        const screenShare = TCIC.SDK.instance.getState(TCIC.TMainState.Screen_Share, 2);
        const isSubCameraStarted = TCIC.SDK.instance.getState(Constant.TStateStartSubCamera, false);
        const vodPlay = TCIC.SDK.instance.getState(TCIC.TMainState.Vod_Play, 2);
        if (vodPlay < 2) {
          window.showToast(i18next.t('请关闭音视频{{arg_0}}后，查看图片', { arg_0: this.roomInfo.courseware }), 'warning');
          return;
        }
        if (isSubCameraStarted) {
          window.showToast(i18next.t('请关闭辅助摄像头后，查看图片'), 'warning');
          return;
        }
        if (screenShare < 2) {
          window.showToast(i18next.t('请结束屏幕后，查看图片'), 'warning');
          return;
        }
      }

      console.log('openPreviewImg', msg);
      const imagePreviewInstance = TCIC.SDK.instance.getComponent('image-preview')?.getVueInstance();
      imagePreviewInstance.setServerImgId(msg.seq);
      imagePreviewInstance.setLocalPerViewImgId(msg.localImgSeq || 0);
      imagePreviewInstance.setComponentShowState(true);
      this.openedImgSeq = msg.seq;
      this.isSendToServer = msg.imgSendSucessFlag === undefined ? true : msg.imgSendSucessFlag;

      if (!msg.ownMsg || !msg.localImgSeq) {
        // 别人发的图片，或者自己本次进房前发的图片,预览优先查看原图
        imagePreviewInstance.showWithUrl(msg.data || msg.preview1 || msg.preview2);
        return;
      }

      // 自己本次进房后发的图片
      const localSeq = msg.localImgSeq;
      if (this.failSendFileMap.has(localSeq)) {
        // 发送失败的图片预览
        const file = this.failSendFileMap.get(localSeq);
        if (typeof file === 'object' && file instanceof File) {
          this.previewImgUrl = window.URL.createObjectURL(file);
        } else {
          this.previewImgUrl = window.URL.createObjectURL(file.files[0]);
        }
        imagePreviewInstance.showWithUrl(this.previewImgUrl);
      } else {
        // 发送成功或者正在发送中
        const imgMsg = this.msgList.find(item => item.localImgSeq === localSeq);
        if (imgMsg) {
          /**
           * 已经发送成功时，会有预览地址，使用预览地址查看图片，本地地址会被回收，引起异常
           * todo这返回的是cos地址，海外访问性能待验证
           */
          imagePreviewInstance.showWithUrl(imgMsg.preview1 || imgMsg.preview2);
        }
      }
    },
    closePreviewImg(localSeq) {
      window.URL.revokeObjectURL(this.previewImgUrl);
    },

    deleteMsg(msg) {
      TCIC.SDK.instance.revokeMessage(msg.seq)
        .then(() => {
          console.debug(`recallMessage->success , msg.seq: ${msg.seq}`);
          window.showToast(i18next.t('删除了一条消息'), 'success');
        })
        .catch((errInfo) => {
          window.showToast(i18next.t('删除消息失败 ({{arg_0}})', { arg_0: JSON.stringify(errInfo) }), 'warning');
        });
    },

    deleteOpenClassMsg(msg) {
      TCIC.SDK.instance.sendGroupCustomMessage('TXOpenclassDeleteExt', `${msg.seq}`)
        .then(() => {
          console.debug(`recallOpenclassMessage->success , msg.seq: ${msg.seq}`);
          window.showToast(i18next.t('删除了一条消息'), 'success');
        })
        .catch((errInfo) => {
          window.showToast(i18next.t('删除消息失败 ({{arg_0}})', { arg_0: JSON.stringify(errInfo) }), 'warning');
        });
    },

    silenceMsg(msg, silence) {
      TCIC.SDK.instance.memberAction({
        actionType: silence ? TCIC.TMemberActionType.Silence : TCIC.TMemberActionType.Silence_Cancel,
        userId: msg.from,
      })
        .then(() => {
        })
        .catch((err) => {
          window.showToast(err.errorMsg, 'error');
        });
    },
    handleSilenceModeUpdate(silenceMode) {
      TCIC.SDK.instance.setSilenceMode(silenceMode).catch((error) => {
        if (error.errorCode === 101) {
          window.showToast(i18next.t('操作过于频繁，请稍后重试。'), 'error');
        } else {
          window.showToast(i18next.t('{{arg_0}}失败: {{arg_1}} {{arg_2}}', {
            arg_0: i18next.t('设置聊天状态'),
            arg_1: error.errorCode, arg_2: error.errorMsg,
          }), 'error');
        }
          });
    },
    async setReplyMsg(msg) {
      if (!msg && !this.replyMsg) return;
      if (msg && this.replyMsg && msg.id === this.replyMsg.id) return;
      this.replyMsg = msg;
      this.$EventBus.$emit('set-reply-msg', msg);

      if (msg?.localImgSeq && !msg?.imgSendSucessFlag) {
        // 图片还没发送成功
        return;
      }
      if (this.msgSending) {
        // 发送中
        return;
      }
      TCIC.SDK.instance.reportLog('setReplyMsg', JSON.stringify(msg));
      if (!msg || !msg.seq) {
        this.replyMsg = null;
        return;
      }
      this.replyMsg = msg;
      if (!!this.replyMsg) {
        if (!!msg.dataExt?.IsPrivateMsg && this.supportPrivateChat) {
          const ownMsg = msg.from === TCIC.SDK.instance.getUserId();
          const targetUserId = ownMsg ? msg.dataExt?.PrivateInfo?.To.ID : msg.dataExt?.PrivateInfo?.From.ID;
          this.currentPrivateChatReceiver = this.privateChatReceiverList.find(item => item.userId === targetUserId);
          if (!this.currentPrivateChatReceiver) {
            this.currentPrivateChatReceiver = this.memberList.find(item => item.userId === targetUserId);
          }
          if (!this.currentPrivateChatReceiver) {
            this.currentPrivateChatReceiver = await this.getMember(targetUserId);
          }
          if (!this.currentPrivateChatReceiver) {
            this.currentPrivateChatReceiver = {
              userName: targetUserId,
              userId: targetUserId,
              role: '',
            };
          }
        } else if (!msg.dataExt?.IsPrivateMsg && this.supportPrivateChat && this.currentSilenceMode === TCIC.TClassSilenceMode.Private_Only && this.isStudent && !this.isAssistant) {
          this.currentPrivateChatReceiver = this.privateChatReceiverList[0] || null;
        } else {
          this.currentPrivateChatReceiver = {
            userName: i18next.t('所有人'),
            userId: null,
            role: '',
          };
        }
      }
    },
  },
};
