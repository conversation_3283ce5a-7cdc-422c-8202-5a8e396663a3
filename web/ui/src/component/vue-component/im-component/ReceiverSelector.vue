<template>
  <div
    class="receiver-selector"
    :class="{ disabled: disabled, 'small-screen': isSmallScreen }"
  >
    <!-- 选中用户展示区 -->
    <span class="selected-receiver-label">{{ $t("发送至") }}</span>
    <div
      :class="['selected-receiver', { 'small-screen': isSmallScreen }]"
      @click="toggleDropdown"
    >
      <span
        class="receiver-icon"
        :class="getIconClass(currentReceiver)"
        :style="getAvatarStyle(currentReceiver)"
      >
        <IconAllMember v-if="getIconClass(currentReceiver) === 'default-all-avatar'" />
      </span>
      <span
        v-if="currentReceiver"
        class="receiver-name"
      >
        {{ currentReceiver ? (currentReceiver.userName || currentReceiver.userId) : $t("所有人") }}
      </span>
      <span
        v-else
        class="receiver-name"
        style="opacity: 0.6"
      >
        {{ $t('加载中...') }}
      </span>
      <span
        v-if="currentReceiver"
        class="arrow-icon"
        :class="{ open: showDropdown }"
      >▾</span>
    </div>

    <!-- 弹出框，搜索框和用户列表 -->
    <div
      v-if="showDropdown"
      class="dropdown"
    >
      <input
        v-model="keyword"
        class="search-input"
        type="text"
        :placeholder="$t('请输入用户')"
        :disabled="disabled"
      >
      <ul class="receiver-list">
        <li
          v-for="receiver in filteredReceivers"
          :key="receiver.userId"
          :class="{ selected: receiver.userId === currentReceiver?.userId }"
          @click="selectReceiver(receiver)"
        >
          <span
            class="receiver-avatar"
            :class="getIconClass(receiver)"
            :style="getAvatarStyle(receiver)"
          >
            <IconAllMember v-if="getIconClass(receiver) === 'default-all-avatar'" />
          </span>
          <span
            class="receiver-name-in-list"
          >{{ receiver.userName }}</span>
          <span
            class="role-label"
            :class="getRoleClass(receiver.role)"
          >
            {{ getRoleText(receiver.role) }}
          </span>
        </li>
      </ul>
    </div>
  </div>
</template>


<script>
import i18next from 'i18next';
import BaseComponent from '@/component/core/BaseComponent';
import IconAllMember from './assets/svg-component/all_members.svg';

export default {
  name: 'ReceiverSelector',
  components: {
    IconAllMember,
  },
  extends: BaseComponent,
  props: {
    privateChatReceiverList: {
      type: Array,
      required: true,
    },
    currentReceiver: {
      type: Object,
      required: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      keyword: '',
      showDropdown: false,
    };
  },
  computed: {
    filteredReceivers() {
      if (!this.keyword) {
        return this.privateChatReceiverList;
      }
      const lowerKeyword = this.keyword.toLowerCase();
      return this.privateChatReceiverList.filter(receiver => receiver.userName.toLowerCase().includes(lowerKeyword));
    },
  },
  watch: {
    disabled(newVal) {
      if (newVal && this.showDropdown) {
        this.showDropdown = false;
      }
    },
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    handleClickOutside(event) {
      if (this.$el && !this.$el.contains(event.target)) {
        this.showDropdown = false;
      }
    },
    toggleDropdown() {
      if (this.disabled || !this.currentReceiver) return;
      this.showDropdown = !this.showDropdown;
    },
    selectReceiver(receiver) {
      this.$emit('update:currentReceiver', receiver);
      this.showDropdown = false;
    },
    getRoleText(role) {
      switch (role) {
        case 'teacher':
          return i18next.t('老师');
        case 'assistant':
          return i18next.t('助教');
        case 'student':
          return '';
        default:
          return '';
      }
    },
    getRoleClass(role) {
      switch (role) {
        case 'teacher':
          return 'teacher';
        case 'assistant':
          return 'assistant';
        default:
          return 'none';
      }
    },
    getIconClass(receiver) {
      if (!receiver || !receiver.userId) {
        return 'default-all-avatar';
      }
      if (!receiver.avatar) {
        return 'default-member-avatar';
      }
      return '';
    },
    // 动态设置背景图
    getAvatarStyle(receiver) {
      if (!receiver) {
        return {
          opacity: 0.6,
        };
      }
      if (receiver && receiver.avatar) {
        return {
          backgroundImage: `url('${receiver.avatar}')`,
          backgroundSize: 'cover',
        };
      }
      return {};
    },
  },
};
</script>

<style lang="less">
.light {
  .receiver-selector {
    --receiver-bg-color: rgb(240,242,248);
    --text-color: #1D2029;
    --active-bg-color: #D1D9EC;
    --search-bg-color: #F8F8FD;
    --placeholder-color: #9CA3AF;
  }
}
.default-all-avatar {
  background-size: 65%;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  // background-image: url('./assets/all_members.svg') !important;
}

.default-member-avatar {
  background-size: 65%;
  background-repeat: no-repeat;
  background-image: url('./assets/single_member.svg') !important;
}
.receiver-selector {
  position: relative;
  width: 200px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: start;

  &.small-screen {
    width: 180px;
  }

  .selected-receiver-label {
    align-self: center;
    margin: 0 8px 0 0;
    color: var(--text-color, #C5CCDB);
    min-width: 36px;
  }

  .receiver-icon {
    width: 22px;
    height: 22px;
    border-radius: 50%;
    margin: 6px;
    background-position: center;
    background-image: none;
  }

  .receiver-name {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    max-height: 36px;
    line-height: 18px;
  }

  .selected-receiver {
    flex-grow: 1;
    font-size: 12px;
    display: flex;
    align-items: center;
    background-color: var(--receiver-bg-color, #2a2d3d);
    padding: 0 6px;
    border-radius: 5px;
    cursor: pointer;
    color: var(--text-color, #CFD4E5);
    .arrow-icon {
      margin-left: auto;
      transition: transform 0.2s;
      transform: rotate(180deg);
    }
    .arrow-icon.open {
      transform: rotate(00deg);
    }
    &.small-screen {
      height: 27px;
      border-radius: 26px;
    }
  }

  .dropdown {
    position: absolute;
    bottom: 130%;
    left: 0;
    background-color: var(--receiver-bg-color, #2a2d3d);
    border-radius: 5px;
    padding: 10px 4px;
    margin-top: 10px;
    width: 100%;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    z-index: 10;

    .search-input {
      width: calc(100% - 40px);
      padding: 5px 30px 5px 10px;
      border-radius: 6px;
      border: none;
      background-color: var(--search-bg-color, #1f2233);
      color: var(--text-color, #d1d5db);
      outline: none;
      background-image: url('./assets/search.svg');
      background-repeat: no-repeat;
      background-size: 16px 16px;
      background-position: right 10px center;
    }

    .search-input::placeholder {
      color: var(--placeholder-color, #6b7280);
    }


    .receiver-list {
      max-height: 200px;
      overflow-y: auto;
      span {
        color: var(--text-color, #CFD4E5);
      }

      li {
        display: flex;
        align-items: center;
        padding: 5px 0;
        cursor: pointer;
        border-radius: 5px;
        transition: background-color 0.2s;
        width: 100%;
        gap: 4px;

        &:hover {
          background-color: var(--active-bg-color, #383c51);
        }

        &.selected {
          background-color: var(--active-bg-color, #41475f);
        }

        .receiver-avatar {
          width: 26px;
          height: 26px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          margin: 4px 0 4px 4px;
          background-position: center;
          background-image: none;
          flex-shrink: 0;
        }

        .receiver-name-in-list{
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .role-label {
          flex-shrink: 0;
          margin-left: 4px;
          margin-right: 7px;
          min-width: 32px;
          padding: 2px 4px;
          font-size: 10px;
          border-radius: 4px;
          color: #fff;
          text-align: center;

          &.teacher {
            background-color: #3e8ad8;
          }

          &.assistant {
            background-color: #5ab6ae;
          }

          &.none {
            display: none;
            width: 0;
          }
        }

      }
    }
  }
}
.receiver-selector.disabled {
  opacity: 0.7;
  pointer-events: none; /* 禁止交互 */
}
</style>
