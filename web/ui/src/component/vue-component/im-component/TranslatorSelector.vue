<template>
  <div class="translator-selector">
    <el-popover
      ref="popover"
      popper-class="translator-selector-popover"
      placement="top-start"
      width="226"
      trigger="click"
    >
      <i
        slot="reference"
        class="ic-translator"
      >
        <IconTranslator v-if="!active" />
        <IconTranslatorActive v-if="active" />
        <!-- <img
          v-show="!active"
          src="./assets/translator.svg"
          alt=""
        >
        <img
          v-show="active"
          src="./assets/translator-active.svg"
          alt=""
        > -->
      </i>
      <div
        class="translator-popper-content"
      >
        <div class="header">
          <p class="title">
            {{ $t('翻译聊天记录') }}
          </p>
          <el-switch
            v-model="active"
            class="language-model-switch"
            active-color="#13A449"
            @change="toggle"
          />
        </div>
        <p class="desc">
          {{ $t('翻译成') }}：
        </p>
        <ul
          class="selector"
          :class="{'inactive': !active}"
        >
          <li
            v-for="(item, index) in translatorList"
            :key="index"
          >
            <a
              :class="{active: item.value === selectedLanguage}"
              @click="selectLanguage(item)"
            >
              {{ $t(item.name) }}
            </a>
          </li>
        </ul>
      </div>
    </el-popover>
  </div>
</template>

<script>
import i18next from 'i18next';
import Constant from '@/util/Constant';
import Util from '@util/Util';
import BaseComponent from '../../core/BaseComponent';
import IconTranslator from './assets/svg-component/translator.svg';
import IconTranslatorActive from './assets/svg-component/translator-active.svg';

const Languages = [
  {
    name: '中文(简体)',
    value: 'zh',
  },
  {
    name: '英语',
    value: 'en',
  },
  {
    name: '日语',
    value: 'ja',
  },
  {
    name: '韩语',
    value: 'ko',
  },
  {
    name: '法语',
    value: 'fr',
  },
  {
    name: '西班牙语',
    value: 'es',
  },
  {
    name: '意大利语',
    value: 'it',
  },
  {
    name: '德语',
    value: 'de',
  },
  {
    name: '土耳其语',
    value: 'tr',
  },
  {
    name: '俄语',
    value: 'ru',
  },
  {
    name: '葡萄牙语',
    value: 'pt',
  },
  {
    name: '越南语',
    value: 'vi',
  },
  {
    name: '印尼语',
    value: 'id',
  },
  {
    name: '泰语',
    value: 'th',
  },
  {
    name: '马来语',
    value: 'ms',
  },
];
export default {
  name: 'TranslatorSelector',
  components: {
    IconTranslator,
    IconTranslatorActive,
  },
  extends: BaseComponent,
  data() {
    return {
      classId: null,
      active: false,
      selectedLanguage: 'zh',
      isPad: TCIC.SDK.instance.isPad(),
      isMobile: TCIC.SDK.instance.isMobile(),
      translatorList: Languages,
      classStatus: -1,
    };
  },
  mounted() {
    const enable = localStorage.getItem('translator_enable');
    const language = localStorage.getItem('translator_language');
    if (enable) {
      this.active = enable === 'true';
    }
    if (language) {
      this.selectedLanguage = language;
    }
    if (this.active) {
      this.applySetTranslate(this.active, this.selectedLanguage);
    }
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Translate_Msg_Notify, this.onTranslateMsgNotify);
  },
  beforeDestroy() {
  },
  methods: {
    toggle(active) {
      this.active = active;
      this.applySetTranslate(active, this.selectedLanguage);
      this.$EventBus.$emit('toggle-translator', active);
    },
    async selectLanguage(item) {
      // 如果已经开启了，先关闭当前语言，再开启新的语言
      if (this.active) {
        await this.applySetTranslate(false, this.selectedLanguage);
      }
      this.selectedLanguage = item.value;
      await this.applySetTranslate(this.active, this.selectedLanguage);
    },

    async applySetTranslate(active, language) {
      try {
        await TCIC.SDK.instance.setTranslate(active ? 1 : 0, language || this.selectedLanguage);
        localStorage.setItem('translator_enable', active);
        if (language) {
          localStorage.setItem('translator_language', language);
        }
      } catch (e) {
        window.showToast(i18next.t('开启翻译机器人失败，请稍后再试'));
      }
    },
    onTranslateMsgNotify(msg) {
      if (!this.active) return;
      const match = msg.results.find(item => item.lang === this.selectedLanguage);
      console.log('onTranslateMsgNotify', msg, match, this.selectedLanguage);
      if (!this.$GlobalData.translateMessages) {
        this.$GlobalData.translateMessages = {};
      }
      if (match) {
        const message = {
          seq: msg.seq,
          content: match.result,
        };
        this.$GlobalData.translateMessages[msg.seq] = message;
        this.$EventBus.$emit('translate', message);
      }
    },
  },
};
</script>
<style lang="less">
.dark .el-popover.translator-selector-popover {
  padding: 0;
}

.translator-selector {
  &.small-screen {
    .ic-translator {
      width: 20px;
      height: 20px;
      margin: 4px;
      img {
        width: 100%;
      }
    }
  }
  .ic-translator {
    width: 16px;
    height: 16px;
    margin: 0px;
    img {
      width: 100%;
    }
  }
}
.ic-translator {
  display: flex;
  width: 30px;
  height: 30px;
  margin: 0 20px;
  opacity: .9;
  align-items: center;
  justify-content: center;
  &:hover {
    cursor: pointer;
    opacity: 1;
  }
  img {
    width: 30px;
    height: 30px;
  }
}
.translator-popper-content {
  .header {
    display: flex;
    height: 56px;
    align-content: center;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    box-shadow: 0 1px 0 0 rgba(255,255,255,0.08);
    .title {
      color: var(--text-color, rgba(255,255,255,0.60));
      font-size: 14px;
    }
    .el-switch {
      &.language-model-switch {
        height: 24px;

        .el-switch__label {
          color: #fff;

          &.is-active {
            color: #fff;
          }

          span {
            font-size: 16px;
          }
        }

        .el-switch__core {
          border-width: 2px;
          width: 40px !important;
          height: 22px !important;
          border-color: var(--switch-border-color, #FFFFFF) !important;
          background-color: var(--switch-bg-color, #FFFFFF) !important;

          &:after {
            top: 0;
            left: 0;
            width: 18px;
            height: 18px;
            background-color: rgba(0,0,0,0.14);
          }
        }
        &.is-checked .el-switch__core {
          border-color: #006EFF !important;
          background-color: #006EFF !important;
          &:after {
            left: 36px;
            background-color: #FFFFFF;
          }
        }

      }
    }
  }
  .desc {
    color: var(--text-color, rgba(255,255,255,0.60));
    font-size: 14px;
    padding: 10px 16px 5px;
  }
  ul {
    opacity: 1;
    height: 55vh;
    max-height: 300px;
    overflow: auto;
    li {
      width: 100%;
      height: 32px;
      margin: 0;
      padding: 0;
      a {
        display: block;
        width: 100%;
        height: 100%;
        font-size: 14px;
        color: var(--text-color, #FFFFFF);
        letter-spacing: 0;
        line-height: 32px;
        text-indent: 48px;
        position: relative;
        &:hover {
          background: #006EFF;
          --text-color: #FFFFFF;
        }
        &.active {
          background: #006EFF;
          --text-color: #FFFFFF;
          &:before {
            content:'\e6da';
            font-family: 'element-icons';
            color: var(--text-color,rgb(69, 61, 61));
            width: 30px;
            height: 30px;
            left: -20px;
            position: absolute;
          }
        }
      }
    }
    &.inactive {
      opacity: .5;
      pointer-events: none;
      li{
        a {
          pointer-events: none;
          &.active {
            background: transparent;
            --text-color: #006EFF;
          }
        }
      }
    }
  }
}
</style>
