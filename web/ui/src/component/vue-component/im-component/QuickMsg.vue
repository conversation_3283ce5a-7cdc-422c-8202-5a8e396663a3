<template>
  <div v-if="msg">
    <li
      :class="['quick-im-component-msg', {
        'small-screen': isSmallScreen
      }]"
    >
      <!-- <div class="filter-blur-bg" /> -->
      <!-- <img
          :class="['quick-im-component-msg-avatar', {
            'student-avatar-default': !msg.teacherMsg,
            'teacher-avatar-default': msg.teacherMsg
          }]"
          :src="msg.avatar"
      />
       -->
      <div
        class="quick-im-component-msg-body"
        :class="{ 'quick-im-component-msg-body-tips': tipsFlag && !userPermission }"
      >
        <i
          v-if="msg.msgType === 'text' && (msg.teacherMsg || msg.assistantMsg)"
          :class="[msg.teacherMsg ? 'teacher-icon' : 'assistant-icon', 'role-icon']"
        >
          <span>
            {{ msg.teacherMsg ? $t(roleInfo.teacher) : $t(roleInfo.assistant) }}
          </span>
        </i>
        <span
          :class="[handup ? 'quick-im-component-msg-user-handup' : 'quick-im-component-msg-user-name',
                   tipsFlag ? 'quick-im-component-msg-tips' :
                   (isSmallScreen ? 'quick-im-component-msg-text' :'')]"
        >{{ tips }}</span>
        <span>{{ name }} </span>
        <span
          v-if="isPrivateMsg"
          class="quick-im-component-msg-user-private-msg-label"
        >{{ $t("（私聊）") }}</span>
        <template v-for="(item, sub_index) in parse(msg.data)">
          <template v-if="item.type === 1">
            <span
              v-for="(part, i) in formatContent(item.content)"
              :key="sub_index + '-' + i"
            >
              <a
                v-if="part.type === 'link'"
                class="tcic-link"
                :href="part.content"
                target="_blank"
                rel="noopener noreferrer"
                style="color: #4A90E2; text-decoration: underline;"
              >{{ part.content }}</a>
              <span
                v-else
                v-text="part.content"
              />
            </span>
          </template>

          <span
            v-else-if="item.type === 2"
            :key="sub_index"
            :class="[
              item.imageClass,
              isSmallScreen ? 'qucik-im-emoji-style-smallscreen' : 'qucik-im-emoji-style'
            ]"
          />
        </template>
      </div>
      <span class="quick-im-component-handup-body">
        <QuickMsgHandUpOperator
          v-if="isTeacher || isAssistant || isSupervisor"
          :user-permission="userPermission"
        />
      </span>
    </li>
  </div>
</template>

<script>
import i18next from 'i18next';
import BaseComponent from '@core/BaseComponent';
import { parseEmoji } from './parseEmoji';

import QuickMsgHandUpOperator from './QuickMsgHandUpOperator.vue';

export default {
  components: {
    QuickMsgHandUpOperator,
  },
  extends: BaseComponent,

  props: {
    msg: {
      type: Object,
      default() {
        return {};
      },
    },
    index: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      isTeacher: false,
      isAssistant: false,
      isSupervisor: false,
      userPermission: null,
      className: '',
      // 是否举手消息
      handup: false,
      tips: '',
      name: '',
      // 是否提示消息
      tipsFlag: false,
      content: '',
      roleInfo: null,
      logo: '',
      isPrivateMsg: false,
      supportUrlLink: false,
      handleLinkClick: null,
    };
  },

  created() {
    // mounted 时才赋值太晚了，会报错
    this.roleInfo = TCIC.SDK.instance.getRoleInfo().roleInfo;
  },

  mounted() {
    this.tips = this.parseType(this.msg);
    this.name = this.parseName(this.msg);
    this.content = this.parseContent(this.msg);
    const { className } = TCIC.SDK.instance.getClassInfo();
    this.className = className;
    const { customContent } = TCIC.SDK.instance.getSchoolInfo();
    this.logo = customContent.logo ? customContent.logo : 'https://class.qcloudclass.com/static/livelogo.png';
    this.makeSureClassJoined(this.onJoinClass);
    this.isPrivateMsg = !!this.msg.dataExt?.IsPrivateMsg;
    this.supportUrlLink = TCIC.SDK.instance.isFeatureAvailable('UrlLink');
    this.handleLinkClick = this.onOpenWithSystemBrowser.bind(this);
    this.$el.addEventListener('click', this.handleLinkClick);
  },
  beforeDestroy() {
    this.$el.removeEventListener('click', this.handleLinkClick);
  },
  methods: {
    onOpenWithSystemBrowser(event) {
      if (event.target.classList.contains('tcic-link')) {
        event.preventDefault();
        this.openWithSystemBrowser(event);
      }
    },
    openWithSystemBrowser(e) {
      TCIC.SDK.instance.openBrowser(e.target.href);
    },
    onJoinClass() {
      if (!this.msg.ownMsg && document.hidden) {
        this.sendNotification();
      }
    },
    formatContent(text) {
      if (!text) return [];
      const urlRegex = /(https?:\/\/[^\s]+)/g;
      const parts = [];

      let lastIndex = 0;
      let match;

      while ((match = urlRegex.exec(text)) !== null) {
        const url = match[0];

        if (match.index > lastIndex) {
          const nonLinkText = text.slice(lastIndex, match.index);
          parts.push({
            type: 'text',
            content: nonLinkText,
          });
        }
        parts.push({
          type: 'link',
          content: url,
          display: url,
        });

        lastIndex = match.index + url.length;
      }

      if (lastIndex < text.length) {
        parts.push({
          type: 'text',
          content: text.slice(lastIndex),
        });
      }

      return parts;
    },

    sendNotification() {
      // 不知道是干什么的，但是有js报错，加个容错
      /**
       * Notification,浏览器增加桌面通知的接口，
       * 应该很少用到,几乎没见过授权弹窗
       */
      if (!window.Notification) {
        return;
      }
      if (Notification.permission === 'granted') {
        const notification = new Notification(this.className, {
          lang: TCIC.SDK.instance.getLanguage(),
          body: `${this.tips}${this.name}: ${this.content}`,
          icon: this.logo,
        });
        notification.addEventListener('click', () => {
          window.focus();
        });
      } else if (Notification.permission !== 'denied') {
        Notification.requestPermission().then((permission) => {
          if (permission === 'granted') {
            const notification = new Notification(this.className, {
              lang: TCIC.SDK.instance.getLanguage(),
              body: `${this.tips}${this.name}: ${this.content}`,
              icon: this.logo,
            });
            notification.addEventListener('click', () => {
              window.focus();
            });
          }
        });
      };
    },
    parseContent(msg) {
      if (msg.convType.toUpperCase() === 'GROUP'
        && msg.msgType === TCIC.TIMMsgType.ImgMessage) {
        return i18next.t('发送了一张图片');
      }
      if (msg.convType.toUpperCase() === 'GROUP'
          && msg.msgType === TCIC.TIMMsgType.FileMessage) {
        return i18next.t('发送了一个文件');
      }
      return msg.data;
    },
    parse(context) {
      return parseEmoji(context);
    },
    parseType(msg) {
      this.isTeacher = false;
      this.handup = false;
      this.tipsFlag = false;
      if (msg.convType === TCIC.TIMConvType.Tips) {
        this.tipsFlag = true;
        let retMsg = '';
        switch (msg.msgType) {
          case TCIC.TIMMsgType.JoinTips:
          case TCIC.TIMMsgType.QuitTips:
          case TCIC.TIMMsgType.SilenceAllTips:
          case TCIC.TIMMsgType.SilenceUserTips: {
            // 系统消息前面不用加前缀
            // retMsg = i18next.t('【系统】');
          }
            break;
          case TCIC.TIMMsgType.HandUpTips: {
            /* const user = TCIC.SDK.instance.getUserInfo(msg.from);
            if(user.currentStatus !== TCIC.TMemberStatus.Online) {
              return;
            } */
            this.isTeacher = TCIC.SDK.instance.isTeacher();
            this.isAssistant = TCIC.SDK.instance.isAssistant();
            this.isSupervisor = TCIC.SDK.instance.isSupervisor();
            this.userPermission = TCIC.SDK.instance.getPermission(msg.from);
            this.handup = true;
            retMsg = i18next.t('【举手】');
          }
            break;
          case TCIC.TIMMsgType.HandUpCancelTips: {
            retMsg = i18next.t('【取消举手】');
          }
            break;
        }
        return retMsg;
      }
      return (msg.nickname || msg.from);
    },
    parseName(msg) {
      if (msg.convType === TCIC.TIMConvType.Tips) {
        return (msg.nickname || msg.from);
      }
      return '';
    },
  },
};
</script>

<style lang="less" scoped>
@import './newEmoji.less';
@import './emoji.less';
.light {
  .quick-im-component-msg {
    background: rgba(255, 255, 255, 0.08);
    background: linear-gradient(141deg, rgba(235, 240, 255, 0.7) 0%, rgba(225, 230, 245, 0.9) 100%);
  }
}

.quick-im-component-msg {
  position: relative;
  z-index: 100000000;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border: none;
  border-radius: 10px;
  background: linear-gradient(141deg, rgba(21, 27, 48, 0.7) 0%, rgba(28, 33, 49, 0.9) 100%);
  // box-shadow: 0 0 3px 0 rgba(32, 32, 32, 0.4);
  overflow: hidden;

  &.small-screen {
    margin-bottom: 6px;
    .quick-im-component-msg-avatar {
      width: 22px;
      height: 22px;
      margin: 4px 4px;
    }

    .quick-im-component-msg-body {
      margin: 5px;
      font-size: 10px;
      line-height: 20px;
      color: var(--text-color, #FFFFFF);
      overflow: hidden;
      text-overflow: ellipsis;
      display:-webkit-box;  //作为弹性伸缩盒子模型显示。
      -webkit-box-orient:vertical;  //设置伸缩盒子的子元素排列方式--从上到下垂直排列
      -webkit-line-clamp:3;  //显示的行
      word-break: break-word;
      overflow-wrap: break-word;
      text-align: left;
      &.quick-im-component-msg-body-tips {
        margin-right: 12px;
      }

      .quick-im-component-msg-user-name {
        color: #63A6FF;
      }
      .quick-im-component-msg-user-private-msg-label{
        font-size: 10px;
        color: #236CFA;
        margin: 0 -4px;
      }
      .quick-im-component-msg-user-handup {
        color: #FFCD23;
      }
      .quick-im-component-msg-tips {
      }
      .quick-im-component-msg-text {
      }
    }
  }

  .qucik-im-emoji-style-smallscreen {
    position: relative;
    top: -6px;
    zoom: 0.3;
    vertical-align: middle;
    margin-left: -5px;
  }

  .qucik-im-emoji-style {
    position: relative;
    top: -4px;
    zoom: 0.55;
    vertical-align: middle;
    margin-left: -5px;
  }

  .quick-im-component-msg-avatar {
    position: relative;
    z-index: 9;
    border-radius: 50%;
    border-color: transparent;
    height: 24px;
    width: 24px;
    object-fit: cover;
    text-indent: -10000px; // 隐藏掉图片失败时显示的裂图图标
    margin: 6px 8px;
    flex-shrink: 0;

    &.student-avatar-default {
      background: url('./assets/student.svg') no-repeat center center;
    }

    &.teacher-avatar-default {
      background: url('./assets/teacher.svg') no-repeat center center;
    }
  }

  .quick-im-component-msg-body {
    position: relative;
    z-index: 9;
    font-size: 18px;
    font-weight: 500;
    line-height: 38px;
    margin: 8px 16px;
    //background: #f00;
    color: var(--text-color, #FFFFFF);
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;  //作为弹性伸缩盒子模型显示。
    -webkit-box-orient: vertical;  //设置伸缩盒子的子元素排列方式--从上到下垂直排列
    -webkit-line-clamp: 3;  //显示的行
    word-break: break-word;
    overflow-wrap: break-word;
    text-align: left;

    .role-icon{
      display: inline-flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      padding: 0 4px;
      border-radius: 2px;
      height: 20px;
      &.assistant-icon{
        background: linear-gradient(152deg, #23BE82 0%, #08AE6E 94%);
      }
      &.teacher-icon{
        background: linear-gradient(152deg, #00A6FF 0%, #006DFF 94%);
      }
      span{
        font-size: 10px;
        color: #fff;
      }
    }

    .quick-im-component-msg-user-name {
      color: #63A6FF;
    }
    .quick-im-component-msg-user-private-msg-label{
      color: #236CFA;
      font-size: 16px;
      margin: 0 -4px;
    }
    .quick-im-component-msg-user-handup {
      color: #FFCD23;
    }
    .quick-im-component-msg-tips {
    }
  }

  .quick-im-component-handup-body {
    position: relative;
    z-index: 9;
    pointer-events: auto!important;
  }
}
</style>
