<template>
  <div class="side-toggle-button-component">
    <div
      class="side-toggle-button"
      @click="toggleExpand"
    >
      <i :class="expanded ? 'el-icon-arrow-right' : 'el-icon-arrow-left'" />
    </div>
  </div>
</template>

<script>
import BaseComponent from '@core/BaseComponent';
import Constant from '@/util/Constant';

export default {
  extends: BaseComponent,

  data() {
    return {
      expanded: true,
    };
  },

  mounted() {
    TCIC.SDK.instance.subscribeState(Constant.TStateFullScreen, (val) => {
      this.expanded = !val;
    });
  },

  methods: {
    toggleExpand(expand, isSync = false) {
      if (typeof expand !== 'boolean') {
        expand = !this.expanded;
      }
      this.expanded = expand;
      if (!isSync) {
        TCIC.SDK.instance.setState(Constant.TStateFullScreen, !this.expanded);
      }
    },
  },
};
</script>

<style lang="less">
.light {
  .side-toggle-button-component  .side-toggle-button{
    background: linear-gradient(180deg, rgba(3, 10, 25, 0.1) -8.19%, rgba(28, 33, 49, 0.3) 100%);
  }
}
.side-toggle-button-component {
  width: 100%;
  height: 100%;

  .side-toggle-button {
    width: 20px;
    height: 40px;
    background: linear-gradient(141deg,rgba(21,27,48,.7),rgba(28,33,49,.9));
    border-radius: 4px 0 0 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 1s;

    &:hover {
      background-color: rgba(0, 0, 0, 0.3);

      i {
        color: #306FF6;
      }
    }
    &:active {
      background-color: rgba(0, 0, 0, 0.3);

      i {
        color: #306FF6;
      }
    }

    i {
      color: #FFFFFF;
      font-size: 20px;
      font-weight: bold;
    }
  }
}
</style>
