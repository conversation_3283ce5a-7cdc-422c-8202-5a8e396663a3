<template>
  <div
    v-if="msg"
    class="im-component-reply"
  >
    <div class="reply-item">
      <span
        class="reply-item-content"
        @click="outputMsg"
      >{{ $t('回复') }}: {{ msg.nickname || msg.from }}</span>
      <span
        class="reply-item-close"
        @click="$emit('click-close')"
      >
        <i class="el-icon-close icon" />
      </span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    msg: {
      type: Object,
      default: null,
    },
  },
  methods: {
    outputMsg() {
      console.log('replyMsg', this.msg);
    },
  },
};
</script>

<style lang="less">
.im-component-reply {
  width: 100%;
  height: 30px;
  .reply-item {
    max-width: 100%;
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    color: var(--text-color, #eee);
    background-color: hsla(0, 0%, 87.5%, .05);
    border: 1px solid #dcdfe6;
    border-radius: 10px;
    padding: 0 5px 0 10px;
    .reply-item-content {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 20px;
    }
    .reply-item-close {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      cursor: pointer;
    }
  }
}
</style>
