<template>
  <div
    v-if="checkSmallScreen && !isPCSmallScreen"
    ref="quickReplyContainer"
    :class="['quick-reply-container', {'small-screen': checkSmallScreen}]"
  >
    <div
      v-if="!isScrollBottom"
      class="mask"
    />
    <div
      v-for="item in wordsArr"
      :key="item"
      class="item"
      @click="$emit('send-quick-msg', item)"
    >
      {{ item }}
    </div>
  </div>
  <div
    v-else-if="checkSmallScreen && isPCSmallScreen"
    :class="['quick-reply-container', {'small-screen': checkSmallScreen}]"
    style="margin-bottom: 5px"
  >
    <div
      v-for="item in wordsArr"
      :key="item"
      class="item"
      @click="$emit('send-quick-msg', item)"
    >
      {{ item }}
    </div>
  </div>
  <div
    v-else
    :class="['quick-reply-container', {'big-screen': !checkSmallScreen}]"
  >
    <div
      v-for="item in wordsArr"
      :key="item"
      class="item"
      @click="$emit('send-quick-msg', item)"
    >
      {{ item }}
    </div>
  </div>
</template>

<script>
import BaseComponent from '@core/BaseComponent';
export default {
  extends: BaseComponent,
  props: {
    wordsArr: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      isScrollBottom: false,
      checkSmallScreen: true,
      isPCSmallScreen: false,
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.quickReplyContainer?.addEventListener('scroll', this.onScroll);
      this.detectSmallScreen();
      this.detectPCSmallScreen();
    });
  },
  destroyed() {
    this.$refs.quickReplyContainer?.removeEventListener('scroll', this.onScroll);
  },
  methods: {
    onScroll(e) {
      const element = e.target;
      // scrollLeft 是一个非整数，而 scrollWidth 和 clientWidth 是四舍五入的，因此确定滚动区域是否滚动到底的唯一方法是查看滚动量是否足够接近某个阈值
      this.isScrollBottom = Math.abs(element.scrollWidth - element.scrollLeft - element.clientWidth) <= 1;
    },
    detectSmallScreen() {
      const w = window.innerWidth;
      const h = window.innerHeight;
      if (w > h && w > 1960 && h > 1080) {
        this.checkSmallScreen = false;
        return false;
      }
      this.checkSmallScreen = true;
      return true;
    },
    detectPCSmallScreen() {
      const isMobile = TCIC.SDK.instance.isMobile();
      const isPad = TCIC.SDK.instance.isPad();
      if (this.checkSmallScreen && !isMobile && !isPad) {
        this.isPCSmallScreen = true;
      }
    },
  },
};
</script>

<style lang="less" scoped>
:global(.light .quick-reply-container) {
  --text-color: rgba(#2B2C30, .8);
}
.quick-reply-container {
    &.big-screen {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      row-gap: 12px;
      column-gap: 8px;
    }
    &.small-screen {
      width: 100%;
      position: relative;
      margin-bottom: 55px;
      padding: 0 10px;
      overflow-x: scroll;

      white-space: nowrap;
      scrollbar-width: none;
      .mask {
        position: fixed;
        right: 0;
        height: 22px;
        width: 76px;
        background: linear-gradient(to right, transparent 0, #1d2130 80%, #1d2130);
        pointer-events: none;
      }
      &::-webkit-scrollbar {
      display: none;
      }
      .item {
        margin-right: 8px;
        display: inline-block;
      }
    }
    .item {
        padding: 0 16px;
        cursor: pointer;
        color: var(--text-color, #CFD4E5);
        font-size: 12px;
        font-weight: 500;
        border-radius: 12px;
        background: rgba(163, 174, 199, 0.2);
        border: rgba(163, 174, 199, 0.5) 1px solid;
    }
}

</style>
