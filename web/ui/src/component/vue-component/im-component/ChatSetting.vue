<template>
  <div
    class="chat-setting"
    :class="{ disabled: disabled, 'small-screen': isSmallScreen }"
  >
    <div
      class="chat-setting-area im-component-tool"
      @click="toggleDropdown"
    >
      <span class="chat-setting-icon">
        <IconChatSetting />
      </span>
      <span v-if="false" class="chat-setting-text">{{ $t('设置') }}</span>
    </div>
    <!-- 弹出框，搜索框和用户列表 -->
    <div
      v-if="showDropdown"
      class="dropdown"
      :class="{'small-screen': isSmallScreen }"
    >
      <ul class="setting-list">
        <li
          v-for="setting in settingsList"
          :key="setting.id"
          :class="{ selected: setting.id === silenceMode }"
          @click="selectSilenceMode(setting)"
        >
          <span>{{ setting.label }}</span>
          <span
            class="selected-label"
            :class="{ noneSelected: setting.id !== silenceMode }"
          />
        </li>
      </ul>
    </div>
  </div>
</template>


<script>
import IconChatSetting from './assets/svg-component/chat_setting.svg';
export default {
  name: 'ChatSetting',
  components: {
    IconChatSetting,
  },
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    silenceMode: {
      type: Number,
      default: 0,
    },
    settingsList: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      showDropdown: false,
      settingList: [],
    };
  },
  watch: {
    disabled(newVal) {
      if (newVal && this.showDropdown) {
        this.showDropdown = false;
      }
    },
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    handleClickOutside(event) {
      if (this.$el && !this.$el.contains(event.target)) {
        this.showDropdown = false;
      }
    },
    toggleDropdown() {
      if (this.disabled) return;
      this.showDropdown = !this.showDropdown;
    },
    selectSilenceMode(silenceMode) {
      this.$emit('update:silenceMode', silenceMode.id);
      this.showDropdown = false;
    },
  },
};
</script>

<style lang="less">
.light{
  .chat-setting {
    --bg-color: rgb(240,242,248);
    --text-color: #1D2029;
    --active-bg-color: #D1D9EC;
  }
}
.chat-setting {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: start;

  .chat-setting-area{
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: auto;
    height: 32px;
    padding: 2px 4px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
    &:hover {
      background-color: #006Eff;
    }

    .chat-setting-icon {
      width: 16px;
      height: 16px;
      margin: 0;
      // background-image: url('./assets/chat_setting.svg');
    }

    .chat-setting-text {
      font-size: 12px;
      color: var(--text-color, #C5CCDB);
      margin-left: 8px;
      @media (max-width: 1920px) {
        display: none;
      }
    }
  }

  &.small-screen {
  }

  .dropdown {
    position: absolute;
    bottom: 130%;
    left: -104px;
    background-color: var(--bg-color, #2a2d3d);
    border-radius: 5px;
    padding: 10px 4px;
    margin-top: 10px;
    width: 160px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    z-index: 10;
    &.small-screen{
      left: 0;
    }
    @media (max-width: 1920px) {
      left: -137px;
    }
    .setting-list {
      max-height: 200px;
      overflow-y: auto;
      span {
        color: var(--text-color, #CFD4E5);
      }

      li {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        cursor: pointer;
        border-radius: 5px;
        transition: background-color 0.2s;

        &:hover {
          background-color: var(--active-bg-color, #383c51);
        }

        &.selected {
          background-color: var(--active-bg-color, #41475f);
        }

        .selected-label {
          margin-left: auto;
          width: 16px;
          height: 16px;
          background-repeat: no-repeat;
          background-size: contain;
          &:before {
            content: '\e6da';
            font-family: 'element-icons';
            font-size: 16px;
            color: var(--text-color, #CFD4E5);
          }
          &.noneSelected {
            display: none;
            width: 0;
          }
        }
      }
    }
  }
}
.chat-setting.disabled {
  opacity: 0.7;
  pointer-events: none;
}
</style>
