<template>
  <div v-if="msg">
    <li
      v-if="msg.convType === 'tips'"
      :class="[
        'im-component-msg',
        {
          'small-screen': isSmallScreen,
        },
      ]"
    >
      <div class="im-component-msg-tips-wrapper">
        <span class="im-component-msg-tips"> {{ msg.nickname || msg.from }} {{ msg.parsedTips }} {{ msg.parsedTime }}</span>
      </div>
    </li>
    <li
      v-else
      :class="[
        'im-component-msg',
        {
          'small-screen': isSmallScreen,
        },
      ]"
    >
      <div
        :class="[
          'im-component-msg-header',
          {
            'own-msg': msg.ownMsg,
          },
        ]"
      >
        <div
          :class="[
            'im-component-msg-user',
            {
              'own-msg': msg.ownMsg,
            },
          ]"
        >
          <i
            v-if="msg.teacherMsg || msg.assistantMsg"
            :class="[msg.teacherMsg ? 'teacher-icon' : 'assistant-icon', 'role-icon']"
          >
            <span>
              {{ msg.teacherMsg ? roleInfo.teacher : roleInfo.assistant }}
            </span>
          </i>
          <span
            v-if="!isPrivateMsg"
            class="im-component-msg-user-name"
            :title="msg.nickname || msg.from"
          >
            {{ msg.nickname || msg.from }}
          </span>
          <span
            v-if="isPrivateMsg"
            class="im-component-msg-user-name"
            :title="msg.nickname || msg.from"
          >
            {{ msg.ownMsg ? translateTip.privateMsgNameFromSelf : translateTip.privateMsgNameFromOther }}
          </span>
          <span class="im-component-msg-user-time">{{ msg.parsedTime }}</span>
          <span
            v-if="isPrivateMsg"
            class="im-component-msg-user-private"
          >{{ $t("（私聊）") }}</span>
        </div>
        <MsgControl
          v-if="showControl"
          :msg="msg"
          :show-reply="showReply"
          @click-reply="setReplyMsg"
          @click-delete="deleteMsg"
          @click-toggle-silence="silenceMsg(!msg.silenceMsg)"
        />
      </div>
      <div
        :class="[
          'im-component-msg-body',
          {
            'own-msg': msg.ownMsg,
          },
        ]"
      >
        <div
          class="bubble"
          :class="{
            'emoji-msg': msg.parsedEmoji.type === 2,
          }"
        >
          <MsgExtReply
            v-if="msg.dataExt && msg.dataExt.replyMsg"
            :msg="msg.dataExt.replyMsg"
            @click-img="openPreviewImg(msg.dataExt.replyMsg)"
          />
          <div
            v-if="msg.msgType === 'img_message'"
            class="im-component-img-control"
          >
            <div
              v-if="imgSendStatus === 1 && msg.ownMsg"
              class="div-per"
            >
              <div class="image-per" />
              <span class="per-font">{{ per }}</span>
            </div>
            <div
              v-else-if="imgSendStatus === 0 && msg.ownMsg"
              class="div-per"
            >
              <div
                class="image-fail"
                @click="reSendImgMsg"
              />
            </div>
            <img
              :class="['image-element', { small: isSmallScreen }]"
              :src="msg.preview1 || msg.preview2 || msg.data"
              @load="onImageLoaded"
              @click="openPreviewImg(msg)"
            >
          </div>

          <div
            v-else-if="msg.msgType === 'file_message'"
            class="im-component-file-message"
            @click="downloadFile(msg.data)"
          >
            <div class="file-info">
              <div
                class="file-icon"
                alt="file"
              />
              <div class="file-details">
                <div class="file-name">
                  {{ msg.desc || $t('未命名文件') }}
                </div>
                <div class="file-size">
                  {{ formatFileSize(msg.ext) }}
                </div>
              </div>
            </div>
          </div>

          <div
            v-else
            class="im-component-text-content"
          >
            <template v-if="msg.parsedEmoji.type === 1">
              <span class="safe-msg-content">
                <template
                  v-for="(part, index) in parsedMessageParts"
                >
                  <a
                    v-if="part.type === 'link'"
                    :key="index"
                    :href="part.content"
                    class="tcic-link"
                    rel="noopener noreferrer"
                    target="_blank"
                  >{{ part.display }}</a>
                  <span
                    v-else
                    :key="index"
                    v-text="part.content"
                  />
                </template>
              </span>

              <translate-msg
                v-if="!isUrl(msg.parsedEmoji.content)"
                :msg="msg"
              />
            </template>
            <img
              v-if="msg.parsedEmoji.type === 2"
              :class="[msg.parsedEmoji.imageClass]"
              style="zoom: 0.5; vertical-align: middle; margin-left: -5px"
              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABAQMAAAAl21bKAAAAA1BMVEUAAACnej3aAAAAAXRSTlMAQObYZgAAAApJREFUCNdjYAAAAAIAAeIhvDMAAAAASUVORK5CYII="
            >
          </div>
        </div>
        <div
          v-if="msg.msgType === 'file_message' && imgSendStatus === 1 && msg.ownMsg"
          class="sending-icon"
          alt="file_sending"
        />
        <div
          v-if="msg.msgType !== 'img_message' && imgSendStatus === 0 && msg.ownMsg"
          class="failed-icon"
          alt="file_failed"
        />
      </div>
    </li>
  </div>
</template>

<script>
import { MsgBaseComponent } from './MsgBase';
import i18next from 'i18next';

export default {
  extends: MsgBaseComponent,
  data() {
    return {
      supportUrlLink: false,
      handleLinkClick: null,
    };
  },
  computed: {
    translateTip() {
      return {
        privateMsgNameFromSelf: i18next.t('我对 {{privateMsgName}}', { privateMsgName: this.privateMsgName }),
        privateMsgNameFromOther: i18next.t('{{privateMsgName}} 对我', { privateMsgName: this.privateMsgName }),
      };
    },
    parsedMessageParts() {
      const text = this.msg.parsedEmoji.content || '';
      const urlRegex = /(https?:\/\/[^\s]+)/g;
      const parts = [];

      let lastIndex = 0;
      let match;

      while ((match = urlRegex.exec(text)) !== null) {
        const url = match[0];

        if (match.index > lastIndex) {
          const nonLinkText = text.slice(lastIndex, match.index);
          parts.push({
            type: 'text',
            content: nonLinkText,
          });
        }
        parts.push({
          type: 'link',
          content: url,
          display: url,
        });

        lastIndex = match.index + url.length;
      }

      if (lastIndex < text.length) {
        parts.push({
          type: 'text',
          content: text.slice(lastIndex),
        });
      }

      return parts;
    },
  },
  mounted() {
    this.handleLinkClick = this.onOpenWithSystemBrowser.bind(this);
    this.$el.addEventListener('click', this.handleLinkClick);
    this.supportUrlLink = TCIC.SDK.instance.isFeatureAvailable('UrlLink');
  },
  beforeDestroy() {
    this.$el.removeEventListener('click', this.handleLinkClick);
  },
  methods: {
    onOpenWithSystemBrowser(event) {
      if (event.target.classList.contains('tcic-link')) {
        event.preventDefault();
        this.openWithSystemBrowser(event);
      }
    },
    openWithSystemBrowser(e) {
      TCIC.SDK.instance.openBrowser(e.target.href);
    },
    formatTextWithLinks(text) {
      if (!text) return '';

      const urlRegex = /(https?:\/\/[^\s]+)/g;

      return this.supportUrlLink ? text.replace(urlRegex, (url) => {
        const href = url.startsWith('http') ? url : `http://${url}`;
        return `<a href="${href}" target="_blank" rel="noopener noreferrer" class="tcic-link">${url}</a>`;
      }) : text;
    },
    hasUrl(text) {
      const urlRegex = /(https?:\/\/[^\s]+(?:\.[a-z]{2,})+(?:\/[^\s]*)?)/gi;
      return urlRegex.test(text);
    },
    downloadFile(fileUrl) {
      if (!fileUrl) return;

      const a = document.createElement('a');
      a.href = fileUrl;
      a.download = this.msg.desc || 'downloaded-file';
      a.target = '_blank';
      a.className = 'tcic-link';

      a.addEventListener('click', (event) => {
        event.preventDefault();
        this.openWithSystemBrowser(event);
      });

      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    },

    formatFileSize(size) {
      if (!size) return i18next.t('未知大小');
      const bytes = parseInt(size, 10);
      if (bytes < 1024) return `${bytes} B`;
      if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
      return `${(bytes / 1024 / 1024).toFixed(1)} MB`;
    },
  },
};
</script>

<style lang="less">
@import './newEmoji.less';
@import './emoji.less';
@import './MsgControl.less';

.im-component-msg {
  margin-bottom: 10px;

  .im-component-msg-tips-wrapper {
    text-align: center;

    .im-component-msg-tips {
      height: 30px !important;
      font-size: 14px;
      font-weight: 500;
      color: #8a9099;
      line-height: 30px;
    }
  }

  .failed-icon {
    width: 16px;
    height: 16px;
    margin-right: 10px;
    background-repeat: no-repeat;
    background-size: contain;
    background-image: url('./assets/file_failed.svg');
  }

  .sending-icon {
    width: 16px;
    height: 16px;
    margin-right: 10px;
    background-repeat: no-repeat;
    background-size: contain;
    background-image: url('./assets/file_sending.svg');
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .im-component-msg-header {
    width: 100%;
    height: 28px;
    margin-bottom: 4px;
    font-weight: 500;
    display: flex;
    flex-direction: row;
    align-items: center;
    align-content: flex-start;

    .role-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      padding: 0 4px;
      border-radius: 2px;
      height: 20px;
      &.assistant-icon {
        background: linear-gradient(152deg, #23be82 0%, #08ae6e 94%);
      }
      &.teacher-icon {
        background: linear-gradient(152deg, #00a6ff 0%, #006dff 94%);
      }
      span {
        font-size: 12px;
        color: #fff;
      }
    }

    &.own-msg {
      // flex-direction: row-reverse;
      .im-component-msg-user {
        text-align: right;
      }
    }

    .im-component-msg-avatar {
      border-radius: 50%;
      border-color: transparent;
      height: 24px;
      width: 24px;
      object-fit: cover;
      text-indent: -10000px; // 隐藏掉图片失败时显示的裂图图标
      background-repeat: no-repeat;
      background-position: 50% 50%;

      &.student-avatar-default {
        background-image: url('./assets/student.svg');
      }

      &.teacher-avatar-default {
        background-image: url('./assets/teacher.svg');
      }
    }

    .im-component-msg-user {
      margin: 0 8px;
      height: 30px;
      font-size: 14px;
      font-weight: 500;
      color: #8a9099;
      line-height: 30px;
      text-align: left;
      flex: 1;
      overflow: hidden;
      display: flex;
      align-items: center;

      &.own-msg{
        justify-content: end;
      }

      .im-component-msg-user-name {
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: calc(100% - 50px);
        vertical-align: top;
      }
      .im-component-msg-user-time {
        display: inline-block;
        vertical-align: top;
        flex-shrink: 0;
        margin: 0 4px;
      }
      .im-component-msg-user-private{
        display: inline-block;
        vertical-align: top;
        color: #236CFA;
        font-size: 12px;
        margin: 0 -4px 0 -8px;
        flex-shrink: 0;
      }
    }
  }

  .im-component-msg-body {
    display: flex;
    flex-direction: row;
    align-items: center;
    align-content: flex-start;
    font-size: 12px;
    font-weight: 500;
    color: #ffffff;
    line-height: 24px;

    .bubble {
      max-width: 85%;
      margin-left: 8px;
      border-radius: 8px;
      // border-radius: 16px 16px 16px 0px;
      padding: 8px;
      color: var(--text-color, #ffffff);
      background: var(--bubble-bg, rgba(223, 223, 223, 0.05));
      word-break: break-word;
      overflow-wrap: break-word;
      text-align: left;
      user-select: text;
      -webkit-user-select: text;

      &.emoji-msg {
        padding: 4px;
      }

      .im-component-img-control {
        display: flex;
        flex-direction: row;
        align-items: center;
        align-content: flex-start;

        div {
          width: 160px;
          height: 90px;
          margin: 0 4px;
          background-repeat: no-repeat;
          background-position: 50% 50%;

          &:hover {
            border-radius: 2px;
            border: 1px solid #306ff6;
          }

          &:active {
            border-radius: 2px;
            border: 1px solid #306ff6;
          }
        }

        .image-element {
          max-height: 150px;
          max-width: 200px;

          &.small {
            max-width: 150px;
          }
        }

        .div-per {
          text-align: right;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          justify-content: center;
          width: 38px !important;
          &:hover {
            border-radius: 2px;
            border: none !important;
          }
        }

        .image-per {
          width: 28px;
          height: 24px;
          margin-right: 5px;
          background-image: url('./assets/ic_net_normal.svg');
          animation: rotating 1.5s linear infinite;
          animation: rotating 1s linear infinite;
          &:hover,
          &:active {
            border: none;
            opacity: 1;
          }
        }

        .image-fail {
          width: 24px;
          height: 24px;
          margin-right: 5px;
          background-image: url('./assets/ic_net_poor.svg');
          cursor: pointer;
          &:hover,
          &:active {
            border: none;
            opacity: 1;
            cursor: pointer;
          }
        }

        .per-font {
          font-size: 12px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #8a9099;
          line-height: 17px;
          margin-top: 5px;
        }
      }

      .im-component-text-content {
        white-space: pre-wrap;
        user-select: text !important;
        -webkit-user-select: text !important;
      }

      *::selection {
        background-color: rgba(223, 223, 223, 0.1);
      }

      * {
        user-select: text;
        -webkit-user-select: text;
      }

      a {
        color: #13a449;
        // padding: 0 3px;
        text-decoration: underline;
        cursor: pointer;
      }

      .im-component-file-message {
        display: flex;
        align-items: center;
        padding: 10px;
        border-radius: 8px;
        max-width: 80%;
        word-break: break-word;
        cursor: pointer; /* 点击整个气泡下载 */
      }

      .file-info {
        display: flex;
        align-items: center;
        flex-grow: 1;
      }

      .file-icon {
        width: 36px;
        height: 36px;
        margin-right: 10px;
        background-image: url('./assets/file.svg');
        background-size: 85%;
        background-repeat: no-repeat;
      }

      .file-details {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .file-name {
        font-size: 14px;
        font-weight: bold;
        color: var(--text-color, #f0f0f0); /* 适配深蓝底色 */
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 220px;
      }

      .file-size {
        font-size: 12px;
        color: var(--text-color, #cccccc); /* 适配深色背景 */
        margin-top: 4px;
      }

      /* 小屏幕适配 */
      @media (max-width: 768px) {
        .file-name {
          max-width: 150px;
        }
        .file-icon {
          width: 30px;
          height: 30px;
        }
      }
    }

    &.own-msg {
      flex-direction: row-reverse;

      .bubble {
        margin-left: 0;
        margin-right: 8px;
        // border-radius: 16px 16px 0px 16px;

        // 自己的回复文字要靠右才加上这个
        // .im-component-text-content {
        //   display: flex;
        //   justify-content: flex-end;
        // }
      }
    }
  }

  &.small-screen {
    .im-component-msg-tips-wrapper {
      .im-component-msg-tips {
        color: #8a9099;
        font-size: 10px;
        line-height: 12px;
        font-weight: 400;
      }
    }

    .im-component-msg-header {
      .im-component-msg-avatar {
        width: 22px;
        height: 22px;
      }

      .im-component-msg-user {
        height: fit-content;
        color: #8a9099;
        font-size: 10px;
        line-height: 12px;
        font-weight: 400;
        .im-component-msg-user-name,
        .im-component-msg-user-time {
          vertical-align: middle;
          flex-shrink: 0;
        }
        .im-component-msg-user-private{
          vertical-align: middle;
          color: #236CFA;
          font-size: 12px;
          flex-shrink: 0;
        }
      }
    }

    .im-component-msg-body {
      line-height: 18px;

      .bubble {
        border-radius: 4px;
        padding: 8px 12px;
        max-width: 90%;
        &.emoji-msg {
          padding: 1px 6px;
        }
      }
    }
  }
}
</style>
