<template>
  <div :class="['handup-list__wrap', isSmallScreen && 'small-screen']">
    <ul
      v-if="handUpMembers.length>0"
      class="handup-list"
    >
      <template v-for="(member, index) in handUpMembers">
        <li
          :key="`${member.userId}_${index}`"
          class="handup-item"
        >
          <div class="handup-icon__status">
            <div :class="getMemberIconClass(member, 'handup')" />
            <div
              v-if="member.handUpActive"
              class="handup-status__text"
            >
              {{ $t('举手中') }}
            </div>
          </div>
          <div class="handup-item__right">
            <div
              class="handup-item__name"
              :title="member.userName"
            >
              {{ member.userName }}
            </div>
            <div class="handup-icon__operator">
              <div
                v-if="!isLive"
                :class="[
                  'handup-item__icon',
                  getMemberIconClass(member, 'stage'),
                ]"
                @click="
                  onMemberAction(
                    member,
                    member.stage ? TTypeStageDown : TTypeStageUp
                  )
                "
              />
              <el-tooltip
                v-if="!isLive"
                v-show="member.stage"
                placement="bottom"
                effect="dark"
                :disabled="getDeviceShowState(member, 'mic') !== 'error'"
                :content="$t('设备不可用（损坏、未授权等）')"
                :hide-after="2000"
              >
                <div
                  :class="[
                    'handup-item__icon',
                    getMemberIconClass(member, 'mic'),
                  ]"
                  @click="
                    onMemberAction(
                      member,
                      isDeviceOpen(member, 'mic') ? TTypeMicClose : TTypeMicOpen
                    )
                  "
                />
              </el-tooltip>
              <el-tooltip
                v-if="!isLive"
                v-show="member.stage"
                placement="bottom"
                effect="dark"
                :disabled="getDeviceShowState(member, 'camera') !== 'error'"
                :content="$t('设备不可用（损坏、未授权等）')"
                :hide-after="2000"
              >
                <div
                  :class="[
                    'handup-item__icon',
                    getMemberIconClass(member, 'camera'),
                  ]"
                  @click="
                    onMemberAction(
                      member,
                      isDeviceOpen(member, 'camera') ? TTypeCameraClose : TTypeCameraOpen
                    )
                  "
                />
              </el-tooltip>
              <div
                v-if="showBoard"
                :class="[
                  'handup-item__icon',
                  getMemberIconClass(member, 'board'),
                ]"
                @click="
                  onMemberAction(
                    member,
                    member.board ? TTypeBoardDisable : TTypeBoardEnable
                  )
                "
              />
            </div>
          </div>
        </li>
      </template>
    </ul>
    <ul
      v-else
      class="handup-empty"
    >
      <span v-if="loading" class="handup-empty__tips">{{ $t('加载中...') }}</span>
      <span v-else class="handup-empty__tips">{{ $t('暂时无人举手') }}</span>
    </ul>
  </div>
</template>

<script>
import i18next from 'i18next';
import Constant from '@/util/Constant';
import BaseComponent from '@core/BaseComponent';
import Lodash from 'lodash';
import MemberIcon from '../member-list-component/MemberIcon';
import '../member-list-component/MemberIcon.less';

export default {
  extends: BaseComponent,
  mixins: [MemberIcon],
  props: {
    handUpMembers: {
      type: Array,
    },
    loading: {
      type: Boolean,
    },
  },
  data() {
    return {
      // TCIC常量重定义，保证在HTML可以直接绑定
      TTypeCameraOpen: TCIC.TMemberActionType.Camera_Open,
      TTypeCameraClose: TCIC.TMemberActionType.Camera_Close,
      TTypeMicOpen: TCIC.TMemberActionType.Mic_Open,
      TTypeMicClose: TCIC.TMemberActionType.Mic_Close,
      TTypeHandUp: TCIC.TMemberActionType.Hand_Up,
      TTypeHandUpCancel: TCIC.TMemberActionType.Hand_Up_Cancel,
      TTypeKickOut: TCIC.TMemberActionType.Kick_Out,
      TTypeBoardEnable: TCIC.TMemberActionType.Board_Enable,
      TTypeBoardDisable: TCIC.TMemberActionType.Board_Disable,
      TTypeSilence: TCIC.TMemberActionType.Silence,
      TTypeSilenceCancel: TCIC.TMemberActionType.Silence_Cancel,
      TTypeStageUp: TCIC.TMemberActionType.Stage_Up,
      TTypeStageDown: TCIC.TMemberActionType.Stage_Down,
      TTypeKickOutForever: TCIC.TMemberActionType.Kick_Out_Forever,
      TStatuesOnline: TCIC.TMemberStatus.Online,
      TDeviceStatusUnknown: TCIC.TDeviceStatus.Unknown,
      TDeviceStatusOpen: TCIC.TDeviceStatus.Open,
      // 是否是直播课
      isLive: false,
      // 是否已经开始上课
      isClassStarted: false,
      isTeacher: false,
      isAssistant: false,
      isSupervisor: false,
      showBoard: true,
      roomInfo: null,
      roleInfo: null,
    };
  },
  mounted() {
    const { roomInfo, roleInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    this.roomInfo = roomInfo;
    this.initEvent();
  },

  methods: {
    initEvent() {
      this.makeSureClassJoined(() => {
        this.isTeacher = TCIC.SDK.instance.isTeacher();
        this.isAssistant = TCIC.SDK.instance.isAssistant();
        this.isSupervisor = TCIC.SDK.instance.isSupervisor();
        if (this.isTeacher || this.isAssistant || this.isSupervisor) {
          this.isLive = TCIC.SDK.instance.isLiveClass();
        }
        this.showBoard = !TCIC.SDK.instance.isVideoOnlyClass();
      });
      // 课堂状态
      this.addLifecycleTCICStateListener(
        TCIC.TMainState.Class_Status,
        (status) => {
          this.isClassStarted = status === TCIC.TClassStatus.Already_Start;
        },
      );
    },

    onMemberAction(user, actionType) {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      if (user.currentStatus !== TCIC.TMemberStatus.Online) {
        // 不允许操作离线学员
        window.showToast(i18next.t('该操作只对在线{{arg_0}}有效', { arg_0: this.roleInfo.student }));
        return;
      }
      if (actionType === TCIC.TMemberActionType.Mic_Open) {
        if (this.getDeviceShowState(user, 'mic') === 'error') {
          // 设备异常，不能开启
          console.log(`[MemberList] can not open mic when device error, ${user.role} ${user.userId} ${user.micState}`);
          return;
        }
      }
      if (actionType === TCIC.TMemberActionType.Camera_Open) {
        if (this.getDeviceShowState(user, 'camera') === 'error') {
          // 设备异常，不能开启
          console.log(`[MemberList] can not open camera when device error, ${user.role} ${user.userId} ${user.cameraState}`);
          return;
        }
      }

      const stageActions = [
        // 上台相关操作
        TCIC.TMemberActionType.Stage_Up,
        TCIC.TMemberActionType.Stage_Down,
        TCIC.TMemberActionType.Camera_Open,
        TCIC.TMemberActionType.Camera_Close,
        TCIC.TMemberActionType.Mic_Open,
        TCIC.TMemberActionType.Mic_Close,
      ];
      if (stageActions.includes(actionType)) {
        // 如果是上台相关功能
        if (this.isLive) {
          // 直播课不允许上台
          window.showToast(i18next.t('直播课不支持{{arg_0}}上台', { arg_0: this.roleInfo.student }));
          return;
        }
        if (classInfo.maxRtcMember === 0) {
          // 上台人数已满后不允许上台
          window.showToast(i18next.t('当前{{arg_0}}设定上台人数为0，成员无法上台', { arg_0: this.roomInfo.name }));
          return;
        }
      }
      if (TCIC.SDK.instance.getState(Constant.TStateCarousel)) {   // 循环上台时禁用上下台
        if (actionType === TCIC.TMemberActionType.Stage_Up || actionType === TCIC.TMemberActionType.Stage_Down) {
          window.showToast(i18next.t('请先结束循环上台'));
          return;
        }
      }
      const boardActions = [
        // 涂鸦相关操作
        TCIC.TMemberActionType.Board_Enable,
        TCIC.TMemberActionType.Board_Disable,
      ];
      if (!this.isClassStarted) {
        // 未开始上课，禁用上台及涂鸦相关操作
        if (
          stageActions.includes(actionType)
            || boardActions.includes(actionType)
        ) {
          window.showToast(i18next.t('{{startRoom}}前禁止该操作', { startRoom: this.roomInfo.startRoom }));
          return;
        }
      }
      // 小程序不允许授权涂鸦
      if (user.device === TCIC.TDevice.Miniprogram && boardActions.includes(actionType)) {
        window.showToast(i18next.t('小程序用户无法使用涂鸦功能'));
        return;
      }
      const param = {
        classId: classInfo.classId,
        classType: classInfo.classType,
        userId: user.userId,
        actionType,
      };
      TCIC.SDK.instance.memberAction(param)
        .then(() => {
          this.$emit('member-action', param);
        })
        .catch((err) => {
          window.showToast(err.errorMsg, 'error');
        });
    },
  },
};
</script>

<style lang="less">
.handup-list__wrap {
  pointer-events: auto;
  padding: 0 14px;

  &.small-screen {
    .handup-list, .handup-empty {
      height: calc(100vmin - 90px - 64px);
      height: calc(100dvmin - 90px - 64px); /* 解决 Chrome 移动端地址栏高度也算入 100vh 的问题 */
      max-height: 360px;
    }

    .handup-list {
      overflow-y: auto;

      .handup-item {
        .handup-item__name {
          font-size: 16px;
        }
      }
    }

    .handup-empty {
      .handup-empty__tips {
        font-size: 18px;
      }
    }
  }

  .handup-list {
    width: 348px;
    height: 374px;
    overflow: auto;
    overflow-x: hidden;
    .handup-item {
      display: flex;
      height: 60px;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .handup-icon__status {
        position: relative;
        width: 44px;
        height: 24px;

        .member-item__icon-handup {
          width: 44px;
        }

        .handup-status__text {
          position: absolute;
          bottom: -14px;
          left: 0px;
          color: #fff;
          background: #13a449;
          font-size: 12px;
          white-space: nowrap;
          padding: 2px;
          width: 100%;
          height: 12px;
          line-height: 12px;
          padding: 2px;
          box-sizing: content-box;
          border-radius: 2px;
          text-align: center;
        }
      }

      .handup-icon__operator {
        width: 144px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }

      .handup-item__right {
        background-color: rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex: 1;
        height: 60px;
        padding: 0 16px;

        :last-child {
          margin-right: 0;
        }
      }

      .handup-item__name,
      .handup-item__icon {
        width: 24px;
        height: 24px;
        background-position: left center;
        background-repeat: no-repeat;
        background-size: contain;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 16px;
      }

      .handup-item__name {
        width: 100px;
        height: 28px;
        line-height: 28px;
        font-size: 18px;
        font-weight: 500;
        color: var(--text-color, #ffffff);
        white-space: nowrap;
        cursor: default;
        // max-width: 90px;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .handup-empty {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 348px;
    height: 374px;

    .handup-empty__tips {
      line-height: 24px;
      font-size: 20px;
      font-weight: 500;
      color: #8A9099;
    }
  }
}
</style>
