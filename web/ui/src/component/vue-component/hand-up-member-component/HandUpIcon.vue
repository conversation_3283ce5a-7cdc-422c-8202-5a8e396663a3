<template>
  <el-popover
    ref="popover"
    v-model="isShowPopover"
    :popper-class="`handup-list__popover ${isVideoPortraitClass ? 'portrait' : ''} ${isSmallScreen ? 'small-screen' : ''}`"
    :visible-arrow="false"
    :disabled="!enablePopover"
    placement="bottom"
    trigger="manual"
    :style="{'height': '100%'}"
    @show="popoverShowCallback"
    @hide="popoverHideCallback"
  >
    <div
      ref="handup-list__header-ref"
      class="handup-list__header component-header"
      :class="{ 'small-screen': isSmallScreen }"
    >
      <div class="handup-title header-title">
        {{ $t('举手') }} ({{ Math.min(handUpCount, 50) }})
      </div>
      <el-button
        size="mini"
        type="text"
        class="handup-clear"
        @click="clearHandUpList"
      >
        {{ $t('清空') }}
      </el-button>
      <div
        class="handup-close header-close"
        @click="isShowPopover = false"
      >
        <i class="el-icon-close icon" />
      </div>
    </div>
    <HandUpList
      ref="handup-list__content-ref"
      :hand-up-members="handUpMembers"
      :loading="loading"
      @member-action="handleMemberAction"
    />
    <div
      v-if="showHandsUpIcon"
      ref="handup-icon-ref"
      slot="reference"
      class="handup-icon__wrap handup-icon__wrap__blur"
      :class="{ 'small-screen': isSmallScreen || isPad }"
      @click="handupIconHandler"
    >
      <div
        v-if="!handUpCountDownTime"
        :class="['handup-icon', {'small-screen': isSmallScreen || isPad, handuping}]"
        :style="(isSmallScreen || isPad) ? isPortrait ? {'margin-left': '-2px'} : {'margin-left': '-4px'} : {}"
      >
        <div
          v-if="(isTeacher || isAssistant || isSupervisor) && handUpCount && !isShowPopover"
          class="handup-icon__number"
        >
          {{ Math.min(handUpCount, 50) }}
        </div>
        <div
          v-if="!isTeacher && !isAssistant && !isSupervisor && handUpCountDownTime"
          class="handup-icon__star"
          :class="{ show: handUpCountDownTime }"
        />
        <el-tooltip
          effect="dark"
          :content="handuping ? $t('举手中') : undefined"
          :disabled="!handuping"
          placement="top-start"
        >
          <div
            v-if="!isSmallScreen && !isPad"
            class="handup-icon__arm"
            :class="{ show: handUpCountDownTime, handuping }"
          >
            <IconHandUp style="width: 32px; height: 32px;" />
          </div>
        </el-tooltip>
      </div>
      <div
        v-if="!isTeacher && !isAssistant && !isSupervisor && handUpCountDownTime"
        class="handup-tip"
      >
        {{ handuping ? $t('举手中') : $t('取消中') }} ({{ handUpCountDownTime }}s)
      </div>
      <div
        v-if="!isClassStarted && isStudent"
        class="handup-icon-layer-shade"
      />
    </div>
  </el-popover>
</template>

<script>
import HandUpList from './HandUpList.vue';
import BaseComponent from '@core/BaseComponent';
import IconHandUp from './assets/svg-component/ic_hand_up.svg';

export default {
  components: {
    HandUpList,
    IconHandUp,
  },
  extends: BaseComponent,
  props: {
    showHandsUpIcon: { // 是否显示举手图标
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isShowPopover: false, // 是否显示popover
      handuping: false, // 是否正在举手
      isTeacher: false,
      isAssistant: false,
      isStudent: false,
      isSupervisor: false,
      handUpBtnPress: false, // 举手状态是否是按压状态
      handUpCountDownTime: 0, // 举手倒计时
      handUpCountDownTimeTask: null, // 举手倒计时任务
      handUpCount: 0, // 举手人数
      popoverBottom: 0,
      isPortrait: true,
      loading: false,
      isVideoPortraitClass: TCIC.SDK.instance.isPortraitClass(),
      isClassStarted: false,
      hasJoinedClass: false,
      ownerTcicComponentName: '',
      tvCtrlName: 'handup',
      handUpMembers: [],
      isPad: TCIC.SDK.instance.isPad(),
    };
  },

  computed: {
    enablePopover() {
      return this.isTeacher || this.isAssistant || this.isSupervisor;
    },
  },

  watch: {
    isClassStarted(val) {
      this.$nextTick(() => this.updateTbmTarget());
    },
    isVisible() {
      this.$nextTick(() => this.updateTbmTarget());
    },
    isShowPopover(value) {
      if (value) {
        this.getHandUpList();
      }
    },
  },

  mounted() {
    const ownerTcicComponent = this.getOwnerTCICComponent();
    if (!TCIC.SDK.instance.isStudent()) {
      this.getHandUpList();
    }
    if (ownerTcicComponent) {
      this.ownerTcicComponentName = ownerTcicComponent.theTCICComponentName;
      this.tvCtrlName = ownerTcicComponent.theTCICComponentName === 'footer-component'
        ? 'handup'
        : `${ownerTcicComponent.theTCICComponentName}-handup`;
    }

    this.initEvent();
    window.addEventListener('blur', () => {
      this.isShowPopover = false;
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.isPortrait = orientation === TCIC.TDeviceOrientation.Portrait;
    });
    this.addLifecycleTCICStateListener(
      TCIC.TMainState.Hand_Up_Member_List,
      (members) => {
        // 只有老师关心举手列表
        if (this.isTeacher || this.isAssistant || this.isSupervisor) {
          this.handUpCount = members.length;
          this.handUpMembers = members;
        }
      },
    );

    // 移到 makeSureClassJoined
    // this.updateTbmTarget();
  },

  beforeDestroy() {
    clearInterval(this.handUpCountDownTimeTask);
    this.uninitTbmButtons();
  },

  methods: {
    initTbmButtons() {
      // console.log('HandUpIcon initTbmButtons', this.tvCtrlName);
      const nodes = [];
      const el = this.$el.querySelector('div.handup-icon');
      if (!el) return;
      nodes.push(window.tbm.generateNode(el));
      window.tbm.updateTarget(this.tvCtrlName, nodes);
    },
    clearHandUpList() {
      TCIC.SDK.instance.clearHandUpMemberList()
        .catch((err) => {
          window.showToast(err.message, 'error');
        });
    },
    async handleMemberAction(param) {
      try {
        if (param.actionType === TCIC.TMemberActionType.Stage_Up) {
          await TCIC.SDK.instance.removeHandUpMember([param.userId]);
        }
        await this.getHandUpList();
      } catch (e) {
        window.showToast(e.errorMsg, 'error');
      }
    },
    uninitTbmButtons() {
      // console.log('HandUpIcon uninitTbmButtons', this.tvCtrlName);
      window.tbm.clearTarget(this.tvCtrlName);
    },
    updateTbmTarget() {
      // 上课后才能举手
      if (this.isClassStarted && this.hasJoinedClass && this.isVisible) {
        this.initTbmButtons();
      } else {
        this.uninitTbmButtons();
      }
    },
    initEvent() {
      // 进入课堂的回调
      this.makeSureClassJoined(() => {
        this.isTeacher = TCIC.SDK.instance.isTeacher();
        this.isAssistant = TCIC.SDK.instance.isAssistant();
        this.isStudent = TCIC.SDK.instance.isStudent();
        this.isSupervisor = TCIC.SDK.instance.isSupervisor();

        this.isClassStarted = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status) === TCIC.TClassStatus.Already_Start;
        this.hasJoinedClass = true;
        this.updateTbmTarget();
      });
      // 课堂状态
      this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, (status) => {
        const isClassStarted = status === TCIC.TClassStatus.Already_Start;
        if (isClassStarted === this.isClassStarted) {
          return;
        }
        this.isClassStarted = isClassStarted;
      });
    },
    // 举手按钮被点击处理
    handupIconHandler() {
      if (this.isTeacher || this.isAssistant || this.isSupervisor) {
        // 如果已经显示了，则隐藏
        if (this.isShowPopover) {
          this.isShowPopover = false;
        } else {
          // 如果已经隐藏了，则需要判断是否有人在举手
          this.isShowPopover = true;
        }
      } else {
        // 举手按钮还没有被点击（避免重复点击）
        if (!this.handUpBtnPress) {
          this.handUpBtnPress = true;
          this.sendHandUpRequest();
        }
      }
    },

    getHandUpList() {
      if (this.loading) return;
      this.loading = true;
      TCIC.SDK.instance.getHandUpList({
        page: 1,
        limit: 50,
      }).then((res) => {
        // 统一通过监听事件获取 members,从而保持两个HandUpIcon组件之间的数据同步
      })
        .catch((err) => {
          window.showToast(err.errorMsg, 'error');
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 举手倒计时
    handUpCountDown() {
      this.handUpCountDownTime = 3;
      // 做一次定时器保护
      clearInterval(this.handUpCountDownTimeTask);
      this.handUpCountDownTimeTask = setInterval(() => {
        this.handUpCountDownTime -= 1;
        if (this.handUpCountDownTime <= 0) {
          this.handUpBtnPress = false;
          clearInterval(this.handUpCountDownTimeTask);
        }
      }, 1000);
    },

    // 学生举手的请求
    sendHandUpRequest() {
      if (!this.handuping) {
        this.handuping = true;
        TCIC.SDK.instance
        .memberAction({
          actionType: TCIC.TMemberActionType.Hand_Up,
          userId: TCIC.SDK.instance.getUserId(),
        })
        .then(() => {
          this.handUpCountDown();
        })
        .catch((err) => {
          // 请求失败的话做举手状态重置
          this.handUpBtnPress = false;
          window.showToast(err.errorMsg, 'error');
        });
      } else {
        this.handuping = false;

        TCIC.SDK.instance
        .memberAction({
          actionType: TCIC.TMemberActionType.Hand_Up_Cancel,
          userId: TCIC.SDK.instance.getUserId(),
        }).then(() => {
          this.handUpCountDown();
        })
        .finally(() => {
          this.handUpBtnPress = false;
        });
      }
    },

    // 举手列表展示的时候回调
    popoverShowCallback() {
      this.$emit('onShow');
      this.isShowPopover = true;
      this.initInteractionEvent();
      if (!this.showHandsUpIcon) {
        return;
      }

      /** popover样式是以top来定位的，这里要改为用bottom来定位，这样动态举手的时候，列表才会往上扩充 */
        const clientHeight = document.body.clientHeight;
        const popoverContent = this.$refs.popover.$refs.popper;
        const $el = this.$refs['handup-icon-ref'];
        this.popoverBottom = clientHeight - $el.getBoundingClientRect().top;
        popoverContent.style.bottom = `${this.popoverBottom}px`;
        popoverContent.style.top = `${clientHeight - this.popoverBottom - 304}px`;
      /** popover样式是以top来定位的，这里要改为用bottom来定位，这样动态举手的时候，列表才会往上扩充 */
    },

    /**
     * 举手列表隐藏的时候回调
     * 隐藏popover的时候，需要清空当前已经举手的列表
     */
    popoverHideCallback() {
      this.$emit('onHide');
      this.isShowPopover = false;
      // TCIC.SDK.instance.clearHandUpMemberList();
      document.removeEventListener(
        'mousedown',
        this.interactionEventHandler,
        true,
      );
      document.removeEventListener(
        'touchstart',
        this.interactionEventHandler,
        true,
      );
    },

    // 举手人数变更回调
    countChangeCallback(count) {
      this.handUpCount = count;
    },

    // 初始化交互事件
    initInteractionEvent() {
      document.addEventListener(
        'touchstart',
        this.interactionEventHandler,
        true,
      );
      document.addEventListener(
        'mousedown',
        this.interactionEventHandler,
        true,
      );
    },

    /**
     * 交互事件的处理
     */
    interactionEventHandler(event) {
      const target = event.target;
      if (
        this.isShowPopover
        && !(
          this.$refs['handup-list__header-ref'].contains(target)
          || this.$refs['handup-list__content-ref'].$el.contains(target)
        )
      ) {
        // this.isShowPopover = false;
      }
    },
  },
};
</script>

<style lang="less">
.el-popover.el-popper.handup-list__popover {
  background: var(--pop-bg-color, #1c2131);
  box-shadow: 0px -3px 2px 0px rgba(0, 0, 0, 0.1);
  border: none;
  border-radius: 4px;
  @media screen and (orientation: portrait) {
    width: var(--handup-popover-width, 100vw);
    left: 0!important;
  }
  @media screen and (orientation: landscape) {
    width: var(--handup-popover-width, auto);
  }
  &.portrait{
    box-sizing: border-box;
  }
  &:not(.portrait):not(.small-screen) {
    top: auto !important;
  }

  .handup-list__header {
    padding: 20px 0 24px 0;
  }
  .handup-clear {
    padding: 0 20px;
  }
  .header-title {
    flex: 1;
  }
  &.small-screen {
    .handup-list__header {
      height: 32px;
      .header-title {
        font-size: 18px;
      }
    }
  }
}
.quick-im-component {
  .handup-icon__wrap {
    pointer-events: auto;
    margin-left: 16px;

    &.small-screen {
      margin-left: 0;
    }
  }

  &:not(.small-screen) {
    .handup-icon__wrap {
      padding-right: 8px;
    }
  }
}

.handup-icon__wrap {
  position: relative;
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  &.small-screen {
    justify-content: center;
  }

  &.handup-icon__wrap__blur {
    transition: all 1s;
    transition-delay: 3s;
    &:hover {
      opacity: 1;
      transition: all 0s;
    }
  }

  .handup-icon-layer-shade {
    width: 100%;
    height: 100%;
    position: absolute;
    opacity: .4;
    left: 0;
    top: 0;
    z-index: 10;
  }
  .handup-icon {
    position: relative;
    z-index: 9;
    padding: 4px;
    margin-left: 4px;
    flex-shrink: 0;
    cursor: pointer;

    .handup-icon__star {
      position: absolute;
      left: 0;
      top: -30px;
      width: 100%;
      height: 100%;
      background: url('./assets/handup-animation/ic_star.svg') no-repeat center
        center;
      background-size: contain;
      &.show {
        animation: handup-icon-star 300ms linear both infinite alternate;
      }
    }

    .handup-icon__arm {
      width: 32px;
      height: 32px;
      background-size: 32px 32px;
      background-position: 50%;
      transform-origin: bottom left;
      position: relative;
      &.handuping:after {
        content: '';
        width: 12px;
        height: 12px;
        position: absolute;
        bottom: 0;
        right: 0;
        background-image: url('./assets/icon-checked.svg');
        background-size: contain;
      }
      &.show {
        animation: handup-icon-arm 600ms linear both infinite alternate;
      }
    }
    .handup-icon__mobile{
      width: 24px;
      height: 24px;
      margin: -2px;
    }

    .handup-icon__body {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: url('./assets/handup-animation/ic_body.svg') no-repeat center
        center;
      background-size: contain;
    }

    .handup-icon__number {
      position: absolute;
      top: -6px;
      right: -6px;
      min-width: 21px;
      min-height: 21px;
      font-size: 12px;
      line-height: 21px;
      text-align: center;
      background: #fa6400;
      color: #fff;
      border-radius: 50%;
      outline: none;
    }
  }

  .handup-tip {
    position: relative;
    z-index: 9;
    white-space: nowrap;
    font-size: 14px;
    color: var(--text-color, #fff);
    height: 40px;
    line-height: 40px;
    padding: 0 6px 0 0px;
    flex-shrink: 0;
  }

  &.small-screen {
    padding: 6px;
    border-radius: 10px;

    .handup-icon {
      width: 16px;
      height: 16px;
      background-size: 100%;
      background-position: 50%;
      background-image: url('./assets/ic_hand_up.svg'); // 移动端先替换为旧版本
      position: relative;
      &.handuping:after {
        content: '';
        width: 12px;
        height: 12px;
        position: absolute;
        bottom: 0;
        right: 0;
        background-image: url('./assets/icon-checked.svg');
        background-size: contain;
      }
    }

    .handup-tip {
      font-size: 12px;
      height: 20px;
      line-height: 20px;
      padding: 0 3px;
    }
  }
}

@keyframes handup-icon-star {
  0% {
    opacity: 0.1;
  }
  100% {
    opacity: 1;
  }
}

@keyframes handup-icon-arm {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(-12deg);
  }

  100% {
    transform: rotate(0deg);
  }
}
</style>
