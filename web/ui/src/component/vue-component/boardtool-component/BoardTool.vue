
<template>
  <div
    v-show="showBoardTool"
    class="board-tools-component"
    :style="{
      '--tool-text-color': brushColor.textToolSelectedColor,
    }"
    :class="{obsolete: !isActiveState, 'pad-device': isPad}"
    @mouseenter="onMouseEnter"
    @mouseleave="onMouseLeave"
    @touchstart="onTouchStart"
  >
    <div
      class="board-tools-component-menu"
      :class="{ expand: expand, 'mobile-device': isMobile, 'small-screen-device': isSmallScreen}"
    >
      <!-- <div class="filter-blur-bg" /> -->
      <ul>
        <li
          v-show="expand || isActive(toolType.TEDU_BOARD_TOOL_TYPE_MOUSE)"
          :class="{
            active: isActive(toolType.TEDU_BOARD_TOOL_TYPE_MOUSE),
          }"
          @click="clickToolType(toolType.TEDU_BOARD_TOOL_TYPE_MOUSE)"
        >
          <el-tooltip
            effect="dark"
            :content="$t('鼠标')"
            placement="left-start"
            :visible-arrow="false"
            :manual="interactiveIsTouchEvent()"
          >
            <button>
              <!-- <i class="ic_tool_cursor" /> -->
              <IconCursor class="ic_tool_cursor" />
            </button>
          </el-tooltip>
        </li>

        <li
          v-show="expand || isActive(toolType.TEDU_BOARD_TOOL_TYPE_PEN)"
          ref="brush-graph-li-ref"
          :class="{
            active: isActive(toolType.TEDU_BOARD_TOOL_TYPE_PEN) || !expand,
          }"
          @click.capture="expandTool($event)"
        >
          <el-tooltip
            effect="dark"
            :content="$t('画笔')"
            placement="left-start"
            :visible-arrow="false"
            :manual="interactiveIsTouchEvent()"
          >
            <el-popover
              v-model="showPenPopover"
              popper-class="back-popper"
              placement="left-start"
              width="212"
              :visible-arrow="false"
              trigger="manual"
              @show="brushTypePopoverShowHandle"
              @hide="brushTypePopoverHideHandle"
            >
              <div
                ref="pen-popover-ref"
                :class="['common-drop', 'board-drop', { 'small-screen-device': isSmallScreen }]"
                @mouseenter="popoverMouseTouchEventHandler('enter', 'pen')"
                @mouseleave="popoverMouseTouchEventHandler('leave', 'pen')"
              >
                <div class="board-drop-list ic-point">
                  <!-- 笔刷粗细 -->
                  <ul>
                    <template v-for="(thin, index) in brushThin.values">
                      <li
                        :key="thin"
                        :class="{ active: brushThin.selectedIndex === index }"
                        @click="setBrushThin(index)"
                      >
                        <i :class="[thin]" />
                      </li>
                    </template>
                  </ul>
                </div>
                <!--颜色面板-->
                <div class="board-drop-list color-pick">
                  <ul>
                    <template v-for="color in brushColor.values">
                      <li
                        :key="color"
                        :class="{ active: color === brushColor.selectedColor }"
                        @click="setBrushColor(color)"
                      >
                        <i :style="'background:' + color" />
                      </li>
                    </template>
                  </ul>
                </div>
                <!--最近面板 === 使用始终只显示最近4个 -->
                <div
                  v-if="!isSmallScreen"
                  class="board-drop-list color-pick"
                  :class="historyBrushColor.length ? 'lately' : 'lately-empty'"
                >
                  <label
                    v-if="historyBrushColor.length"
                    class="text-lately"
                  >{{ $t('最近使用') }}</label>
                  <label
                    v-else
                    class="text-lately"
                  >{{ $t('无最近使用') }}</label>
                  <ul>
                    <template v-for="color in historyBrushColor">
                      <li
                        :key="color"
                        @click="setBrushColor(color)"
                      >
                        <i :style="'background:' + color" />
                      </li>
                    </template>
                  </ul>
                </div>
              </div>
              <!-- <i
                slot="reference"
                class="ic_tool_pen"
              /> -->
              <IconPencil
                slot="reference"
                class="ic_tool_pen"
              />
            </el-popover>
          </el-tooltip>
        </li>

        <li
          v-show="expand || brushType.types.indexOf(currentToolType) > -1"
          ref="graph-li-ref"
          :class="{
            active: brushType.types.indexOf(currentToolType) > -1 || !expand,
          }"
          @click="expandToolbar();setBrushType(brushType.selectedIndex);popoverMouseTouchEventHandler(showGraphPopover ? 'leave': 'enter', 'graph');"
        >
          <el-tooltip
            effect="dark"
            :content="$t('形状')"
            placement="left-start"
            :visible-arrow="false"
            :manual="interactiveIsTouchEvent()"
          >
            <el-popover
              v-model="showGraphPopover"
              popper-class="back-popper"
              placement="left-start"
              width="212"
              :visible-arrow="false"
              trigger="manual"
              @show="brushTypePopoverShowHandle"
              @hide="brushTypePopoverHideHandle"
            >
              <div
                ref="graph-popover-ref"
                :class="['common-drop', 'board-drop', { 'small-screen-device': isSmallScreen }]"
                @mouseenter="popoverMouseTouchEventHandler('enter', 'graph')"
                @mouseleave="popoverMouseTouchEventHandler('leave', 'graph')"
              >
                <!-- by开发：选择相关形状工具时，鼠标光标变成对应的工具形状和颜色 -->
                <div class="board-drop-list ic-shape">
                  <!-- 具体工具类型（直线，矩形，箭头，圆） -->
                  <ul>
                    <template v-for="(type, index) in brushType.values">
                      <li
                        :key="type"
                        :class="{ active: brushType.selectedIndex === index }"
                        @click="setBrushType(index)"
                      >
                        <i :class="[type]" />
                      </li>
                    </template>
                  </ul>
                </div>
                <div class="board-drop-list ic-point">
                  <!-- 实线虚线 -->
                  <ul>
                    <template v-for="(line, index) in lineType.values">
                      <li
                        :key="line"
                        :class="{ active: lineType.selectedIndex === index }"
                        @click="setLineType(index)"
                      >
                        <i :class="[`ic-line-${lineType.name[index]}`]" />
                      </li>
                    </template>
                  </ul>
                  <!-- 笔刷粗细 -->
                  <ul>
                    <template v-for="(thin, index) in brushThin.values">
                      <li
                        :key="thin"
                        :class="{ active: brushThin.selectedIndex === index }"
                        @click="setBrushThin(index)"
                      >
                        <i :class="[thin]" />
                      </li>
                    </template>
                  </ul>
                </div>

                <!--颜色面板-->
                <div class="board-drop-list color-pick">
                  <ul>
                    <template v-for="color in brushColor.values">
                      <li
                        :key="color"
                        :class="{ active: color === brushColor.selectedColor }"
                        @click="setBrushColor(color)"
                      >
                        <i :style="'background:' + color" />
                      </li>
                    </template>
                  </ul>
                </div>
                <!--最近面板 === 使用始终只显示最近4个 -->
                <div
                  v-if="!isSmallScreen"
                  class="board-drop-list color-pick"
                  :class="historyBrushColor.length ? 'lately' : 'lately-empty'"
                >
                  <label
                    v-if="historyBrushColor.length"
                    class="text-lately"
                  >{{ $t('最近使用') }}</label>
                  <label
                    v-else
                    class="text-lately"
                  >{{ $t('无最近使用') }}</label>
                  <ul>
                    <template v-for="color in historyBrushColor">
                      <li
                        :key="color"
                        @click="setBrushColor(color)"
                      >
                        <i :style="'background:' + color" />
                      </li>
                    </template>
                  </ul>
                </div>
              </div>
              <!-- <i
                slot="reference"
                class="ic_tool_shape"
              >
              </i> -->
              <IconShape
                slot="reference"
                class="ic_tool_shape"
              />
            </el-popover>
          </el-tooltip>
        </li>

        <li
          v-show="expand || isActive(toolType.TEDU_BOARD_TOOL_TYPE_ERASER)"
          ref="eraser-li-ref"
          :class="{
            active: isActive(toolType.TEDU_BOARD_TOOL_TYPE_ERASER),
          }"
          @click="clickToolType(toolType.TEDU_BOARD_TOOL_TYPE_ERASER);popoverMouseTouchEventHandler(showEraserPopover ? 'leave': 'enter', 'eraser');"
        >
          <el-tooltip
            effect="dark"
            :content="$t('橡皮')"
            placement="left-start"
            :visible-arrow="false"
            :manual="interactiveIsTouchEvent()"
          >
            <el-popover
              v-model="showEraserPopover"
              popper-class="back-popper"
              placement="left"
              :visible-arrow="false"
              trigger="manual"
            >
              <div
                ref="eraser-popover-ref"
                class="common-drop clean-drop"
                @mouseenter="popoverMouseTouchEventHandler('enter', 'eraser')"
                @mouseleave="popoverMouseTouchEventHandler('leave', 'eraser')"
              >
                <ul>
                  <li
                    :class="{ active: isActive(toolType.TEDU_BOARD_TOOL_TYPE_ERASER) }"
                    @click="setToolType(toolType.TEDU_BOARD_TOOL_TYPE_ERASER)"
                  >
                    <!-- <i class="ic_tool_eraser_sub_eraser" /> -->
                    <IconEraser class="ic_tool_eraser_sub_eraser" />
                  </li>
                  <li
                    v-show="currentBoardIsNotH5Element"
                    @click="operateBoard('clear')"
                  >
                    <!-- <i class="ic_tool_eraser_sub_clean" /> -->
                    <IconClean class="ic_tool_eraser_sub_clean" />
                  </li>
                </ul>
              </div>
              <!-- <i
                slot="reference"
                class="ic_tool_eraser"
              /> -->
              <IconEraser
                slot="reference"
                class="ic_tool_eraser"
              />
            </el-popover>
          </el-tooltip>
        </li>
        <li
          v-show="expand || isActive(toolType.TEDU_BOARD_TOOL_TYPE_RECT_SELECT)"
          :class="{
            flex: true,
            active:
              isActive(toolType.TEDU_BOARD_TOOL_TYPE_RECT_SELECT),
          }"
          @click="clickToolType(toolType.TEDU_BOARD_TOOL_TYPE_RECT_SELECT)"
        >
          <el-tooltip
            effect="dark"
            :content="$t('框选')"
            placement="left-start"
            :visible-arrow="false"
            :manual="interactiveIsTouchEvent()"
          >
            <!-- <i class="ic_tool_choice" /> -->
            <IconSelect class="ic_tool_choice" />
          </el-tooltip>
        </li>
        <li
          v-if="!isSmallScreen"
          v-show="expand || isActive(toolType.TEDU_BOARD_TOOL_TYPE_TEXT)"
          ref="text-li-ref"
          :class="{
            active: isActive(toolType.TEDU_BOARD_TOOL_TYPE_TEXT),
          }"
          @click="clickToolType(toolType.TEDU_BOARD_TOOL_TYPE_TEXT);popoverMouseTouchEventHandler(showTextPopover ? 'leave' : 'enter', 'text');"
        >
          <el-tooltip
            effect="dark"
            :content="$t('文本')"
            placement="left-start"
            :visible-arrow="false"
            :manual="interactiveIsTouchEvent()"
          >
            <el-popover
              v-model="showTextPopover"
              popper-class="back-popper"
              placement="left-start"
              width="212"
              :visible-arrow="false"
              trigger="manual"
            >
              <div
                ref="text-popover-ref"
                :class="['common-drop', 'board-drop', { 'small-screen-device': isSmallScreen }]"
                @mouseenter="popoverMouseTouchEventHandler('enter', 'text')"
                @mouseleave="popoverMouseTouchEventHandler('leave', 'text')"
              >
                <div class="board-drop-list popper-text">
                  <ul>
                    <template v-for="(value, index) in textSize.values">
                      <li
                        :key="value"
                        :class="{ active: textSize.selectedIndex === index }"
                        @click="setTextSize(index)"
                      >
                        <i :class="['text-' + value]">{{ value }}</i>
                      </li>
                    </template>
                  </ul>
                </div>
                <!--颜色面板-->
                <div class="board-drop-list color-pick">
                  <ul>
                    <template v-for="color in brushColor.values">
                      <li
                        :key="color"
                        :class="{
                          active: color === brushColor.textToolSelectedColor,
                        }"
                        @click="setTextColor(color)"
                      >
                        <i :style="'background:' + color" />
                      </li>
                    </template>
                  </ul>
                </div>
              </div>
              <!-- <i
                slot="reference"
                class="ic_tool_text"
                :style="tooTextIcon"
              /> -->
              <IconText
                slot="reference"
                class="ic_tool_text"
              />
            </el-popover>
          </el-tooltip>
        </li>
        <li
          v-show="expand || isActive(toolType.TEDU_BOARD_TOOL_TYPE_LASER)"
          :class="{
            active: isActive(toolType.TEDU_BOARD_TOOL_TYPE_LASER),
            flex: true,
          }"
          @click="clickToolType(toolType.TEDU_BOARD_TOOL_TYPE_LASER)"
        >
          <el-tooltip
            effect="dark"
            :content="$t('激光笔')"
            placement="left-start"
            :visible-arrow="false"
            :manual="interactiveIsTouchEvent()"
          >
            <!-- <i class="ic_tool_laser" /> -->
            <IconLaser class="ic_tool_laser" />
          </el-tooltip>
        </li>
        <li
          v-if="!isMobile"
          v-show="expand"
          class="flex"
          @click="operateBoard('snapshot')"
        >
          <el-tooltip
            effect="dark"
            :content="$t('截图')"
            placement="left-start"
            :visible-arrow="false"
            :manual="interactiveIsTouchEvent()"
          >
            <IconCut />
          </el-tooltip>
        </li>
        <li
          v-if="!isSmallScreen"
          v-show="expand"
          ref="math-li-ref"
          @click="popoverMouseTouchEventHandler(showMathPopover ? 'leave': 'enter', 'math');"
        >
          <el-tooltip
            effect="dark"
            :content="$t('绘图工具')"
            placement="left-start"
            :visible-arrow="false"
            :manual="interactiveIsTouchEvent()"
          >
            <el-popover
              v-model="showMathPopover"
              popper-class="back-popper"
              placement="left"
              :visible-arrow="false"
              trigger="manual"
            >
              <div
                ref="math-popover-ref"
                class="common-drop clean-drop"
                @mouseenter="popoverMouseTouchEventHandler('enter', 'math')"
                @mouseleave="popoverMouseTouchEventHandler('leave', 'math')"
              >
                <ul>
                  <template v-for="(item, index) in mathToolCfg.items">
                    <li
                      :key="index"
                      @click="useMathTool(item.type)"
                    >
                      <!-- <i :class="item.iconClass" /> -->
                      <Component :is="item.component" :class="item.iconClass"/>
                    </li>
                  </template>
                </ul>
              </div>
              <!-- <i
                slot="reference"
                class="ic_tool_math"
              /> -->
              <IconRuler
                slot="reference"
                class="ic_tool_math"
              />
            </el-popover>
          </el-tooltip>
        </li>
        <li
          v-if="!isSmallScreen"
          v-show="currentBoardIsNotH5Element && (expand || isActive(toolType.TEDU_BOARD_TOOL_TYPE_BOARD_CUSTOM_GRAPH))"
          ref="custom-li-ref"
          :class="{
            active: isActive(toolType.TEDU_BOARD_TOOL_TYPE_BOARD_CUSTOM_GRAPH),
          }"
          @click="expandToolbar();popoverMouseTouchEventHandler(showCustomPopover ? 'leave': 'enter', 'custom');"
        >
          <el-tooltip
            effect="dark"
            :content="$t('自定义图形')"
            placement="left-start"
            :visible-arrow="false"
            :manual="interactiveIsTouchEvent()"
          >
            <el-popover
              v-model="showCustomPopover"
              popper-class="back-popper"
              placement="left"
              :visible-arrow="false"
              trigger="manual"
            >
              <div
                ref="custom-popover-ref"
                class="common-drop list-drop"
                @mouseenter="popoverMouseTouchEventHandler('enter', 'custom')"
                @mouseleave="popoverMouseTouchEventHandler('leave', 'custom')"
              >
                <el-scrollbar>
                  <ul>
                    <template v-for="(item, index) in customGraphCfg.items">
                      <li
                        :key="index"
                        :class="{ active: customGraphCfg.selectedIndex === index }"
                        @click="useCustomGraph(index)"
                      >
                        <i
                          class="icon-bgcolor"
                          :style="`background-image: url(${item.url});background-color: ${boardBackgroundColor}`"
                        />
                        <label>{{ $t('自定义图形') }} {{ index + 1 }}</label>
                      </li>
                    </template>
                  </ul>
                </el-scrollbar>
                <ul>
                  <li
                    @click="customGraphDlgVisible=true"
                  >
                    <i class="ic_tool_custom_sub_add" />
                    <label>{{ $t('添加自定义图形') }}</label>
                  </li>
                </ul>
              </div>
              <!-- <i
                slot="reference"
                class="ic_tool_custom"
              /> -->
              <IconMath
                slot="reference"
                class="ic_tool_custom"
              />
            </el-popover>
          </el-tooltip>
        </li>
        <hr
          v-show="expand"
          class="ic-hr"
        >
        <!-- by开发：不可能状态加样式"disabled" -->
        <li
          v-show="expand"
          :class="{ disabled: !enableUndo, flex: true }"
          @click="operateBoard('undo')"
        >
          <el-tooltip
            effect="dark"
            :content="$t('撤销')"
            placement="left-start"
            :visible-arrow="false"
            :manual="interactiveIsTouchEvent()"
          >
            <!-- <i class="ic_tool_return" /> -->
            <IconBack class="ic_tool_return" />
          </el-tooltip>
        </li>
        <li
          v-show="expand"
          :class="{ disabled: !enableRedo, flex: true }"
          @click="operateBoard('redo')"
        >
          <el-tooltip
            effect="dark"
            :content="$t('恢复')"
            placement="left-start"
            :visible-arrow="false"
            :manual="interactiveIsTouchEvent()"
          >
            <!-- <i class="ic_tool_recover" /> -->
            <IconForward class="ic_tool_recover" />
          </el-tooltip>
        </li>
      </ul>
      <div
        v-if="isMobile"
        v-show="showToggleBtn"
        class="board-tools-component-btn"
        style="padding-bottom: 16px; padding-top: 8px"
        @click="toggleToolbar"
      >
        <i />
      </div>
    </div>
    <div
      v-if="!isMobile"
      v-show="showToggleBtn"
      class="board-tools-component-btn"
      @click="toggleToolbar"
    >
      <i />
    </div>

    <el-dialog
      :title="$t('添加自定义图形')"
      :visible.sync="customGraphDlgVisible"
      append-to-body
      custom-class="board-tools-dialog-add-custom-graph"
      :width="isSmallScreen ? isPortrait ? '80%' : '50%' : '400px'"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      modal
    >
      <div>
        <div style="display: flex">
          <span style="color: red; margin-right: 10px">*</span>
          <el-input
            v-model="customGraphDlgInput"
            :placeholder="$t('请输入图片地址（HTTPS协议）')"
            clearable
          />
        </div>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          size="mini"
          @click="addToCustomGraphList"
        >
          {{ $t('添加') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import i18next from 'i18next';
import Constant from '@/util/Constant';
import Snapshot from '@util/Snapshot';
import BaseComponent from '@core/BaseComponent';
import moment from 'moment';
import ToolbarIcon from './ToolIcon';
import Util from '@/util/Util';
import DocumentUtil from '@/util/Document';
const TEduBoard = window.TEduBoard;

import {
  IconEraser,
  IconBack,
  IconForward,
  IconPencil,
  IconText,
  IconCut,
  IconShape,
  IconRuler,
  IconCustom,
  IconCursor,
  IconLaser,
  IconStraightRuler,
  IconTriangleRuler,
  IconIsoscelesTriangleRuler,
  IconProtractor,
  IconCompasses,
  IconClean,
  IconMath,
  IconSelect,
} from './assets/svg-component';

const TEduBoardToolType = TEduBoard.TEduBoardToolType;
// console.log('TEduBoardToolType', TEduBoardToolType, IconMath);

const TEduMathToolType = TEduBoard.TEduMathToolType;
console.log('TEduMathToolType', TEduMathToolType);

// 自定义箭头
TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_ARROW = -1;

const popoverMap = {
  graph: {
    iconRefName: 'brush-graph-li-ref',
    popoverRefName: 'graph-popover-ref',
  },
  pen: {
    iconRefName: 'brush-graph-li-ref',
    popoverRefName: 'pen-popover-ref',
  },
  text: {
    iconRefName: 'text-li-ref',
    popoverRefName: 'text-popover-ref',
  },
  eraser: {
    iconRefName: 'eraser-li-ref',
    popoverRefName: 'eraser-popover-ref',
  },
  math: {
    iconRefName: 'math-li-ref',
    popoverRefName: 'math-popover-ref',
  },
  custom: {
    iconRefName: 'custom-li-ref',
    popoverRefName: 'custom-popover-ref',
  },
};

export default {
  name: 'BoardTools',
  components: {
    IconEraser,
    IconCut,
    IconBack,
    IconForward,
    IconPencil,
    IconText,
    IconShape,
    IconRuler,
    IconCustom,
    IconCursor,
    IconClean,
    IconMath,
    IconLaser,
    IconSelect,
  },
  extends: BaseComponent,
  mixins: [ToolbarIcon],
  data() {
    return {
      isElectron: TCIC.SDK.instance.isElectron(),
      isMobile: TCIC.SDK.instance.isMobile(),
      isPad: TCIC.SDK.instance.isPad(),
      isPortrait: false,
      showBoardTool: true,
      showPopover: '', // graph / eraser / text / math / custom
      popoverHideTask: null, // popover面板隐藏任务
      toolType: TEduBoardToolType,
      panelActiveStateTask: null, // 半透明白板工具栏任务
      isActiveState: true, // 白板工具栏是否是活跃状态
      expand: true, // 收起/展开
      showToggleBtn: true, // 收起-展开按钮
      brushColor: {
        selectedColor: '#FF0000',
        textToolSelectedColor: '#FF0000',
        values: [
          '#ffffff',
          '#9C9B9B',
          '#1F3756',
          '#000000',
          '#FF0000',
          '#7ED321',
          '#F8E71C',
          '#F5A623',
          '#0044FF',
          '#50E3C2',
          '#9013FE',
          '#FF959B',
        ],
      },
      // 将最近使用的颜色按顺序展示在最近使用中，越近使用排名越靠左
      historyBrushColor: [],

      brushThin: {
        selectedIndex: 1,
        values: ['point-8', 'point-12', 'point-16', 'point-20'],
        thins: [25, 50, 75, 100],
      },

      lineType: {
        selectedIndex: 0,
        values: [TEduBoard.TEduBoardLineType.TEDU_BOARD_LINE_TYPE_SOLID, TEduBoard.TEduBoardLineType.TEDU_BOARD_LINE_TYPE_DOTTED],
        name: ['solid', 'dotted'],
      },

      // 笔刷类型
      brushType: {
        selectedIndex: 0, // 默认为曲线
        values: ['ic-line', 'ic-box', 'ic-arrow', 'ic-circular'], // 曲线 矩形 椭圆 直线
        types: [
          TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_LINE,
          TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_RECT,
          TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_ARROW,
          TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_OVAL,
        ],
      },

      // 文本工具配置
      textSize: {
        selectedIndex: 1,
        values: [14, 16, 18, 20],
        sizes: [280, 320, 360, 400],
      },

      // 绘图工具配置
      mathToolCfg: {
        items: [
          {
            type: TEduMathToolType.RULER,
            iconClass: 'ic_tool_math_sub_straight_ruler',
            component: IconStraightRuler,
          },
          {
            type: TEduMathToolType.TRIANGLE,
            iconClass: 'ic_tool_math_sub_triangle_ruler',
            component: IconTriangleRuler,
          },
          {
            type: TEduMathToolType.ISOSCELES_TRIANGLE,
            iconClass: 'ic_tool_math_sub_isosceles_triangle_ruler',
            component: IconIsoscelesTriangleRuler,
          },
          {
            type: TEduMathToolType.PROTRACTOR,
            iconClass: 'ic_tool_math_sub_protractor',
            component: IconProtractor,
          },
          {
            type: TEduMathToolType.COMPASSES,
            iconClass: 'ic_tool_math_sub_compasses',
            component: IconCompasses,
          },
        ],
      },

      // 白板背景颜色
      boardBackgroundColor: TCIC.SDK.instance.getBoardBackgroundColor(),

      // 自定义图形配置
      customGraphCfg: {
        selectedIndex: -1,
        items: [
          { url: 'https://qcloudimg.tencent-cloud.cn/raw/8858506edfe98d8dab7f558bc8f222ac.svg' },
          { url: 'https://qcloudimg.tencent-cloud.cn/raw/b78e62583ebf5a2512ae527367dda3a2.svg' },
          { url: 'https://qcloudimg.tencent-cloud.cn/raw/8cf4b80361b93a2fc6ec25d6f4c2bd76.svg' },
        ],
      },
      customGraphDlgVisible: false,
      customGraphDlgInput: '',

      enableRedo: false, // 是否可以恢复
      enableUndo: false, //  是否可以撤销
      undoStatusBeforeScreenShare: false, // 屏幕共享前的撤销状态
      redoStatusBeforeScreenShare: false, // 屏幕共享前的重做状态
      currentToolType: TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_PEN,
      teduBoard: null,
      classId: null,
      isModifyBrushColor: false, // 是否有修复笔刷颜色
      canShowBoardTool: false,
      hideToolbar: false,
    };
  },
  computed: {
    showGraphPopover: {
      get() {
        return this.showPopover === 'graph';
      },
      set(newValue) {
        this.setPopoverState('graph', newValue);
      },
    },
    showPenPopover: {
      get() {
        return this.showPopover === 'pen';
      },
      set(newValue) {
        this.setPopoverState('pen', newValue);
      },
    },
    showEraserPopover: {
      get() {
        return this.showPopover === 'eraser';
      },
      set(newValue) {
        this.setPopoverState('eraser', newValue);
      },
    },
    showTextPopover: {
      get() {
        return this.showPopover === 'text';
      },
      set(newValue) {
        this.setPopoverState('text', newValue);
      },
    },
    showMathPopover: {
      get() {
        return this.showPopover === 'math';
      },
      set(newValue) {
        this.setPopoverState('math', newValue);
      },
    },
    showCustomPopover: {
      get() {
        return this.showPopover === 'custom';
      },
      set(newValue) {
        this.setPopoverState('custom', newValue);
      },
    },
    // 当前白板的内容不是 H5 元素
    currentBoardIsNotH5Element() {
      const currentBoardId = this.teduBoard?.getCurrentBoard();
      let onlineElIdMap = localStorage.getItem('onlineElIdMap');
      onlineElIdMap = onlineElIdMap ? JSON.parse(onlineElIdMap) : {};
      return !onlineElIdMap[currentBoardId];
    },
  },
  watch: {
    showPopover(value) {
      if (value) {
        clearTimeout(this.panelActiveStateTask);
        this.panelActiveStateTask = null;
        this.initInteractionEvent();
      } else {
        // 如果二级面板关闭了，则启动渐隐任务
        this.panelActiveStateTaskHandler();
      }
    },
  },

  mounted() {
    // 注册显示状态
    TCIC.SDK.instance.registerState(Constant.TStateIsShowBoardToolComponent, '白板工具条是否显示', false);
    // 联奕大教学模式不展示
    const isCollegeClass = TCIC.SDK.instance.isCollegeClass();
    if (isCollegeClass && this.isElectron) {
      this.hideToolbar = true;
    }
    this.isActiveState = true;
    this.panelActiveStateTaskHandler();
    this.initEvent();

    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.isPortrait = orientation === TCIC.TDeviceOrientation.Portrait;
      this.expand = !this.isPortrait;
      this.showToggleBtn = !this.isPortrait;
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Layout, (layout) => {
      this.updateComponent({
        zIndex: 351,      // 白板工具栏层级设置为350
      });
      this.updateVisiable();
    });


    // 监听白板工具变化
    this.addLifecycleTCICStateListener(Constant.TStateBoardToolType, (toolType) => {
      if (this.teduBoard) {
        if (toolType !== this.currentToolType) {
          TCIC.SDK.instance.reportLog('boardTool', `toolType change ${toolType}`);
          this.setToolTypeUI(toolType); // 修改一下UI
        }
      }
    }, {
      noEmitWhileSubscribe: true,
      noEmitWhileRegister: true,
    });

    TCIC.SDK.instance
      .promiseState(TCIC.TMainState.Board_Create, true)
      .then(() => {
        this.teduBoard = TCIC.SDK.instance.getBoard();
        // 白板已经创建
        this.initBoardEvent();
      });

    TCIC.SDK.instance
      .promiseState(TCIC.TMainState.Board_Ready, true)
      .then(() => {
        // 初始化设置画笔颜色
        this.teduBoard && this.setBrushColor(this.brushColor.selectedColor);

        // 初始化设置字体大小
        this.teduBoard && this.setTextSize(1);

        // 开启白板工具栏拖动功能
        this.toggleDraggable(true);
      });

    // 监听沉浸式
    this.addLifecycleTCICStateListener(Constant.TStateImmerseMode, this.onImmerseModeChange);

    this.addLifecycleTCICStateListener(Constant.TStateBigVideoMode, (isBigVideoMode) => {
      this.updateVisiable();
    });
  },

  beforeDestroy() {
    clearTimeout(this.popoverHideTask);
    clearTimeout(this.panelActiveStateTask);
  },

  methods: {
    // 判断当前使用的白板工具是否为某个特定的工具
    isActive(idealToolType) {
      return this.currentToolType === idealToolType;
    },
    onImmerseModeChange(enable) {
      if (!TCIC.SDK.instance.checkPermission(TCIC.TResourcePath.Board, TCIC.TPermissionFlag.Read)) {
        return;
      }
      if (enable) {
        this.isMouseMove = false;
        const isScreenShare = TCIC.SDK.instance.getState(TCIC.TMainState.Screen_Share, 2) !== 2;
        if (!isScreenShare || !this.isElectron) {
          TCIC.SDK.instance.setState(Constant.TStateIsShowBoardToolComponent, false);
          this.hide();
        }
      } else {
        if (this.canShowBoardTool) {
          TCIC.SDK.instance.setState(Constant.TStateIsShowBoardToolComponent, true);
          this.show();
        }
      }
    },
    onJoinClass() {
      this.classId = TCIC.SDK.instance.getClassInfo().classId;
      this.getHistoryBrushColor();
    },
    initEvent() {
      // 监听加入课堂事件
      this.makeSureClassJoined(this.onJoinClass);
      this.addLifecycleTCICStateListener(
        TCIC.TMainState.Screen_Share,
        (value) => {
          const isStartSubCamera = TCIC.SDK.instance.getState(Constant.TStateStartSubCamera);
          this.screenShareMode = (value === 0 || value === 1) && !isStartSubCamera;
          TCIC.SDK.instance.reportLog('boardTool', `screenShareMode ${this.screenShareMode}`);
          // 进入屏幕分享模式，将白板工具设置成鼠标
          if (this.screenShareMode) {
            this.setToolType(this.toolType.TEDU_BOARD_TOOL_TYPE_MOUSE);
          }

          if (value === 0) {
            // 开始屏幕共享保存撤销重做状态
            this.redoStatusBeforeScreenShare = this.enableRedo;
            this.undoStatusBeforeScreenShare = this.enableUndo;
            this.enableUndo = TCIC.SDK.instance.getState(Constant.TStateScreenShareBoardUndo);
            this.enableRedo = TCIC.SDK.instance.getState(Constant.TStateScreenShareBoardRedo);
          } else if (value === 2) {
            // 结束屏幕共享恢复撤销重做状态
            this.enableUndo = this.undoStatusBeforeScreenShare;
            this.enableRedo = this.redoStatusBeforeScreenShare;
          }
        },
      );
      // 屏幕共享状态下的白板撤销重做状态
      this.addLifecycleTCICStateListener(Constant.TStateScreenShareBoardRedo, (value, oldValue) => {
        this.enableRedo = value;
      });
      this.addLifecycleTCICStateListener(Constant.TStateScreenShareBoardUndo, (value, oldValue) => {
        this.enableUndo = value;
      });


      // 白板权限变更
      this.addLifecycleTCICStateListener(TCIC.TMainState.Board_Permission, (value, oldValue) => {
        if (!value) {
          // 如果权限被回收了, 则将二级菜单隐藏
          this.showPopover = '';
        }
      });

      // 权限变化
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Permission_Update, (permissionList) => {
        TCIC.SDK.instance.promiseState(Constant.TStateDeviceDetect, false).then(() => {
          if (!TCIC.SDK.instance.checkPermission(TCIC.TResourcePath.Board, TCIC.TPermissionFlag.Read)) {
            return;
          }
          // 遍历权限列表，找到具有屏幕分享权限(不是辅助摄像头)的人
          const sharerPermissionList = permissionList.filter(permission => permission.screen > 0 && permission.screen !== 3 && permission.screenState < 2);
          // 如果展示点播组件，则不主动显示白板工具栏
          const isVodPlaying = TCIC.SDK.instance.getState(Constant.TStateVodPlayerVisible, false);
          // 是否有白板权限
          const boardPermission = TCIC.SDK.instance.getState(TCIC.TMainState.Board_Permission, false);
          // 是否有人共享屏幕
          const isShareScreen = sharerPermissionList.length > 0;
          // 是否自己共享屏幕
          const isSelfShareScreen = isShareScreen && sharerPermissionList[0].userId === TCIC.SDK.instance.getUserId();
          // 是否是1v1纯视频课
          const isOneOnOneVideoClass = TCIC.SDK.instance.isOneOnOneClass() && !TCIC.SDK.instance.isClassLayoutHasDoc();
          // 显示白板工具条的条件
          // 1. 没有播放视频
          // 2. 没有设备检测
          // 3. 拥有白板权限
          // 4. 没有人屏幕共享或者自己在屏幕共享
          // 5. 不是1v1纯视频课
          const canShowBoardTool = !isVodPlaying && boardPermission && (!isShareScreen || (isSelfShareScreen && !TCIC.SDK.instance.isWeb())) && !isOneOnOneVideoClass;
          if (this.canShowBoardTool !== canShowBoardTool) { // 避免沉浸模式下，权限列表任何状态变更导致工具条显示出来
            this.canShowBoardTool = canShowBoardTool;
            if (this.canShowBoardTool) {
              TCIC.SDK.instance.setState(Constant.TStateIsShowBoardToolComponent, true);
              this.show();
            } else {
              TCIC.SDK.instance.setState(Constant.TStateIsShowBoardToolComponent, false);
              this.hide();
            }
          }
        });
      });
    },

    // 初始化交互事件
    initInteractionEvent() {
      document.addEventListener('touchstart', this.interactionEventHandler, true);
      document.addEventListener('mousedown', this.interactionEventHandler, true);
    },

    /**
     * 交互事件的处理
     */
    interactionEventHandler(event) {
      const target = event.target;
      if (!this.showPopover) {
        return;
      }
      const cfg = popoverMap[this.showPopover];
      if (!cfg) {
        return;
      }
      if (!(this.$refs[cfg.popoverRefName].contains(target) || this.$refs[cfg.iconRefName].contains(target))) {
        // 如果点击的范围不在对应的popover或icon，则隐藏popover
        this.showPopover = '';
        document.removeEventListener('mousedown', this.interactionEventHandler, true);
        document.removeEventListener('touchstart', this.interactionEventHandler, true);
      }
    },

    initBoardEvent() {
      this.teduBoard = TCIC.SDK.instance.getBoard();
      // 白板可恢复状态改变回调
      this.teduBoard.on(
        TEduBoard.EVENT.TEB_OPERATE_CANREDO_STATUS_CHANGED,
        (enable) => {
          this.enableRedo = enable;
        },
      );

      // 白板可撤销状态改变回调
      this.teduBoard.on(
        TEduBoard.EVENT.TEB_OPERATE_CANUNDO_STATUS_CHANGED,
        (enable) => {
          this.enableUndo = enable;
        },
      );

      // 监听事件，image为截图内容的base64数据
      this.teduBoard.on(TEduBoard.EVENT.TEB_SNAPSHOT, ({ image, userData }) => {
        TCIC.SDK.instance.reportLog('snapshot', `on TEB_SNAPSHOT, ${userData}`);
        let defaultPrevent = false;
        TCIC.SDK.instance.emit(TEduBoard.EVENT.TEB_SNAPSHOT, {
          image,
          userData,
          preventDefault: (prevent) => {
            defaultPrevent = prevent;
          },
        });
        if (!defaultPrevent) {
          this.saveSnapshot(image, userData);
        }
      });

      this.teduBoard.on(TEduBoard.EVENT.TEB_ADDELEMENT, ({ id, userData }) => {
        TCIC.SDK.instance.reportLog('boardTool', `on TEB_ADDELEMENT, id ${id}, userData ${userData}`);
      });

      this.teduBoard.on(TEduBoard.EVENT.TEB_GOTOBOARD, (boardIds, fileId, operator) => {
        TCIC.SDK.instance.reportLog('boardTool', `on TEB_GOTOBOARD, boardIds ${boardIds}, operator ${operator}`);
      });

      this.teduBoard.on(TEduBoard.EVENT.TEB_SWITCHFILE, (fileId) => {
        if (fileId !== '#DEFAULT') {
          const fileInfo = this.teduBoard.getFileInfo(fileId);
          console.log('======================:  ', 'TEB_SWITCHFILE', ' fileId:', fileId, fileInfo);
          // 视频课件
          if (fileInfo.fileType === 4) {
            this.hideToolbar = true;
          } else {
            this.hideToolbar = false;
          }
        } else {
          console.log('======================:  ', 'TEB_SWITCHFILE default', ' fileId:', fileId);
          this.hideToolbar = false;
        }
      });
    },

    /**
     * 设置Popover状态
     */
    setPopoverState(type, value) {
      if (value) {
        this.showPopover = type;
      } else {
        if (this.showPopover === type) {
          this.showPopover = '';
        }
      }
    },

    /**
     * 点击白板工具栏
     */
    clickToolType(type) {
      if (this.expand) {
        this.setToolType(type);
        if (type === this.toolType.TEDU_BOARD_TOOL_TYPE_RECT) {
          this.brushType.selectedIndex = this.brushType.types.findIndex(val => val === type);
        }
      } else {
        this.expandToolbar();
      }
    },

    /**
     * 设置白板工具栏
     */
    setToolType(type) {
      if (!this.teduBoard) return;
      TCIC.SDK.instance.reportLog('boardTool', `setToolType ${type}`);
      this.setToolTypeUI(type);
      TCIC.SDK.instance.getComponent('board-component').getVueInstance()
        .setToolType(type);
      const screenShareCallMethodParams = {
        module: 'board',
        method: 'setToolType',
        params: [type],
      }; // 屏幕分享下通信的数据
      this.callElectronMethod(screenShareCallMethodParams);
    },

    /**
     * 设置白板箭头
     */
    setToolArrow(arrow) {
      TCIC.SDK.instance.getComponent('board-component').getVueInstance()
          .setGraphStyle({
            endArrowType: arrow,
          });
      const screenShareCallMethodParams = {
        module: 'board',
        method: 'setGraphStyle',
        params: [{
          endArrowType: arrow,
        }],
      }; // 屏幕分享下通信的数据
      this.callElectronMethod(screenShareCallMethodParams);
    },

    /**
     * 设置白板箭头
     */
    setLineType(index) {
      this.lineType.selectedIndex = index;
      TCIC.SDK.instance.getComponent('board-component').getVueInstance()
          .setGraphStyle({
            lineType: this.lineType.values[index],
          });
      const screenShareCallMethodParams = {
        module: 'board',
        method: 'setGraphStyle',
        params: [{
          lineType: this.lineType.values[index],
        }],
      }; // 屏幕分享下通信的数据
      this.callElectronMethod(screenShareCallMethodParams);
    },

    /**
     * 设置白板工具栏UI
     */
    setToolTypeUI(type) {
      this.currentToolType = type;
    },

    /**
     * 设置白板工具类型
     */
    setBrushType(index) {
      this.brushType.selectedIndex = index;
      if (this.brushType.types[index] === TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_ARROW) {
        this.setToolType(TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_LINE);
        this.setToolArrow(TEduBoard.TEduBoardArrowType.TEDU_BOARD_ARROW_TYPE_SOLID);
      } else {
        this.setToolType(this.brushType.types[index]);
        this.setToolArrow(TEduBoard.TEduBoardArrowType.TEDU_BOARD_ARROW_TYPE_NONE);
      }
    },

    /**
     * 设置画笔粗细
     */
    setBrushThin(index) {
      if (!this.teduBoard) return;
      this.brushThin.selectedIndex = index;
      const thin = this.brushThin.thins[index];
      TCIC.SDK.instance.reportLog('boardTool', `setBrushThin ${thin}`);
      this.teduBoard.setBrushThin(thin);
      const screenShareCallMethodParams = {
        module: 'board',
        method: 'setBrushThin',
        params: [thin],
      }; // 屏幕分享下通信的数据
      this.callElectronMethod(screenShareCallMethodParams);
    },

    /**
     * 设置画笔颜色
     */
    setBrushColor(color) {
      if (!this.teduBoard) return;
      this.brushColor.selectedColor = color;
      TCIC.SDK.instance.reportLog('boardTool', `setBrushColor ${color}`);
      this.teduBoard.setBrushColor(color);
      TCIC.SDK.instance.setState(Constant.TStateBoardBrushColor, color);
      this.isModifyBrushColor = true; // 有修改了画笔颜色
      const screenShareCallMethodParams = {
        module: 'board',
        method: 'setBrushColor',
        params: [color],
      }; // 屏幕分享下通信的数据
      this.callElectronMethod(screenShareCallMethodParams);
    },

    /**
     * 设置文字工具字体大小
     */
    setTextSize(index) {
      if (!this.teduBoard) return;
      this.textSize.selectedIndex = index;
      const size = this.textSize.sizes[index];
      TCIC.SDK.instance.reportLog('boardTool', `setTextSize ${size}`);
      this.teduBoard.setTextSize(size);
      const screenShareCallMethodParams = {
        module: 'board',
        method: 'setTextSize',
        params: [size],
      }; // 屏幕分享下通信的数据
      this.callElectronMethod(screenShareCallMethodParams);
    },

    /**
     * 设置文字工具颜色
     * @param color #ff0000
     */
    setTextColor(color) {
      if (!this.teduBoard) return;
      this.brushColor.textToolSelectedColor = color;
      TCIC.SDK.instance.reportLog('boardTool', `setTextColor ${color}`);
      this.teduBoard.setTextColor(color);
      const screenShareCallMethodParams = {
        module: 'board',
        method: 'setTextColor',
        params: [color],
      }; // 屏幕分享下通信的数据
      this.callElectronMethod(screenShareCallMethodParams);
    },

    /**
     * 操作白板
     */
    operateBoard(operateType) {
      if (!this.teduBoard) return;
      let userData;
      const screenShareCallMethodParams = {
        module: 'board',
        method: '',
        params: [],
      }; // 屏幕分享下通信的数据
      switch (operateType) {
        case 'clear':
          TCIC.SDK.instance.showMessageBox(
            i18next.t('确定要清空白板内容吗？'),
            i18next.t('白板清空后，无法恢复'),
            [i18next.t('清空'), i18next.t('取消')],
            async (index) => {
              if (index === 0) {
                if (this.screenShareMode && this.isElectron) {
                  screenShareCallMethodParams.method = 'clear';
                  screenShareCallMethodParams.params = [];
                  this.callElectronMethod(screenShareCallMethodParams);
                } else {
                  await DocumentUtil.addSnapshotMark('clear');
                  TCIC.SDK.instance.reportLog('boardTool', 'clear');
                  this.teduBoard.clear();
                }
              }
            },
          );
          break;
        case 'snapshot':
          userData = `${this.classId}-${moment().format('YYYYMMDDHHmmss')}.png`; // `snapshot-${Date.now()}.png`;
          TCIC.SDK.instance.reportLog('snapshot', `screenShareMode ${this.screenShareMode}, ${userData}`);
          if (this.screenShareMode) {
            // 如果是屏幕分享
            TCIC.SDK.instance.snapshotVideo('', TCIC.TTrtcVideoStreamType.Sub)
              .then((snapshot) => {
                TCIC.SDK.instance.reportLog('snapshot', `snapshotVideo success, ${userData}`);
                this.saveSnapshot(snapshot?.data, userData);
              })
              .catch((err) => {
                TCIC.SDK.instance.reportLog('snapshot', `snapshotVideo error, ${userData}, ${err.toString()}`);
              });
          } else {
            // 调用截图接口
            screenShareCallMethodParams.method = '';
            screenShareCallMethodParams.params = [];
            this.teduBoard.snapshot({ userData });
            this.callElectronMethod(screenShareCallMethodParams);
          }
          break;
        case 'undo':
          if (this.screenShareMode && this.isElectron) {
            screenShareCallMethodParams.method = 'undo';
            screenShareCallMethodParams.params = [];
            this.callElectronMethod(screenShareCallMethodParams);
          } else {
            TCIC.SDK.instance.reportLog('boardTool', 'undo');
            this.teduBoard.undo();
          }
          break;
        case 'redo':
          if (this.screenShareMode && this.isElectron) {
            screenShareCallMethodParams.params = [];
            screenShareCallMethodParams.method = 'redo';
            this.callElectronMethod(screenShareCallMethodParams);
          } else {
            TCIC.SDK.instance.reportLog('boardTool', 'redo');
            this.teduBoard.redo();
          }
          break;
      }
    },

    /**
     * 具体实现在 electron/src/boardRenderer.ts 中
     */
    callElectronMethod(screenShareCallMethodParams, { isAutoCall = false } = {}) {
      // 如果是屏幕分享模式
      if (this.screenShareMode) {
        if (screenShareCallMethodParams.method) {
          !isAutoCall && TCIC.SDK.instance.reportLog('boardTool', `callElectronMethod, method ${screenShareCallMethodParams.method}, has window.Electron ${!!window?.Electron}`);
          window?.Electron?.callMethod(screenShareCallMethodParams);
        }

        // 如果是设置工具类型, 同时设置一下笔刷颜色，笔刷粗细，文字字号，文字颜色
        if (screenShareCallMethodParams.method === 'setToolType') {
          // 设置笔刷颜色
          this.callElectronMethod({
            module: 'board',
            method: 'setBrushColor',
            params: [this.teduBoard.getBrushColor()],
          }, { isAutoCall: true });
          this.callElectronMethod({
            module: 'board',
            method: 'setBrushThin',
            params: [this.teduBoard.getBrushThin()],
          }, { isAutoCall: true });
          this.callElectronMethod({
            module: 'board',
            method: 'setTextSize',
            params: [this.teduBoard.getTextSize()],
          }, { isAutoCall: true });
          this.callElectronMethod({
            module: 'board',
            method: 'setTextColor',
            params: [this.teduBoard.getTextColor()],
          }, { isAutoCall: true });
        }
        screenShareCallMethodParams.params = [];
        screenShareCallMethodParams.method = '';
      }
    },

    // 保存截图，image 图片的base64数据
    saveSnapshot(image, snapshotName) {
      const snapName = snapshotName || `${this.classId}-${moment().format('YYYYMMDDHHmmss')}.png`;
      if (!image?.length) {
        TCIC.SDK.instance.reportLog('snapshot', `saveSnapshot error, ${snapName}, image data empty`);
        window.showToast(i18next.t('保存截图失败，请重试'), 'error');
        return;
      }
      // 保存截图
      TCIC.SDK.instance.reportLog('snapshot', `saveSnapshot, ${snapName}, ${image?.substr(0, 50)}`);
      Snapshot.saveSnapshot(image, snapName).then(
        (result) => {
          console.log('saveSnapshot success', result);
          TCIC.SDK.instance.reportLog('snapshot', `saveSnapshot success, ${snapName}`);
          let tipText = i18next.t('截图保存成功');
          if (this.isElectron) {
            tipText = i18next.t('截图已保存至桌面');
          }
          window.showToast(tipText, 'success');
        },
        (error) => {
          console.error('saveSnapshot error', error);
          TCIC.SDK.instance.reportLog(
            'snapshot',
            `saveSnapshot error, ${snapName}, code: ${error.errcode}, message: ${error.errmsg}`,
          );
          if (error.module === 'electron') {
            // 如果是electron截图报错，则用web的兜底方案
            window.saveSnapshot(error.snapId, image, snapName);
          } else {
            window.showToast(i18next.t('保存截图失败，请重试'), 'error');
          }
        },
      );
    },

    /**
     * 展开白板工具栏
     */
    expandTool(event) {
      // 如果工具栏是展开的
      if (this.expand) {
        // 如果显示了画笔面板
        if (this.showPenPopover) {
          this.popoverMouseTouchEventHandler('leave', 'pen');
        } else {
          this.popoverMouseTouchEventHandler('enter', 'pen');
        }
        // 如果工具栏面板是展开的
        // 此时被点击，则默认切换为当前面板选择的工具
        this.setToolType(TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_PEN);
        this.setBrushThin(this.brushThin.selectedIndex);
        this.setBrushColor(this.brushColor.selectedColor);
      } else {
        // 如果工具栏面板是折叠的
        this.expandToolbar();
        event.stopPropagation();
      }
    },

    // 展开或者折叠白板工具
    toggleToolbar() {
      if (this.expand) {
        this.collapseToolbar();
      } else {
        this.expandToolbar();
      }
    },

    // 展开白板工具
    expandToolbar() {
      this.expand = true;
      this.showToggleBtn = true;
    },

    // 折叠白板工具
    collapseToolbar() {
      this.expand = false; // 折叠起来
      this.showToggleBtn = false; // 是否显示折叠按钮
    },

    /**
     * 获取历史笔刷数据
     */
    getHistoryBrushColor() {
      const key = `TIW_BRUSH_HISTORY_COLOR-${this.classId}`;
      const historyBrushColor = localStorage.getItem(key);
      if (historyBrushColor) {
        this.historyBrushColor = JSON.parse(historyBrushColor);
        if (Util.isArray(this.historyBrushColor) && this.historyBrushColor.length) {
          this.brushColor.selectedColor = this.historyBrushColor[0];
          this.teduBoard && this.setBrushColor(this.brushColor.selectedColor);
        }
      } else {
        this.historyBrushColor = [];
      }
    },

    /**
     * 设置历史的笔刷数据
     */
    setHistoryBrushColor() {
      const key = `TIW_BRUSH_HISTORY_COLOR-${this.classId}`;
      localStorage.setItem(
        key,
        JSON.stringify(this.historyBrushColor || []),
      );
    },

    /**
     * 笔刷配置的popover显示时
     */
    brushTypePopoverShowHandle() {
      this.isModifyBrushColor = false; // 是否有修改笔刷颜色
    },

    /**
     * 笔刷配置的popover隐藏时
     */
    brushTypePopoverHideHandle() {
      if (this.isModifyBrushColor) {
        // 如果有修改了画笔颜色
        const color = this.brushColor.selectedColor;
        this.historyBrushColor = this.historyBrushColor.filter(item => item !== color);
        this.historyBrushColor.unshift(color);
        if (this.historyBrushColor.length > 4) {
          this.historyBrushColor.length = 4; // 截掉多余的
        }
        this.setHistoryBrushColor();
        this.isModifyBrushColor = false; // 是否有修复笔刷颜色
      }
    },

    /**
     * 工具栏面板是否激活状态监测任务
     */
    panelActiveStateTaskHandler() {
      // 经过测试这里不能准确判断盒子/tv 与 手机/pad 的区别
      // 适配android TV 模式下支持遥控板，不默认隐藏白板工具栏
      // if (TCIC.SDK.instance.isMobileNative()) {
      //   return;
      // }
      this.panelActiveStateTask = setTimeout(() => {
        this.panelActiveStateTask = null;
        this.isActiveState = false;
      }, 3000);
    },

    // 鼠标进入工具栏
    onMouseEnter() {
      clearTimeout(this.panelActiveStateTask);
      this.panelActiveStateTask = null;
      this.isActiveState = true;
    },

    // 鼠标移出工具栏
    onMouseLeave() {
      if (this.showPopover) {
        // 显示二级面板期间不启动隐藏任务
      } else {
        // 如果几个二级面板都没有展开，才启动隐藏任务
        this.panelActiveStateTaskHandler();
      }
    },

    // 触发touch事件，则3s后透明工具栏
    onTouchStart() {
      clearTimeout(this.panelActiveStateTask);
      this.panelActiveStateTask = null;
      this.isActiveState = true;
      if (this.showPopover) {
        // 显示二级面板期间不启动隐藏任务
      } else {
        // 如果几个二级面板都没有展开，才启动隐藏任务
        this.panelActiveStateTaskHandler();
      }
    },

    // popover鼠标和touch事件处理
    popoverMouseTouchEventHandler(eventType, toolType) {
      clearTimeout(this.popoverHideTask);

      if (eventType === 'enter') {
        this.showPopover = toolType;
      } else if (eventType === 'leave') {
        this.popoverHideTask = setTimeout(() => {
          this.showPopover = '';
        }, 200);
      }
    },
    updateVisiable() {
      const classLayout = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Layout, TCIC.TClassLayout.Top);
      let isTeacherVideoMax = TCIC.SDK.instance.getState(Constant.TStateBigVideoMode, false);
      if (TCIC.SDK.instance.isInteractClass() &&  TCIC.SDK.instance.isVideoDocClass() && classLayout !== TCIC.TClassLayout.Three) {
        isTeacherVideoMax = false; // 互动班课非三分屏布局时，不存在大视频模式
      }

      if (isTeacherVideoMax || (this.isSmallScreen && !TCIC.SDK.instance.isTeacher())) {
        this.toggleDraggable(false);
        // 大视频模式、学生手机端不显示工具栏
        this.hideToolbar = true;
      } else {
        this.hideToolbar = false;
        setTimeout(() => {
          this.toggleDraggable(true);
        }, 50);
      }
    },
    toggleDraggable(draggable) {
      if (this.isElectron || this.isPad || TCIC.SDK.instance.isWeb()) {
        this.toggleComponentDrag(draggable, '.board-tools-component', null, false);
      }
    },

    /**
     * 使用绘图工具
     */
    useMathTool(type) {
      if (!this.teduBoard) return;
      // 绘图用曲线笔刷
      this.setBrushType(0);
      if (this.screenShareMode && this.isElectron) {
        this.callElectronMethod({
          module: 'board',
          method: 'useMathTool',
          params: [type],
        });
      } else {
        TCIC.SDK.instance.reportLog('boardTool', `useMathTool ${type}`);
        this.teduBoard.useMathTool(type);
      }
    },

    /**
     * 使用自定义图形
     */
    useCustomGraph(index) {
      if (!this.teduBoard) return;
      this.customGraphCfg.selectedIndex = index;
      const url = this.customGraphCfg.items[index].url;
      console.log('useCustomGraph', index, url);
      this.setToolType(TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_BOARD_CUSTOM_GRAPH);
      if (this.screenShareMode && this.isElectron) {
        this.callElectronMethod({
          module: 'board',
          method: 'addElement',
          params: [TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_CUSTOM_GRAPH, url],
        });
      } else {
        TCIC.SDK.instance.reportLog('boardTool', `addElement CustomGraph ${url}`);
        this.teduBoard.addElement(
          TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_CUSTOM_GRAPH,
          url,
        );
      }
      window.showToast(i18next.t('已设置自定义图形，请在白板上拖动进行绘制'));
    },

    /**
     * 添加自定义图形
     */
    addToCustomGraphList() {
      const url = this.customGraphDlgInput;
      if (!url) {
        window.showToast(i18next.t('请输入图片地址（HTTPS协议）'));
        return;
      }
      const [urlWithoutQuery] = url.split('?');
      if (!/^https:\/\//i.test(urlWithoutQuery) || !/\.(bmp|jpg|jpeg|png|gif|svg|webp|ico)$/i.test(urlWithoutQuery)) {
        window.showToast(i18next.t('请输入正确的图片地址'));
        return;
      }
      this.customGraphDlgVisible = false;
      this.customGraphDlgInput = '';
      let index = this.customGraphCfg.items.findIndex(item => item.url === url);
      if (index < 0) {
        this.customGraphCfg.items.push({
          url,
          fromUser: true,
        });
        index = this.customGraphCfg.items.length - 1;
      }
      this.useCustomGraph(index);
    },
  },
};
</script>
<style lang="less">
@--color-primary: #006eff;
@--color-public: #14181d;
@--color-disable: #b9bbbc;
@--color-back: #1c2131;
@--color-iconbg: #333;

@--btn-size: 28px;
@--btn-size-small: 28px;
@--btn-icon-size: 24px;
@--btn-icon-size-small: 22px;
@--btn-border-radius: 2px;
@--btn-margin: 8px 6px;
@--btn-margin-small: 6px;
@--tool-width: 52px; // 36 + 8 * 2

@--popover-btn-size: 36px;
@--popover-btn-size-small: 26px;
@--popover-btn-icon-size: 24px;
@--popover-btn-border-radius: 2px;
@--popover-btn-margin: 8px 8px;

@--popover-list-item-width: auto;
@--popover-list-item-height: 48px;
@--popover-list-item-border-radius: 4px;
@--popover-list-item-padding: 12px;
@--popover-list-item-margin: 8px 8px;

// @images:
//   ic_tool_eraser_sub_eraser,
//   ic_tool_eraser_sub_clean,
//   ic_tool_math_sub_straight_ruler,
//   ic_tool_math_sub_triangle_ruler,
//   ic_tool_math_sub_isosceles_triangle_ruler,
//   ic_tool_math_sub_protractor,
//   ic_tool_math_sub_compasses;
// .loop(@index) when (@index > 0) {
//   @image: extract(@images, @index);
//   .@{image} {
//     background: url('@assets/images/tools/@{image}.svg') no-repeat center;
//   }
//   .loop(@index - 1);
// }

// .loop(length(@images));

.ic_tool_custom_sub_add::before {
  content: '\e6d9';
  color: var(--icon-color, #fff);
  font-family: element-icons;
  font-size: 18px;
}

.light{
  .common-drop{
    --bg-color: #fff;
    .active, li:not(.disabled):hover {
      --icon-color: #fff;
    }
  }
  .board-tools-component{
    --bg-color: #fff;
    --text-color: #1D2029;
    --icon-color: #1D2029;
    .active, li:not(.disabled):hover {
      --icon-color: #fff;
    }
  }
}

/*白板工具栏*/
.board-tools-component {
  --icon-color: #fff;
  position: relative;
  top: 0;
  .flex {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .disabled {
    --icon-color: @--color-disable;
  }
  &.obsolete {
    opacity: 1;
    transition: all 1s;
    &:hover {
      opacity: 1;
      transition: all 1s;
    }

    &:active {
      opacity: 1;
      transition: all 1s;
    }
  }
  .mobile-device {
    ul li:hover {
      span.tip {
        display: none;
      }
    }

    ul li:active {
      span.tip {
        display: none;
      }
    }
  }


  .small-screen-device {
    ul li {
      margin: @--btn-margin-small;
      width: @--btn-size-small;
      height: @--btn-size-small;
    }
    ul i {
      width: @--btn-size-small;
      height: @--btn-size-small;
      background-size: @--btn-icon-size-small;
    }
  }

  &-menu {
    display: flex;
    position: relative;
    z-index: 2;
    border-radius: 4px;
    //background: @--color-back;
    background: var(--bg-color, #1C2333);
    box-shadow: 0 0 3px 0 rgba(32, 32, 32, 0.4);
    padding: 0;
    overflow: hidden;

    &.expand {
      display: block;
      box-shadow: 0 3px 5px 0 rgba(0, 0, 0, 0.2);
    }

    ul {
      position: relative;
      z-index: 9;
      li {
        position: relative;
        width: @--btn-size;
        height: @--btn-size;
        margin: @--btn-margin;
        border-radius: @--btn-border-radius;
        &:hover {
          transition: all 0.6s;
          background: @--color-primary;
        }

        &>span.el-tooltip, button.el-tooltip {
          display: flex;
          cursor: pointer;
          align-items: center;
          justify-content: center;
          height: @--btn-size;
          width: @--btn-size;
        }

        &:active, &.active {
          transition: all 0.6s;
          background: @--color-primary;
        }

        &:hover {
          span.tip {
            display: block;
          }
        }

        &:active {
          span.tip {
            display: block;
          }
        }

        &.disabled {
          i {
            opacity: 0.3;
          }

          &:hover {
            background: transparent;
          }

          &:active {
            background: transparent;
          }
        }

        span {
          &.tip {
            position: absolute;
            right: 30px;
            bottom: 60px;
            padding: 8px;
            border-radius: 4px;
            white-space: nowrap;
            color: #fff;
            font-size: 14px;
            background: @--color-back;
            display: none;
          }

          &.corner-mark {
            position: absolute;
            width: 8px;
            height: 8px;
          }
        }

        i {
          display: flex;
          align-items: center;
          justify-content: center;
          width: @--btn-size;
          height: @--btn-size;
          background-size: @--btn-icon-size;
        }
      }

      .ic-hr {
        display: flex;
        width: @--btn-size;
        margin: 0 auto;
        height: 1px;
        border: 1px solid #8a9099;
        opacity: 0.5;
      }
    }

  }

  &-btn {
    position: relative;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 70%;
    height: 16px;
    margin: -4px auto;
    //background: @--color-back;
    background: var(--bg-color, #1C2333);;
    box-shadow: 0 0 3px 0 rgba(32, 32, 32, 0.4);
    border-radius: 0 0 4px 4px;
    &:hover {
      background: @--color-primary;
      transition: all 0.6s;
      --icon-color: #fff;
    }

    &:active {
      background: @--color-primary;
      transition: all 0.6s;
    }

    i {
      display: flex;
      width: 16px;
      height: 8px;
      background-size: contain;
      transition: all 0.3s;
      &:before{
        content: '\e6e1';
        color: var(--icon-color, #fff);
        font-family: element-icons;
        font-size: 16px;
        margin-top: -4px;
      }
    }
  }

  /* pad 样式 */
  &.pad-device {
    .board-tools-component-menu ul li i {
      width: 40px;
      height: 40px;
      background-size: 27px;
    }
    .board-tools-component-btn {
      margin-left: 3px;
    }
  }
  /* 黑色弹框 */
  .back-dialog {
    .el-dialog {
      padding: 25px;
      background: #1c2131;

      h3 {
        color: #e3e3e3;
        font-size: 16px;
        margin: 20px 0 10px 0;
      }

      span {
        &.tip {
          color: #fff;
          font-size: 12px;
        }
      }

      .el-dialog__header {
        padding: 0;
      }

      .el-dialog__body {
        padding: 0;
      }

      .el-dialog__footer {
        padding: 0;
        margin-top: 30px;

        .el-button {
          width: 64px;
          height: 30px;
          padding: 0;

          &--border {
            border: 1px solid #888;
            color: #fff;
            background: none;
          }
        }
      }
    }
  }

  /*橡皮-清除弹框*/

  .back-dialog {
    .el-dialog {
      &.clean-dialog {
        .el-dialog__header {
          .el-dialog__headerbtn {
            font-weight: bold;
          }
        }

        .el-dialog__body {
          padding: 10px;
        }
      }
    }
  }

  /* 修复在mac端收缩端工具栏，被展开时会被focus上端bug */
  li:focus, span:focus, i:focus {
    outline: none;
  }
}

.el-popover.el-popper {
  /*工具内容选择面板*/

  &.back-popper {
    min-width: 0px;
    padding: 0 16px 0 0;
    background: transparent;
    border: none;
    box-shadow: none;
    margin-right: 0;

    .common-drop {
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2);
      border-radius: 4px;
      background: var(--bg-color, @--color-back);
      color: var(--text-color, #fff);

      .icon-bgcolor {
        background-color: @--color-iconbg;
      }
    }

    .board-drop {
      padding: 0 16px;

      &-list {
        padding: 10px 0;
        border-bottom: 0.5px solid rgba(#8a9099, 0.5);

        &:last-child {
          border-bottom: none;
        }

        ul {
          display: flex;
          flex-wrap: wrap;
          padding: 0 8px;
          margin: 0 -10px;

          li {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            margin: 5px;
            border-radius: 2px;
          }
        }

        &.popper-text {
          li {
            &:hover {
              background: @--color-primary;
              --text-color: #fff;
              cursor: default;
            }

            &:active, &.active {
              background: @--color-primary;
              --text-color: #fff;
              cursor: default;
            }

            i {
              color: var(--text-color, #fff);
              &.text-20 {
                font-size: 20px;
              }

              &.text-14 {
                font-size: 14px;
              }

              &.text-16 {
                font-size: 16px;
              }

              &.text-18 {
                font-size: 18px;
              }
            }
          }
        }

        &.ic-point {
          li {
            &:hover {
              background: @--color-primary;

              i {
                background: #fff;
                border-color: #fff;
              }
            }

            &:active, &.active {
              background: @--color-primary;

              i {
                background: #fff;
                border-color: #fff;
              }
            }
          }

          i {
            display: flex;
            background: #666;
            border-radius: 20px;

            &.ic-line-solid {
              width: 18px;
              height: 1px;
              border: 1px solid #666;
            }
            &.ic-line-dotted {
              width: 18px;
              height: 1px;
              background: transparent!important;
              border: 1px dashed #666;
            }

            &.point-8 {
              width: 8px;
              height: 8px;
            }

            &.point-12 {
              width: 12px;
              height: 12px;
            }

            &.point-16 {
              width: 16px;
              height: 16px;
            }

            &.point-20 {
              width: 20px;
              height: 20px;
            }
          }
        }

        &.ic-shape {
          li {
            &:hover {
              background: @--color-primary;

              i {
                border-color: #fff;
                &.ic-arrow {
                  background:url('./assets/ic-arrow.svg') no-repeat center center;
                }
              }
            }

            &:active, &.active {
              background: @--color-primary;
              i {
                border-color: #fff;
                &.ic-arrow {
                  background:url('./assets/ic-arrow.svg') no-repeat center center;

                }
              }
            }
          }

          i {

            &.ic-box {
              width: 18px;
              height: 16px;
              border: 2px solid #666;
            }

            &.ic-arrow {
              width: 20px;
              height: 20px;
              background:url('./assets/ic-arrow-gray.svg') no-repeat center center;
              background-size: contain;
            }

            &.ic-line {
              width: 18px;
              height: 1px;
              border: 1px solid #666;
              transform: rotate(-45deg);
            }

            &.ic-circular {
              width: 20px;
              height: 20px;
              border: 2px solid #666;
              border-radius: 20px;
            }
          }
        }

        &.color-pick {
          li {
            &:hover {
              background-color: @--color-primary;
              i {
                // border: 2px solid #fff;

              }
            }

            &:active, &.active {
              background-color: @--color-primary;
              i {
                // border: 2px solid #fff;

              }
            }
          }

          i {
            width: 20px;
            height: 20px;
            border: 1px solid #999;
            border-radius: 10px;
            background: #666;
          }
        }

        &.lately {
          padding-top: 20px;
          padding-bottom: 14px;

          &-empty {
            padding-bottom: 24px;
          }
        }

        .text-lately {
          padding-left: 16px;
          font-size: 12px;
        }
      }

      &.small-screen-device{
        scale: 0.8;
      }
    }

    .clean-drop {
      ul {
        display: flex;

        li {
          display: flex;
          align-items: center;
          justify-content: center;
          width: @--popover-btn-size;
          height: @--popover-btn-size;
          border-radius: @--popover-btn-border-radius;
          margin: @--popover-btn-margin;

          &:hover {
            background: @--color-primary;
          }

          &:active, &.active {
            background: @--color-primary;
          }

          i {
            display: flex;
            align-items: center;
            justify-content: center;
            width: @--popover-btn-size;
            height: @--popover-btn-size;
            background-size: @--popover-btn-icon-size;
          }
        }
      }
    }

    .list-drop {
      padding: 6px 0;

      .el-scrollbar__wrap {
        max-height: 226px;
        margin-bottom: 0px !important;
      }

      ul {
        li {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          color: var(--text-color, #fff);
          width: @--popover-list-item-width;
          height: @--popover-list-item-height;
          border-radius: @--popover-list-item-border-radius;
          padding: @--popover-list-item-padding;
          margin: @--popover-list-item-margin;

          &:hover {
            background: @--color-primary;
            --text-color: #fff;
          }

          &:active, &.active {
            background: @--color-primary;
            --text-color: #fff;
          }

          i {
            display: inline-block;
            width: @--popover-btn-icon-size;
            height: @--popover-btn-icon-size;
            margin-right: 8px;
            background-position: center;
            background-repeat: no-repeat;
            background-size: contain;
          }

          label {
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            line-height: @--popover-btn-icon-size;
          }
        }
      }
    }
  }
}

.brush-tool__corner {
  position: absolute;
  right: 8px;
  bottom: 8px;

  &.tool-type-1 {
    // 画笔工具
    right: 12px;
    width: 5px;
    height: 5px;
    border-top: 2px solid var(--border-color);
    border-left: 2px solid var(--border-color);
    border-top-left-radius: 100%;
    transform: rotate(40deg) scale(1.2);
    display: block;

    &::after {
      content: '';
      position: absolute;
      left: 2px;
      bottom: 2px;
      width: 3px;
      height: 3px;
      border-bottom: 2px solid var(--border-color);
      border-right: 2px solid var(--border-color);
      border-bottom-right-radius: 100%;
    }
  }

  &.tool-type-4 {
    // 直线
    position: absolute;
    right: 10px;
    width: 8px;
    height: 1px;
    border-bottom: 2px solid var(--border-color);
  }

  &.tool-type-5 {
    // 圆形
    position: absolute;
    width: 7px;
    height: 7px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
  }

  &.tool-type-6 {
    // 矩形
    position: absolute;
    width: 7px;
    height: 7px;
    border: 2px solid var(--border-color);
  }
}
</style>
