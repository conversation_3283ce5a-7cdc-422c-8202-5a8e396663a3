import IconEraser from './eraser.svg';
import IconBack from './back.svg';
import IconForward from './forward.svg';
import IconPencil from './pencil.svg';
import IconText from './text.svg';
import IconShape from './shape.svg';
import IconRuler from './ruler.svg';
import IconCustom from './custom.svg';
import IconCursor from './arrow.svg';
import IconLaser from './laser.svg';
import IconCut from './cut.svg';
import IconMath from './math.svg';
import IconClean from './clean.svg';
import IconStraightRuler from './ic_tool_math_sub_straight_ruler.svg';
import IconTriangleRuler from './ic_tool_math_sub_triangle_ruler.svg';
import IconIsoscelesTriangleRuler from './ic_tool_math_sub_isosceles_triangle_ruler.svg';
import IconProtractor from './ic_tool_math_sub_protractor.svg';
import IconCompasses from './ic_tool_math_sub_compasses.svg';
import IconSelect from './select.svg';

//   ic_tool_math_sub_straight_ruler,
//   ic_tool_math_sub_triangle_ruler,
//   ic_tool_math_sub_isosceles_triangle_ruler,
//   ic_tool_math_sub_protractor,
//   ic_tool_math_sub_compasses;
export {
   IconEraser,
   IconBack,
   IconForward,
   IconPencil,
   IconText,
   IconShape,
   IconRuler,
   IconCustom,
   IconCursor,
   IconLaser,
   IconMath,
   IconCut,
   IconClean,
   IconStraightRuler,
   IconTriangleRuler,
   IconIsoscelesTriangleRuler,
   IconProtractor,
   IconCompasses,
   IconSelect,
};


