<svg width="179" height="179" viewBox="0 0 179 179" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.4" filter="url(#filter0_d_130_4742)">
<path d="M127.427 25.3631C149.321 38.338 164 62.2048 164 89.5C164 130.645 130.645 164 89.5 164C48.3548 164 15 130.645 15 89.5C15 48.3548 48.3548 15 89.5 15" stroke="url(#paint0_linear_130_4742)" style="" stroke-linecap="round" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_d_130_4742" x="0.5" y="0.5" width="178" height="178" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="7"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.647059 0 0 0 0 0.996078 0 0 0 0 0.2 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_130_4742"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_130_4742" result="shape"/>
</filter>
<linearGradient id="paint0_linear_130_4742" x1="128.5" y1="23.5" x2="126" y2="2" gradientUnits="userSpaceOnUse">
<stop stop-color="#A5FE33" style="stop-color:#A5FE33;stop-color:color(display-p3 0.6471 0.9961 0.2000);stop-opacity:1;"/>
<stop offset="1" stop-color="#A5FE33" stop-opacity="0" style="stop-color:none;stop-color:none;stop-opacity:0;"/>
</linearGradient>
</defs>
</svg>
