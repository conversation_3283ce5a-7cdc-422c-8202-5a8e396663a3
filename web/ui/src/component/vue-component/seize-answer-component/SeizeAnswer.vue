<!-- eslint-disable vue/max-attributes-per-line -->
<template>
  <div class="seize-answer-dialog">
    <div class="seize-answer-dialog-hd">
      <div>{{ $t('抢答器') }}</div>
      <i v-if="currentStatus !== 1 && currentStatus !== 2 && (isTeacher || isAssistant)" class="el-icon-close"
         @click="onClose"
      />
    </div>
    <div class="seize-answer-body">
      <!-- 老师开始抢答按钮 -->
      <div v-if="currentStatus === 0 && (isTeacher || isAssistant)" class="start-seize-btn" @click="prepareSeize">
        <img class="icon-seize" src="./assets/seize.svg">
        <span>{{ $t('开启抢答') }}</span>
      </div>
      <!-- 学生等待 -->
      <div v-if="currentStatus === 0 && !isTeacher && !isAssistant" class="waiting">
        <div>{{ $t('暂未开始') }}</div>
        <div>{{ $t('即将开始，请稍等') }}</div>
      </div>
      <!-- 抢答开始，老师倒计时界面 -->
      <div
        v-if="(currentStatus === 1 || currentStatus === 2) && (isTeacher || isAssistant) || (isStudent && currentStatus === 1)"
        class="countdown-prepare-teacher"
      >
        <div class="circle-content">
          <div class="countdown-number">
            {{ countDownNumber }}
          </div>
          <div v-if="currentStatus === 1" class="text">
            {{ isStudent ? $t('抢答即将开始') : $t('等待抢答') }}
          </div>
          <div v-if="currentStatus === 2" class="text">
            {{ `${$t('抢答中')} ${seizeMemberList.length}/${studentCount}` }}
          </div>
        </div>
        <svg class="countdown-circle-container">
          <circle :key="currentStatus"
                  :class="{ cirlce3seconds: currentStatus === 1, cirlce5seconds: currentStatus === 2 }" class="countdown-circle"
                  r="67" cx="75" cy="75"
          />
          <circle :key="currentStatus + 1"
                  :class="{ endCircle3seconds: currentStatus === 1, endCircle5seconds: currentStatus === 2 }" class="end-circle"
                  r="4" cx="142" cy="75"
          />
        </svg>
        <img src="./assets/background.svg" class="circle-fill" alt="">
        <img src="./assets/outlines-teacher.svg" class="circle-outlines" alt="">
      </div>
      <!-- 学生抢答倒计时界面 -->
      <div v-if="(currentStatus === 2) && isStudent && !isSeized" class="countdown-prepare-student"
           :class="{ isPointer: currentStatus === 2 }" @click="handleSeize"
      >
        <!-- 准备抢答 -->
        <div v-if="currentStatus === 1" class="circle-content circle-content-prepare">
          <div class="countdown-number">
            {{ countDownNumber }}
          </div>
          <div class="text">
            {{ $t('抢答即将开始') }}
          </div>
        </div>
        <!-- 抢答开始 -->
        <div v-if="currentStatus === 2" class="circle-content circle-content-start">
          <div class="countdown-tips">
            {{ $t('点击抢答') }}
          </div>
          <div class="text">
            {{ countDownTip }}
          </div>
        </div>
        <svg class="countdown-circle-container">
          <circle :key="currentStatus"
                  :class="{ cirlce3seconds: currentStatus === 1, cirlce5seconds: currentStatus === 2 }" class="countdown-circle"
                  r="67" cx="75" cy="75"
          />
          <circle :key="currentStatus + 1"
                  :class="{ endCircle3seconds: currentStatus === 1, endCircle5seconds: currentStatus === 2 }" class="end-circle"
                  r="4" cx="142" cy="75"
          />
        </svg>
        <img src="./assets/background-student.svg" class="circle-fill" alt="">
        <img src="./assets/outlines-student.svg" class="circle-outlines" alt="">
      </div>
      <!-- 学生已抢答 -->
      <div v-if="isSeized && currentStatus === 2" class="student-seized">
        <div class="circle-content">
          <div class="countdown-tips">
            {{ $t('已抢答') }}
          </div>
        </div>
        <svg class="countdown-circle-container">
          <circle :key="currentStatus" class="countdown-circle " r="67" cx="75" cy="75" />
        </svg>
        <img src="./assets/seized.svg" class="circle-fill" alt="">
        <img src="./assets/outlines-seized.svg" class="circle-outlines" alt="">
      </div>
      <!-- 抢答结束老师面板 -->
      <div v-if="currentStatus === seizeStatus.end && (isTeacher || isAssistant)" class="seize-end">
        <div class="seizeList">
          <div class="seize-name" :style="{color: seizeMemberList.length === 0 ? '#CFD4E5' : '#FFF'}">
            {{ seizeSuccessName }}
          </div>
          <div v-if="seizeMemberList.length !== 0" class="tips">
            {{ seizeResult }}
          </div>
        </div>
        <div class="end-btn">
          <div v-if="seizeMemberList.length !== 0" class="onStage-btn" @click="onStage">
            {{ $t('上台') }}
          </div>
          <div :class="{ 'reSeize-btn': seizeMemberList.length !== 0, hightLight: seizeMemberList.length === 0 }"
               @click="onStart"
          >
            {{ $t('重启抢答') }}
          </div>
        </div>
      </div>
      <!-- 抢答学生结束面板 -->
      <div v-if="currentStatus === seizeStatus.end && isStudent" class="seize-end">
        <!-- 学生本人抢答成功 -->
        <div v-if="isOwnSeized" class="own-seized">
          <img src="./assets/seize-success.svg" class="seize-success" alt="">
          <div class="success-text">
            {{ $t('抢答成功') }}
          </div>
        </div>
        <!-- 抢答失败或者非本人抢答成功 -->
        <div v-else class="seizeList">
          <div class="seize-name">
            {{ studentEndTips }}
          </div>
          <div class="tips">
            {{ seizeResult }}
          </div>
        </div>
      </div>
      <!-- 抢答过程中老师离线 -->
      <div v-if="currentStatus === -1" class="offline-container">
        <div class="offline-text">
          {{ $t('老师已离开，等待老师上线') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BaseComponent from '../../core/BaseComponent';

const seizeStatus = {
  init: 0,
  prepare: 1,
  start: 2,
  end: 3,
  offline: -1,
};

export default {
  extends: BaseComponent,
  data() {
    return {
      taskId: 'seize_answer', // 任务id,
      currentStatus: seizeStatus.init,
      hasJoinClass: false,
      isTeacher: false,
      isAssistant: false,
      isStudent: false,
      seizeStatus,
      taskContent: {
        status: 0, // 当前状态
        remainSeconds: 0, // 剩余的秒数
        time: 0, // 当前时间戳
        triggerId: '',
      },
      countDownNumber: 0,
      remainSeconds: 0,
      // 房间所有成员
      memberList: [],
      // 抢答成功的成员列表
      seizeMemberList: [],
      // 是否抢答成功
      isSeized: false,
      canSeize: true,
      teacherId: '',
      assistants: [],
      userId: '',
    };
  },
  computed: {
    // 学生列表
    students() {
      return this.memberList.filter(item => TCIC.SDK.instance.isStudent(item.userId));
    },
    // 学生总数
    studentCount() {
      return this.students.length;
    },
    // 抢答成功的nickName
    seizeSuccessName() {
      return this.seizeMemberList.length === 0 ? this.$t('无人抢答') : this.seizeMemberList[0].nickname;
    },
    countDownTip() {
      return this.$t('{{arg_0}}后结束抢答', { arg_0: `${this.countDownNumber}s` });
    },
    studentEndTips() {
      return this.canSeize ? this.$t('抢答结束') : this.$t('抢答失败');
    },
    seizeResult() {
      if (this.seizeMemberList.length === 0) {
        return this.$t('无人抢答');
      }
      if (this.isTeacher) {
        return this.$t('抢答成功');
      }
      return `${this.$t('恭喜')} ${this.seizeMemberList[0].nickname} ${this.$t('抢答成功')}`;
    },
    // 抢答成功的同学详细信息
    stageStudent() {
      if (this.seizeMemberList.length !== 0) {
        return this.memberList.find(user => user.userId === this.seizeMemberList[0].userId);
      }
      return {};
    },
    // 学生本人是否抢答成功
    isOwnSeized() {
      return this.seizeMemberList.length > 0 && this.seizeMemberList[0].userId === this.userId;
    },
  },
  watch: {
    countDownNumber(val) {
      if (val === 0 && this.isTeacher) {
        clearInterval(this.timer);
        switch (this.currentStatus) {
          // 3s倒计时结束，prepare => start，开始5s倒计时
          case seizeStatus.prepare:
            this.updateStatus(seizeStatus.start);
            this.remainSeconds = 5;
            this.countDownNumber = 5;
            this.updateTask();
            this.startTimer();
            break;
            // 5s倒计时结束，start => end，抢答结束
          case seizeStatus.start:
            this.onEnded();
            break;
        }
      }
    },
  },
  mounted() {
    this.isAssistant = TCIC.SDK.instance.isAssistant();
    this.isTeacher = TCIC.SDK.instance.isTeacher();
    this.isStudent = TCIC.SDK.instance.isStudent();
    this.userId = TCIC.SDK.instance.getUserId();
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Task_Updated, this.onTaskUpdate);
    this.makeSureClassJoined(this.onJoinClass);
    this.toggleComponentDrag(true, '.seize-answer-dialog-hd');
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Member_Join, (memberId) => {
      // 老师重新上线将学生status置为init
      if (memberId === this.teacherId && this.currentStatus === seizeStatus.offline && this.isStudent) {
        this.currentStatus = 0;
      }
      this.getMemberList();
    });
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Member_Exit, (userId) => {
      // 在抢答阶段老师或者助教退出
      if ((this.currentStatus === 1 || this.currentStatus === 2) && this.isStudent) {
        if ((userId === this.teacherId || this.assistants.find(assistantId => assistantId === userId))) {
          this.updateStatus(seizeStatus.offline);
        }
      }
    });
    // 更新最初的状态
    TCIC.SDK.instance.getTasks(0).then((result) => {
      result.tasks.forEach((taskInfo) => {
        this.onTaskUpdate(taskInfo);
      });
    });

    // 监听课堂结束
    this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, (currentStatus) => {
      if (currentStatus === TCIC.TClassStatus.Has_Ended) {
        if (this.isTeacher) {
          // 如果是老师就关闭
          this.onClose();
        } else {
          // 如果是学生
          this.onEnded();
          this.hide();
        }
      }
    });
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
  methods: {
    onJoinClass() {
      this.hasJoinClass = true;
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      this.getMemberList();
      this.teacherId = TCIC.SDK.instance.getClassInfo().teacherId;
      this.assistants = TCIC.SDK.instance.getClassInfo().assistants;
    },
    // 上台
    onStage() {
      this.$EventBus.$emit('member-action', this.stageStudent, TCIC.TMemberActionType.Stage_Up);
    },
    // 第一次打开抢答器调用
    onStart() {
      this.currentStatus = seizeStatus.init;
      this.seizeMemberList = [];
      this.isSeized = false;
      this.canSeize = true;
      this.updateTask();
    },
    // 点击关闭
    onClose() {
      this.stopTimer();
      this.stopTask();
      this.hide();
    },
    // 获取房间成员列表
    getMemberList() {
      return TCIC.SDK.instance.getClassMemberList({
        page: 1,
        limit: 10, // 最大限制 1000;
        type: TCIC.TMemberType.All,
        keyword: '',
      }).then((res) => {
        this.memberList = res.members;
      });
    },
    // 上报抢答任务
    handleSeize() {
      if (!this.canSeize) {
        return;
      }
      //  没在抢答状态，或者当前角色不为学生
      if (!(this.currentStatus === seizeStatus.start && this.isStudent)) {
        return;
      }
      // 已经抢答成功
      if (this.isSeized) {
        return;
      }
      this.updateTask('seizeEvent').then(() => {
        this.isSeized = true;
      });
      // 第一次抢答的task出结果之前不允许再次点击
      this.canSeize = false;
    },
    // 更新任务
    updateTask(eventType) {
      if (!this.isTeacher && !this.isAssistant && eventType !== 'seizeEvent') return;
      this.taskContent = {
        time: TCIC.SDK.instance.getServerTimestamp(),
        status: this.currentStatus,
        remainSeconds: this.remainSeconds,
        triggerId: TCIC.SDK.instance.getUserId(),
        type: eventType || 'event',
        seizeStudent: this.seizeMemberList.length === 0 ? null : this.seizeMemberList[0],
      };
      return TCIC.SDK.instance.updateTask(this.taskId, JSON.stringify(this.taskContent))
        .then((task) => { })
        .catch((error) => {
          window.showToast(error.errorMsg);
          TCIC.SDK.instance.reportEvent('update_seize_answer_tool', error, -1);
        });
    },
    // 收到任务处理
    onTaskUpdate(taskInfo) {
      // 不是本任务，或者是巡课
      if (taskInfo.taskId !== this.taskId || TCIC.SDK.instance.isSupervisor()) {
        return;
      }
      // 任务结束
      if (taskInfo.status === 0) {
        this.stopTimer();
        this.hide();
        return;
      }
      this.show();
      const contentJson = JSON.parse(taskInfo.content);
      // 收到学生的抢答task，push到seizeMemberList 此刻状态必须为start，此次抢答才有效
      if ((this.isTeacher || this.isAssistant) && contentJson.type === 'seizeEvent' && this.currentStatus === seizeStatus.start) {
        TCIC.SDK.instance.getUserInfo(contentJson.triggerId)
          .then((userInfo) => {
            this.seizeMemberList.push(userInfo);
          });
        return;
      }
      // 抢答结束，重新进房，恢复上一次状态
      if (contentJson.status === 3 && this.currentStatus === 0) {
        this.updateStatus(seizeStatus.end);
        // contentJson.seizeStudent 有可能为null
        contentJson.seizeStudent && this.seizeMemberList.push(contentJson.seizeStudent);
      }
      // 任务触发任务id不为自己
      if (contentJson.triggerId !== TCIC.SDK.instance.getUserId()) {
        switch (contentJson.status) {
          case seizeStatus.init:
            this.currentStatus = seizeStatus.init;
            this.seizeMemberList = [];
            this.isSeized = false;
            this.canSeize = true;
            this.stopTimer();
            break;
          case seizeStatus.prepare:
            this.currentStatus = seizeStatus.prepare;
            this.remainSeconds = contentJson.remainSeconds;
            this.countDownNumber = contentJson.remainSeconds;
            this.startTimer();
            break;
          case seizeStatus.start:
            if (contentJson.type === 'seizeEvent') {
              return;
            }
            this.currentStatus = seizeStatus.start;
            this.remainSeconds = contentJson.remainSeconds;
            this.countDownNumber = contentJson.remainSeconds;
            this.startTimer();
            break;
          case seizeStatus.end:
            contentJson.seizeStudent && this.seizeMemberList.push(contentJson.seizeStudent);
            this.currentStatus = seizeStatus.end;
          default:
            break;
        }
      }
    },
    // 开始倒计时
    startTimer() {
      this.stopTimer();
      const start = Date.now();
      this.timer = setInterval(() => {
        const diff = this.remainSeconds - (((Date.now() - start) / 1000));
        this.countDownNumber = Math.max(0, Math.round(diff));
      }, 1000);
    },
    // 停止倒计时
    stopTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
    // 停止任务
    stopTask() {
      if (!this.isTeacher && !this.isAssistant) {
        return;
      }
      TCIC.SDK.instance.stopTask(this.taskId)
        .catch((error) => {
          TCIC.SDK.instance.reportEvent('stop_seizeAnswer_tool', error, -1);
        });
    },
    // 更新状态
    updateStatus(status) {
      this.currentStatus = status;
    },
    // 准备抢答，倒计时3s
    prepareSeize() {
      this.updateStatus(seizeStatus.prepare);
      this.remainSeconds = 3;
      this.countDownNumber = 3;
      this.startTimer();
      this.updateTask();
    },
    // 抢答结束
    onEnded() {
      this.updateStatus(seizeStatus.end);
      this.stopTimer();
      this.updateTask();
    },
  },
};
</script>

<style lang="less">
@font-face {
  font-family: DIN-Bold;
  src: url(./assets/DIN-Bold.ttf);
}
.dark .seize-answer-dialog {
  background-color: rgba(29, 32, 41);
  color: #CFD4E5;
  width: 100%;
  padding: 0 20px 20px;
  border-radius: 10px;

  &-hd {
    display: flex;
    cursor: move;
    align-items: center;
    border-bottom: 0.5px solid rgba(238, 238, 238, 0.2);
    font-size: 16px;
    padding: 20px 0;

    div:first-child {
      font-size: 16px;
      flex: 1;
    }

    i {
      cursor: pointer;
    }
  }

  .seize-answer-body {
    display: flex;
    align-items: center;
    height: 217px;
    justify-content: center;

    .start-seize-btn {
      background-color: #236CFA;
      color: #fff;
      cursor: pointer;
      border-radius: 8px;
      width: 300px;
      height: 76px;
      display: flex;
      align-items: center;
      justify-content: center;

      .icon-seize {
        margin-right: 10px;
      }

      span {
        font-size: 22px;
        line-height: 76px;
        text-align: center;
      }
    }

    .waiting {
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      align-items: center;
      justify-content: center;

      >:first-child {
        font-size: 22px;
        margin-bottom: 8px;
      }

      >:last-child {
        font-size: 16px;
      }
    }

    .countdown-prepare-teacher {
      position: relative;
      height: 150px;
      width: 150px;
      text-align: center;

      .circle-content {
        position: absolute;
        display: flex;
        flex-direction: column;
        transform: translate(-50%, -50%);
        width: 150px;
        height: 150px;
        color: white;
        top: 64%;
        left: 50%;

        .countdown-number {
          font-family: DIN-Bold;
          height: 70px;
          font-size: 60px;
          font-weight: 700;
        }

        .text {
          margin-top: 5px;
          height: 17px;
          font-size: 12px;
        }
      }

      .countdown-circle-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 150px;
        height: 150px;
        transform: rotateY(-180deg) rotateZ(-90deg);

        .countdown-circle {
          stroke-dasharray: 421px;
          stroke-dashoffset: 0px;
          stroke-linecap: round;
          stroke-width: 4px;
          fill: none;
          stroke: #A5FE33;
        }

        .end-circle {
          fill: #A5FE33;
          transform-origin: center center;
        }
      }

      .circle-fill {
        position: absolute;
        clip-path: circle(50% at 50% 50%);
        width: 134px;
        transform: translate(-50%, -50%);
        top: 50%;
        left: 50%;
      }

      .circle-outlines {
        position: absolute;
        clip-path: circle(50% at 50% 50%);
        width: 178px;
        transform: translate(-50%, -50%);
        top: 50%;
        left: 50%;
      }
    }

    .countdown-prepare-student {
      position: relative;
      height: 150px;
      width: 150px;
      text-align: center;

      .circle-content {
        z-index: 10;
        position: absolute;
        display: flex;
        flex-direction: column;
        transform: translate(-50%, -50%);
        width: 150px;
        height: 150px;
        color: white;
        top: 80%;
        left: 50%;

        .countdown-number {
          font-family: DIN-Bold;
          height: 70px;
          font-size: 60px;
          font-weight: 700;
        }

        .countdown-tips {
          font-size: 22px;
          font-weight: 500;
        }

        .text {
          margin-top: 5px;
          height: 17px;
          font-size: 12px;
        }
      }

      .circle-content-prepare {
        top: 60%;
      }

      .circle-content-start {
        top: 80%;
      }

      .countdown-circle-container {
        z-index: 10;
        position: absolute;
        top: 0;
        left: 0;
        width: 150px;
        height: 150px;
        transform: rotateY(-180deg) rotateZ(-90deg);

        .countdown-circle {
          stroke-dasharray: 421px;
          stroke-dashoffset: 0px;
          stroke-linecap: round;
          stroke-width: 2px;
          fill: none;
          stroke: #FFFFFF;
        }

        .end-circle {
          fill: #FFFFFF;
          transform-origin: center center;
        }
      }

      .circle-fill {
        position: absolute;
        clip-path: circle(50% at 50% 50%);
        width: 134px;
        transform: translate(-50%, -50%);
        top: 50%;
        left: 50%;
      }

      .circle-outlines {
        position: absolute;
        clip-path: circle(50% at 50% 50%);
        width: 168px;
        transform: translate(-50%, -50%);
        top: 50%;
        left: 50%;
      }
    }

    .cirlce3seconds {
      animation: countDown 3s linear;
    }

    .cirlce5seconds {
      animation: countDown 5s linear;
    }

    .endCircle3seconds {
      animation: endCircle 3s linear;
    }

    .endCircle5seconds {
      animation: endCircle 5s linear;
    }

    @keyframes countDown {
      from {
        stroke-dashoffset: 0px;
      }

      to {
        stroke-dashoffset: 421px;
      }
    }

    @keyframes endCircle {
      from {
        transform: rotate(0deg);
      }

      to {
        transform: rotate(-360deg);
      }
    }

    .student-seized {
      position: relative;
      height: 150px;
      width: 150px;
      text-align: center;
      opacity: 0.5;
      .circle-content {
        z-index: 10;
        position: absolute;
        display: flex;
        flex-direction: column;
        transform: translate(-50%, -50%);
        width: 150px;
        height: 150px;
        color: white;
        top: 88%;
        left: 50%;

        .countdown-tips {
          font-size: 22px;
          font-weight: 500;
        }

      }

      .countdown-circle-container  {
        z-index: 10;
        position: absolute;
        top: 0;
        left: 0;
        width: 150px;
        height: 150px;
        transform: rotateY(-180deg) rotateZ(-90deg);

        .countdown-circle {
          stroke-dasharray: 421px;
          stroke-dashoffset: 0px;
          stroke-linecap: round;
          stroke-width: 2px;
          fill: none;
          stroke: #FFFFFF;
        }

      }

      .circle-fill {
        position: absolute;
        clip-path: circle(50% at 50% 50%);
        width: 134px;
        transform: translate(-50%, -50%);
        top: 50%;
        left: 50%;
      }

      .circle-outlines {
        position: absolute;
        clip-path: circle(50% at 50% 50%);
        width: 168px;
        transform: translate(-50%, -50%);
        top: 50%;
        left: 50%;
      }
    }

    .seize-end {
      .seizeList {
        text-align: center;
        .seize-name {
          width: 100%;
          height: 31px;
          font-size: 22px;
          font-weight: 500;
        }

        .tips {
          font-size: 16px;
          color: #CFD4E5;
          width: 100%;
          height: 22px;
          margin-top: 6px;
        }
      }

      .own-seized {
        position: relative;
        .seize-success {
          width: 150px
        }

        .success-text {
          position: absolute;
          width: 150px;
          top: 50%;
          transform: translate(0, -50%);
          font-size: 22px;
          color: #1D2029;
          font-weight: 500;
          text-align: center;
        }
      }

      .end-btn {
        display: flex;
        flex-direction: row;
        margin-top: 40px;

        .onStage-btn {
          width: 170px;
          height: 46px;
          background-color: #236CFA;
          border-radius: 46px;
          font-size: 16px;
          font-weight: 500;
          text-align: center;
          line-height: 46px;
          color: #FFFFFF;
          cursor: pointer;
          margin-right: 18px;
        }

        .reSeize-btn {
          width: 170px;
          height: 46px;
          background-color: #1D2029;
          border: 1px solid #FFFFFF;
          border-radius: 46px;
          font-size: 16px;
          font-weight: 500;
          text-align: center;
          line-height: 46px;
          color: #FFFFFF;
          cursor: pointer;
        }
      }
    }

    .offline-container {
      .offline-text {
        font-size: 20px;
      }
    }

  }

  .isPointer {
    cursor: pointer;
  }

  .hightLight {
    width: 170px;
    height: 46px;
    background-color: #236CFA;
    border-radius: 46px;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    line-height: 46px;
    color: #FFFFFF;
    cursor: pointer;
  }
}
</style>
