<template>
  <div
    :class="['switch-orientation', className, { 'is-ios': isIOSNative, 'is-andriod': isAndroidNative} ]"
    @click="changeOrientation"
  />
</template>

<script>
import BaseComponent from '@/component/core/BaseComponent';

export default {
  extends: BaseComponent,
  props: {
    className: {
      required: false,
      type: String,
      default: '',
    },
  },
  data() {
    return {
      isPortrait: false,
      isIOSNative: TCIC.SDK.instance.isIOS() && TCIC.SDK.instance.isMobileNative(),
      isAndroidNative: TCIC.SDK.instance.isAndroid() && TCIC.SDK.instance.isMobileNative(),
    };
  },
  mounted() {
    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.isPortrait = orientation === TCIC.TDeviceOrientation.Portrait;
    });
  },
  methods: {
    changeOrientation() {
      TCIC.SDK.instance.setDeviceOrientation(this.isPortrait ? TCIC.TDeviceOrientation.Landscape : TCIC.TDeviceOrientation.Portrait);
    },
  },
};
</script>

<style lang="less">
  .switch-orientation {
    background-image: url('./assets/orientation.png') ;
    background-size: 100%;
    background-position: 50%;
    width: 30px;
    height: 30px;
    right: 25px;
    background-color: rgba(28,33,49,.5);
    border-radius: 8px;
    position: fixed;
    top: calc(100% - 45px);
    z-index: 1000;
    &.is-portrait {
      top: 28%;
      left: 10px;
      right: 0;
      &.is-ios {
        top: calc(34% + env(safe-area-inset-top));
      }
      &.is-andriod {
        top: calc(30% + 40px)
      }
    }
  }
</style>
