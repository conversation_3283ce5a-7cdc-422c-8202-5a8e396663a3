<template>
  <div
    ref="wrap"
    :class="['video-wrap-component', {'touch-layout' : isMobile || isTouchTablet, 'small-screen' : isSmallScreen,}]"
  >
    <div
      v-if="showDragMask"
      class="drag-mask"
    />
    <div
      :class="[
        'video-list',
        `layout-${videoLayout}`,
        `mode-${videoMode}`,
        { 'header-fix': isMobileHeaderFix },
        { 'student-layout': !isTeacher },
        { 'hidden' : !isShowVideoList},
        {'portrait-screen': isPortrait},
        {'no-stage-member': (stageCount === 0) && isSupervisor}
      ]"
    >
      <div
        id="teacher-wrap"
        ref="teacher"
        class="teacher-wrap com-wrap"
        :class="[{'not-display' : (
          videoLayout === VideoWrapLayout.PicInPic
          || videoLayout === VideoWrapLayout.VideoDoc
          || (videoLayout === VideoWrapLayout.Three && !showScroll
          ))}, {'set-minsize': setMinSize}]"
      />
      <div
        v-show="videoMode!=='teacher'"
        id="student-wrap"
        ref="student"
        :class="['student-wrap', 'com-wrap', {'top-layout': videoLayout === VideoWrapLayout.Top, 'three-layout': videoLayout === VideoWrapLayout.Three && !showScroll}]"
        @scroll="scrollEvent"
      />
      <template
        v-if="(videoLayout === VideoWrapLayout.Top
          || videoLayout === VideoWrapLayout.PicInPic
          || videoLayout === VideoWrapLayout.VideoDoc
          || videoLayout === VideoWrapLayout.Three) && showScroll"
      >
        <el-button
          class="video-pack left"
          @click.stop.prevent="scrollVideoH(-videoWidth-8)"
        >
          <i />
        </el-button>
        <el-button
          class="video-pack right"
          @click.stop.prevent="scrollVideoH(videoWidth+8)"
        >
          <i />
        </el-button>
      </template>
      <template v-else-if="[VideoWrapLayout.Left, VideoWrapLayout.Right].includes(videoLayout) && showScroll && !isSmallScreen">
        <el-button
          class="video-pack top"
          @click.stop.prevent="scrollVideoV(-videoHeight-8)"
        >
          <i />
        </el-button>
        <el-button
          class="video-pack bottom"
          @click.stop.prevent="scrollVideoV(videoHeight+8)"
        >
          <i />
        </el-button>
      </template>
    </div>
    <div
      v-if="(videoLayout === VideoWrapLayout.Top
        || videoLayout === VideoWrapLayout.Double
        || videoLayout === VideoWrapLayout.PicInPic
        || videoLayout === VideoWrapLayout.VideoDoc)
        && showHidden
        && !isLiveClass"
      :class="[
        'video-up',
        {'video-down' : !isShowVideoList},
        {'header-fix' : isMobileHeaderFix},
        {'small-double-line' : videoLayout === VideoWrapLayout.Double && isSmallDoubleLine}
      ]"
    >
      <el-button
        :class="[{'no-stage-member': (stageCount === 0) && isSupervisor}]"
        @click.stop.prevent="toggleShow"
      >
        <i />
      </el-button>
    </div>
    <div
      v-else-if="videoLayout === VideoWrapLayout.Right && showHidden && !isLiveClass"
      :class="['layout-side', 'layout-right', {'video-left' : !isShowVideoList, 'portrait-screen': isPortrait}]"
    >
      <el-button
        :class="[{'no-stage-member': (stageCount === 0) && isSupervisor}]"
        @click="toggleShow"
      >
        <i />
      </el-button>
      <SwitchOrientationComponent
        v-if="isSmallScreen && isStudent && !isMiniProgramWebview && !isWeb"
        :class-name="isPortrait ? 'is-portrait' : ''"
      />
      <footerToolComponent
        v-if="isSmallScreen && isStudent && isPortrait && boardPermission"
      />
    </div>
    <div
      v-else-if="videoLayout === VideoWrapLayout.Left && showHidden && !isLiveClass"
      :class="['layout-side', 'layout-left', {'video-right' : !isShowVideoList, 'portrait-screen': isPortrait}]"
    >
      <el-button
        :class="[{'no-stage-member': (stageCount === 0) && isSupervisor}]"
        @click="toggleShow"
      >
        <i />
      </el-button>
    </div>
    <!-- <div id="screen-dom-id" /> -->
  </div>
</template>


<script>
import i18next from 'i18next';
import BaseComponent from '@core/BaseComponent';
import Constant from '@/util/Constant';
import Drag from '@util/Drag';
import VirtualElement from '@util/VirtualElement';
import VideoWall from '@util/VideoWall.js';
import Lodash from 'lodash';
import footerToolComponent from '../boardfooter-component/BoardFooter.vue';
import SwitchOrientationComponent from '@vueComponent/videowrap-component/SwitchOrientationComponent';

export default {
  components: {
    footerToolComponent,
    SwitchOrientationComponent,
  },
  extends: BaseComponent,
  props: {},
  data() {
    return {
      VideoWrapLayout: {
        Top: TCIC.TClassLayout.Top,
        Right: TCIC.TClassLayout.Right,
        Left: TCIC.TClassLayout.Left,
        Double: TCIC.TClassLayout.Double,
        Three: TCIC.TClassLayout.Three,
        PicInPic: TCIC.TClassLayout.PicInPic,
        VideoDoc: TCIC.TClassLayout.VideoDoc,
        Video: TCIC.TClassLayout.Video,
        Screen: 'screen',
      },
      rightSideWidth: 286,
      isShowVideoList: true,
      isEnterClass: false,
      isTeacher: false,     // 当前是否老师
      isAssistant: false,     // 当前是否助教
      isSupervisor: false,
      isMobile: false,    // 是否移动端
      isTouchTablet: false,
      isBoardReady: false,      // 白板是否加载完成
      isEnableDrag: false,
      isMobileHeaderFix: false, // 是否需要针对移动端的标题栏进行校正
      videoLayout: TCIC.TClassLayout.Top,
      videoMode: 'full',
      showHidden: true,
      showScroll: false,
      showDragMask: false,
      teacherCom: null,
      studentComs: [],
      dragMap: new Map(),
      positionMap: new Map(),     // 相对白板位置
      curZindex: 2,
      sessionId: '',
      boardTarget: new VirtualElement(0, 0, 0, 0),
      taskBuf: [],      // 缓存任务列表
      delayLoadMap: new Map(),    // 延时加载学生
      isDoubleLine: false,
      isSmallDoubleLine: false,   // 是否显示不下隐藏按钮
      isLiveClass: false,       // 是否直播课
      hasTeacherLive: true,   // 是否有老师视频视频流
      retryCount: 0,          // 重试次数
      isClassStart: false,    // 是否上课
      maxStudentVideoCount: 0,  // 当前布局能够显示的最大学生视频数
      videoWall: null,
      setMinSize: false,    // 是否设置老师wrap(iPad避免宽度未撑开)
      hasWebScreenShare: false,   // 是否存在Web端屏幕共享
      roleInfo: {},
      isPortrait: false,
      isStudent: false,
      isOneOnZero: false, // 是否是1v0课
      isIOSNative: false,
      isAndroidNative: false,
      stageCount: 0,
      boardPermission: false,
      isMiniProgramWebview: false,
      isPad: false,
      isWeb: false,
      videoWidth: 160,
      videoHeight: 90,
    };
  },
  mounted() {
    const { roleInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    this.isMobile = TCIC.SDK.instance.isMobile();
    this.isTeacher = TCIC.SDK.instance.isTeacher();
    this.isStudent = TCIC.SDK.instance.isStudent();
    this.isAssistant = TCIC.SDK.instance.isAssistant();
    this.isSupervisor = TCIC.SDK.instance.isSupervisor();
    this.isOneOnZero = TCIC.SDK.instance.getClassInfo().maxRtcMember === 0;
    this.isIOSNative = TCIC.SDK.instance.isIOS() && TCIC.SDK.instance.isMobileNative();
    this.isAndroidNative = TCIC.SDK.instance.isAndroid() && TCIC.SDK.instance.isMobileNative();
    this.isMiniProgramWebview = TCIC.SDK.instance.isMiniProgramWebview();
    this.isPad = TCIC.SDK.instance.isPad();
    this.isWeb = TCIC.SDK.instance.isWeb();
    // 注册状态
    TCIC.SDK.instance.registerState(Constant.TStateVideoCtrlUserId, '当前显示的视频悬浮窗用户id', '');
    TCIC.SDK.instance.registerState(Constant.TStateVideoDubbleLine, '当前是否双排展示', this.isDoubleLine);
    TCIC.SDK.instance.registerState(Constant.TStateSkipTeacherUpdateLayout, '是否跳过老师layout更新', false);
    this.isLiveClass = TCIC.SDK.instance.isLiveClass();
    // 监听加入课堂事件
    this.makeSureClassJoined(this.onJoinClass);
    this.addLifecycleTCICStateListener(TCIC.TMainState.Board_Permission, (flag) => {
      this.boardPermission = !!flag;
    });
    // 监听事件
    this.addLifecycleTCICEventListener(Constant.TEventComponentUpdateLayout, this.onTargetLayoutChange);
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Task_Updated, this.onTaskUpdate);

    this.addLifecycleTCICEventListener(TCIC.TMainEvent.AV_Add, (info) => {
      console.log('init layout', info);
      this.doLoadUserComponent(info);
    });

    this.addLifecycleTCICEventListener(TCIC.TMainEvent.AV_Remove, (info, reason) => {
      this.doRemoveUserComponent(info, reason);
    });

    this.addLifecycleTCICStateListener(Constant.TStateBigVideoMode, (val) => {
      if (this.checkVideoDrag()) {
        if (val) {
          this.disableAllDrag();
        } else {
          this.enableAllDrag();
        }
      }
    });

    // 上课后加载缓存组件
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start)
      .then(() => {
        Array.from(this.delayLoadMap.keys()).forEach((userId) => {
          this.loadUserComponent(this.delayLoadMap.get(userId));
        });
        this.updateTeacherComponent(this.isShowVideoList);
      });

    // 加载组件加载前上抛的学生
    TCIC.SDK.instance.getPermissionList()
      .forEach((permission) => {
        if (permission.stage) {
          this.doLoadUserComponent(permission);
        }
      });
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Info_Ready, true)
      .then(() => {
        this.isLiveClass = TCIC.SDK.instance.isLiveClass();
        this.isEnterClass = true;
        this.isTouchTablet = TCIC.SDK.instance.supportTouch();
        // 随机生成会话id
        this.sessionId = `${TCIC.SDK.instance.getUserId()}_${Math.floor(Math.random() * 65535)}`;
      });

    // 处理白板事件
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Board_Init, true)
      .then(() => {
        this.isBoardReady = true;
        if (this.checkVideoDrag()) {  // 老师端则开启视频可拖动支持
          this.enableAllDrag();
        }
        // 处理缓存的任务
        this.taskBuf.forEach((taskInfo) => {
          this.onTaskUpdate(taskInfo);
        });
        this.taskBuf = [];
      });

    // 处理鼠标滚动事件
    this.$el.addEventListener('mousewheel', (event) => {
      const ev = event || window.event;
      const down = ev.wheelDelta ? ev.wheelDelta < 0 : ev.detail > 0;
      if (this.videoLayout === this.VideoWrapLayout.Top) {
        this.scrollVideoH(down ? 10 : -10);
      } else if (this.videoLayout === this.VideoWrapLayout.Right) {
        this.scrollVideoV(down ? 10 : -10);
      } else if (this.videoLayout === this.VideoWrapLayout.Left) {
        this.scrollVideoV(down ? 10 : -10);
      } else if (this.videoLayout === this.VideoWrapLayout.Screen) {
        this.scrollVideoV(down ? 10 : -10);
      } else if (this.videoLayout === this.VideoWrapLayout.Three) {
        this.scrollVideoH(down ? 10 : -10);
      }
    });

    // 监听窗口尺寸变更
    window.addEventListener('resize', this.onWindowResize);

    // 监听屏幕分享状态
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Permission_Update, this.onPermissionUpdate);

    this.addLifecycleTCICEventListener(Constant.TStateResetVideoPosition, () => {
      this.resetAllDrag();
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.isPortrait = orientation === TCIC.TDeviceOrientation.Portrait;
      this.updateTeacherComponent(this.isShowVideoList);
    });

    // 公开课监听流事件
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Stream_Update, (streams) => {
      if (TCIC.SDK.instance.isLiveClass() && this.isSmallScreen) {
        const teacherId = TCIC.SDK.instance.getClassInfo().teacherId;
        // eslint-disable-next-line max-len
        const teacherMainStream = streams.filter(stream => stream.userId === teacherId && stream.type === TCIC.TStreamType.Main);
        this.hasTeacherLive = teacherMainStream && teacherMainStream.length > 0;
      }
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Mute_All, (muteAll, oldStatus) => {
      if (this.isTeacher || this.isAssistant) {
        if (muteAll) {
          window.showToast(i18next.t('已开启全员静音'));
        } else {
          window.showToast(i18next.t('已解除全员静音'));
        }
      } else {
        if (muteAll) {
          window.showToast(i18next.t('{{arg_0}}已开启全员静音', { arg_0: this.roleInfo.teacher }));
        } else {
          window.showToast(i18next.t('{{arg_0}}已解除全员静音', { arg_0: this.roleInfo.teacher }));
        }
      }
    }, { noEmitWhileSubscribe: true, noEmitWhileRegister: true });

    // 公开课下课后隐藏视频
    this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, this.onClassStatusChange);
    // 监听辅助摄像头
    this.addLifecycleTCICStateListener(Constant.TStateShowSubCameraComponent, (flag) => {
      if (this.isLiveClass && this.videoLayout === this.VideoWrapLayout.Video) {
        this.layoutLiveClassGridVideos();
      }
    });

    this.videoWall = new VideoWall(this.boardTarget.getBoundingClientRect());
    this.videoWall.setLayoutCallback((dom, left, top, width, height) => {
      // if ((!this.isTeacher && !this.isAssistant && !this.isSupervisor) || !TCIC.SDK.instance.getState(Constant.TStateVideoWallMode)) {
      //   return;      // 非老师或非视频墙状态不处理视频墙布局事件
      // }
      TCIC.SDK.instance.updateComponent(dom.tagName.toLowerCase(), {
        position: 'fixed',
        left: `${left}px`,
        top: `${top}px`,
        width: `${width}px`,
        height: `${height}px`,
        zIndex: this.isLiveClass ? 301 : 200,
        style: 'overflow: visible;',
      }, dom.getAttribute('label')).then(() => {
        this.recordDomPosition(dom, this.curZindex);
        this.updateDragInfo(dom, true, this.boardTarget, this.curZindex);
      });
    });

    // 更新任务信息
    TCIC.SDK.instance.getTasks(0).then((tasks) => {
      if (tasks && tasks.tasks && tasks.tasks.length > 0) {
        tasks.tasks.forEach(task => this.onTaskUpdate(task));
      }
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Stage_Count, (stageCount) => {
      this.stageCount = stageCount;
    });
  },
  beforeDestroy() {
    // 移除所有拖动
    if (this.teacherCom) {
      this.removeDrag(this.teacherCom);
    }
    this.studentComs.forEach((ele) => {
      this.removeDrag(ele);
    });
    window.removeEventListener('resize', this.onWindowResize);
  },
  methods: {
    onPermissionUpdate() {
      if (this.isTeacher || this.isAssistant || this.isSupervisor) {
        const permissionList = TCIC.SDK.instance.getPermissionList();
        // 有屏幕共享权限或者播片权限
        // 正在屏幕共享或者播片
        // 非web端
        const sharePermissionList = permissionList.filter(permission => (permission.screen === 1 || permission.screen === 2) && permission.screenState < 2);
        // 非web端屏幕共享需要开启或者关闭拖动
        const pcShare = sharePermissionList.filter(permission => permission.platform !== TCIC.TPlatform.Web);
        if (pcShare.length > 0) {   // 非Web端屏幕共享时禁用拖动
          this.disableAllDrag();
        } else if (this.checkVideoDrag()) {
          this.enableAllDrag();
        }

        // 老师web屏幕共享需要将视频最大化
        if (this.isTeacher) {
          const webShare = sharePermissionList.filter(permission => permission.platform === TCIC.TPlatform.Web);
          if (webShare.length === 0) {
            if (this.hasWebScreenShare) {     // 只有在之前有屏幕共享时才重置位置
              this.resetDragDom(this.teacherCom, false);
              this.hasWebScreenShare = false;
            }
          } else {
            this.isEnableDrag = false;
            this.hasWebScreenShare = true;
            this.videoWall.clearVideos();
            if (this.teacherCom && this.dragMap.has(this.teacherCom.id)) {
              this.dragMap.get(this.teacherCom.id).enableDrag(false);
            }
            this.studentComs.forEach((ele) => {
              this.resetDragDom(ele, !this.isEnableDrag);
            });
            this.curZindex = 2;
            // 最大化老师视频位置并同步
            // this.updateScreenDomPosition();
          }
        }
      }
    },
    updateScreenDomPosition() {
      const posInfo = { left: 0, top: 0, width: 1, height: 1, zIndex: 2 };
      const screenCom = TCIC.SDK.instance.getComponent('screen-component');
      // 屏幕共享的推流比例不一定是16:9，可能超过boardTarget大小，用整个board-component的rect
      // const rect = this.boardTarget.getBoundingClientRect();
      const rect = TCIC.SDK.instance.getComponent('board-component').getBoundingClientRect();
      const realWidth = rect.width;
      const realHeight = rect.height;
      if (screenCom) {
        this.updateUserTask(screenCom.id, posInfo);
        this.positionMap.set(screenCom.id, posInfo);
        const screenLayout = {
          position: 'fixed',
          left: `${rect.left}px`,
          top: `${rect.top}px`,
          width: `${realWidth}px`,
          height: `${realHeight}px`,
          zIndex: posInfo.zIndex,
          style: 'overflow: visible;',
          // display: 'block', // 不要改变 display，否则在屏幕共享关闭后调整videowrap，就会导致这一层挡住白板区域
        };
        // console.log('[VideoWrapComponent] updateComponent screen-component', screenLayout);
        TCIC.SDK.instance.updateComponent('screen-component', screenLayout, screenCom.getAttribute('label'), true);
      }
    },
    /**
     * 设置视频布局
     * @param layout  布局类型(top、right) 默认为top
     */
    setWrapLayout(layout) {
      const isShareScreen = (TCIC.SDK.instance.getState(TCIC.TMainState.Screen_Share, 2) !== 2);
      console.log(`VideoWrapComponent::setWrapLayout=>layout: ${layout}, isShareScreen: ${isShareScreen}`);
      if (this.videoLayout === layout) {    // 忽略布局未改变
        // console.warn('VideoWrapComponent::setWrapLayout=>ignore with no change', layout);
        return;
      }
      TCIC.SDK.instance.reportEvent('wrap_layout', { layout });
      if (layout === this.VideoWrapLayout.Top
          || layout === this.VideoWrapLayout.Right
          || layout === this.VideoWrapLayout.Left
          || layout === this.VideoWrapLayout.Double) {
        if (this.videoLayout === this.VideoWrapLayout.Screen) {  // 之前是屏幕分享
          if (this.videoMode === 'teacher') { // 当前为teacher模式
            if (!this.isLiveClass) {
              TCIC.SDK.instance.updateComponent('teacher-component', {
                width: '100%',
                height: '100%',
                style: 'overflow: visible;',
              });
            }
          }
        }
        const teacherDom = TCIC.SDK.instance.getComponent('teacher-component');
        if (this.videoLayout === this.VideoWrapLayout.Three && teacherDom) {
          // 之前是三分屏布局，恢复老师位置及拖拽
          // 当position设置为relative时，top/left属性指定了元素的上/左边界离开其正常位置的偏移
          TCIC.SDK.instance.updateComponent('teacher-component', {
            position: 'relative',
            top: '0',
            left: '0',
            width: `${this.videoWidth}px`,
            height: `${this.videoHeight}px`,
            style: 'overflow: visible;',
          });
          if (this.checkVideoDrag()) {
            this.enableDrag(teacherDom,  this.isEnableDrag);
          }
        }

        // 路径：1、三栏布局 2、学生端主动把老师画面放大到全屏 3、教师端切换回顶部布局
        // 问题：导致 iPad 学生端看到教师端视频挡住了大部分的画布，且无法设置
        const isNeedResetBigVideoModeTeacherComponent = teacherDom
          && this.videoLayout === this.VideoWrapLayout.Three
          && TCIC.SDK.instance.getState(Constant.TStateBigVideoMode)
          && TCIC.SDK.instance.isPad()
          && layout === this.VideoWrapLayout.Top;
        if (isNeedResetBigVideoModeTeacherComponent) {
          TCIC.SDK.instance.setState(Constant.TStateBigVideoMode, false);
          TCIC.SDK.instance.updateComponent('teacher-component', {
            position: 'relative',
            top: 0,
            width: `${this.videoWidth}px`,
            height: `${this.videoHeight}px`,
            style: 'overflow: visible;',
          });
        }

        this.videoLayout = layout;

        if (teacherDom && teacherDom.parentNode !== this.$refs.teacher) {
          console.log(`[VideoWrapComponent] layout ${layout}, move teacher-component to teacherWarp`);
          this.$refs.teacher.appendChild(teacherDom);
          if (TCIC.SDK.instance.isIOS() && TCIC.SDK.instance.isPad()) {   // 设置最小尺寸
            this.setMinSize = true;
          }
        }
        this.updateAllControlDirect();
        if (this.checkVideoDrag() && !this.isEnableDrag && !isShareScreen) { // 检测是否要开启视频拖动
          this.enableAllDrag();
        }
      } else if (layout === this.VideoWrapLayout.Three) {
        // 三分屏布局
        // 进入三分屏布局时重置视频拖动
        this.setMinSize = false;
        this.videoLayout = layout;
        const teacherDom = TCIC.SDK.instance.getComponent('teacher-component');
        if (teacherDom) {
          this.resetAllDrag();
          const root = document.getElementById('app');
          if (teacherDom.parentNode !== root) {
            console.log(`[VideoWrapComponent] layout ${layout}, move teacher-component to app`);
            root.appendChild(teacherDom);
          }
          this.updateAllControlDirect();
          if (this.checkVideoDrag() && !this.isEnableDrag && !isShareScreen) { // 检测是否要开启视频拖动
            this.enableAllDrag();
          }
          this.enableDrag(teacherDom,  false); // 三分屏，关闭老师的视频拖拽
        }
      } else if (layout === this.VideoWrapLayout.Screen) {
        this.updateAllControlDirect();
        if (this.isEnableDrag) { // 屏幕分享模式关闭视频拖动
          this.disableAllDrag();
        }
        // 之前为三分布局，需要改变视频流改位置
        if (this.videoLayout === this.VideoWrapLayout.Three) {
          const teacherDom = TCIC.SDK.instance.getComponent('teacher-component');
          if (teacherDom && teacherDom.parentNode !== this.$refs.teacher) {
            teacherDom.style.display = 'none';
            this.$refs.teacher.appendChild(teacherDom);
          }
        }
        this.videoLayout = layout;
      } else if (this.isLiveClass && layout === this.VideoWrapLayout.Video) {
        // 公开课纯视频，处理老师布局
        this.videoLayout = layout;
        this.addTeacherToGridVideos();
      } else {
        this.videoLayout = layout;
        if (this.checkVideoDrag() && !this.isEnableDrag && !isShareScreen) { // 检测是否要开启视频拖动
          this.enableAllDrag();
        }
        this.updateAllControlDirect();
        console.warn(`VideoWrapComponent::setWrapLayout=>unresolve layout: ${layout}`);
      }
      this.isDoubleLine = layout === this.VideoWrapLayout.Double;
      const lastFlag = TCIC.SDK.instance.getState(Constant.TStateVideoDubbleLine, false);
      if (this.isDoubleLine !== lastFlag) { // 发生变更
        TCIC.SDK.instance.setState(Constant.TStateVideoDubbleLine, this.isDoubleLine);
        this.studentComs.forEach((ele) => {
          if (this.dragMap.has(ele.id)) {
            this.dragMap.get(ele.id).updateZoom((this.isDoubleLine && ele.getAttribute('drag') !== 'true') ? 0.49 : 1);
          }
        });
      }
      setTimeout(() => {
        this.updateScroll();
        this.checkMaxVisibleStudentVideoCount();
      }, 100);
      this.isSmallDoubleLine = document.body.clientWidth < 950;
      // 关闭所有悬浮控制窗口
      TCIC.SDK.instance.setState(Constant.TStateVideoCtrlUserId, '');
      // 监听窗口变化
      const resizeObserver = new ResizeObserver((entries) => {
        this.onBoundingClientRectChange(entries[0].contentRect);
      });
      resizeObserver.observe(this.$el);
    },
    /**
     * 设置视频模式(是否仅展示老师)
     * @param mode 类型(full、teacher) 默认为full
     */
    setWrapMode(mode) {
      console.log(`VideoWrapComponent::setWrapMode=>mode: ${mode}`);
      if (this.videoMode === mode) {    // 忽略模式未改变
        // console.warn('VideoWrapComponent::setWrapMode=>ignore with no change', mode);
        return;
      }
      TCIC.SDK.instance.reportEvent('wrap_mode', { mode });
      if (mode === 'full') {
        this.videoMode = mode;
        this.showHidden = true;
        if (!this.isLiveClass) {
          TCIC.SDK.instance.updateComponent('teacher-component', {
            width: `${this.videoWidth}px`,
            height: `${this.videoHeight}px`,
            style: 'overflow: visible;',
          });
        }
        this.$nextTick(() => {
          const wrap = this.$refs.student;
          this.studentComs.forEach((dom) => {
            wrap.appendChild(dom);
            this.updateComControlLayout(dom);
          });
          this.updateScroll();
          if (this.checkVideoDrag() && !this.isEnableDrag) { // 检测是否要开启视频拖动
            this.enableAllDrag();
          }
        });
      } else if (mode === 'teacher') {
        this.videoMode = mode;
        this.showHidden = false;
        if (this.videoLayout !== this.VideoWrapLayout.Screen) {  // 非屏幕分享模式时，teacher模式下
          if (!this.isLiveClass) {
            TCIC.SDK.instance.updateComponent('teacher-component', {
              width: '100%',
              height: '100%',
              style: 'overflow: visible;',
            });
          }
        }
        if (this.isEnableDrag) { // 仅老师模式关闭视频拖动
          this.disableAllDrag();
        }
      } else if (mode === 'two') {
        this.videoMode = mode;
        this.showHidden = false;
        if (this.videoLayout !== 'screen') {  // 非屏幕分享模式时，teacher模式下
          if (!this.isLiveClass) {
            TCIC.SDK.instance.updateComponent('teacher-component', {
              width: '100%',
              height: '50%',
              style: 'overflow: visible;',
            });
          }
        }
        if (this.isEnableDrag) { // 仅老师模式关闭视频拖动
          this.disableAllDrag();
        }
      }
      // 关闭所有悬浮控制窗口
      TCIC.SDK.instance.setState(Constant.TStateVideoCtrlUserId, '');
      setTimeout(() => {
        this.checkMaxVisibleStudentVideoCount();
      }, 100);
    },
    addTeacherToGridVideos() {
      const studentsWrap = this.$refs.student;
      if (!this.isLiveClass
          || this.videoLayout !== this.VideoWrapLayout.Video
          || !studentsWrap) {
        return;
      }
      const teacherDom = TCIC.SDK.instance.getComponent('teacher-component');
      if (teacherDom.parentNode !== studentsWrap && !this.isMobile) {
        console.log('[VideoWrapComponent] addTeacherToGridVideos, move teacher-component to studentsWrap');
        studentsWrap.appendChild(teacherDom);
      }
    },
    layoutLiveClassGridVideos: Lodash.throttle(function () {
      const studentsWrap = this.$refs.student;
      if (!this.isLiveClass
          || this.videoLayout !== this.VideoWrapLayout.Video
          || !studentsWrap) {
        return;
      }
      const isVideoLiveClass = this.isLiveClass && this.videoLayout === this.VideoWrapLayout.Video;
      const isSubCameraShow = TCIC.SDK.instance.getState(Constant.TStateShowSubCameraComponent, false);
      const width = studentsWrap.clientWidth - 24;
      // const height = studentsWrap.clientHeight;
      let count = 1 + this.studentComs.length;
      if (isVideoLiveClass && isSubCameraShow) {  // 视频公开课辅助摄像头算一路视频
        count += 1;
      }
      let column = 1;
      if (count >= 5) {
        column  = 3;
      } else if (count >= 2) {
        column = 2;
      }
      this.videoWidth = Math.floor(width / column);
      this.videoHeight = Math.floor(this.videoWidth * 9 / 16);
      const row = Math.ceil(count / column);
      const totalH = this.videoHeight * row + (row - 1) * 4;
      if (totalH >= studentsWrap.clientHeight && row > 0) {
        // 容器高度不够时，按最大高度来布局
        this.videoHeight = Math.floor((studentsWrap.clientHeight - (row - 1) * 4) / row);
        this.videoWidth = Math.floor(this.videoHeight * 16 / 9);
      }
      const layout = {
        top: '0',
        left: '0',
        position: 'relative',
        width: `${this.videoWidth}px`,
        height: `${this.videoHeight}px`,
        display: 'block',
        style: 'overflow: visible;margin:2px;',
      };
      TCIC.SDK.instance.updateComponent('teacher-component', layout).then((sucess) => {
        const teacherDom = TCIC.SDK.instance.getComponent('teacher-component');
        if (sucess && teacherDom) {
          teacherDom.getVueInstance().setControlDirect(count > 1 ? 'bottom' : 'hide');
        }
      });

      this.studentComs.forEach((dom) => {
        TCIC.SDK.instance.updateComponent(dom.tagName.toLowerCase(), layout, dom.getAttribute('label'), true);
        dom.getVueInstance().setControlDirect('bottom');
      });
      if (isVideoLiveClass && isSubCameraShow) {
        TCIC.SDK.instance.updateComponent('sub-camera-component', layout);
      }
      this.disableAllDrag();
    }, 500),
    onBoundingClientRectChange(rect) {
      if (this.isLiveClass && this.videoLayout === this.VideoWrapLayout.Video) {
        this.layoutLiveClassGridVideos();
      }
    },
    /** 复原所有拖动 */
    resetAllDrag() {
      this.videoWall.clearVideos();
      // 重置老师
      if (this.teacherCom) {
        this.resetDragDom(this.teacherCom, !this.isEnableDrag, this.videoLayout === this.VideoWrapLayout.Three);
      }
      this.studentComs.forEach((ele) => {
        this.resetDragDom(ele, !this.isEnableDrag);
      });
      // 重置层级
      this.curZindex = 2;
    },
    /**
     * 复原所有拖动并禁用拖动
     */
    disableAllDrag() {
      console.log('VideoWrapComponent::disableAllDrag=>enter');
      this.isEnableDrag = false;
      this.resetAllDrag();
      this.curZindex = 2;
    },
    /**
     * 激活所有拖动
     */
    enableAllDrag() {
      console.log('VideoWrapComponent::enableAllDrag=>enter');
      this.isEnableDrag = true;
      if (this.teacherCom) {
        // 互动班课三分屏布局禁用老师视频拖动
        this.enableDrag(this.teacherCom,  this.videoLayout !== this.VideoWrapLayout.Three);
      }
      this.studentComs.forEach((ele) => {
        this.enableDrag(ele, true, true);
      });
    },
    /**
     * 学生视频位置更新
     */
    updateDomPosition(dom, inWrap) {
      if (dom.tagName.toLowerCase() === 'teacher-component') {  // 忽略老师
        return ;
      }
      if (TCIC.SDK.instance.isTeacher()) {
        const carouselCom = TCIC.SDK.instance.getComponent('carousel-component');
        if (carouselCom && carouselCom.getVueInstance()) {
          carouselCom.getVueInstance().lockStudent(dom.getAttribute('label'), !inWrap);
        }
      }
    },
    /**
     * 检测当前能展示的最大学生数量
     */
    checkMaxVisibleStudentVideoCount() {
      if (this.isTeacher || this.isAssistant || this.isSupervisor) {
        const curVideoCount = this.getMaxVisibleStudentVideoCount();
        if (this.maxStudentVideoCount !== curVideoCount) {
          this.maxStudentVideoCount = curVideoCount;
          TCIC.SDK.instance.reportEvent('carousel_maxcount', {
            count: curVideoCount,
            layout: this.videoLayout,
            width: document.body.clientWidth,
            height: document.body.clientHeight,
          });
          const carouselCom = TCIC.SDK.instance.getComponent('carousel-component');
          if (carouselCom && carouselCom.getVueInstance()) {
            carouselCom.getVueInstance().updateCount(this.maxStudentVideoCount);
          }
        }
      }
    },
    /**
     * 获取当前能展示的学生视频数量
     */
    getMaxVisibleStudentVideoCount() {
      if (this.videoMode === 'teacher') {
        return 0;
      } if (this.videoMode === 'two') {
        return 1;
      }
      if (this.videoLayout === this.VideoWrapLayout.Double) {    // 双排模式
        return 16;
      } if (this.videoLayout === this.VideoWrapLayout.Screen) {   // 屏幕分享
        return 2;
      } if (this.videoLayout === this.VideoWrapLayout.Top) {
        return Math.floor((document.body.clientWidth - 330) / (this.videoWidth + 8));
      } if ([this.VideoWrapLayout.Right, this.VideoWrapLayout.Left].includes(this.videoLayout)) {
        return Math.floor((document.body.clientHeight - 307) / (this.videoHeight + 8));
      }
      if (this.videoLayout === this.VideoWrapLayout.Three) {
        return Math.floor((document.body.clientWidth - 456) / (this.videoWidth + 8));
      }
      return 0;
    },
    /**
     * 最大化指定视频
     */
    maxUserVideo(dom, operatorId) {
      // 三分屏双击缩放老师画面 同步任务, 防止一直来回updateTask
      if (this.videoLayout === this.VideoWrapLayout.Three && !operatorId && dom.tagName.toLowerCase() === 'teacher-component') {
        TCIC.SDK.instance.updateTask('toggle-user-video', JSON.stringify({
          layout: this.videoLayout,
          sessionId: this.sessionId,
          operatorId: TCIC.SDK.instance.getUserId(),
          domId: dom.id,
          action: 'maxUserVideo',
        }));
      }
      this.prevStyle = TCIC.SDK.instance.getComponentLayout(dom.tagName.toLowerCase(), dom.getAttribute('label'));
      console.log('prevStyle', this.prevStyle);
      // 开启视频墙模式
      this.updateVideoWallState(true);
      this.moveOutDom(dom);
      this.addVideoToVideoWall(dom, false);
      this.videoWall.updateRender();
      setTimeout(() => {
        if (this.dragMap.has(dom.id)) {
          this.dragMap.get(dom.id).updateZoom(1);
        }
        this.updateComControlLayout(dom);
      }, 300);
    },
    resetUserVideo(videoDom, operatorId) {
      if (this.videoLayout === this.VideoWrapLayout.Three && !operatorId && videoDom.tagName.toLowerCase() === 'teacher-component') {
        TCIC.SDK.instance.updateTask('toggle-user-video', JSON.stringify({
          layout: this.videoLayout,
          sessionId: this.sessionId,
          operatorId: TCIC.SDK.instance.getUserId(),
          domId: videoDom.id,
          action: 'resetUserVideo',
        }));
      }
      this.removeVideoFromVideoWall(videoDom);
      TCIC.SDK.instance.updateComponent(
        videoDom.tagName.toLowerCase(),
        { ...this.prevStyle, display: 'block' },
        videoDom.getAttribute('label'),
      );
    },
    /**
     * 一键视频墙
     */
    quickVideoWall(enable) {
      if (enable) {
        this.updateVideoWallState(true);
        const classInfo = TCIC.SDK.instance.getClassInfo();
        this.studentComs.forEach((dom) => {
          const userId = this.getDomUserId(dom);
          this.moveOutDom(dom);
          if (classInfo?.assistants?.includes(userId)) {    // 助教放在头部
            this.videoWall.unshiftVideo(this.getDomUserId(dom), dom, false);
          } else {
            this.videoWall.pushVideo(this.getDomUserId(dom), dom, false);
          }
        });
        if (this.videoLayout !== this.VideoWrapLayout.Three) {
          this.moveOutDom(this.teacherCom);
          this.videoWall.unshiftVideo(this.getDomUserId(this.teacherCom), this.teacherCom, false);
        } else {
          this.videoWall.removeVideo(this.getDomUserId(this.teacherCom), false);
        }
        this.videoWall.updateRender();    // 主动刷新布局
      } else {
        this.resetAllDrag();
        this.updateVideoWallState(false);
      }
      setTimeout(() => {    // 延时更新控制栏位置，避免获取过期坐标
        [this.teacherCom, ...this.studentComs].forEach((dom) => {
          this.updateComControlLayout(dom); // 更新控制栏
        });
      }, 100);
    },
    // 判断是否应该开启视频拖动
    checkVideoDrag() {
      if (this.isPad) {
        // pad上拖动的mask会导致点不出来 videoctrl，不拖动了
        return false;
      }
      return (
        (this.isTeacher || this.isAssistant || this.isSupervisor)
          && this.videoMode !== 'teacher'
          && this.videoLayout !== this.VideoWrapLayout.Screen)
          || (TCIC.SDK.instance.isLiveClass() && this.isSmallScreen);
    },
    // 更新所有组件的控制栏位置(拖动时会隐藏控制栏)
    updateAllControlDirect(isDrag = false) {
      if (this.teacherCom) {
        this.updateComControlLayout(this.teacherCom, isDrag);
      }
      this.studentComs.forEach((ele) => {
        this.updateComControlLayout(ele, isDrag);
      });
    },
    // 刷新滚动控制栏状态
    updateScroll() {
      if (this.videoMode === 'full') {
        const studentScroll = this.$refs.student;
        if (this.videoLayout === this.VideoWrapLayout.Top || this.videoLayout === this.VideoWrapLayout.Three) {
          this.showScroll = (studentScroll.scrollWidth - studentScroll.clientWidth > 10);
        } else if ([this.VideoWrapLayout.Right, this.VideoWrapLayout.Left].includes(this.videoLayout)) {
          this.showScroll = (studentScroll.scrollHeight - studentScroll.clientHeight > 10);
        } else {
          this.showScroll = false;
        }
      } else {
        this.showScroll = false;
      }
    },
    // 初始化视频组件拖动功能
    initDrag(dom) {
      const self = this;
      const wrapAreaDom = this.$refs.wrap;
      const targetArray = [wrapAreaDom, this.boardTarget];
      const dragger = new Drag({
        dragEle: dom,
        targetEles: targetArray,
        pad: 15,
        lockRadio: true,
        enableDrag: true,
        enableResize: false,
        onTargetChange(drag, target) {
          if (target === self.boardTarget) {   // 进入白板区
            drag.enableResize(true);
            drag.reSize(self.videoWidth * 1.5, self.videoHeight * 1.5);
          } else if (target === wrapAreaDom) {
            dragger.enableResize(false);
            drag.reSize(self.videoWidth, self.videoHeight);
          }
        },
        onResize: (x, y, w, h, cursor, isReSize) => { // 位置更新
          if (isReSize && (w < self.videoWidth || h < self.videoHeight)) {  // 添加最小尺寸限制
            return;
          }
          if (dom.getAttribute('delete') === 'true') {    // 组件移除后不处理
            return ;
          }
          let vzoom = 1;
          if (dragger.m_zoom < 1) {    // 误差校正
            vzoom = (1.0 / dragger.m_zoom - 0.01).toFixed(2);
          }
          TCIC.SDK.instance.updateComponent(dom.tagName.toLowerCase(), {
            position: 'fixed',
            left: `${x * dragger.m_zoom}px`,
            top: `${y * dragger.m_zoom}px`,
            width: `${w}px`,
            height: `${h}px`,
            zIndex: 500,
            style: `padding: 0px; overflow: visible; zoom: ${vzoom};`,
          }, dom.getAttribute('label'), false);
          document.body.style.cursor = cursor;
          dom.classList.add('drag');
          this.updateComControlLayout(dom, true);
        },
        onStartDrag: (drag, x, y) => {
          console.log(`VideoWrapComponent::initDrag[${dom.getAttribute('label')}]=>onStartDrag`);
          self.showDragMask = true;
          this.updateDomPosition(dom, false);
        },
        onEndDrag: (drag, position, target) => {
          console.log(`VideoWrapComponent::initDrag[${dom.getAttribute('label')}]=>onEndDrag`);
          self.showDragMask = false;
          if (dom.getAttribute('delete') === 'true') {    // 组件移除后不处理
            return ;
          }
          if (target === wrapAreaDom) { // 回到视频区D
            TCIC.SDK.instance.updateComponent(dom.tagName.toLowerCase(), {
              position: 'relative',
              left: '0px',
              top: '0px',
              width: `${this.videoWidth}px`,
              height: `${this.videoHeight}px`,
              zIndex: 'auto',
              style: 'overflow: visible; touch-action: none;',
            }, dom.getAttribute('label'), false);
            self.moveInDom(dom);
            self.updateDragInfo(dom, false, self.boardTarget, 0);
            // drag.updateDelay(230);
            drag.updateZoom(dom.getAttribute('label') === 'default' || !this.isDoubleLine ? 1 : 0.49);
            this.updateDomPosition(dom, true);
            self.removeVideoFromVideoWall(dom, TCIC.SDK.instance.getState(Constant.TStateVideoWallMode));
          } else if (target === this.boardTarget) {  // 进入白板区
            const zIndex = self.curZindex;
            self.curZindex += 1;
            TCIC.SDK.instance.updateComponent(dom.tagName.toLowerCase(), {
              position: 'fixed',
              zIndex: 500,
              style: 'overflow: visible;',
            }, dom.getAttribute('label'), false);
            self.moveOutDom(dom);
            self.recordDomPosition(dom, zIndex);
            self.updateDragInfo(dom, true, self.boardTarget, zIndex);
            drag.updateZoom(1);
            self.addVideoToVideoWall(dom, TCIC.SDK.instance.getState(Constant.TStateVideoWallMode));
            // drag.updateDelay(0);
          }
          document.body.style.cursor = 'default';
          dom.classList.remove('drag');
          this.updateVideoZIndex();
          setTimeout(() => {
            this.updateComControlLayout(dom);
          }, 200);
        },
        onStartResize: (drag, x, y) => {
          console.log(`VideoWrapComponent::initDrag[${dom.getAttribute('label')}]=>onStartResize`);
          self.showDragMask = true;
          if (TCIC.SDK.instance.isAndroid() && dom && dom.getVueInstance()) {   // 移动端在修改尺寸时暂停视频渲染
            dom.getVueInstance().onPauseRender();
          }
        },
        onEndResize: (drag, position) => {
          console.log(`VideoWrapComponent::initDrag[${dom.getAttribute('label')}]=>onEndResize`);
          if (dom.getAttribute('delete') === true) {    // 组件移除后不处理
            return ;
          }
          const zIndex = self.curZindex;
          self.curZindex += 1;
          TCIC.SDK.instance.updateComponent(dom.tagName.toLowerCase(), {
            zIndex,
            style: 'overflow: visible;',
          }, dom.getAttribute('label'), false);
          self.showDragMask = false;
          self.recordDomPosition(dom, zIndex);
          document.body.style.cursor = 'default';
          dom.classList.remove('drag');
          self.updateDragInfo(dom, true, this.boardTarget, zIndex);
          dom.getVueInstance().enableSmallMode(false);    // 关闭缩小模式
          this.updateVideoZIndex();
          setTimeout(() => {
            this.updateComControlLayout(dom);
          }, 200);
          if (TCIC.SDK.instance.isAndroid() && dom && dom.getVueInstance()) {   // 移动端在修改尺寸时暂停视频渲染
            dom.getVueInstance().onResumeRender();
          }
        },
      });
      if (TCIC.SDK.instance.isLiveClass()) {    // 公开课开启尺寸变更
        dragger.enableResize(true);
      }
      this.dragMap.set(dom.id, dragger);
    },
    // 移除视频组件拖动功能
    removeDrag(dom) {
      if (this.dragMap.has(dom.id)) {
        this.dragMap.get(dom.id)
          .destory();
        this.dragMap.delete(dom.id);
      }
    },
    // 将组件移出视频区
    moveOutDom(dom) {
      if (dom.getAttribute('drag') === 'true') {
        return;
      }
      const app = document.getElementById('app');
      if (app && dom) {
        app.appendChild(dom);
        dom.setAttribute('drag', 'true');
        // this.updateComControlLayout(dom);
        dom.getVueInstance().onLeaveVideoWrap(true);
        if (this.isEnableDrag && this.dragMap.has(dom.id)) {
          this.dragMap.get(dom.id)
            .enableResize(true);
        }
        if (this.isTeacherDom(dom)) this.setMinSize = false;
      } else {
        console.warn('VideoWrapComponent::moveOutDom=>with out dom');
      }
      this.$nextTick(() => {
        this.updateScroll();
      });
      this.updateDomPosition(dom, false);
      if (this.videoLayout === this.VideoWrapLayout.Double) {   // 双排布局需要处理zoom
        const drag = this.dragMap.get(dom.id);
        drag && drag.updateZoom(1);
      }
    },
    // 将组件移回视频区
    moveInDom(dom) {
      if (dom.getAttribute('drag') !== 'true') {
        return;
      }
      if (dom.tagName.toLowerCase() === 'teacher-component') {  // 老师
        this.$refs.teacher.appendChild(dom);
        if (TCIC.SDK.instance.isIOS() && TCIC.SDK.instance.isPad()) {
          this.setMinSize = true;
        }
      } else {  // 学生
        let idx = this.studentComs.indexOf(dom);
        while (idx + 1 < this.studentComs.length && this.studentComs[idx + 1].getAttribute('drag') === 'true') {
          idx += 1;
        }
        if (idx + 1 < this.studentComs.length) {
          this.$refs.student.insertBefore(dom, this.studentComs[idx + 1]);
        } else {
          this.$refs.student.appendChild(dom);
        }
        setTimeout(() => {    // 延时展示节点(nexttick仍无法获取实际总宽高)
          this.showMeTheDom(dom);
          this.updateScroll();
        }, 50);
      }
      dom.setAttribute('drag', 'false');
      // this.updateComControlLayout(dom);
      dom.getVueInstance()
        .enableSmallMode(false);    // 关闭缩小模式
      dom.getVueInstance()
        .onLeaveVideoWrap(false);
      if (this.isEnableDrag && this.dragMap.has(dom.id)) {
        this.dragMap.get(dom.id)
          .enableResize(false);
      }
      this.positionMap.delete(dom.id);
      this.updateDomPosition(dom, true);
      if (this.videoLayout === this.VideoWrapLayout.Double) {   // 双排布局需要处理zoom
        const drag = this.dragMap.get(dom.id);
        drag && drag.updateZoom(0.49);
      }
    },
    // 让视频组件可见
    showMeTheDom(dom) {
      let dt = 0; let i = 0;
      if (this.videoLayout === 'top') {
        for (i = 0; i < this.studentComs.length && this.studentComs[i] !== dom; i++) {
          if (this.studentComs[i].getAttribute('drag') !== 'true') {
            dt += (this.videoWidth + 8);
          }
        }
        this.scrollVideoH(dt, false);
      } else {  // right 或 screen
        for (i = 0; i < this.studentComs.length && this.studentComs[i] !== dom; i++) {
          if (this.studentComs[i].getAttribute('drag') !== 'true') {
            dt += (this.videoHeight + 8);
          }
        }
        this.scrollVideoV(dt, false);
      }
    },
    // 开启视频组件拖动功能
    enableDrag(dom, flag, needZoom = false) {
      if (!dom) {
        console.warn('enableDrag failed, dom invalid');
        return;
      }
      if (!this.dragMap.has(dom.id)) {
        this.initDrag(dom);
      } else {
        const dragger = this.dragMap.get(dom.id);
        if (dragger.getDragElement() !== dom) {  // 视频组件发生变更
          dragger.destory();
          this.initDrag(dom);
        }
      }
      this.dragMap.get(dom.id).enableDrag(flag);
      this.updateDomDragZoom(dom);
    },
    // 重置DOM拖动位置
    resetDragDom(dom, isDeActive = true, isSimpleRest = false) {
      if (dom && this.dragMap.has(dom.id)) {
        if (isDeActive) {
          this.dragMap.get(dom.id)
            .enableDrag(false);
        }
        if (dom.getAttribute('drag') === 'true') {
          if (!isSimpleRest) {
            TCIC.SDK.instance.updateComponent(dom.tagName.toLowerCase(), {
              position: 'relative',
              left: '0px',
              top: '0px',
              width: `${this.videoWidth}px`,
              height: `${this.videoHeight}px`,
              zIndex: 'auto',
              style: 'overflow: visible;',
            }, dom.getAttribute('label'), false);
            this.moveInDom(dom);
          } else { // 仅清除位置记录
            dom.setAttribute('drag', 'false');
            dom.getVueInstance()
              .enableSmallMode(false);    // 关闭缩小模式
            dom.getVueInstance()
              .onLeaveVideoWrap(false);
            this.positionMap.delete(dom.id);
          }
          this.updateDragInfo(dom, false, this.boardTarget, 0);
          this.updateComControlLayout(dom);
          this.updateDomDragZoom(dom);
        }
        this.removeVideoFromVideoWall(dom, TCIC.SDK.instance.getState(Constant.TStateVideoWallMode));
      }
    },
    // 更新视频组件控制栏位置
    updateComControlLayout(dom, isDrag = false) {
      try {
        if (isDrag) { // 拖动时隐藏
          dom.getVueInstance().setControlDirect('hide');
        } else if (dom.getAttribute('drag') === 'true') {  // 拖动视频位置显示在顶端
          dom.getVueInstance()
            .setControlDirect('top');
        } else {
          if (this.videoLayout === this.VideoWrapLayout.Top
              || this.videoLayout === this.VideoWrapLayout.Double
              || this.videoLayout === this.VideoWrapLayout.PicInPic
              || this.videoLayout === this.VideoWrapLayout.VideoDoc
              || this.videoLayout === this.VideoWrapLayout.Video) {
            dom.getVueInstance()
              .setControlDirect('bottom');
          } else if (this.videoLayout === this.VideoWrapLayout.Three) {
            dom.getVueInstance().setControlDirect(dom === this.teacherCom ? 'left' : 'bottom');
          } else if (this.videoLayout === this.VideoWrapLayout.Left) {
            dom.getVueInstance().setControlDirect('right');
          } else {
            dom.getVueInstance().setControlDirect('left');
          }
        }
      } catch (err) {
        console.warn('VideoWrapComponent::updateComControlLayout=>failed', err);
      }
    },
    // 显示我的窗口(仅打开摄像头前调用/避免打开失败)
    showMyVideo() {
      if (!this.isShowVideoList) {    // 当前视频栏为隐藏状态
        const dom = this.studentComs.filter(dom => dom.getAttribute('label') === TCIC.SDK.instance.getUserId())[0];
        if (dom && dom.getAttribute('drag') !== 'true') {   // 非拖动状态(不在白板区)
          this.toggleShow(true);   // 显示视频栏
        }
      }
    },
    // 显示/隐藏视频区
    toggleShow(show, isSync = false) {
      if (typeof show !== 'boolean') {
        show = !this.isShowVideoList;
      }
      if (show === this.isShowVideoList) {  // 忽略重复请求
        return;
      }
      this.isShowVideoList = show;
      if (!isSync) {
        TCIC.SDK.instance.setState(Constant.TStateFullScreen, !this.isShowVideoList);
      }
      const wrapAreaDom = this.$refs.wrap;
      if (this.isShowVideoList) {
        this.dragMap.forEach((dragger, id) => {
          dragger.addTargetElement(wrapAreaDom);
        });
        this.updateTeacherComponent(true);
      } else {
        this.dragMap.forEach((dragger, id) => {
          dragger.removeTargetElement(wrapAreaDom);
        });
        // 隐藏所有组件的控制栏
        this.updateAllControlDirect();
        this.updateTeacherComponent(false);
      }
    },
    updateTeacherComponent(showctrl) {
      const teacherLayout = {};
      if (this.videoLayout === this.VideoWrapLayout.Three) {
        teacherLayout.width = `${this.rightSideWidth}px`;
      } else {
        if (showctrl) {
          teacherLayout.width = (this.isSmallScreen && this.isPortrait) ? '100%' : '160px';
        } else {
          teacherLayout.width = '100%';
        }
      }
      // console.log(`updateTeacherComponent teacher-component, showctrl ${showctrl}`, teacherLayout);
      TCIC.SDK.instance.updateComponent('teacher-component', teacherLayout);
    },
    // 水平滚动
    scrollVideoH(dt, isRelative = true) {
      const studentScroll = this.$refs.student;
      if (!isRelative) {
        console.log(`VideoWrapComponent::scrollVideoH=>dt: ${dt}, isRelative: ${isRelative}/${studentScroll.scrollWidth}`);
      }
      let scrollPos = isRelative ? studentScroll.scrollLeft + dt : dt;
      if (scrollPos < 0) {
        scrollPos = 0;
      } else if (scrollPos > studentScroll.scrollWidth - studentScroll.offsetWidth) {
        scrollPos = studentScroll.scrollWidth - studentScroll.offsetWidth;
      }
      studentScroll.scrollLeft = scrollPos;
    },
    // 垂直滚动
    scrollVideoV(dt, isRelative = true) {
      const studentScroll = this.$refs.student;
      if (!isRelative) {
        console.log(`VideoWrapComponent::scrollVideoV=>dt: ${dt}, isRelative: ${isRelative}/${studentScroll.scrollHeight}`);
      }
      let scrollPos = isRelative ? studentScroll.scrollTop + dt : dt;
      if (scrollPos < 0) {
        scrollPos = 0;
      } else if (scrollPos > studentScroll.scrollHeight - studentScroll.offsetHeight) {
        scrollPos = studentScroll.scrollHeight - studentScroll.offsetHeight;
      }
      studentScroll.scrollTop = scrollPos;
    },
    // 滚动事件
    scrollEvent(event) {
      // 关闭所有悬浮控制窗口
      TCIC.SDK.instance.setState(Constant.TStateVideoCtrlUserId, '');
    },
    // 记录可拖动视频组件
    recordDomPosition(dom, zIndex) {
      console.info(`VideoWrapComponent::recordDomPosition=>id: ${dom.id}, zIndex: ${zIndex}`);
      const relativePos = this.getRelativePosition(dom, this.boardTarget, zIndex);
      if (relativePos) {
        console.log(`VideoWrapComponent::recordDomPosition=>${dom.id}, ${JSON.stringify(relativePos)}`);
        this.positionMap.set(dom.id, relativePos);
      } else {  // 位置信息异常时延时刷新位置信息
        console.warn(`VideoWrapComponent::recordDomPosition=>reset position: ${dom.id}`);
        this.$nextTick(() => {
          this.updateAllDragDomPosition();
        });
      }
    },
    // 计算视频的白板相对位置
    getRelativePosition(dom, boardDom, zIndex) {
      if (dom && boardDom) {
        const domRect = dom.getBoundingClientRect();
        const boardRect = boardDom.getBoundingClientRect();
        if (domRect.width === 0
            || domRect.height === 0
            || domRect.left + 1 < boardRect.left
            || domRect.top + 1 < boardRect.top) {
          return null;
        }
        return {
          left: ((domRect.left - boardRect.left) / boardRect.width).toFixed(3),
          top: ((domRect.top - boardRect.top) / boardRect.height).toFixed(3),
          width: (domRect.width / boardRect.width).toFixed(3),
          height: (domRect.height / boardRect.height).toFixed(3),
          zIndex,
        };
      }
      return null;
    },
    // 更新位置信息
    updateDragTask: Lodash.throttle(function () {
      // 只有老师助教等才能updateTask
      if (!TCIC.SDK.instance.isTeacher()
      && !TCIC.SDK.instance.isAssistant()
      && !TCIC.SDK.instance.isSupervisor()) return;
      const positionArray = Array.from(this.positionMap.keys()).map(domId => ({
        domId,
        info: this.positionMap.get(domId),
      }));
      TCIC.SDK.instance.updateTask('task-drag-array', JSON.stringify({
        sessionId: this.sessionId,
        array: positionArray,
      }))
        .catch((err) => {
          console.warn('VideoWrapComponent::updateDragTask=>fail: ', err);
        });
    }, 500, {
      leading: false,
      trailing: true,
    }),
    // 更新用户位置信息
    updateUserTask(domId, info) {
      this.positionMap.set(domId, info);
      this.updateDragTask();
    },
    // 同步可拖动视频位置
    updateDragInfo(dom, isDragging, boardDom, zIndex) {
      if (isDragging) {
        const relativePos = this.getRelativePosition(dom, boardDom, zIndex);
        if (relativePos) {
          console.log('VideoWrapComponent::updateDragInfo=>', JSON.stringify(relativePos));
          this.updateUserTask(dom.id, relativePos);
        }
      } else {
        console.log('VideoWrapComponent::updateDragInfo=>reset');
        this.updateUserTask(dom.id, {});
      }
    },
    // 更新可拖动视频位置
    updateDragDomPosition(id, info, rect, update = true) {
      const dom = document.getElementById(id);
      if (!dom) {
        console.warn('VideoWrapComponent::updateDragDomPosition=>id not found: ', id);
        return;
      }

      // if (this.isMobile && !this.isPad && this.isStudent && dom.tagName.toLowerCase() === 'teacher-component') {
      //   return;
      // }

      if (this.videoLayout === this.VideoWrapLayout.Three
            && !this.isSmallScreen
            && dom.tagName.toLowerCase() === 'teacher-component') {
        // 三分屏布局时，pad和pc端不处理teacher-component的布局数据，客户端自行布局
        TCIC.SDK.instance.reportEvent('ignore_teacher_video_update', { layout: this.videoLayout, info, rect });
        return;
      }
      if (info.width || info.height) {  // 需要拖动
        if (rect.width === 0 || rect.height === 0) {
          console.warn('VideoWrapComponent::updateDragDomPosition=>invalid rect with ', this.isBoardReady, rect);
          return;
        }
        if (dom.getAttribute('drag') !== 'true') {  // 当前仍在视频栏
          this.moveOutDom(dom);
        }
        const realWidth = info.width * rect.width;
        const realHeight = info.height * rect.height;
        if (!TCIC.SDK.instance.isLiveClass()) {    // 公开课禁用缩小模式
          const isSmallMode = (realWidth < this.videoWidth || realHeight < this.videoHeight);
          dom.getVueInstance().enableSmallMode(isSmallMode);   // 更新缩小模式
        }
        TCIC.SDK.instance.updateComponent(dom.tagName.toLowerCase(), {
          position: 'fixed',
          left: `${info.left * rect.width + rect.left}px`,
          top: `${info.top * rect.height + rect.top}px`,
          width: `${realWidth}px`,
          height: `${realHeight}px`,
          zIndex: info.zIndex,
          style: 'overflow: visible;',
        }, dom.getAttribute('label'), true);

        if (info.zIndex > this.curZindex) { // 同步更新本地zIndex初始值
          this.curZindex = info.zIndex + 1;
        }
        // 添加视频墙(老师或助教要放在第一位)
        if (this.isTeacherDom(dom)) {
          this.videoWall.unshiftVideo(this.getDomUserId(dom), dom, update);
        } else if (this.isAssistantDom(dom)) {
          const classInfo = TCIC.SDK.instance.getClassInfo();
          if (this.teacherCom && this.videoWall.checkVideo(classInfo.teacherId)) { // 有老师则把老师重新置到头部
            this.videoWall.removeVideo(classInfo.teacherId, false);
            this.videoWall.unshiftVideo(this.getDomUserId(dom), dom, false);
            this.videoWall.unshiftVideo(classInfo.teacherId, this.teacherCom, update);
          } else {
            this.videoWall.unshiftVideo(this.getDomUserId(dom), dom, update);
          }
        } else {
          this.videoWall.pushVideo(this.getDomUserId(dom), dom, update);
        }
      } else {    // 不需要拖动
        if (dom.getAttribute('drag') === 'true') {  // 当前仍在白板区
          this.moveInDom(dom);
        }
        this.videoWall.removeVideo(this.getDomUserId(dom), false);
        let width = `${this.videoWidth}px`;
        if (this.isSmallScreen) {
          if (this.isPortrait) {
            width = '100%';
          } else {
            width = '160px';
          }
        }
        TCIC.SDK.instance.updateComponent(dom.tagName.toLowerCase(), {
          position: 'relative',
          left: '0px',
          top: '0px',
          width,
          height: this.isSmallScreen ? '80px' : `${this.videoHeight}px`,
          zIndex: 'auto',
          style: 'overflow: visible;',
        }, dom.getAttribute('label'), false);
      }
      setTimeout(() => {    // 延时更新控制栏位置，避免获取过期坐标
        this.updateComControlLayout(dom); // 更新控制栏
        this.updateDomDragZoom(dom);
      }, 100);
    },
    // 更新白板区域视频组件位置
    updateAllDragDomPosition() {
      if (this.videoMode === 'teacher') {
        return;
      }
      const boardRect = this.boardTarget.getBoundingClientRect();
      const videoArr = [...this.studentComs];
      if (this.teacherCom) {
        videoArr.unshift(this.teacherCom);
      }
      videoArr.forEach((dom) => {
        if (dom) {
          if (this.positionMap.has(dom.id)) {   // 有位置
            this.updateDragDomPosition(dom.id, this.positionMap.get(dom.id), boardRect);
          } else {    // 没有位置
            this.updateDragDomPosition(dom.id, { top: 0, left: 0, width: 0, heigth: 0 }, boardRect);
          }
        }
      });
      this.updateScreenDomPosition();
      this.updateVideoZIndex();
    },
    // 更新可拖动区域尺寸
    updateBoardArea(board) {
      if (board) {
        const areaRadio = (16 / 9).toFixed(2);
        const boardRect = board.getBoundingClientRect();
        const boardRadio = (boardRect.width / boardRect.height).toFixed(2);
        this.isLiveClass = TCIC.SDK.instance.isLiveClass();
        const isBigVideoMode = TCIC.SDK.instance.getState(Constant.TStateBigVideoMode, false);
        if (this.isLiveClass && isBigVideoMode) {
          // 公开课大视频布局时，白板悬浮在右上角，此时取boardRect，其他情况按16比9等比取
          this.boardTarget.update(boardRect.left, boardRect.top, boardRect.width, boardRect.height);
        } else if (boardRadio > areaRadio) {
          const tempWidth = boardRect.height * areaRadio;
          const dx = (boardRect.width - tempWidth) / 2;
          this.boardTarget.update(boardRect.left + dx, boardRect.top, tempWidth, boardRect.height);
        } else {
          const tempHeight = (boardRect.width / areaRadio).toFixed(2);
          const dy = (boardRect.height - tempHeight) / 2;
          this.boardTarget.update(
            Math.floor(boardRect.left),
            Math.floor(boardRect.top + dy),
            Math.ceil(boardRect.width),
            Math.ceil(tempHeight),
          );
        }
        const rect = {
          ...this.boardTarget.getBoundingClientRect(),
        };
        const padding = this.videoWall.in_padding;
        rect.top -= padding;
        rect.left -= padding;
        rect.width += padding;
        rect.height += padding;
        this.videoWall.updateRect(rect, TCIC.SDK.instance.getState(Constant.TStateVideoWallMode));
      }
    },
    // 进课堂事件处理
    onJoinClass() {
      // 加载老师组件
      let showTeacherPromise = Promise.resolve();
      // 非直播课学生延后到上课后显示
      // 三分屏一开始就显示老师
      if (!this.isTeacher && !TCIC.SDK.instance.isLiveClass() && this.videoLayout !== TCIC.TClassLayout.Three) {
        // eslint-disable-next-line max-len
        showTeacherPromise = TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start);
      }
      showTeacherPromise.then(() => {
        const hideTeacher = TCIC.SDK.instance.isLiveClass() && this.isSmallScreen && !this.isClassStart;
        const afterLoadComponent = () => {
          this.$nextTick(() => {
            if (this.teacherCom) {
              TCIC.SDK.instance.setState(Constant.TStateTeacherComponentLoaded, true);
              if (this.isBoardReady && this.isEnableDrag) {
                this.enableDrag(this.teacherCom, true);
              }
              this.updateComControlLayout(this.teacherCom);
            }
            if (TCIC.SDK.instance.isLiveClass() && this.isSmallScreen) {    // 直播课小屏初始化老师位置
              this.resetTeacherPosition(true);
            } else {
              this.updateAllDragDomPosition();
            }
          });
        };
        if (!this.isLiveClass) {
          let height = `${this.videoHeight}px`;
          if (this.videoMode === 'teacher') {
            height = '100%';
          }
          if (this.isSmallScreen) {
            height = '80px';
          }
          let parendDomId = 'teacher-wrap';
          let teacherLayout = {
            position: 'relative',
            width: this.videoMode === 'teacher' || (this.isMobile && !this.isPad) ? '100%' :  `${this.videoWidth}px`,
            height,
            display: hideTeacher ? 'none' : 'inline-block',
            style: 'overflow: visible;',
          };
          if (this.videoLayout === this.VideoWrapLayout.Three) {
            parendDomId = 'app';
            teacherLayout = {}; // afterLoadComponent 里通过事件触发整体 updateLayout
          }
          console.log(`[VideoWrapComponent] loadComponent teacher-component in ${parendDomId}`, teacherLayout);
          TCIC.SDK.instance.loadComponent('teacher-component', teacherLayout, parendDomId)
            .then((dom) => {
              if (dom === null) {
                console.warn('VideoWrapComponent::mounted=>load teacher component fail!');
                return;
              }
              this.teacherCom = dom;
              afterLoadComponent();
            });
        } else {
          afterLoadComponent;
        }
      });

      // 初始化布局
      this.setWrapLayout(this.videoLayout);
      // 小屏布局校正
      if (this.isSmallScreen) {
        this.addLifecycleTCICStateListener(Constant.TStateHeaderVisible, (visible) => {
          this.isMobileHeaderFix = visible;
        });
      }

      TCIC.SDK.instance.promiseState(Constant.TStateDeviceDetect, false).then(() => {
        setTimeout(() => {    /** 进课堂后延时计算是否需要滚动条，在些之前无法获取有效宽高 */
          this.updateScroll();
          this.checkMaxVisibleStudentVideoCount();
        }, 500);
      });

      // 课前预览音视频
      const isPreviewVideo = TCIC.SDK.instance.getState(Constant.TStateLocalAVBeforeClassBegin);
      const userPermission = TCIC.SDK.instance.getPermission(TCIC.SDK.instance.getUserId());
      // 未上课且不在台上，且需要课前预览 则加载自己视频组件
      if (!this.isClassStart && isPreviewVideo && !userPermission.stage && !this.isTeacher && !this.isSupervisor) {
        this.loadUserComponent(userPermission);
        // 上课时移除
        TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start)
          .then(() => {
            const myPermission = TCIC.SDK.instance.getPermission(TCIC.SDK.instance.getUserId());
            if (!myPermission.stage) {
              this.removeUserComponent(userPermission, TCIC.TPermissionUpdateReason.LocalPreview);
            }
          });
      }

      // 移动端更新白板尺寸
      this.updateBoardArea(TCIC.SDK.instance.getComponent('board-component'));
      // 不是老师则监听循环状态并提示
      if (!this.isTeacher) {
        this.addLifecycleTCICEventListener(TCIC.TMainEvent.Stage_Loop, (info) => {
          if (info.action === 'start') {
            window.showToast(i18next.t('{{arg_0}}已开启循环上台功能', { arg_0: this.roleInfo.teacher }));
          } else if (info.action === 'stop') {
            window.showToast(i18next.t('{{arg_0}}已关闭循环上台功能', { arg_0: this.roleInfo.teacher }));
          }
        });
      }

      this.onPermissionUpdate();
    },
    // 白板区域更新事件处理
    onTargetLayoutChange(target) {
      if (target.theTCICComponentName === 'board-component') {
        console.log('VideoWrapComponent::onTargetLayoutChange=>enter with board-component');
        setTimeout(() => {
          // nexttick仍无法获取正确尺寸
          this.updateBoardArea(target.$el);
          if (TCIC.SDK.instance.isLiveClass() && this.isSmallScreen) {    // 直播课小屏初始化老师位置
            this.resetTeacherPosition(true);
          } else {
            this.updateAllDragDomPosition();
          }
        }, 50);
      }
    },
    // 窗口尺寸变更事件处理
    onWindowResize() {
      console.log('VideoWrapComponent::onWindowResize=>enter');
      setTimeout(() => {
        // nexttick仍无法获取正确尺寸
        this.updateBoardArea(TCIC.SDK.instance.getComponent('board-component'));
        this.updateAllDragDomPosition();
        this.updateScroll();
        this.updateAllControlDirect();
        if (document.body.clientWidth > 0) {  // 检测是否能显示双排的隐藏按钮
          this.isSmallDoubleLine = document.body.clientWidth < 950;
        }
        this.checkMaxVisibleStudentVideoCount();
      }, 100);
    },
    // 更新视频状态状态并通过task保存视频墙状态
    updateVideoWallState(flag) {
      const lastState = TCIC.SDK.instance.getState(Constant.TStateVideoWallMode, false);
      if (lastState !== flag) {
        TCIC.SDK.instance.setState(Constant.TStateVideoWallMode, flag);
        // 同步任务
        if (flag) {
          TCIC.SDK.instance.updateTask(Constant.TConstantVideoWallTaskId);
        } else {
          TCIC.SDK.instance.stopTask(Constant.TConstantVideoWallTaskId);
        }
      }
    },
    // 任务信息更新
    onTaskUpdate(taskInfo) {
      if (taskInfo.taskId === 'task-drag-array') {  // 新版位置同步任务协议(1.6.2开始支持)
        TCIC.SDK.instance.promiseState(TCIC.TMainState.Board_Init, true)
          .then(() => {
            try {
              const jsonData = JSON.parse(taskInfo.content);
              this.processTaskUpdate(jsonData);
            } catch (err) {
              console.error(`VideoWrapComponent::onTaskUpdate=>parse task ${taskInfo.taskId} fail: ${err.toString()}`);
            }
          });
      } else if (taskInfo.taskId === 'toggle-user-video') {
        const jsonData = JSON.parse(taskInfo.content);
        if (jsonData.sessionId === this.sessionId) {
          return;
        }
        TCIC.SDK.instance.promiseState(Constant.TStateTeacherComponentLoaded, true).then(() => {
          if (this.videoLayout === jsonData.layout) {
            const dom = document.getElementById(jsonData.domId);
            if (dom) {
              if (jsonData.action === 'maxUserVideo') {
                this.maxUserVideo(dom, jsonData.operatorId);
                TCIC.SDK.instance.setState(Constant.TStateSkipTeacherUpdateLayout, true);
              } else {
                this.resetUserVideo(dom, jsonData.operatorId);
              }
            }
          }
        });
      } else if (taskInfo.taskId.startsWith('drag-')) {  // 仅处理拖动相关任务 ---- 兼容老版本同步任务(后面可以删除)
        if (!this.isBoardReady) {   // 白板未初始化完成则先缓存
          this.taskBuf.push(taskInfo);
          return;
        }
        try {
          const jsonData = JSON.parse(taskInfo.content);
          if (TCIC.SDK.instance.isTeacher() && jsonData.sessionId === this.sessionId) { // 老师则忽略自己本次产生的任务
            return;
          }
          if (jsonData.width || jsonData.height) {  // 仅记录可拖动的视频组件位置信息
            console.log(`VideoWrapComponent::onTaskUpdate=>${jsonData.domId}, ${JSON.stringify(jsonData)}`);
            this.positionMap.set(jsonData.domId, jsonData);
          } else {
            this.positionMap.delete(jsonData.domId);
          }
          this.updateDragDomPosition(jsonData.domId, jsonData, this.boardTarget.getBoundingClientRect());
          this.updateVideoZIndex();
        } catch (err) {
          console.warn(`VideoWrapComponent::onTargetLayoutChange=>parse task ${taskInfo.taskId} fail: ${err.toString()}`);
        }
      } else if (taskInfo.taskId === 'carousel') {   // 循环上台处理
        if (!TCIC.SDK.instance.isTeacher()) {
          if (taskInfo.status === 0) {    // 任务结束
            window.showToast(i18next.t('{{arg_0}}已关闭循环上台功能', { arg_0: this.roleInfo.teacher }));
          } else {
            window.showToast(i18next.t('{{arg_0}}已开启循环上台功能', { arg_0: this.roleInfo.teacher }));
          }
        }
      } else if (taskInfo.taskId === Constant.TConstantVideoWallTaskId) {
        TCIC.SDK.instance.setState(Constant.TStateVideoWallMode, !!taskInfo.status);
      }
    },
    onClassStatusChange(status) {
      this.isClassStart = status === TCIC.TClassStatus.Already_Start;
      if (this.isSmallScreen) {
        if (!this.isLiveClass) {
          // TCIC.SDK.instance.updateComponent('teacher-component', {
          //   display: this.isClassStart ? 'inline-block' : 'none',
          // });
          if (!this.isClassStart) {
            this.toggleShow(false);
          }
          if (status > 1) { // 课堂已结束或过期
            if (this.teacherCom) {
              this.resetDragDom(this.teacherCom);
            }
          }
        }
        if (this.isClassStart) {
          this.toggleShow(false);
        }
      }
    },
    // 更新(重新计算)本地层级
    updateVideoZIndex() {
      setTimeout(() => {
        let curZIndex = 200;
        const comArray = [...this.studentComs];
        if (this.teacherCom) {
          comArray.unshift(this.teacherCom);
        }
        comArray
          .filter(a => this.positionMap.has(a.id))
          .sort((a, b) => this.positionMap.get(a.id).zIndex - this.positionMap.get(b.id).zIndex)
          .forEach((dom) => {
            dom.style.zIndex = curZIndex;
            curZIndex += 1;
          });
      }, 100);
    },
    // 加载学生视频
    loadUserComponent(info) {
      if (!this.isShowVideoList && info.userId === TCIC.SDK.instance.getUserId()) {  // 视频处于隐藏状态则先显示
        this.toggleShow(true);
        this.$nextTick(() => {
          this.loadUserComponent(info);
        });
        return;
      }
      let width = `${this.videoWidth}px`;
      if (this.isSmallScreen) {
        if (this.isPortrait) {
          width = '100%';
        } else {
          width = '160px';
        }
      }
      TCIC.SDK.instance.reportLog('loadUserVideo', `[VideoWrapComponent] loadStudentComponent, userId ${info.userId}`);
      TCIC.SDK.instance.loadComponent('student-component', {
        position: 'relative',
        width,
        height: this.isSmallScreen ? '80px' : `${this.videoHeight}px`,
        display: 'inline-block',
        style: 'overflow: visible;',
      }, 'student-wrap', info.userId)
        .then((ele) => {
          if (ele === null) {
            TCIC.SDK.instance.reportLog('[TRTC]', `loadStudentComponent error: ${info.userId}`);
            console.warn(`VideoWrapComponent::mounted=>load student component fail: ${info.userId}`);
            return;
          }

          const classInfo = TCIC.SDK.instance.getClassInfo();
          const userId = TCIC.SDK.instance.getUserId();
          const wrap = this.$refs.student;
          let afterDom = null;
          if (classInfo.assistants.includes(info.userId)) { // 助教
            if (this.isLiveClass && this.videoLayout === this.VideoWrapLayout.Video) {
              // 公开课纯视频布局，老师/助教/学生都在student-wrap中
              afterDom = TCIC.SDK.instance.getComponent('teacher-component');
            }
            this.studentComs.unshift(ele);
          } else { // 学生
            if (info.userId === userId) { // 自己是学生
              // 如果有助教，在助教后面插入自己的视频
              if (this.studentComs.length >= 1 && classInfo.assistants.includes(this.studentComs[0].getAttribute('label'))) {
                afterDom = this.studentComs[0];
                this.studentComs.splice(1, 0, ele);
              } else {
                this.studentComs.unshift(ele);
                if (this.isLiveClass && this.videoLayout === this.VideoWrapLayout.Video) {
                  // 公开课纯视频布局，老师/助教/学生都在student-wrap中
                  afterDom = TCIC.SDK.instance.getComponent('teacher-component');
                }
              }
            } else { // 其他学生
              afterDom = wrap.lastChild;
              this.studentComs.push(ele);
            }
          }
          if (afterDom) {
            wrap.insertBefore(ele, afterDom.nextSibling);
          } else {
            if (wrap.firstChild) {
              wrap.insertBefore(ele, wrap.firstChild);
            } else {
              wrap.appendChild(ele);
            }
          }
          if (info.userId === userId) {
            if (TCIC.SDK.instance.isInteractClass()) {
              // eslint-disable-next-line max-len
              const loadBeforeClassStart = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status) === TCIC.TClassStatus.Not_Start;
              // 设备检测完成，且开始上课后才显示该提示
              TCIC.SDK.instance.promiseState(Constant.TStateDeviceDetect, false)
                .then(() => TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start))
                .then(() => {
                  const audioEnable = info.mic;
                  const videoEnable = info.camera;
                  const stageEnable = info.stage;
                  if (audioEnable && videoEnable) {
                    if (loadBeforeClassStart && TCIC.SDK.instance.getState(Constant.TStateLocalAVBeforeClassBegin)) {
                      const audioCapture = TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Capture);
                      const videoCapture = TCIC.SDK.instance.getState(TCIC.TMainState.Video_Capture);
                      if (audioCapture && videoCapture) {
                        window.showToast(i18next.t('你已被{{arg_0}}邀请上台，音视频将被打开~'), { arg_0: this.roleInfo.teacher });
                      } else if (audioCapture) {
                        window.showToast(i18next.t('你已被{{arg_0}}邀请上台，视频已关闭~'), { arg_0: this.roleInfo.teacher });
                      } else if (videoCapture) {
                        window.showToast(i18next.t('你已被{{arg_0}}邀请上台，麦克风已关闭~'), { arg_0: this.roleInfo.teacher });
                      } else {
                        window.showToast(i18next.t('你已被{{arg_0}}邀请上台，音视频已关闭~'), { arg_0: this.roleInfo.teacher });
                      }
                    } else {
                      window.showToast(i18next.t('你已被{{arg_0}}邀请上台，音视频将被打开~'), { arg_0: this.roleInfo.teacher });
                    }
                  } else if (audioEnable) {
                    window.showToast(i18next.t('你已上台，为了不干扰{{arg_0}}秩序，您的视频已被关闭，如有需要，请举手申请。'), { arg_0: this.roleInfo.name });
                  } else if (videoEnable) {
                    window.showToast(i18next.t('你已上台，为了不干扰{{arg_0}}秩序，您的麦克风已被关闭，如有需要，请举手申请。'), { arg_0: this.roleInfo.name });
                  } else if (stageEnable) {
                    window.showToast(i18next.t('你已上台，为了不干扰{{arg_0}}秩序，您的音视频已被关闭，如有需要，请举手申请。'), { arg_0: this.roleInfo.name });
                  }
                });
            }
          }
          if (this.isLiveClass && this.videoLayout === this.VideoWrapLayout.Video) {
            this.layoutLiveClassGridVideos();
          }
          this.$nextTick(() => {  // 更新悬浮工具栏位置
            if (this.positionMap.has(ele.id)) {   // 有位置记录则更新位置
              const boardRect = this.boardTarget.getBoundingClientRect();
              this.updateDragDomPosition(ele.id, this.positionMap.get(ele.id), boardRect);
            } else {
              // 当新加载这个人没有位置信息，就是不在台上的时候

              // 假1v1需求，学生端只显示自己，隐藏其他学生，当我在舞台上的时候不要隐藏
              if (TCIC.SDK.instance.isFake1v1()) {
                // 我已经在台上了，新进来的人我要和他们交流，不能隐藏
                const myDOM = document.querySelector(`[label="${TCIC.SDK.instance.getUserId()}"]`);
                const isMeOnStage = myDOM && myDOM.getAttribute('drag');
                if (this.isStudent && !isMeOnStage) {
                // 隐藏这个学生、静音这个学生
                  if (
                    TCIC.SDK.instance.isStudent(info.userId)
                    && info.userId !== TCIC.SDK.instance.getUserId()
                  ) {
                    TCIC.SDK.instance.reportLog('handleFake1v1WhenLoadStudentComponent', `
targetUserId: ${info.userId}
myUserId: ${TCIC.SDK.instance.getUserId()}
isMeOnStage: ${isMeOnStage}
`);

                    TCIC.SDK.instance.updateComponent(ele.tagName.toLowerCase(), {
                      display: 'none !important',
                    }, ele.getAttribute('label'), false);

                    TCIC.SDK.instance.notify(TCIC.TTrtcEvent.Audio_Mute, {
                      userId: info.userId,
                      mute: true,
                    });
                  }
                }
              }
            }
            if (this.isBoardReady && this.isEnableDrag) {
              this.enableDrag(ele, true, true);
            }
            this.updateComControlLayout(ele);
            this.$nextTick(() => {    // 避免误算悬浮工具栏高度
              this.updateScroll();
            });
          });
        });
    },

    // 移除学生视频
    removeUserComponent(info, reason) {
      if (!this.isShowVideoList && info.userId === TCIC.SDK.instance.getUserId()) {  // 视频处于隐藏状态则先显示
        this.toggleShow(true);
        this.$nextTick(() => {
          this.removeUserComponent(info, reason);
        });
        return;
      }
      this.videoWall.removeVideo(info.userId, TCIC.SDK.instance.getState(Constant.TStateVideoWallMode));
      const newStudents = [];
      this.studentComs.forEach((ele) => {
        if (!ele) {
          console.warn('removeUserComponent=>ignore undefind element');
        } else if (ele.getAttribute('label') === info.userId) {
          ele.setAttribute('delete', 'true');     // 标记为移除
          if ((this.isTeacher || this.isAssistant || this.isSupervisor) && ele.getAttribute('drag') === 'true') {  // 重置学生视频位置
            this.updateDragInfo(ele, false, null, 0);
            this.positionMap.delete(ele.id);
          }
        } else {
          newStudents.push(ele);
        }
      });
      this.studentComs = newStudents;
      TCIC.SDK.instance.reportLog('removeUserVideo', `[VideoWrapComponent] removeStudengComponent, userId ${info.userId}, reason ${reason}`);
      TCIC.SDK.instance.removeComponent('student-component', info.userId).then(() => {
        this.$nextTick(() => {
          this.updateScroll();
        });
      });
      if (info.userId === TCIC.SDK.instance.getUserId()
          && reason !== TCIC.TPermissionUpdateReason.KickOut  // 被踢时不提示
          && reason !== TCIC.TPermissionUpdateReason.KickOutForever
          && reason !== TCIC.TPermissionUpdateReason.LocalPreview
      ) {
        window.showToast(i18next.t('你已下台，暂时无法参与音视频互动~'));
      }
      if (this.isLiveClass && this.videoLayout === this.VideoWrapLayout.Video) {
        this.layoutLiveClassGridVideos();
      }
    },
    // 更新元素的拖动缩放参数
    updateDomDragZoom(dom) {
      if (this.dragMap.has(dom.id)) {
        const needZoom = this.isDoubleLine && dom.getAttribute('drag') !== 'true' && dom.getAttribute('label') !== 'default';
        this.dragMap.get(dom.id).updateZoom(needZoom ? 0.49 : 1);
      }
    },
    // 重置老师视频位置
    resetTeacherPosition(update = true) {
      if (!this.teacherCom) {
        console.warn('VideoWrapComponent::resetTeacherPosition=>enter with teacher component not ready');
        return;
      }
      let boardRect = this.boardTarget.getBoundingClientRect();
      // Fix 修复移动端概率未收到onTargetLayoutChange导致本地无有效白板尺寸引起异常
      if (boardRect.width < 1 || boardRect.height < 1) {    // 白板尺寸异常时，直接从组件获取
        const boardCom = TCIC.SDK.instance.getComponent('board-component');
        if (boardCom) {
          boardRect = boardCom.getBoundingClientRect();
        }
        if (boardRect.width < 1 || boardRect.height < 1) {    // 组件获取失败则延时重试
          this.retryCount += 1;
          if (this.retryCount < 3) {
            setTimeout(() => {
              this.resetTeacherPosition(update);
            }, 1000);
            return ;
          }
        }
      } else {
        this.retryCount = 0;
      }
      let width = 160; let height = 90;
      if (document.body.clientWidth < document.body.clientHeight) { // 竖屏
        width = Math.floor(document.body.clientWidth / 3);
        height = Math.floor(width * 9 / 16);
      }
      const margin = 16;
      const dstRect = {
        left: ((boardRect.width - width - margin) / boardRect.width).toFixed(2),
        top: (margin / boardRect.height).toFixed(2),
        width: (width / boardRect.width).toFixed(2),
        height: (height / boardRect.height).toFixed(2),
        zIndex: 200,
      };
      console.log(`VideoWrapComponent::resetTeacherPosition=>${this.teacherCom.id}, ${JSON.stringify(dstRect)}`);
      this.positionMap.set(this.teacherCom.id, dstRect);
      if (update) {
        this.updateDragDomPosition(this.teacherCom.id, dstRect, boardRect);
      }
    },
    // 加载学生组件
    doLoadUserComponent(info) {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      if (!classInfo || classInfo.teacherId === info.userId) { // 忽略老师事件
        return;
      }
      if (info.userId !== TCIC.SDK.instance.getUserId() && TCIC.SDK.instance.isLiveClass()) {
        const onStaging = TCIC.SDK.instance.getState(TCIC.TMainState.Stage_Status, false);
        if (onStaging) {
          window.showToast(i18next.t('{{arg_0}}上台', { arg_0: info.userName }));
        }
      }
      if (info.userId === TCIC.SDK.instance.getUserId() && TCIC.SDK.instance.getState(Constant.TStateLocalAVBeforeClassBegin)) {  // 自己的视频，开启课前展示时一开始就展示
        this.loadUserComponent(info);
      } else {  // 其他人的视频，开始上课后，才展示远端视频
        if (TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status) === TCIC.TClassStatus.Already_Start) {
          this.loadUserComponent(info);
        } else {
          this.delayLoadMap.set(info.userId, info);
        }
      }
    },
    // 移除学生组件
    doRemoveUserComponent(info, reason) {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      if (classInfo.teacherId === info.userId) { // 忽略老师事件
        return;
      }
      if (TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status) === TCIC.TClassStatus.Already_Start) {
        this.removeUserComponent(info, reason);
      } else {
        this.delayLoadMap.delete(info.userId);
      }
    },
    // 判断节点是否老师
    isTeacherDom(dom) {
      return dom.tagName.toLowerCase() === 'teacher-component';
    },
    isAssistantDom(dom) {   // 判断节点是否是助教
      const classInfo = TCIC.SDK.instance.getClassInfo();
      return dom.tagName.toLowerCase() === 'student-component' && classInfo?.assistants.includes(dom.getAttribute('label'));
    },
    getDomUserId(dom) {
      if (this.isTeacherDom(dom)) {
        const classInfo = TCIC.SDK.instance.getClassInfo();
        return classInfo.teacherId;
      }
      return dom.getAttribute('label');
    },
    addVideoToVideoWall(dom, update = true) {
      if (this.isTeacherDom(dom)) {
        this.videoWall.unshiftVideo(this.getDomUserId(dom), dom, update);
      } else if (this.isAssistantDom(dom)) {
        const classInfo = TCIC.SDK.instance.getClassInfo();
        if (this.teacherCom && this.videoWall.checkVideo(classInfo.teacherId)) { // 有老师则把老师重新置到头部
          this.videoWall.removeVideo(classInfo.teacherId, false);
          this.videoWall.unshiftVideo(this.getDomUserId(dom), dom, false);
          this.videoWall.unshiftVideo(classInfo.teacherId, this.teacherCom, update);
        } else {
          this.videoWall.unshiftVideo(this.getDomUserId(dom), dom, update);
        }
      } else {
        this.videoWall.pushVideo(this.getDomUserId(dom), dom, update);
      }
    },
    removeVideoFromVideoWall(dom, update = true) {    // 从视频墙中移除视频
      if (this.videoWall.removeVideo(this.getDomUserId(dom), update)) { // 视频墙视频清空后重置状态
        this.updateVideoWallState(false);
      }
    },
    /**
     * 处理假1v1场景的音视频逻辑
     * 1、学生被拖上画布，学生可以和老师以及其他所有学生视频、语音
     * 2、学生被拖上视频区，学生仅可和老师视频、语音，其他学生不可见不可听
     */
    handleFake1v1AV(item) {
      // 不是学生，不作处理
      if (!this.isStudent) return console.warn('handleFake1v1AV not student');

      const userDom = document.getElementById(item.domId);
      if (!userDom) return console.warn('handleFake1v1AV not userDom', item.domId);
      const userId = this.getDomUserId(userDom);
      if (!userId) return console.warn('handleFake1v1AV not userId', userDom);

      const myUserId = TCIC.SDK.instance.getUserId();
      const userOnStage = item?.info?.width;

      // 假1v1需求，学生拖到白板上在其他学生端显示，否则对其他学生隐藏
      if (myUserId !== userId && TCIC.SDK.instance.isStudent(userId)) {
        if (userOnStage) {
          // 老师拖动了其他学生到画布，我作为学生要能看到这个其他学生，并且建立音视频通话，自己永远都是显示的
          TCIC.SDK.instance.updateComponent('student-component', {
            display: 'inline-block',
          }, userId, false);

          // 建立音视频通话
          TCIC.SDK.instance.notify(TCIC.TTrtcEvent.Audio_Mute, {
            userId,
            mute: false,
          });

          TCIC.SDK.instance.reportLog('updateOtherInstageInfo', `
myUserId: ${myUserId}
userId: ${userId}
mute: false
userOnStage: ${userOnStage}
`);
        } else {
          // 老师把其他学生从画布拖回到视频区，我作为学生要看不到这个其他学生，并且取消音视频通话
          TCIC.SDK.instance.updateComponent('student-component', {
            display: 'none !important',
          }, userId, false);

          // 取消音视频通话
          TCIC.SDK.instance.notify(TCIC.TTrtcEvent.Audio_Mute, {
            userId,
            mute: true,
          });
        }

        TCIC.SDK.instance.reportLog('updateOtherInstageInfo', `
myUserId: ${myUserId}
userId: ${userId}
mute: true,
userOnStage: ${userOnStage}
`);
      }

      // 如果被拖动到舞台、到视频区的是自己
      if (myUserId === userId) {
        const teacherId = TCIC.SDK.instance.getClassInfo().teacherId;
        const assistantIds = TCIC.SDK.instance.getClassInfo().assistants;

        const exculdeUserIds = [teacherId, ...assistantIds, TCIC.SDK.instance.getUserId()];
        const allPermissonList = TCIC.SDK.instance.getPermissionList();

        const otherStudentIds = allPermissonList.filter(item => !exculdeUserIds.includes(item.userId)).map(item => item.userId);

        if (otherStudentIds.length) {
        // 被拖上了舞台，订阅所有其他学生的音视频
          if (userOnStage) {
            otherStudentIds.forEach((id) => {
              // 建立音视频通话
              TCIC.SDK.instance.notify(TCIC.TTrtcEvent.Audio_Mute, {
                userId: id,
                mute: false,
              });
            });

            TCIC.SDK.instance.reportLog('updateMyInstageInfo', `
myUserId: ${myUserId}
otherStudentIds: ${JSON.stringify(otherStudentIds)}
mute: false
userOnStage: ${userOnStage}
`);
          } else {
            // 被拖回了视频区，取消订阅其他所有学生的音视频
            otherStudentIds.forEach((id) => {
              // 取消音视频通话
              TCIC.SDK.instance.notify(TCIC.TTrtcEvent.Audio_Mute, {
                userId: id,
                mute: true,
              });
            });

            TCIC.SDK.instance.reportLog('updateMyInstageInfo', `
myUserId: ${myUserId}
otherStudentIds: ${JSON.stringify(otherStudentIds)}
mute: true
userOnStage: ${userOnStage}
`);
          }
        }
      }
    },
    // 处理位置同步任务
    processTaskUpdate(data) {
      // data.sessionId 是发起这个操作的人的 userId + 时间戳

      if (data.sessionId === this.sessionId) { // 老师则忽略自己本次产生的任务
        return;
      }

      if (TCIC.SDK.instance.isFake1v1()) {
        TCIC.SDK.instance.reportLog('processTaskUpdate fake1v1', `
myUserId: ${TCIC.SDK.instance.getUserId()}
data: ${JSON.stringify(data)}
`);
      }

      this.positionMap.clear();
      data.array.forEach((item) => {
        // 假1v1的场景，学生没拖拽到画布上时不与其他人进行音视频通信
        if (TCIC.SDK.instance.isFake1v1()) {
          this.handleFake1v1AV(item);
        }

        const isOnStage = item.info.width || item.info.height;

        if (isOnStage) {  // 仅记录可拖动的视频组件位置信息
          this.positionMap.set(item.domId, item.info);
        } else {
          this.positionMap.delete(item.domId);
        }

        this.updateDragDomPosition(item.domId, item.info, this.boardTarget.getBoundingClientRect(), false);
        this.updateVideoZIndex();
      });
    },
  },
};

</script>

<style lang="less">
@--color-primary: #006EFF;
@--color-public: #14181D;
@--color-disable: #b9bbbc;
@--color-back: #1C2131;

.video-wrap-component {
  width: 100%;
  height: 100%;
  pointer-events: none;

  .drag-mask {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 498;
    pointer-events: auto;
  }

  &:hover {
    .video-up, .layout-side {
      .el-button {
        background: @--color-back;
        transition: all .5s;

        i {
          opacity: 1;
        }
      }
    }
  }
  &:active {
    .video-up, .layout-side {
      .el-button {
        background: @--color-back;
        transition: all .5s;

        i {
          opacity: 1;
        }
      }
    }
  }

  &.touch-layout {
    .video-list {
      .video-pack {
        opacity: 1;
      }
    }
  }

  .video-up {
    &.small-double-line {   /** 双排小布局 */
      top: 110px;
      left: calc(50% - 20px);
      width: 40px;

      .el-button {
        height: 20px;
      }
    }
  }

  .video-list {
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: center;
    padding: 7px;
    background-color: var(--primary-color, #14181d);
    pointer-events: auto;
    touch-action: none;
    align-items: center;
    /** 公开课视频墙布局 */
    &.layout-video {
      padding: 0;
      .teacher-wrap {
        margin: 0;
        padding: 0;
      }
      #student-wrap {
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
        background-color: #14181d;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        align-content: center;
      }
    }

    &.hidden {
      position: relative;
      top: -5000px;
    }

    &.student-layout {
      .student-wrap {
        pointer-events: auto;   /** 必须为auto否则会影响Android端的滚动 */
        touch-action: auto;   /** 必须为auto否则会影响Android端的滚动 */
      }
    }

    .video-pack {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      padding: 0;
      border: 0;
      background: @--color-back;
      opacity: 0;
      z-index: 1;

      i {
        display: flex;
        width: 20px;
        height: 20px;
        background: url("./assets/icon_pack.svg") no-repeat;
      }

      &:focus {
        background: @--color-back;
      }

      &:hover {
        background-color: @--color-primary;
      }
      &:active {
        background-color: @--color-primary;
      }

      &.left, &.right {
        position: absolute;
        top: 0px;
        width: 24px;
        height: 78px;
        margin: 16px 0px;
      }

      &.left {
        left: 15px;
        border-radius: 4px 0 0 4px;
      }

      &.right {
        right: 75px;
        border-radius: 0 4px 4px 0;

        i {
          transform: rotate(180deg);
        }
      }
    }

    &.header-fix {
      .video-pack {
        &.left, &.right {
          top: 45px;
          height: 55px;
          margin: 0;
        }
      }
    }

    &:hover {
      background-image: radial-gradient(50% 152%, var(--primary-color-light, #242C47) 18%, var(--primary-color, #14181D) 100%);
      box-shadow: 0px -3px 2px 0px rgba(0, 0, 0, 0.1);

      .video-pack {
        opacity: 1;
        transition: all .6s;
      }
    }
    &:active {
      background-image: radial-gradient(50% 152%, var(--primary-color-light, #242C47) 18%, var(--primary-color, #14181D) 100%);
      box-shadow: 0px -3px 2px 0px rgba(0, 0, 0, 0.1);

      .video-pack {
        opacity: 1;
        transition: all .6s;
      }
    }

    .teacher-wrap {
      margin-left: 48px;
      &.set-minsize {
        min-width: 166px;
        min-height: 96px;
      }
      &.not-display {
        margin-left: 0px;
        padding: 0px;
      }
    }

    .student-wrap {
      margin-left: 2px;
      margin-right: 108px;
      white-space: nowrap;
      overflow-x: scroll;
      overflow-y: hidden;
      &.three-layout {
        margin-right: 0px;
        margin-left: 16px;
      }
      &::-webkit-scrollbar {
        display: none;
      }

      student-component {
        vertical-align: top;
        display: inline-block!important;
        margin: 2px;
        pointer-events: auto;
      }
    }

    .com-wrap {
      display: inline-block;
      padding-top: 3px;
    }

    /** 双排布局 */
    &.layout-double {
      .teacher-wrap {
        margin-left: 0px;
        min-width: 166px;
        min-height: 100px;
        background-image: url(./assets/video_empty.png);
        background-size: contain;
        background-repeat: no-repeat;
      }

      .student-wrap {
        display: flex;
        flex-wrap: wrap;
        flex-direction: column;
        margin-right: 0;
        zoom: 49%;
        width: 1318px;
        height: 100%;
        background-image: url(./assets/video_empty.png);
        background-repeat: repeat;
        background-size: 164.3px 95.8px;
        background-position: 2.6px 2.5px;
        align-content: start;

        student-component {
          margin: 2.5px 2px 2px 2px;
        }
      }
    }

    /** 侧边布局 */
    &.layout-right, &.layout-left {
      flex-direction: column;
      align-items: flex-end;

      &:hover,&:active {
        background-image: radial-gradient(152% 50%, var(--primary-color) 18%, var(--primary-color-light) 100%);
        box-shadow: 0px 2px -3px 0px rgba(0, 0, 0, 0.1);

        .video-pack {
          opacity: 1;
          transition: all .6s;
        }
      }


      .com-wrap {
        display: block;
      }

      .teacher-wrap {
        margin-left: 0;
        margin-top: 48px;
      }

      .student-wrap {
        position: relative;
        margin: 0 0 108px 0;
        width: 100%;
        overflow-x: hidden;
        overflow-y: scroll;

        student-component:not(.drag) {
          margin: 0 0 8px 0;
          display: block !important;
          margin-left: auto;
        }
      }

      .video-pack {
        &.top, &.bottom {
          position: absolute;
          left: 0px;
          width: 148px;
          height: 24px;
          margin: 0px 16px;
        }

        &.top {
          top: 12px;
          border-radius: 4px 4px 0 0;

          i {
            transform: rotate(90deg);
          }
        }

        &.bottom {
          bottom: 72px;
          border-radius: 0 0 4px 4px;

          i {
            transform: rotate(270deg);
          }
        }
      }
    }

    /** 屏幕分享布局 */
    &.layout-screen {
      flex-direction: column;
      background: transparent;
      padding: 5px;

      .com-wrap {
        display: block;
      }

      .teacher-wrap {
        margin-left: 0px;
        width: 166px;
        height: 94px;
      }

      .student-wrap {
        overflow-x: hidden;
        overflow-y: scroll;
        margin-right: auto;

        student-component {
          margin: 0 0 8px 0;
          display: block !important;
        }
      }
    }

    /** 大班布局 */
    &.mode-teacher:not(.layout-screen) {
      .video-component {
        border: none !important;
        border-radius: 0px !important;
        margin: 0px !important;
      }
    }

    &.mode-teacher:not(.layout-screen) {
      padding: 0px;

      .teacher-wrap {
        margin: 0px;
        padding: 0px;
        width: 100%;
        height: 100%;
      }

      .video-component {
        .video__content {
          border-radius: 0;
        }
      }
    }

    /** 1v1布局 */
    &.mode-two:not(.layout-screen) {

      .teacher-wrap {
        margin: 0px;
        padding: 0px;
        width: 100%;
        height: 50%;

        teacher-component {
          padding: 3px;
          width: 100% !important;
          height: 100% !important;
        }
      }

      .student-wrap {
        margin: 0px;
        padding: 0px;
        width: 100%;
        height: 50%;
        overflow: hidden;

        student-component {
          padding: 3px;
          width: 100% !important;
          height: 100% !important;
        }
      }

      .video-component {
        .video__content {
          border-radius: 0;
        }
      }
    }
  }

  .video-up {
    position: absolute;
    top: 35px;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    pointer-events: auto;

    .el-button {
      width: 40px;
      height: 40px;
      padding: 0;
      border-radius: 4px;
      border: none;
      background: rgba(0, 0, 0, 0.10);

      &:focus {   /** 覆盖全局样式 */
        background: rgba(0, 0, 0, 0.10);
      }

      &:hover {
        background-color: @--color-primary;
      }

      i {
        display: inline-block;
        width: 20px;
        height: 20px;
        background: url("./assets/icon_pack.svg") no-repeat center;
        transform: rotate(90deg);
        transition: all .5s;
        opacity: 0.4;
      }
    }

    &.video-down {
      margin-top: 0;
      top: 0px;
      transition: top 0.2s ease-in-out;

      &.header-fix {
        transition: top 0.2s ease-in-out;
        top: 45px;
      }

      .el-button {
        padding: 0;
        border-radius: 0 0 4px 4px;

        i {
          transform: rotate(270deg);
          transition: all .5s;
        }
      }
    }
  }

  .layout-side {
    position: absolute;
    bottom: 18px;
    display: flex;
    align-items: center;
    justify-content: left;
    width: 40px;
    pointer-events: auto;

    .el-button {
      width: 40px;
      height: 40px;
      padding: 0;
      border-radius: 0 4px 4px 0;
      border: none;
      background: rgba(0, 0, 0, 0.10);

      &:focus {   /** 覆盖全局样式 */
        background: rgba(0, 0, 0, 0.10);
      }

      &:hover {
        background-color: @--color-primary;
      }

      i {
        display: inline-block;
        width: 20px;
        height: 20px;
        background: url("./assets/icon_pack.svg") no-repeat center;
        transform: rotate(180deg);
        transition: all .5s;
        opacity: 0.4;
      }
    }

    &.video-left, &.video-right {
      margin-top: 0;
      width: 40px;
      right: 0px;
      bottom: 80px;

      .el-button {
        padding: 0;
        border-radius: 4px 0 0 4px;

        i {
          transform: rotate(0deg);
          transition: all .5s;
        }
      }
    }
    &.layout-left {
      right: 0px;
      bottom: 18px;
      .el-button i {
        transform: rotate(0deg);
      }
      &.video-right {
        left: 0px;
        bottom: 80px;
        .el-button i {
          transform: rotate(180deg);
        }
      }
    }
  }

  .video-up, .layout-side {
    .el-button {
      &:active {
        background: red;
        background: rgba(0, 0, 0, 0.10);
        animation-name: mobile-click;
        animation-duration: 1.5s;
      }

      @keyframes mobile-click {
        0% {
          background: @--color-primary;
        }
        100% {
          background: rgba(0, 0, 0, 0.10);
        }
      }
    }
  }
}

.video-wrap-component .video-list.layout-right .student-wrap student-component[drag='true'] {
  margin-left: 0px !important;
}

.video-wrap-component .video-list.layout-picinpic .student-wrap {
  margin-right: 0px;
}
.video-wrap-component .video-list.layout-videodoc .student-wrap {
  margin-right: 0px;
}
/** 小屏布局 */
&.small-screen {
    .no-stage-member {
      display: none;
    }
    .video-list {
      justify-content: flex-start;
    }
    .switch-orientation {
      background-image: url('./assets/orientation.png') ;
      background-size: 100%;
      background-position: 50%;
      width: 30px;
      height: 30px;
      right: 25px;
      background-color: rgba(28,33,49,.5);
      border-radius: 8px;
      position: fixed;
      top: calc(100% - 45px);
      z-index: 1000;
      &.is-portrait {
        top: 28%;
        left: 15px;
        right: 0;
        &.is-ios {
          top: calc(30% + env(safe-area-inset-top));
        }
        &.is-andriod {
          top: calc(30% + 40px)
        }
      }
    }
    .video-list.layout-right {
      width: calc(100% - 5px);
      background: #222329;
      margin-left: 15px;
      padding: 0;
      &.portrait-screen {
        width: 115px;
        // .teacher-wrap {
        //   height: 80px;
        // }
        .student-wrap {
          width: 100%;
          height: 100%;
          student-component:not(.drag) {
            margin: 0 0 5px 0;
        }
        }
      }
      &.is-one {
        display: none;
      }

      .teacher-wrap {
        margin-top: 0px;
        width: 100%;
      }
      .student-wrap {
        margin-bottom: 0px;
        // height: calc(100% - 80px);
        padding: 0;
        student-component:not(.drag) {
            margin: 0 0 8px 0;
        }
      }
    }
    .layout-side {
      top: 0px;
      bottom: 0;
      margin: 0;
      width: 15px;
      background: none;
      .el-button {
        width: 30px;
        height: 55px;
        background: #222329;
        border-radius: 2px;
      }
      .el-button i {
        background-image: url('./assets/icon_right.svg');
        transform: rotate(0deg);
        width: 15px;
      }
      &.portrait-screen {
        .el-button {
          height: 40px;
          width: 15px;
        }
      }
      &.video-left {
        top: 0px;
        bottom: 0;
        margin: 0;
        width: 15px;
        .el-button {
          position: absolute;
          width: 20px;
          i {
            transform: rotate(180deg);
            transition: all .5s;
            background-position: 70%;
          }

        }
      }

    }
  }

</style>
