.member-item__online {
  display: inline-block;
  min-width: 30px;
  background: #7ED321;
  color: black;
  text-align: center;
  border-radius: 2px;
  font-size: 12px;
  height: 16px;
  line-height: 16px;
}

.member-item__offline {
  display: inline-block;
  min-width: 30px;
  background: #999999;
  color: black;
  border: solid 1px #999999;
  text-align: center;
  border-radius: 4px;
  font-size: 12px;
  height: 16px;
  line-height: 16px;
}

.member-item__icon {
  display: inline-block;
  background-position: center;
  background-size: contain !important;
  cursor: pointer;
  width: 24px;
  height: 24px;
  vertical-align: middle;
  padding-top: 24px;
  opacity: .9;

  &:hover {
    opacity: 1;
  }

  &.cursor-default {
    cursor: default;
  }

  // 不显示的icon
  &.icon-hide {
    display: none;
  }

  // 不可用的icon
  &.icon-no {
    cursor: default;
    opacity: .5;

    &:hover {
      opacity: .5;
    }
  }

  // 异常状态的icon
  &.icon-error {
    cursor: not-allowed;
    opacity: .5;

    &:hover {
      opacity: .5;
    }
  }
  
  // loading
  &.icon-loading {
    background: url('../video-component/assets/icon_loading.gif');
  }
}

// handup
.member-item__icon-handup {
  display: inline-block;
  width: 24px;
  height: 24px;
  background: url('./assets/member-list__handup-on.svg') no-repeat center;
  background-size: contain;
  cursor: unset;

  .text {
    background: #13a449;
    text-align: center;
    position: absolute;
    padding: 2px 4px;
    transform: scale(0.5);
    border-radius: 4px;
    left: -10px;
    right: -10px;
    bottom: 4px;
  }
}

// stage
.member-item__icon-stage {
  &.icon-on {
    background: url('./assets/member-list__stage-on.svg') no-repeat center;
  }
  &.icon-off, &.icon-no {
    background: url('./assets/member-list__stage-off.svg') no-repeat center;
  }
}

// mic
.member-item__icon-mic {
  &.icon-on {
    background: url('./assets/member-list__mic-on.svg') no-repeat center;
  }
  &.icon-off, &.icon-no, &.icon-error {
    background: url('./assets/member-list__mic-off.svg') no-repeat center;
  }
}

// camera
.member-item__icon-camera {
  &.icon-on {
    background: url('./assets/member-list__camera-on.svg') no-repeat center;
  }
  &.icon-off, &.icon-no, &.icon-error {
    background: url('./assets/member-list__camera-off.svg') no-repeat center;
  }
}

// board
.member-item__icon-board {
  &.icon-on {
    background: url('./assets/member-list__board-on.svg') no-repeat center;
  }
  &.icon-off, &.icon-no {
    background: url('./assets/member-list__board-off.svg') no-repeat center;
  }
}

// screen
.member-item__icon-screen {
  &.icon-on {
    background: url('./assets/member-list__screen-on.svg') no-repeat center;
  }
  &.icon-off, &.icon-no {
    background: url('./assets/member-list__screen-off.svg') no-repeat center;
  }
}

// chat
.member-item__icon-chat {
  &.icon-on {
    background: url('./assets/member-list__chat-on.svg') no-repeat center;
  }
  &.icon-off, &.icon-no {
    background: url('./assets/member-list__chat-off.svg') no-repeat center;
  }
  &.icon-disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: url('./assets/member-list__chat-off.svg') no-repeat center;
  }
}

// trophy
.member-item__icon-trophy {
  background: url('./assets/member-list__trophy.svg') no-repeat center;
}

// kick
.member-item__icon-kick {
  background: url('./assets/member-list__kick-off.svg') no-repeat center;
}

// 浅色背景下的深色icon
.light-bg {
  // stage
  .member-item__icon-stage {
    &.icon-on {
      background: url('./assets/dark-icon/member-list__stage-on.svg') no-repeat center;
    }
    &.icon-off, &.icon-no {
      background: url('./assets/dark-icon/member-list__stage-off.svg') no-repeat center;
    }
  }

  // mic
  .member-item__icon-mic {
    &.icon-on {
      background: url('./assets/dark-icon/member-list__mic-on.svg') no-repeat center;
    }
    &.icon-off, &.icon-no, &.icon-error {
      background: url('./assets/dark-icon/member-list__mic-off.svg') no-repeat center;
    }
  }

  // camera
  .member-item__icon-camera {
    &.icon-on {
      background: url('./assets/dark-icon/member-list__camera-on.svg') no-repeat center;
    }
    &.icon-off, &.icon-no, &.icon-error {
      background: url('./assets/dark-icon/member-list__camera-off.svg') no-repeat center;
    }
  }

  // board
  .member-item__icon-board {
    &.icon-on {
      background: url('./assets/dark-icon/member-list__board-on.svg') no-repeat center;
    }
    &.icon-off, &.icon-no {
      background: url('./assets/dark-icon/member-list__board-off.svg') no-repeat center;
    }
  }

  // screen
  // .member-item__icon-screen {
  //   &.icon-on {
  //     max-width: 30px;
  //     min-width: 30px;
  //     background: url('./assets/dark-icon/member-list__screen-on.svg') no-repeat center;
  //   }
  //   &.icon-off, &.icon-no {
  //     background: url('./assets/dark-icon/member-list__screen-off.svg') no-repeat center;
  //   }
  // }

  // chat
  .member-item__icon-chat {
    &.icon-on {
      background: url('./assets/dark-icon/member-list__chat-on.svg') no-repeat center;
    }
    &.icon-off, &.icon-no {
      background: url('./assets/dark-icon/member-list__chat-off.svg') no-repeat center;
    }
  }

  // trophy
  // .member-item__icon-trophy {
  //   background: url('./assets/dark-icon/member-list__trophy.svg') no-repeat center;
  // }

  // kick
  .member-item__icon-kick {
    background: url('./assets/dark-icon/member-list__kick-off.svg') no-repeat center;
  }
}
