<template>
  <div
    class="float-member-list-component"
    :class="isPortrait ? 'portrait' : ''"
  >
    <section class="member-list__search">
      <el-input
        v-model.trim="searchKey"
        class="course-ware-input"
        :placeholder="$t('输入名字搜索')"
        prefix-icon="el-icon-search"
        maxlength="10"
        clearable
        @input="searchName"
      />
    </section>
    <section class="member-list__content">
      <ul
        v-infinite-scroll="loadMore"
        class="infinite-list"
        style="overflow: auto"
        :infinite-scroll-distance="0"
      >
        <li
          v-for="(item, index) in renderMemberList"
          :key="item.user_id"
          class="infinite-list-item"
          @click.stop="(event) => showModal(Object.assign(item, {index}), event)"
        >
          <div class="member-item">
            <div class="member-list__user-name-wrap">
              <span
                :class="[
                  'device-icon',
                  { offline: item.currentStatus !== TStatuesOnline },
                  getPlatformStringFromCode(item.device).icon,
                ]"
                :title="getPlatformStringFromCode(item.device).label"
              />
              <i
                :class="[
                  item.role === 'teacher'
                    ? 'teacher-icon'
                    : item.role === 'assistant'
                      ? 'assistant-icon'
                      : 'student-icon',
                  'role-icon',
                ]"
              >
                <span>
                  {{ roleInfo[item.role] }}
                </span>
              </i>
              <span
                :class="[
                  'member-list__user-name',
                  { offline: item.currentStatus !== TStatuesOnline },
                  { student: item.role === 'student' },
                ]"
              >
                <HighlightText :text="item.colorName" />
              </span>
            </div>
            <div class="member-list__member-status">
              <div class="icon-wrapper">
                <i
                  v-if="item.currentStatus === TStatuesOnline && isClassStarted && !isLive"
                  :class="[
                    getMemberIconClass(item, 'stage')
                  ]"
                />
              </div>
              <div class="icon-wrapper">
                <i
                  v-if="item.currentStatus === TStatuesOnline && item.stage && isClassStarted"
                  :class="[
                    getMemberIconClass(item, 'mic', loadingMicTimestamp && !!loadingMemberMap.get(item.userId + '-mic'))
                  ]"
                />
              </div>
              <div class="icon-wrapper">
                <i
                  v-if="item.currentStatus === TStatuesOnline && item.stage && isClassStarted"
                  :class="[
                    getMemberIconClass(item, 'camera', loadingCameraTimestamp && !!loadingMemberMap.get(item.userId + '-camera'))
                  ]"
                />
              </div>
              <div class="icon-wrapper">
                <i
                  v-if="item.currentStatus === TStatuesOnline"
                  :class="[
                    getMemberIconClass(item, 'chat')
                  ]"
                />
              </div>
            </div>
          </div>
        </li>
        <div
          v-if="isLoading"
          class="bottom-tip"
        >
          {{ $t('加载中...') }}
        </div>
        <p
          v-if="!hasMore"
          class="bottom-tip"
        >
          {{ $t("没有更多了") }}
        </p>
      </ul>
    </section>
    <section
      v-if="canMemberStageUp"
      class="member-list__footer"
    >
      <div
        v-if="canMemberStageUp"
        class="footer-btn"
        @click="muteAction"
      >
        <MuteAll
          ref="muteRef"
        />
        {{ isMuteAll ? $t('取消全体静音') : $t('全体静音') }}
      </div>
    </section>
    <section
      class="list-item-operate light-bg"
      :style="{
        display: modalData.visible ? 'flex' : 'none',
        top: isPortrait ? `${modalTop}px` : ''
      }"
    >
      <div class="list-item-title">
        <div
          class="list-title-name"
          @click="printMember(modalData)"
        >
          <i
            :class="[
              modalData.role === 'teacher'
                ? 'teacher-icon'
                : modalData.role === 'assistant'
                  ? 'assistant-icon'
                  : 'student-icon',
              'role-icon',
            ]"
          >
            <span>
              {{ roleInfo[modalData.role] }}
            </span>
          </i>
          <span
            v-dompurify-html="modalData.colorName"
            :class="[
              'member-list__user-name',
              { offline: modalData.currentStatus !== TStatuesOnline },
              { student: modalData.role === 'student' },
            ]"
          />
        </div>
        <div
          class="list-title-close"
          @click.stop="hideModal"
        >
          <i class="el-icon-close icon" />
        </div>
      </div>
      <!-- 上台 -->
      <div
        v-if="!isLive && isClassStarted && modalData.currentStatus === TStatuesOnline"
        class="list-item-action"
        @click.stop="onMemberAction(modalData, modalData.stage ? TTypeStageDown : TTypeStageUp)
        "
      >
        <i
          :class="[
            getMemberIconClass(modalData, 'stage'),
          ]"
        />
        {{
          modalData.stage
            ? $t("下台")
            : $t("上台")
        }}
      </div>
      <!-- 麦克风 -->
      <div
        v-if="isClassStarted && modalData.currentStatus === TStatuesOnline && modalData.stage"
        class="list-item-action"
        @click.stop="
          onMemberAction(
            modalData,
            isDeviceOpen(modalData, 'mic')
              ? TTypeMicClose
              : TTypeMicOpen
          )
        "
      >
        <i
          :class="[
            getMemberIconClass(modalData, 'mic', loadingMicTimestamp && !!loadingMemberMap.get(modalData.userId + '-mic')),
          ]"
        />
        {{
          isDeviceOpen(modalData, 'mic')
            ? $t("静音")
            : (getDeviceShowState(modalData, 'mic') === 'error' ? $t('设备不可用（损坏、未授权等）') : $t("解除静音"))
        }}
      </div>
      <!-- 摄像头 -->
      <div
        v-if="isClassStarted && modalData.currentStatus === TStatuesOnline && modalData.stage"
        class="list-item-action"
        @click.stop="
          onMemberAction(
            modalData,
            isDeviceOpen(modalData, 'camera')
              ? TTypeCameraClose
              : TTypeCameraOpen
          )
        "
      >
        <i
          :class="[
            getMemberIconClass(modalData, 'camera', loadingCameraTimestamp && !!loadingMemberMap.get(modalData.userId + '-camera')),
          ]"
        />
        {{
          isDeviceOpen(modalData, 'camera')
            ? $t("停止视频")
            : (getDeviceShowState(modalData, 'camera') === 'error' ? $t('设备不可用（损坏、未授权等）') : $t("开启视频"))
        }}
      </div>
      <!-- 白板 -->
      <div
        v-if="!isVideoOnlyClass && isClassStarted && modalData.currentStatus === TStatuesOnline"
        class="list-item-action"
        @click.stop="onMemberAction(modalData, modalData.board ? TTypeBoardDisable : TTypeBoardEnable)"
      >
        <i
          :class="[
            getMemberIconClass(modalData, 'board'),
          ]"
        />{{
          modalData.board
            ? $t("禁用白板")
            : $t("开启白板")
        }}
      </div>
      <!-- 聊天 -->
      <div
        v-if="modalData.currentStatus === TStatuesOnline"
        class="list-item-action"
        @click.stop="
          onMemberAction(
            modalData,
            modalData.silence ? TTypeSilenceCancel : TTypeSilence
          )
        "
      >
        <i
          :class="[
            getMemberIconClass(modalData, 'chat'),
          ]"
        />{{
          modalData.silence
            ? $t("允许聊天")
            : $t("禁止发言")
        }}
      </div>
      <div
        v-if="modalData.currentStatus === TStatuesOnline && modalData.role !== 'teacher'"
        class="list-item-action"
        @click.stop="onKick(modalData)"
      >
        <i
          :class="[
            getMemberIconClass(modalData, 'kick'),
          ]"
        />
        {{ translateTip.kickout }}
      </div>
    </section>
  </div>
</template>

<script>
import i18next from 'i18next';
import BaseComponent from '@/component/core/BaseComponent';
import Constant from '@/util/Constant';
import DeviceUtil from '@/util/DeviceUtil';
import MuteAll from './sub-component/MuteAll';
import HighlightText from '@/component/ui-component/highlight-text-component/HighlightText';
import Lodash from 'lodash';
import MemberIcon from './MemberIcon';
import './MemberIcon.less';

export default {
  components: {
    MuteAll,
    HighlightText,
  },
  extends: BaseComponent,
  mixins: [MemberIcon],
  data() {
    return {
      // TCIC常量重定义，保证在HTML可以直接绑定
      TTypeCameraOpen: TCIC.TMemberActionType.Camera_Open,
      TTypeCameraClose: TCIC.TMemberActionType.Camera_Close,
      TTypeMicOpen: TCIC.TMemberActionType.Mic_Open,
      TTypeMicClose: TCIC.TMemberActionType.Mic_Close,
      TTypeHandUp: TCIC.TMemberActionType.Hand_Up,
      TTypeHandUpCancel: TCIC.TMemberActionType.Hand_Up_Cancel,
      TTypeKickOut: TCIC.TMemberActionType.Kick_Out,
      TTypeBoardEnable: TCIC.TMemberActionType.Board_Enable,
      TTypeBoardDisable: TCIC.TMemberActionType.Board_Disable,
      TTypeSilence: TCIC.TMemberActionType.Silence,
      TTypeSilenceCancel: TCIC.TMemberActionType.Silence_Cancel,
      TTypeStageUp: TCIC.TMemberActionType.Stage_Up,
      TTypeStageDown: TCIC.TMemberActionType.Stage_Down,
      TTypeKickOutForever: TCIC.TMemberActionType.Kick_Out_Forever,
      TTypeScreenShareOpen: TCIC.TMemberActionType.Screen_Share_Open,
      TTypeScreenShareClose: TCIC.TMemberActionType.Screen_Share_Close,
      TStatuesOnline: TCIC.TMemberStatus.Online,
      TDeviceStatusUnknown: TCIC.TDeviceStatus.Unknown,
      TDeviceStatusOpen: TCIC.TDeviceStatus.Open,
      // 其他变量定义
      arrowOffset: 0,
      memberList: [],
      isLoading: false,
      hasMore: true,
      intervalList: [10, 20, 30, 40, 50, 60, 70, 80, 90],
      pageIndex: 1,
      pageCount: 1,
      modalTop: 45,
      // 是否是直播课
      isLive: false,
      isCDNClass: false, // 是否CDN课
      isOneOnOneClass: false, // 是否是1v1课
      isVideoOnlyClass: false, // 是否纯视频课
      canMemberStageUp: true, // 成员是否能上台
      // 是否已经开始上课
      isClassStarted: false,
      // 是否可见
      isVisible: false,
      // 奖杯TaskId
      trophyTaskId: 'trophy',
      trophyList: [],
      isSendingTrophy: false,
      totalMemberCount: 0, // 成员总人数
      offlineMemberCount: 0, // 离线人数
      showAllMember: true, // 是否展示所有成员
      searchKey: '', // 搜索关键字
      pageSize: 10, // 每一页显示人数
      popoverSTO: null,
      carousel: false,
      carouselTips: i18next.t('循环上台'),
      carouselTimer: null, // 文案定时器
      interval: 30,
      isMobile: false, // 是否移动端
      isElectron: false, // 是否桌面端
      isPad: false, // 是否pad
      isPortrait: false, // 是否为竖屏
      roleInfo: {},
      roomInfo: {},
      loading: true,
      totalMembers: [],
      modalData: {},
      isMuteAll: false,
      refreshMember: false,
      userId: '',
      loadingMemberMap: new Map(), // `${userId}-camera` -> state, `${userId}-mic` -> state
      // loadingMemberMap 内容的更新不会触发刷新UI，增加参数用来触发UI刷新
      loadingCameraTimestamp: 0,
      loadingMicTimestamp: 0,
    };
  },

  computed: {
    translateTip() {
      return {
        kickout: i18next.t('请出{{arg_0}}', { arg_0: this.roomInfo.name }),
      };
    },
    renderMemberList() {
      const { memberList, trophyList } = this;
      const classInfo = TCIC.SDK.instance.getClassInfo();
      const everyoneTrophy = trophyList.find(item => item.userId === 'everyone');
      return memberList.map((member) => {
        const renderMember = { ...member };
        const trophy = trophyList.find(item => item.userId === member.userId);
        renderMember.trophy = (trophy ? trophy.count : 0) + (everyoneTrophy ? everyoneTrophy.count : 0);
        if (this.searchKey.length > 0) {
          renderMember.colorName = `${member.userName.replace(this.searchKey, `<span class="em">${this.searchKey}</span>`)}`;
        } else {
          renderMember.colorName = `${member.userName}`;
        }
        switch (true) {
          case classInfo.assistants.includes(member.userId):
            renderMember.role = 'assistant';
            renderMember.showTrophyIcon = false;
            break;
          case member.userId === classInfo.teacherId:
            renderMember.role = 'teacher';
            renderMember.showTrophyIcon = false;
            break;
          case member.role === 3:
            renderMember.role = 'supervisor';
            renderMember.showTrophyIcon = false;
            break;
          default:
            renderMember.role = 'student';
            renderMember.showTrophyIcon = true;
            break;
        }
        return renderMember;
      }).filter((item) => {
        return item.role !== 'supervisor';
      }).sort((a, b) => {
        if (a.role === 'teacher') {
          return -1;
        }
        // 在线的在前面
        if (a.currentStatus === TCIC.TMemberStatus.Online && b.currentStatus !== TCIC.TMemberStatus.Online) {
          return -1;
        }
      });
    },
  },
  watch: {
    renderMemberList(val) {
      // modalData 里的也要更新
      if (this.modalData.userId) {
        const modalUser = val?.find(item => item.userId === this.modalData.userId);
        if (modalUser) {
          this.modalData = {
            ...modalUser,
            visible: modalUser.currentStatus === TCIC.TMemberStatus.Online,
          };
        } else {
          this.hideModal();
        }
      }
    },
  },
  async mounted() {
    if (this.isTCICComponent) {
      // 允许拖动
      this.toggleComponentDrag(true, '.member-list-component-header');
    }
    const { roleInfo, roomInfo } = TCIC.SDK.instance.getRoleInfo();
    const DeviceOrientation = TCIC.TMainState.Device_Orientation;
    const Portrait = TCIC.TDeviceOrientation.Portrait;

    this.roleInfo = roleInfo;
    this.roomInfo = roomInfo;
    this.isPortrait = TCIC.SDK.instance.getState(DeviceOrientation, Portrait) === Portrait;
    this.isMobile = TCIC.SDK.instance.isMobile();
    this.isElectron = TCIC.SDK.instance.isElectron();
    this.isPad = TCIC.SDK.instance.isPad();
    this.userId = TCIC.SDK.instance.getUserId();
    this.hasMemberListPermission = TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isAssistant() || TCIC.SDK.instance.isSupervisor();
    this.addLifecycleTCICStateListener(
      TCIC.TMainState.Class_Status,
      (status) => {
        this.isClassStarted = status !== TCIC.TClassStatus.Not_Start;
      },
    );
    this.handleMemberUpdate();
    this.addLifecycleTCICStateListener(
      TCIC.TMainState.Member_List_Page_Count,
      (pageCount) => {
        this.pageCount = pageCount;
      },
      { noEmitWhileSubscribe: true },
    );
    this.addLifecycleTCICStateListener(Constant.TStateHeaderMemberActive, (isVisible) => {
      this.isVisible = isVisible;
      if (isVisible) {    // 重新显示后重置内容
        this.pageIndex = 1;
        this.showAllMember = true;
        this.searchKey = '';
        this.refreshMember = true;
        this.getMemberList();
      }
    });
    this.addLifecycleTCICStateListener(
      TCIC.TMainState.Member_List_Total_Member_Count,
      (count) => {
        this.totalMemberCount = count;
      },
    );
    this.addLifecycleTCICStateListener(
      TCIC.TMainState.Member_List_Offline_Member_Count,
      (count) => {
        this.offlineMemberCount = count;
      },
    );
    // 监听奖杯发放事件
    this.addLifecycleTCICEventListener(
      TCIC.TMainEvent.Task_Updated,
      this.onTrophyTaskUpdate,
    );
    this.makeSureClassJoined(this.onJoinClass);
    // 初始化循环上台状态
    this.carousel = TCIC.SDK.instance.getState(Constant.TStateCarousel, false);
    this.addLifecycleTCICStateListener(Constant.TStateCarousel, (flag) => {
      // 同步循环同步状态
      this.carousel = flag;
    });
    this.interval = TCIC.SDK.instance.getState(
      Constant.TStateCarouselInterval,
      30,
    );
    this.addLifecycleTCICStateListener(
      Constant.TStateCarouselInterval,
      (interval) => {
        // 同步循环同步状态
        this.interval = interval;
      },
    );
    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.isPortrait = orientation === TCIC.TDeviceOrientation.Portrait;
    });
    this.addLifecycleTCICEventListener(Constant.TStateLoadingCameraState, ({ value, userId, type }) => {
      if (this.hasMemberListPermission) { // 是否有花名册权限
        const index = this.memberList.findIndex(item => item.userId === userId);
        if (index === -1) {
          console.log(`[FloadMemberList] TStateLoadingCameraState but user not found ${userId}`);
          return;
        }
        const member = this.memberList[index];
        console.log(`[FloadMemberList] TStateLoadingCameraState ${userId} ${type} ${JSON.stringify(value)}`);
        this.loadingMemberMap.set(`${userId}-camera`, value?.loading ? value : null);
        this.loadingCameraTimestamp = Date.now();
        if (typeof value.open === 'boolean') {
          let newCameraState = value.open ? TCIC.TDeviceStatus.Open : TCIC.TDeviceStatus.Closed;
          if (!value.open) {
            if (typeof value.deviceState === 'number') {
              // 学生返回的设备状态
              newCameraState = value.deviceState;
            } else if (userId === this.userId) {
              // 自己的设备状态
              newCameraState = TCIC.SDK.instance.getState(TCIC.TMainState.Video_Device_Status);
            }
          }
          console.log(`[FloadMemberList] TStateLoadingCameraState, ${userId}, isSelf ${userId === this.userId}, cameraState ${member.cameraState} -> ${newCameraState}`);
          if (member.cameraState !== newCameraState) {
            member.cameraState = newCameraState;
          }
          // 直接修改数组项不会触发刷新，改用 splice
          // this.memberList[index] = member;
          this.memberList.splice(index, 1, member);

          // modalData 里的也要更新
          if (this.modalData.userId === userId) {
            this.modalData = {
              ...this.modalData,
              cameraState: member.cameraState,
            };
          }
        }
      }
    });
    this.addLifecycleTCICEventListener(Constant.TStateLoadingMicState, ({ value, userId, type }) => {
      if (this.hasMemberListPermission) { // 是否有花名册权限
        const index = this.memberList.findIndex(item => item.userId === userId);
        if (index === -1) {
          console.log(`[FloadMemberList] TStateLoadingMicState but user not found ${userId}`);
          return;
        }
        const member = this.memberList[index];
        console.log(`[FloadMemberList] TStateLoadingMicState ${userId} ${type} ${JSON.stringify(value)}`);
        this.loadingMemberMap.set(`${userId}-mic`, value?.loading ? value : null);
        this.loadingMicTimestamp = Date.now();
        if (typeof value.open === 'boolean') {
          let newMicState = value.open ? TCIC.TDeviceStatus.Open : TCIC.TDeviceStatus.Closed;
          if (!value.open) {
            if (typeof value.deviceState === 'number') {
              // 学生返回的设备状态
              newMicState = value.deviceState;
            } else if (userId === this.userId) {
              // 自己的设备状态
              newMicState = TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Device_Status);
            }
          }
          console.log(`[FloadMemberList] TStateLoadingMicState, ${userId}, isSelf ${userId === this.userId}, micState ${member.micState} -> ${newMicState}`);
          if (member.micState !== newMicState) {
            member.micState = newMicState;
          }
          // 直接修改数组项不会触发刷新，改用 splice
          // this.memberList[index] = member;
          this.memberList.splice(index, 1, member);

          // modalData 里的也要更新
          if (this.modalData.userId === userId) {
            this.modalData = {
              ...this.modalData,
              micState: member.micState,
            };
          }
        }
      }
    });
  },
  beforeDestroy() {
    this.isVisible = false;
  },
  methods: {
    handleMemberUpdate() {
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Member_Join, (userId) => {
        this.updateUser(userId);
      });
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Member_Exit, (userId) => {
          const index = this.memberList.findIndex(item => item.userId === userId);
          if (index === -1) {
            // 没在列表中，不处理
          } else {
            this.memberList[index].currentStatus = TCIC.TMemberStatus.Offline;
          }
      });
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Member_Stage_Up, ({ userId }) => {
          const index = this.memberList.findIndex(item => item.userId === userId);
          if (index === -1) {
            // 没在列表中，不处理
          } else {
            this.memberList[index].stage = true;
          }
      });
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Member_Stage_Down, ({ userId }) => {
          const index = this.memberList.findIndex(item => item.userId === userId);
          if (index === -1) {
            // 没在列表中，不处理
          } else {
            this.memberList[index].stage = false;
          }
      });
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Member_Info_Update, (memberInfo) => {
        const index = this.memberList.findIndex(item => item.userId === memberInfo.userId);
          if (index === -1) {
            // 没在列表中，不处理
          } else {
            this.memberList.splice(index, 1, { ...this.memberList[index], ...memberInfo });
          }
        console.log(`[MemberList] TMainEvent.Member_Info_Update ${memberInfo.userId}`, memberInfo);
      });
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Permission_Update, (permissionInfo) => {
        console.log(`[MemberList] TMainEvent.Permission_Update ${permissionInfo.userId}`, permissionInfo);
        for (const permissionItem of permissionInfo) {
          const memberIndex = this.memberList.findIndex(item => item.userId === permissionItem.userId);
          if (memberIndex !== -1) {
            this.memberList.splice(memberIndex, 1, {
              ...this.memberList[memberIndex],
              ...permissionItem,
            });
          }
        }
      });
    },
    updateUser(userId) {
      this.getMember(userId).then((user) => {
        console.log(`[MemberList] TMainEvent.Member_Join ${userId}`, user);
        const index = this.memberList.findIndex(item => item.userId === userId);
        if (index === -1) {
          this.memberList.push(user);
        } else {
          this.memberList.splice(index, 1, user);
        }
      });
    },
    getMember(userId) {
      return TCIC.SDK.instance.getUserProfile(userId, false).then((res) => {
        console.log('[memberList] getMember', res);
        const memberInfo = new TCIC.TMemberInfo();
        memberInfo.deserialize(res);
        return memberInfo;
      });
    },
    muteAction() {
      this.isMuteAll = !this.isMuteAll;
      this.$refs?.muteRef?.switchTrigger && this.$refs?.muteRef?.switchTrigger(this.isMuteAll);
    },
    showModal(data, event) {
      this.modalData = Object.assign({}, data, { visible: data.currentStatus === TCIC.TMemberStatus.Online });
      if (event) {
        const clientTop = event.target.getBoundingClientRect().top;
        const maxTop = window.innerHeight - (45 + 90 + 275);
        this.modalTop = Math.min(clientTop, maxTop);
      }
    },
    hideModal() {
      this.modalData = Object.assign({}, { visible: false });
    },
    printMember(user) {
      // uglifyjs生产环境会去掉console，这里加上 window 强制保留
      window.console.log('[MemberList] printMember', user);
    },
    onJoinClass() {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      this.isLive = TCIC.SDK.instance.isLiveClass();
      this.isOneOnOneClass = TCIC.SDK.instance.isOneOnOneClass();
      this.isVideoOnlyClass = TCIC.SDK.instance.isVideoOnlyClass();
      this.isCDNClass = TCIC.SDK.instance.isUnitedLiveClass();
      this.canMemberStageUp = TCIC.SDK.instance.getClassInfo().maxRtcMember !== 0;
      // 初始化奖杯数据
      this.initTrophyCount();
    },
    onDisableClick: Lodash.throttle(
      function () {
        if (this.carousel) {
          window.showToast(i18next.t('请先结束当前循环上台，再修改设置！'));
        }
      },
      500,
      {
        leading: true,
        trailing: false,
      },
    ),
    onCarouselChange(flag, event) {
      // 循环上台状态变更
      const carouselCom = TCIC.SDK.instance.getComponent('carousel-component');
      const classInfo = TCIC.SDK.instance.getClassInfo();
      if (classInfo && classInfo.maxRtcMember < 1) {
        this.carousel = false;
        event.target.checked = false;
        window.showToast(i18next.t('当前{{arg_0}}不允许{{arg_1}}上台', {
          arg_0: this.roomInfo.name,
          arg_1: this.roleInfo.student,
        }));
        return;
      }
      if (carouselCom && carouselCom.getVueInstance()) {
        if (flag) TCIC.SDK.instance.setState(
          Constant.TStateCarouselInterval,
          this.interval,
        );
        carouselCom
          .getVueInstance()
          .updateInterval(flag ? this.interval * 1000 : 0)
          .catch((err) => {
            this.carousel = false;
            event.target.checked = false;
            if (err.errorCode === 10632) {
              window.showToast(i18next.t('当前人数较少，无法开启循环上台功能'));
            } else {
              window.showToast(i18next.t('开启循环上台功能失败'));
            }
          });
        // if (!carouselCom.getVueInstance().updateInterval(flag ? this.interval * 1000 : 0)) {
        //   this.carousel = false;
        //   event.target.checked = false;
        //   window.showToast(i18next.t('当前人数较少，无法开启循环上台功能'));
        //   return;
        // };
      } else {
        this.carousel = false;
        event.target.checked = false;
        window.showToast(i18next.t('开启循环上台功能失败'));
        return;
      }
      this.carouselTips = flag ? i18next.t('已开启') : i18next.t('已关闭');
      if (this.carouselTimer) {
        clearTimeout(this.carouselTimer);
      }
      this.carouselTimer = setTimeout(() => {
        this.carouselTips = i18next.t('循环上台');
        this.carouselTimer = null;
      }, 3000);
    },
    onKick(user) {
      if (user.currentStatus !== TCIC.TMemberStatus.Online) {
        window.showToast(i18next.t('该操作只对在线{{arg_0}}有效', { arg_0: this.roleInfo.student }));
      } else {
        TCIC.SDK.instance.showMessageBox(
          '',
          i18next.t('是否确认将{{arg_0}}移出{{arg_1}}？', {
            arg_0: user.userName,
            arg_1: this.roomInfo.name,
          }),
          [i18next.t('确定'), i18next.t('取消')],
          (index, options) => {
            if (index === 0) {
              if (options[0]) {
                this.onMemberAction(
                  user,
                  TCIC.TMemberActionType.Kick_Out_Forever,
                );
              } else {
                this.onMemberAction(user, TCIC.TMemberActionType.Kick_Out);
              }
            }
          },
          [
            {
              text: i18next.t('不允许该{{arg_0}}再次加入该{{arg_1}}', {
                arg_0: this.roleInfo?.student,
                arg_1: this.roomInfo?.name,
              }),
              value: false,
            },
          ],
        );
      }
    },
    async onMemberAction(user, actionType) {
      console.log(`[FloatMemberList] onMemberAction ${user.userId} ${actionType}`);
      const classInfo = TCIC.SDK.instance.getClassInfo();
      if (user.currentStatus !== TCIC.TMemberStatus.Online) {
        // 不允许操作离线学员
        window.showToast(i18next.t('该操作只对在线{{arg_0}}有效', {
          arg_0: this.roleInfo.student,
        }));
        return;
      }
      if (actionType === TCIC.TMemberActionType.Mic_Open) {
        if (this.getDeviceShowState(user, 'mic') === 'error') {
          // 设备异常，不能开启
          console.log(`[MemberList] can not open mic when device error, ${user.role} ${user.userId} ${user.micState}`);
          return;
        }
      }
      if (actionType === TCIC.TMemberActionType.Camera_Open) {
        if (this.getDeviceShowState(user, 'camera') === 'error') {
          // 设备异常，不能开启
          console.log(`[MemberList] can not open camera when device error, ${user.role} ${user.userId} ${user.cameraState}`);
          return;
        }
      }

      const stageActions = [
        // 上台相关操作
        TCIC.TMemberActionType.Stage_Up,
        TCIC.TMemberActionType.Stage_Down,
        TCIC.TMemberActionType.Camera_Open,
        TCIC.TMemberActionType.Camera_Close,
        TCIC.TMemberActionType.Mic_Open,
        TCIC.TMemberActionType.Mic_Close,
        TCIC.TMemberActionType.Screen_Share_Open,
        TCIC.TMemberActionType.Screen_Share_Close,
      ];
      if (stageActions.includes(actionType)) {
        // 如果是上台相关功能
        if (this.isLive) {
          // 直播课不允许上台
          window.showToast(i18next.t('直播课不支持{{arg_0}}上台', {
            arg_0: this.roleInfo.student,
          }));
          return;
        }
        if (classInfo.maxRtcMember === 0) {
          // 上台人数已满后不允许上台
          if (user.role !== 'teacher') {
            window.showToast(i18next.t('当前{{arg_0}}设定上台人数为0，成员无法上台', {
              arg_0: this.roomInfo.name,
            }));
            return;
          }
        }
        if (TCIC.SDK.instance.getState(Constant.TStateCarousel)) {
          // 循环上台时禁用上下台
          if (
            actionType === TCIC.TMemberActionType.Stage_Up
						|| actionType === TCIC.TMemberActionType.Stage_Down
          ) {
            window.showToast(i18next.t('请先结束循环上台'));
            return;
          }
        }
      }

      const boardActions = [
        // 涂鸦相关操作
        TCIC.TMemberActionType.Board_Enable,
        TCIC.TMemberActionType.Board_Disable,
      ];
      if (!this.isClassStarted) {
        // 未开始上课，禁用上台及涂鸦相关操作
        if (
          stageActions.includes(actionType)
					|| boardActions.includes(actionType)
        ) {
          console.log(`[FloatMemberList] onMemberAction ${user.userId} ${actionType}, class not started, return`);
          // window.showToast('上课前禁止该操作');
          return;
        }
      }
      // 先邀请上台在授予屏幕共享权限
      if (
        !user.stage
				&& actionType === TCIC.TMemberActionType.Screen_Share_Open
      ) {
        window.showToast(i18next.t('用户未上台无法使用屏幕共享功能'));
        return;
      }
      // 小程序不允许授权涂鸦
      if (
        user.device === TCIC.TDevice.Miniprogram
				&& actionType === TCIC.TMemberActionType.Board_Enable
      ) {
        window.showToast(i18next.t('小程序用户无法使用涂鸦功能'));
        return;
      }
      // 小程序不允许使用屏幕共享功能
      if (
        user.device === TCIC.TDevice.Miniprogram
				&& actionType === TCIC.TMemberActionType.Screen_Share_Open
      ) {
        window.showToast(i18next.t('小程序用户无法使用屏幕共享功能'));
        return;
      }
      // 移动端不允许使用屏幕共享功能
      if (
        user.device === TCIC.TDevice.Phone
				&& actionType === TCIC.TMemberActionType.Screen_Share_Open
      ) {
        window.showToast(i18next.t('移动端用户无法使用屏幕共享功能'));
        return;
      }

      let deviceType;
      let flag;
      switch (actionType) {
        case TCIC.TMemberActionType.Camera_Open:
        case TCIC.TMemberActionType.Camera_Close:
          {
            const loadingState = this.loadingMemberMap.get(`${user.userId}-camera`);
            if (!!loadingState?.loading) {
              console.log(`[FloatMemberList] onMemberAction ${user.userId} ${actionType} when ${loadingState.loading}, return`);
              // 不用显示toast，以免误点toast导致花名册关闭
              // window.showToast(user.userId === this.userId ? i18next.t('稍等片刻，请勿重复操作'): i18next.t('正在授权，请稍等'));
              return;
            }
            deviceType = 'camera';
            flag = actionType === TCIC.TMemberActionType.Camera_Open;
          }
          break;
        case TCIC.TMemberActionType.Mic_Open:
        case TCIC.TMemberActionType.Mic_Close:
          {
            const loadingState = this.loadingMemberMap.get(`${user.userId}-mic`);
            if (!!loadingState?.loading) {
              console.log(`[FloatMemberList] onMemberAction ${user.userId} ${actionType} when ${loadingState.loading}, return`);
              // 不用显示toast，以免误点toast导致花名册关闭
              // window.showToast(user.userId === this.userId ? i18next.t('稍等片刻，请勿重复操作'): i18next.t('正在授权，请稍等'));
              return;
            }
            deviceType = 'mic';
            flag = actionType === TCIC.TMemberActionType.Mic_Open;
          }
          break;
      }

      const promise = deviceType
        ? DeviceUtil.toggleRemoteDeviceWithLoadingEvent(
          user.userId,
          deviceType,
          flag,
          () => TCIC.SDK.instance.memberAction({
            userId: user.userId,
            actionType,
          }),
          {
            caller: 'FloatMemberList',
            reason: `memberAction-${actionType}`,
          },
        )
        : TCIC.SDK.instance.memberAction({
          userId: user.userId,
          actionType,
        });

      try {
        await promise;
        this.pageIndex = (Math.floor(user.index / this.pageSize) + 1);
        this.refreshMember = true;
        // this.getMemberList();
        this.updatePopper();
      } catch (err) {
        window.showToast(err.errorMsg, 'error');
      }
      this.hideModal();
    },
    loadMore() {
      if (this.isLoading || !this.hasMore) {
        return;
      }
      this.pageIndex += 1;
      this.getMemberList();
    },
    getMemberList() {
      this.isLoading = true;
      let filterType = TCIC.TMemberType.All;
      if (!this.showAllMember && this.searchKey.length === 0) { // 不搜索且选择离线tab才会查询离线用户
        filterType = TCIC.TMemberType.Offline;
      }
      // 构建查询参数
      const filter = {
        page: this.pageIndex,
        limit: this.pageSize,
        type: filterType,
        keyword: this.searchKey,
      };
      TCIC.SDK.instance.getClassMemberList(filter).then((res) => {
        console.log('getMemberList', res);
        for (const member of res.members) {
          const index = this.memberList.findIndex(item => item.userId === member.userId);
            if (index !== -1) {
              this.memberList[index] = member;
            } else {
              this.memberList.push(member);
            }
            // this.memberList.push(member);
        }
        this.isLoading = false;
        if (this.memberList.length >= res.total) {
          console.log('getMemberList', this.memberList, res.total);
          this.hasMore = false;
        }
      });
    },
    getPlatformStringFromCode(device) {
      // 将平台码转换成可阅读的文本
      switch (device) {
        case TCIC.TDevice.Windows:
        case TCIC.TDevice.Mac:
          return {
            icon: 'pc',
            label: i18next.t('电脑'),
          };
        case TCIC.TDevice.Pad:
          return {
            icon: 'pad',
            label: i18next.t('平板'),
          };
        case TCIC.TDevice.Phone:
          return {
            icon: 'phone',
            label: i18next.t('手机'),
          };
        case TCIC.TDevice.TV:
          return {
            icon: 'pc',
            label: 'TV',
          };
        case TCIC.TDevice.Miniprogram:
          return {
            icon: 'mini',
            label: i18next.t('小程序'),
          };
        default:
          return {
            icon: 'unknow',
            label: i18next.t('未知'),
          };
      }
    },
    onTrophyTaskUpdate(taskInfo) {
      if (taskInfo.taskId === this.trophyTaskId) {
        const trophyComponent =					TCIC.SDK.instance.getComponent('trophy-component');
        this.trophyList = trophyComponent.getVueInstance().getList();
      }
    },
    initTrophyCount() {
      const trophyComponent =				TCIC.SDK.instance.getComponent('trophy-component');
      if (trophyComponent) {
        this.trophyList = trophyComponent.getVueInstance().getList();
      } else {
        console.error('trophy-component has not loaded yet');
      }
    },
    sendTrophy(user) {
      if (!this.$hasRole(['teacher', 'assistant', 'supervisor'])) {
        window.showToast(i18next.t('不可操作'));
        return;
      }
      if (user.currentStatus !== TCIC.TMemberStatus.Online) {
        window.showToast(i18next.t('该操作只对在线{{arg_0}}有效', { arg_0: this.roleInfo.student }));
        return;
      }
      if (!this.isClassStarted) {
        // 未开始上课，禁用发送奖杯相关操作
        // window.showToast('上课前禁止该操作');
        return;
      }
      const trophyComponent =				TCIC.SDK.instance.getComponent('trophy-component');
      trophyComponent.getVueInstance().distributeWithThrottle(user.userId);
    },
    searchName: Lodash.debounce(function (val) {
      this.searchKey = val.trim();
      this.pageIndex = 1;
      this.hasMore = true;
      this.memberList = [];
      this.getMemberList();
    }, 400),
    filterOffline(flag) {
      if (!this.searchKey) {
        this.showAllMember = !flag;
        this.pageIndex = 1; // 切换TAB时重置页码
        this.getMemberList();
      }
    },
    updatePopper(delay = 800) {
      // 通知父组件刷新位置
      // 延时刷新popper(延时500ms仍能复现不更新的情况)
      clearTimeout(this.popoverSTO);
      this.popoverSTO = setTimeout(() => {
        const parentPopper = this.$parent;
        if (parentPopper && parentPopper.updatePopper && this.isVisible) {
          parentPopper.updatePopper();
        }
      }, delay);
    },
  },
};
</script>

<style lang="less">
.bottom-tip {
  height: 30px;
  line-height: 30px;
  text-align: center;
}
.float-member-list-component {
  width: 100%;
	height: 100%;
	padding: 16px 16px 0;
	display: flex;
	flex-direction: column;
	background-color: #1c2131;
  color: #FFF;

  &.portrait{
    .list-item-operate{
      height: 275px;
    }
  }
	.member-list__user-name {
    margin-left: 5px;
		font-size: 14px;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
		line-height: 24px;
		width: calc(100% - 60px);
		text-align: left;
		display: block;
		.student {
			width: calc(100% - 20px);
		}

		.em {
			color: #006eff;
		}

		&.offline {
			color: #8a9099;
		}
	}
	.role-icon {
		margin-left: 5px;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
		padding: 0 4px;
		border-radius: 2px;
		height: 20px;
		&.assistant-icon {
			background: linear-gradient(152deg, #23be82 0%, #08ae6e 94%);
			span {
				color: #fff;
			}
		}
		&.teacher-icon {
			background: linear-gradient(152deg, #00a6ff 0%, #006dff 94%);
			span {
				color: #fff;
			}
		}
		&.student-icon {
			border: 1px solid #c4c4c4;
			border-radius: 2px;
		}
		span {
			font-size: 12px;
			color: #c4c4c4;
		}
	}
	.member-list__title {
		display: flex;
		height: 45px;
		justify-content: space-between;
		align-items: center;
		.title-text {
			text-align: center;
			flex: 1;
		}
	}
	.member-list__search {
		.el-input {
			// width: 162px;
			height: 30px;
			background-color: #292d38;
			input.el-input__inner {
				padding-left: 50px;
				height: 100%;
				background: rgba(0, 0, 0, 0.1);
				border: none;
			}

			input::-webkit-input-placeholder {
				font-weight: 400;
				color: #8a9099;
				line-height: 22px;
			}

			// i.el-input__icon.el-icon-search {
			// 	width: 24px;
			// 	height: 24px;
			// 	content: url("./assets/member-list__search.png");
			// }
		}
	}
	.member-list__content {
    height: calc(100% - 104px);
    flex-grow: 1;
		.infinite-list {
			height: 100%;
			display: flex;
			flex-direction: column;
			.infinite-list-item {
				flex: 1;
				max-height: 40px;
        margin-top: 4px;
        &:first-child{
          margin: 0;
        }
				.member-item {
					height: 40px;
					display: flex;
					align-items: center;
					.member-list__user-name-wrap {
						display: flex;
						align-items: center;
						width: 120px;
            flex: 1;
						overflow: hidden;
						.device-icon {
							width: 22px;
							height: 22px;
							background-size: 100% auto;
							display: inline-block;
							vertical-align: middle;

							&.pc {
								background: url("./assets/mb-pc.svg") no-repeat center;

								&.offline {
									background: url("./assets/mb-pc.svg") no-repeat center;
								}
							}

							&.phone {
								background: url("./assets/mb-phone.svg") no-repeat center;

								&.offline {
									background: url("./assets/mb-phone.svg") no-repeat center;
								}
							}

							&.pad {
								background: url("./assets/device-pad.svg") no-repeat center;

								&.offline {
									background: url("./assets/device-pad-off.svg") no-repeat
										center;
								}
							}

							&.mini {
								background: url("../member-list-component/assets/device-mini.svg")
									no-repeat center;

								&.offline {
									background: url("../member-list-component/assets/device-mini-off.svg")
										no-repeat center;
								}
							}

							&.unknow {
								background: url("./assets/device-unknow.svg") no-repeat center;

								&.offline {
									background: url("./assets/device-unknow-off.svg") no-repeat
										center;
								}
							}
						}
					}
					.member-list__member-status {
						margin-left: 30px;
						align-items: center;
						flex: 1;
						display: flex;
						justify-content: space-around;

            .icon-wrapper {
              width: 48px;
              display: flex;
              justify-content: center;
            }
					}
				}
			}
		}
	}
	.member-list__footer {
		border-top: 1px solid #b8b9bb83;
		padding-top: 8px;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		margin: 0 -12px;
		padding: 12px;
		.footer-btn {
			margin-right: 5px;
			flex: 1;
			padding: 10px 16px;
			text-align: center;
			background-color: #292d38;
			cursor: pointer;
			border-radius: 12px;
			position: relative;
			max-width: 200px;
			&:last-child {
				margin-right: 0;
			}
			.mute-all-sub-component {
        display: none;
				position: absolute;
				top: 0;
				left: 0;
				bottom: 0;
				right: 0;
			}
		}
		.mute-all {
			display: flex;
			padding: 0;
			width: 100px;
		}

		.carousel {
			display: flex;
			padding: 20px 0;
			justify-content: center;
			align-items: center;

			&.mobile {
				padding: 24px 0;
			}
			.carousel-config {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 30px;
				.carousel-input {
					width: 35px;

					input {
						padding-left: 5px;
						padding-right: 0px;
						font-size: 14px;
						text-align: left;
						border: none;
						background: #000;
					}

					span.el-input-number__decrease,
					span.el-input-number__increase {
						border: none;
						width: 12px;
						background-color: transparent;

						.el-icon-arrow-up:before {
							content: "\25B2";
						}
						.el-icon-arrow-down:before {
							content: "\25BC";
						}
					}
				}
			}

			.carousel-tips {
				color: #a1a8b4;
				font-size: 14px;
			}
		}
	}
	.list-item-operate {
		display: flex;
		border-radius: 12px;
		background-color: white;
		position: absolute;
		top: 45px;
		left: 40px;
		width: 80%;
		height: calc(100% - 100px);
		flex-direction: column;
		padding: 12px;
		color: #000;
		z-index: 2;
		.list-item-title {
			flex: 2;
			display: flex;
			align-items: center;
			margin: 0 -12px;
			padding: 0 12px;
			justify-content: space-between;
			max-height: 50px;
			.list-title-name {
				height: 100%;
				width: 70%;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				display: flex;
				align-items: center;
			}
			.list-title-close {
				height: 100%;
				width: 30%;
				display: flex;
				align-items: center;
				justify-content: flex-end;
			}
		}
		.list-item-action {
			margin: 0 -12px;
			flex: 2;
			border-top: 1px solid #c4c4c4;
			display: flex;
			align-items: center;
			padding: 0 16px;
			max-height: 50px;

      .member-item__icon {
        margin-right: 10px;
      }
		}
	}

  // icon 相关直接复用 MemberIcon.less
}
</style>
