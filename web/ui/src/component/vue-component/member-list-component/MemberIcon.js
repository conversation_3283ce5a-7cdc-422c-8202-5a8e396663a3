const iconListNeedOnline = ['stage', 'mic', 'camera', 'board', 'chat', 'screen', 'kick'];
const iconListNeedOnStage = ['mic', 'camera', 'screen'];

export default {
  data() {
    return {
      isMuteAll: false,
    };
  },
  mounted() {
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Joined_Class, true).then(() => {
      this.isMuteAll = TCIC.SDK.instance.getState(TCIC.TMainState.Silence_Mode, TCIC.TClassSilenceMode.Free_Chat) === TCIC.TClassSilenceMode.All_Mute;
      this.addLifecycleTCICStateListener(TCIC.TMainState.Silence_Mode, (value) => {
        this.isMuteAll = value === TCIC.TClassSilenceMode.All_Mute;
      });
    });
  },
  methods: {
    getMemberIconClass(item, iconType, isLoading = false, currentSelfState = false) {
      const baseClass = `member-item__icon member-item__icon-${iconType}`;
      const isOnline = item.currentStatus === TCIC.TMemberStatus.Online;
      let stateClass = '';
      if (iconListNeedOnline.includes(iconType) && !isOnline) {
        // 离线就不显示的
        stateClass = 'icon-hide not-online';
      } else if (iconListNeedOnStage.includes(iconType) && !item.stage) {
        // 不在台上就不显示的
        stateClass = 'icon-hide not-stage';
      } else {
        // 要显示的

        const userId = TCIC.SDK.instance.getUserId();
        const isSelf = userId === item.userId;

        switch (iconType) {
          case 'handup':
            stateClass = (item.handUpTimes > 0 || item.handUp) ? '' : 'icon-hide';
            break;
          case 'stage':
            stateClass = isLoading ? 'icon-loading' : item[iconType] ? 'icon-on' : 'icon-off';
            break;
          case 'board':
            stateClass = isLoading ? 'icon-loading' : item[iconType] ? 'icon-on' : 'icon-off';
            break;
          case 'mic':
          case 'camera':
            if (isSelf) {
              stateClass = `icon-${currentSelfState ? 'on' : 'off'}`;
              break;
            }
            stateClass = isLoading ? 'icon-loading' : `icon-${this.getDeviceShowState(item, iconType)}`;
            break;
          case 'chat':
            // 不禁言才是 on
            // eslint-disable-next-line no-nested-ternary
            stateClass = isLoading ? 'icon-loading' : this.isMuteAll ? 'icon-disabled' : (item.silence ? 'icon-off' : 'icon-on');
            break;
          case 'screen':
            // 0 没有权限；1 分享屏幕或窗口权限，2 分享播片权限，3 分享辅助摄像头权限
            stateClass = isLoading ? 'icon-loading' : item.screen !== 0 ? 'icon-on' : 'icon-off';
            break;
          case 'trophy':
            // 不在线就置灰
            stateClass = isOnline ? '' : 'icon-no';
            break;
          case 'kick':
            // 不区分状态
            break;
        }
      }
      return [baseClass, stateClass].join(' ');
    },
    // deviceType: mic / camera
    getDeviceShowState(item, deviceType) {
      // 没开课不会走到这里，不增加isClassStarted了
      // if (!this.isClassStarted || this.isLive) {
      //   return 'no';
      // }
      if (!item[deviceType]) {
        return 'off';
      }
      const deviceState = item[`${deviceType}State`];
      if (deviceState === TCIC.TDeviceStatus.Unknown || deviceState === TCIC.TDeviceStatus.Open) {
        return 'on';
      }
      if (TCIC.SDK.instance.isDeviceAbnormal(deviceState)) {
        return 'error';
      }
      return 'off';
    },
    isDeviceOpen(item, deviceType) {
      if (!item[deviceType]) {
        return false;
      }
      const deviceState = item[`${deviceType}State`];
      return deviceState === TCIC.TDeviceStatus.Unknown || deviceState === TCIC.TDeviceStatus.Open;
    },
  },
};
