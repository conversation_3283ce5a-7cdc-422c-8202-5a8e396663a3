<template>
  <div
    v-if="!teacherShowGridVideos && !isFootEmpty"
    :class="['footer-component',{
      'footer-fold': upShow,
      'small-screen':isSmallScreen,
      'right-align': upShow && !isScreenShareOnElectron,
      'short-mode': isShortMode,
      'screen-sharing-mode': isShareScreen,
      'live-class': isLiveClass,
      'is-none': (isPortrait && !isOneOnOneVideoClassOrOneOnOneBigClass),
      'is-board-full': isBoardFullscreen,
      'has-camera-foot' : hasCameraFoot
    }]"
  >
    <div class="filter-blur-bg" />
    <!-- 讨论区 -->
    <QuickIMComponent
      v-if="!upShow && !isSubCameraStarted && !disableQuickIM"
      :class="['quick-im-foot', showPPTTool ? 'show-ppt-im' : '']"
      @on-edit-status-change="onEditStatusChange"
    />
    <!-- 连麦 -->
    <div
      v-if="isSmallScreen && isLiveClass && (enableStage || isOnStage)"
      class="student-stage"
    >
      <i
        v-if=" !isOnStage && (stageStatus !== 0 && stageStatus !== 2)"
        class="ic-s-stage"
        @click="onAskStage"
      />
      <!-- 连麦成功 -->
      <i
        v-if="isOnStage"
        class="ic-s-stage approve"
        @click="onHangUpStage"
      />
      <!-- 连麦申请中 -->
      <div
        v-if="!isOnStage && stageStatus === 0"
        class="ic-s-stage-dynamic"
      >
        <i class="ic-s-stage" />
        <div class="s-stage-loading">
          <span />
          <span />
          <span />
        </div>
      </div>
      <!-- 连麦连接中 -->
      <div
        v-if="!isOnStage && stageStatus === 2"
        class="ic-s-stage-dynamic disabled"
      >
        <i class="ic-s-stage" />
        <div class="s-stage-loading">
          <span />
          <span />
          <span />
        </div>
      </div>
      <label v-if="isOnStage">{{ $t('结束') }}</label>
      <label v-if="!isOnStage && stageStatus === 0">{{ $t('申请中') }}</label>
      <label v-if="!isOnStage && stageStatus === 2">{{ $t('连接中') }}</label>
      <label v-if="!isOnStage && (stageStatus !== 0 && stageStatus !== 2)">{{ $t('上台') }}&nbsp;2</label>
    </div>
    <!-- 白板工具区 -->
    <footerToolComponent
      v-show="hasFootTools && !upShow"
      :class="['foot-tool', showPPTTool ? 'show-ppt-foot' : '']"
      @change-is-page-file="onChangeIsPageFile"
    />
    <!-- 视频工具区 -->
    <div
      v-if="hasVideoCtrl && !upShow"
      ref="videoCtrl"
      style="width:100%;height:100%"
    />

    <!-- 手机横屏学生端的视频操作 -->
    <CameraFootComponent
      v-if="hasCameraFoot"
      v-show="!upShow"
      :class="['quick-im', isBoardFullscreen ? 'is-board-full': '']"
    />

    <!--展开/收起-->
    <div
      v-if="!isSmallScreen"
      :class="[
        'footer-tool-fold',
        {'right-hidden':!upShow && isScreenShareOnElectron},
        {'left-show':upShow && isScreenShareOnElectron},
        {'up-show':upShow && !isScreenShareOnElectron}
      ]"
      @click="upFold"
    >
      <span><i class="ppt-tool__next">
        <IconArrow />
      </i></span>
    </div>

    <!-- 在小尺寸下白板添加，列表区与ppt工具栏合并 -->
  </div>
</template>
<script>
import i18next from 'i18next';
import BaseComponent from '@/component/core/BaseComponent';
import Constant from '@/util/Constant';
import QuickIMComponent from './../im-component/QuickIM.vue';
import CameraFootComponent from './../video-component/CameraFootComponent.vue';
import footerToolComponent from './../boardfooter-component/BoardFooter.vue';
import IconArrow from './assets/svg-component/icon_pack.svg';
import { getStartedStatus } from '@/util/class';

export default {
  name: 'FooterComponent',
  components: {
    QuickIMComponent,
    CameraFootComponent,
    footerToolComponent,
    IconArrow,
  },
  extends: BaseComponent,
  data() {
    return {
      // 底部展开/收起
      upShow: false,
      disableQuickIM: false,
      isScreenShareOnElectron: false,  // 桌面端老师屏幕分享
      isSubCameraStarted: false,    // 是否开启了辅助摄像头
      boardPermission: false,       // 白板权限
      editFlag: false,    // 是否正在输入
      quickMsgVisible: true,  // 是否显示消息列表
      isLiveClass: false, // 是否公开课
      isVideoOnlyClass: false, // 是否纯视频课
      isOneOnOneVideoClass: false, // 是不是1v1纯视频课
      isOneOnOneVideoClassOrOneOnOneBigClass: false,    // 是不是1v1纯视频课或1v1大班课
      enableStage: false, // 是否允许上台
      stageStatus: TCIC.TCommandStatus.None, // 是否正在请求连麦
      isShareScreen: false,
      hasVideoCtrl: false, // 公开课显示视频控制栏
      vodCtrlDom: null,
      teacherShowGridVideos: false,  // 当前老师是否视频墙布局
      isOnStage: false, // 权限列表中stage
      isTeacher: false,
      isAssistant: false,
      isSupervisor: false,
      isPortrait: false,
      isMobile: false,
      showPPTTool: false,
      isShowFoot: true,
      isBoardFullscreen: false,
      roleInfo: {},
      isClassStarted: false,
      isBigRoom: false,
      maxRtcMember: 0,
      classLayout: TCIC.TClassLayout.Top,
      hasSideIM: false,
    };
  },
  computed: {
    isShortMode() {
      return !this.editFlag && this.isScreenShareOnElectron && this.quickMsgVisible;
    },
    hasCameraFoot() {
      // 不判断 upShow，那个用 v-show 控制
      return !this.isSubCameraStarted && this.isSmallScreen && this.isOnStage && !this.isTeacher;
    },
    hasFootTools() {
      let hasBoardCtrl = false;
      if (this.isBigRoom) {
        hasBoardCtrl = this.boardPermission;
      } else {
        // 未开课的情况下，也支持渲染底部工具，服务于课前默认课件预览
        hasBoardCtrl = !this.isClassStarted || this.boardPermission;
      }
      return (hasBoardCtrl || !this.isMobile)
          && !this.isScreenShareOnElectron
        && !this.hasVideoCtrl
        && !this.isVideoOnlyClass
        && !this.isOneOnOneVideoClass;
    },
    isFootEmpty() {
      let res = false;
      if (this.hasSideIM // 显示聊天边栏时不显示quick聊天
        && this.maxRtcMember === 0 // 1v0 不显示举手
        && this.disableQuickIM
        && !this.hasFootTools
        && !this.hasVideoCtrl
        && !this.hasCameraFoot
      ) {
        res = true;
      }
      return res;
    },
  },
  watch: {
    upShow(val) {
      TCIC.SDK.instance.setState(Constant.TStateIsHideFooterComponent, val);
    },
  },
  mounted() {
    this.roleInfo = TCIC.SDK.instance.getRoleInfo().roleInfo;
    this.isMobile = TCIC.SDK.instance.isMobile();
    this.isClassStarted = getStartedStatus();
    this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, (status) => {
      this.isClassStarted = status === TCIC.TClassStatus.Already_Start;
    });
    // 注册显示状态
    TCIC.SDK.instance.registerState(Constant.TStateIsShowFooterComponent, '底部工具条是否显示', false);
    TCIC.SDK.instance.registerState(Constant.TStateIsHideFooterComponent, '底部工具条是否隐藏', false);
    // 监听沉浸式
    this.addLifecycleTCICStateListener(Constant.TStateImmerseMode, this.onImmerseModeChange);
    // 屏幕分享状态变化
    this.addLifecycleTCICStateListener(TCIC.TMainState.Screen_Share, this.onScreenShareUpdate);
    this.addLifecycleTCICStateListener(TCIC.TMainState.Board_Permission, this.onBoardPermissionChange);
    this.addLifecycleTCICStateListener(Constant.TStateChatTipsEnable, (isVisible) => {
      this.quickMsgVisible = isVisible;
    });
    // 监听设备方向变更
    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.isPortrait = orientation === TCIC.TDeviceOrientation.Portrait;
    });
    // 监听全屏状态变更
    this.addLifecycleTCICStateListener(Constant.TStateFullScreen, (flag) => {
      this.isBoardFullscreen = flag;
    });
    this.makeSureClassJoined(() => {
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      this.isSupervisor = TCIC.SDK.instance.isSupervisor();
      this.isLiveClass = TCIC.SDK.instance.isLiveClass();
      this.isVideoOnlyClass = TCIC.SDK.instance.isVideoOnlyClass();
      this.isBigRoom = TCIC.SDK.instance.isBigRoom();
      this.maxRtcMember = TCIC.SDK.instance.getClassInfo().maxRtcMember;
      this.classLayout = TCIC.SDK.instance.getClassLayout();
      this.hasSideIM = TCIC.SDK.instance.getClassLayoutMainFrame(this.classLayout).sideIM;
      this.isOneOnOneVideoClass = TCIC.SDK.instance.isOneOnOneClass() && !TCIC.SDK.instance.isClassLayoutHasDoc();
      this.isOneOnOneVideoClassOrOneOnOneBigClass = TCIC.SDK.instance.isOneOnOneClass() && (!TCIC.SDK.instance.isClassLayoutHasDoc() || TCIC.SDK.instance.isBigRoom());
      if (this.isLiveClass && (this.isTeacher || this.isAssistant || this.isSupervisor) && !TCIC.SDK.instance.isClassLayoutHasDoc()) {
        this.addLifecycleTCICStateListener(TCIC.TMainState.Stage_Count, (stageCount) => {
          this.teacherShowGridVideos = stageCount >= 1;
          if (this.teacherShowGridVideos) {
            this.hideVodCtrlCom();
          } else if (this.hasVideoCtrl) {
            this.showVodCtrlCom();
          }
          if (stageCount === 0) { // 重置视频控制栏
            TCIC.SDK.instance.setState(Constant.TStateVideoCtrlUserId, '');
          }
        });
      }
      if (!this.isSmallScreen) {
        const that = this;
        function updateShowVideoCtrl() {
          const isBigVideoMode = TCIC.SDK.instance.getState(Constant.TStateBigVideoMode, false);
          const classLayout = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Layout, TCIC.TClassLayout.Top);
          if (that.isLiveClass) {
            that.hasVideoCtrl = isBigVideoMode && (that.isTeacher || that.isAssistant || that.isSupervisor);
          } else {
            that.hasVideoCtrl = isBigVideoMode && classLayout === TCIC.TClassLayout.Three && (that.isTeacher || that.isAssistant || that.isSupervisor);
          }
          if (that.hasVideoCtrl && !that.upShow) {
            that.showVodCtrlCom();
          } else {
            that.hideVodCtrlCom();
          }
        }
        this.addLifecycleTCICStateListener(Constant.TStateBigVideoMode, () => {
          updateShowVideoCtrl();
        });
        this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Layout, (layout) => {
          this.classLayout = layout;
          this.hasSideIM = TCIC.SDK.instance.getClassLayoutMainFrame(this.classLayout).sideIM;
          updateShowVideoCtrl();
        });
      }
    });

    // 是否开启连麦监听
    this.enableStage = TCIC.SDK.instance.getState(TCIC.TMainState.Enable_Stage, false);
    this.addLifecycleTCICStateListener(TCIC.TMainState.Enable_Stage, (enable) => {
      console.log(`===>>> : Intro : Enable_Stage : ${enable}, ${this.enableStage}`);
      this.enableStage = enable;
      if (!this.enableStage) {
        TCIC.SDK.instance.setCommandStatus(TCIC.TCommandID.Stage, TCIC.TMainState.Ask_Stage_Status, TCIC.TCommandStatus.None);
      }
    });

    const isSmallScreen = TCIC.SDK.instance.isMobile() && !TCIC.SDK.instance.isPad();
    if (isSmallScreen) {
      // 上台相关的状态监听
      this.stageStatus = TCIC.SDK.instance.getState(TCIC.TMainState.Ask_Stage_Status, TCIC.TCommandStatus.None);
      this.addLifecycleTCICStateListener(TCIC.TMainState.Ask_Stage_Status, (status) => {
        if (this.stageStatus === status) {
          // 状态相同。先忽略
          return;
        }
        this.stageStatus = status;
        const deviceOrientation = TCIC.SDK.instance.getState(TCIC.TMainState.Device_Orientation, TCIC.TDeviceOrientation.Landscape);
        if (deviceOrientation === TCIC.TDeviceOrientation.Landscape) {
          switch (this.stageStatus) {
            case TCIC.TCommandStatus.Create: {
              this.stageDrawer = false;
              console.log(`===>>> : footer : 正在上台 : ${this.stageStatus}`);
              window.showToast(i18next.t('正在上台'));
              break;
            }
            case TCIC.TCommandStatus.Cancel: {
              window.showToast(i18next.t('上台超时'));
              window.setTimeout(() => {
                TCIC.SDK.instance.setCommandStatus(TCIC.TCommandID.Stage, TCIC.TMainState.Ask_Stage_Status, TCIC.TCommandStatus.None);
              }, 3000);
              break;
            }
            case TCIC.TCommandStatus.Reject: {
              window.showToast(i18next.t('{{arg_0}}拒绝了你的上台请求', { arg_0: this.roleInfo.teacher }));
              window.setTimeout(() => {
                TCIC.SDK.instance.setCommandStatus(TCIC.TCommandID.Stage, TCIC.TMainState.Ask_Stage_Status, TCIC.TCommandStatus.None);
              }, 3000);
              break;
            }
            case TCIC.TCommandStatus.Approve: {
              TCIC.SDK.instance.approveToStage({}).then(() => {
                const isTeacherInviteMe = TCIC.SDK.instance.getState(TCIC.TMainState.Invite_Stage_Status, false);
                if (!isTeacherInviteMe) {
                  window.showToast(i18next.t('{{arg_0}}同意了你的上台请求', { arg_0: this.roleInfo.teacher }));
                }
                TCIC.SDK.instance.updateComponent('stage-video-list-component', { display: 'block' }).then();
              })
                .catch(() => {
                  window.showToast(i18next.t('上台遇到问题'));
                  TCIC.SDK.instance.hangupToStage();
                });
              break;
            }
          }
        }
        console.log(`===>>> : Ask_Stage_Status : ${status}`);
      });

      this.isOnStage = TCIC.SDK.instance.getState(TCIC.TMainState.Stage_Status, false);
      this.addLifecycleTCICStateListener(TCIC.TMainState.Stage_Status, (status) => {
        this.isOnStage = status;
      });
      document.body.addEventListener('mousedown', (event) => {
        const className = event?.target?.offsetParent?.className;
        if (className?.includes('quick-im') || className?.includes('board-brush')) return;
        this.onShowFoot(this.isShowFoot);
      });
    }
  },
  methods: {
    onChangeIsPageFile(value) {
      this.showPPTTool = value;
    },
    onImmerseModeChange(enable) {
      if (!this.editFlag) {
        TCIC.SDK.instance.setState(Constant.TStateIsShowFooterComponent, !enable);
        this.onShowFoot(enable);
      }
    },
    onShowFoot(value) {
      if (value) {
        // this.hide();
        // this.isShowFoot = false;
      } else {
        this.show();
        this.isShowFoot = true;
      }
    },
    // 底部展开/收起
    upFold() {
      this.upShow = !this.upShow;
      if (this.hasVideoCtrl && !this.upShow) {
        this.showVodCtrlCom();
      }
    },
    onBoardPermissionChange(flag) {
      this.boardPermission = !!flag;
    },
    onScreenShareUpdate(flag) {
      const isSharingPPt = TCIC.SDK.instance.getState(Constant.TStatePPtSharingState, false);
      const vodPlay = TCIC.SDK.instance.getState(TCIC.TMainState.Vod_Play, 2);
      this.isSubCameraStarted = TCIC.SDK.instance.getState(Constant.TStateStartSubCamera, false);
      const isVodPlayOnElectron = TCIC.SDK.instance.isTeacherOrAssistant()
        && TCIC.SDK.instance.isElectron()
        && (vodPlay < 2);
      this.isScreenShareOnElectron = TCIC.SDK.instance.isElectron()
        && (flag < 2 || (flag === 2 && isSharingPPt))
        && !this.isSubCameraStarted
        && !isVodPlayOnElectron; // 老师在electron下屏幕分享
      if (this.isScreenShareOnElectron) {
        this.toggleComponentDrag(true, '.filter-blur-bg');
      } else {
        this.toggleComponentDrag(false, '.filter-blur-bg');
      }
      if (flag < 2 && !this.isSubCameraStarted) {
        // vodCtrlDom跟随footer隐藏，需将setFooterCtrlCom设置为null
        const videoDom = TCIC.SDK.instance.getComponent('teacher-component');
        if (videoDom) {
          videoDom.getVueInstance().setFooterCtrlCom(null);
        }
        this.isShareScreen = true;
      } else {
        this.isShareScreen = false;
        // 结束屏幕共享，showVideoCtrl为true时需要显示视频控制器
        if (this.hasVideoCtrl && !this.upShow) {
          this.showVodCtrlCom();
        }
      }
    },
    onEditStatusChange(flag) {
      this.editFlag = flag;
    },
    onAskStage() {
      const isStudent = !(TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isSupervisor());
      if (!isStudent) {
        // 仅限学生操作
        return;
      }
      if (this.stageStatus === TCIC.TCommandStatus.None) {
        TCIC.SDK.instance.getUserInfo(TCIC.SDK.instance.getUserId())
          .then((userInfo) => {
            const req = new TCIC.TCommandReq();
            req.cmd = TCIC.TCommandID.Stage;
            // req.userId = item.userId; // 请求连麦不用填userId
            req.classId = TCIC.SDK.instance.getClassInfo().classId;
            req.type = TCIC.TCommandStatus.Create;

            // 自定义参数
            const customData = new TCIC.TStageCommandParam();
            customData.nickname = userInfo.nickname;
            customData.device = TCIC.SDK.instance.getDeviceType();
            req.param = customData;
            TCIC.SDK.instance.sendCommand(req).then((result) => {
              // this.isAskStaging = true;
              console.log(`===>>> : ${userInfo.userId} :开始请求上台`);
            })
              .catch((error) => {
                // TODO: 从本地删除，并刷新
                // this.isAskStaging = false;
                window.showToast(error.errorMsg, 'error');
                console.log(`===>>> : askStage : ${userInfo.userId}`);
              });
          });
      }
    },
    // onCancelStage() {
    //   const req = new TCIC.TCommandReq();
    //   req.cmd = TCIC.TCommandID.Stage;
    //   // req.userId = item.userId; // 请求连麦不用填userId
    //   req.classId = TCIC.SDK.instance.getClassInfo().classId;
    //   req.type = TCIC.TCommandStatus.Cancel;
    //   // 取消不用发自定义参数
    //   TCIC.SDK.instance.sendCommand(req).then((result) => {
    //     // this.isAskStaging = false;
    //     console.log('===>>> : 主动取消上台');
    //     window.showToast(i18next.t('已取消上台'));
    //   })
    //     .catch((error) => {
    //       // TODO: 从本地删除，并刷新
    //       // this.isAskStaging = false;
    //       console.log('===>>> : 主动取消上台');
    //     });
    // },
    onHangUpStage() {
      const isStudent = TCIC.SDK.instance.isStudent();
      if (!isStudent) {
        // 仅限学生操作
        return;
      }
      console.log('===>>> : 学生挂断 : trtc关麦克风/摄像头');
      // TODO : 调用下台接口
      TCIC.SDK.instance.hangupToStage({}).then(() => {
        TCIC.SDK.instance.setCommandStatus(TCIC.TCommandID.Stage, TCIC.TMainState.Ask_Stage_Status, TCIC.TCommandStatus.None);
      });
    },
    showVodCtrlCom() {
      if (this.teacherShowGridVideos) {
        // 展示视频墙时不显示footer， 直接return
        return;
      }
      const that = this;
      this.$nextTick(() => {
        if (!that.vodCtrlDom) {
          TCIC.SDK.instance.loadComponent('vod-footer-ctrl-component',  {
            display: 'block',
            style: 'position: relative;',
          }).then((dom) => {
            if (that.$refs.videoCtrl) {
              that.$refs.videoCtrl.appendChild(dom);
            }
            const videoDom = TCIC.SDK.instance.getComponent('teacher-component');
            if (videoDom) {
              videoDom.getVueInstance().setFooterCtrlCom(dom);
            }
            that.vodCtrlDom = dom;
          });
        } else {
          if (that.$refs.videoCtrl && that.vodCtrlDom.parentNode !== that.$refs.videoCtrl) {
            that.$refs.videoCtrl.appendChild(that.vodCtrlDom);
          }
          const videoDom = TCIC.SDK.instance.getComponent('teacher-component');
          if (videoDom) {
            videoDom.getVueInstance().setFooterCtrlCom(that.vodCtrlDom);
          }
          TCIC.SDK.instance.updateComponent('vod-footer-ctrl-component',  { display: 'block' }).then();
        }
      });
    },
    hideVodCtrlCom() {
      const videoDom = TCIC.SDK.instance.getComponent('teacher-component');
      if (videoDom && videoDom.getVueInstance()) {
        videoDom.getVueInstance().setFooterCtrlCom(null);
      }
      TCIC.SDK.instance.updateComponent('vod-footer-ctrl-component',  { display: 'none' }).then();
    },
  },
};
</script>
<style lang="less">
@import 'footer.less';
.quick-im-foot, .foot-tool {
  width: 50%;
  height: 100%;
}
.show-ppt-im {
  width: 30%;
}
.show-ppt-foot {
  width: 70%;
}
.small-screen {
  .foot-tool {
    flex-grow: 1;
    width: 80%;
  }
}
</style>
