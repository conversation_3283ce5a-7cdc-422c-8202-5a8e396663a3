<template>
  <div class="invite-share-dialog-component">
    <div class="invite-dialog-mobile__wrap">
      <el-row
        class="share-header__wrap"
        type="flex"
        justify="space-between"
      >
        <el-col
          :span="12"
          class="share-panel-title"
        >
          {{ $t('分享') }}
        </el-col>
        <el-col
          :span="12"
          class="share-panel-close"
        >
          <a
            class="share-close-btn"
            @click="hide"
          >
            <i class="el-icon-close icon" />
          </a>
        </el-col>
      </el-row>

      <el-row
        class="share-items__wrap"
        type="flex"
        justify="center"
      >
        <el-col
          :span="12"
          class="share-item_wrap"
        >
          <div
            class="share-wx"
            @click="shareWx"
          >
            <div class="share-wx-icon" />
            <div class="share-text">
              {{ $t('微信好友') }}
            </div>
          </div>
        </el-col>
        <el-col
          :span="12"
          class="share-item_wrap"
        >
          <div
            v-clipboard:copy="copyText"
            v-clipboard:success="onCopySuccess"
            v-clipboard:error="onCopyError"
            class="copy-link"
          >
            <div class="share-link-icon" />
            <div class="share-text">
              {{ $t('复制链接') }}
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import i18next from 'i18next';
import Constant from '@/util/Constant';
import Util from '@/util/Util';
import BaseComponent from '@core/BaseComponent';

export default {
  name: 'ShareDialogMobile',
  extends: BaseComponent,
  data() {
    return {
      shareClassRoomInfo: {
        userName: '',
        teacherName: '',
        classId: '',
        className: '',
        startTime: '',
        endTime: '',
        shareUrl: '',
        logoSrc: '',
      },
      roomInfo: {},
      roleInfo: {},
    };
  },

  computed: {
    // 要复制的内容
    copyText() {
      return [
        i18next.t('{{arg_0}} 邀请你参加直播课', { arg_0: this.shareClassRoomInfo.userName }),
        `${this.roomInfo.roomName}: ${this.shareClassRoomInfo.className}`,
        `${this.roomInfo.roomID}: ${this.shareClassRoomInfo.classId}`,
        `${i18next.t('时间')}: ${i18next.t('{{arg_0}} 至 {{arg_1}}', { arg_0: this.shareClassRoomInfo.startTime, arg_1: this.shareClassRoomInfo.endTime })}`,
        `${this.roleInfo.teacher}: ${this.shareClassRoomInfo.teacherName}`,
        `${i18next.t('链接')}: ${this.shareClassRoomInfo.shareUrl}`,
        i18next.t('点击打开链接或复制这段话打开【{{room}}】参加直播课', { room: this.roomInfo.room }),
      ].join('\n');
    },
  },

  mounted() {
    const { roleInfo, roomInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    this.roomInfo = roomInfo;
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Joined_Class, true)
      .then(() => {
        const classInfo = TCIC.SDK.instance.getClassInfo();
        TCIC.SDK.instance.getUserInfo(TCIC.SDK.instance.getUserId())
          .then((userInfo) => {
            this.shareClassRoomInfo.userName = userInfo.nickname;
          });
        TCIC.SDK.instance.getUserInfo(classInfo.teacherId)
          .then((userInfo) => {
            this.shareClassRoomInfo.teacherName = userInfo.nickname;
          });
        this.shareClassRoomInfo.classId = classInfo.classId;
        this.shareClassRoomInfo.className = classInfo.className;
        this.shareClassRoomInfo.startTime = Util.formatTime(classInfo.startTime, 'yyyy-MM-DD HH:mm');
        this.shareClassRoomInfo.endTime = Util.formatTime(classInfo.endTime, 'HH:mm');
        this.shareClassRoomInfo.logoSrc = 'https://main.qcloudimg.com/raw/be5d8bc407204d0e1dea30bacd6d006b.png';  // 默认logo
      });
    this.addLifecycleTCICStateListener(Constant.TStateShareClassroomInfo, (shareInfo) => {
      if (typeof shareInfo === 'object') {
        this.shareClassRoomInfo = Object.assign(this.shareClassRoomInfo, shareInfo);
      }
    });
  },

  methods: {
    onCopySuccess() {
      window.showToast(i18next.t('复制成功'), 'success');
      this.hide();
    },

    onCopyError() {
      window.showToast(i18next.t('复制失败'), 'error');
    },

    // 处理订阅的课堂分享数据
    shareClassroomInfoChangeHandler(classroomInfo) {
      this.shareClassRoomInfo = classroomInfo;
    },

    /**
     * 通知微信分享
     */
    shareWx() {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      let action = 'share-class';
      let data = {
        classId: this.shareClassRoomInfo.classId,
        className: this.shareClassRoomInfo.className,
        startTime: this.shareClassRoomInfo.startTime,
        endTime: this.shareClassRoomInfo.endTime,
        teacherId: this.shareClassRoomInfo.teacherId,
        teacherName: this.shareClassRoomInfo.teacherName,
        userId: this.shareClassRoomInfo.userId,
        userName: this.shareClassRoomInfo.userName,
      };
      if (TCIC.SDK.instance.isLiveClass()) {
        action = 'share-class-miniprogram';
        data = {
          classId: this.shareClassRoomInfo.classId,
          className: this.shareClassRoomInfo.className,
        };
      }

      TCIC.SDK.instance.sendCustomMessage({
        action,
        data,
      });
      this.hide();
    },
  },
};
</script>

<style lang="less">
.invite-share-dialog-component {
  width: 100%;
  height: 100%;

  .invite-dialog-mobile__wrap {
    position: absolute;
    top: 0;
    right: 0;
    padding: 24px 24px 24px 20px;
    width: 278px;
    background: rgba(0, 0, 0, 0.5);
    box-shadow: 0px 0px 40px 0px rgba(0, 0, 0, 0.5);

    .share-header__wrap {
      position: relative;

      &::before {
        content: "";
        display: block;
      }

      .share-panel-title {
        height: 20px;
        font-size: 14px;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
      }

      .share-panel-close {
        text-align: right;
        .share-close-btn {
          color: #fff;
          font-size: 0.8rem;
        }
      }
    }

    .share-items__wrap {
      margin-top: 20px;
      text-align: center;

      .share-text {
        margin-top: 12px;
        height: 20px;
        line-height: 20px;
        font-size: 14px;
        font-weight: 400;
        color: #ffffff;
      }

      .share-wx-icon,
      .share-link-icon {
        display: inline-block;
        width: 44px;
        height: 44px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
      }

      .share-wx-icon {
        background-image: url("@/assets/wechat-icon.svg");
      }

      .share-link-icon {
        background-image: url("@/assets/copylink-icon.svg");
      }
    }
  }
}
</style>
