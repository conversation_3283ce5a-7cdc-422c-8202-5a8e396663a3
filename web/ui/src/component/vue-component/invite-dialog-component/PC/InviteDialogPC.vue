<template>
  <div class="invite-share-dialog-component">
    <div
      ref="invite-dialog-ref"
      class="share-dialog__wrap"
    >
      <div class="share-dialog__inner">
        <div class="invite-dialog-header__wrap">
          <span class="invite-dialog-title__text">{{ $t('分享') }}</span>
          <i
            class="invite-dialog-close__btn"
            @click="hide"
          />
        </div>

        <div class="invite-dialog-body__wrap">
          <el-tabs v-model="activeName">
            <!-- 小程序 -->
            <el-tab-pane
              v-if="showMiniProgram"
              :label="$t('小程序')"
              name="first"
              class="mini-program-qrcode_wrap"
            >
              <div class="share-class-title__text">
                {{ shareClassRoomInfo.className }}
              </div>
              <img
                id="qrcode"
                :src="shareClassRoomInfo.xcxQRCodeBase64"
                class="qrcode__image"
              >
              <div class="share-qrcode-tip">
                {{ $t('手机扫码分享') }}
              </div>
              <div class="invite-mini-program-footer__wrap">
                <el-button
                  v-if="!isPad"
                  plain
                  class="copy-mini-program__btn"
                  @click="copyMiniProgramQRImg"
                >
                  {{ $t('复制小程序码') }}
                </el-button>
                <el-button
                  plain
                  class="save-image__btn"
                  :class="{'width__btn': isPad}"
                  @click="saveMiniProgramQRImg"
                >
                  {{ $t('保存图片') }}
                </el-button>
              </div>
            </el-tab-pane>
            <!-- 文字 -->
            <el-tab-pane
              :label="$t('文字')"
              name="second"
            >
              <ul class="share-content-text__wrap">
                <li class="share-content__item">
                  <div class="share-label__text">
                    {{ roomInfo.roomName }}
                  </div>
                  <div class="share-content__text">
                    {{ shareClassRoomInfo.className }}
                  </div>
                </li>
                <li class="share-content__item">
                  <div class="share-label__text">
                    {{ roomInfo.roomID }}
                  </div>
                  <div class="share-content__text">
                    {{ shareClassRoomInfo.classId }}
                    <span
                      v-if="shareClassRoomInfo.classId"
                      v-clipboard:copy="shareClassRoomInfo.classId"
                      v-clipboard:success="onCopySuccess"
                      v-clipboard:error="onCopyError"
                      class="share-copy-classNo__btn"
                    >{{ $t('复制') }}</span>
                  </div>
                </li>
                <li class="share-content__item">
                  <div class="share-label__text">
                    {{ $t('时间') }}
                  </div>
                  <div class="share-content__text">
                    {{ shareClassRoomInfo.startTime }}{{ $t('至') }}{{ shareClassRoomInfo.endTime }}
                  </div>
                </li>
                <li class="share-content__item">
                  <div class="share-label__text">
                    {{ roleInfo.teacher }}
                  </div>
                  <div class="share-content__text">
                    {{ shareClassRoomInfo.teacherName }}
                  </div>
                </li>
                <li class="share-content__item">
                  <div class="share-label__text">
                    {{ $t('链接') }}
                  </div>
                  <div class="share-content__text">
                    <p class="share-class-link">
                      {{ shareClassRoomInfo.shareUrl }}
                    </p>
                    <span>{{ translateTip.content }}</span>
                  </div>
                </li>
              </ul>
              <div class="invite-dialog-footer__wrap">
                <span
                  v-clipboard:copy="copyText"
                  v-clipboard:success="onCopySuccess"
                  v-clipboard:error="onCopyError"
                  class="invite-dialog-copy__btn"
                >{{ $t('复制') }}</span>
              </div>
            </el-tab-pane>
            <!-- 扫码 -->
            <el-tab-pane
              :label="$t('扫码')"
              name="third"
            >
              <div class="share-content-qrcode_wrap">
                <div class="share-class-title__text">
                  {{ shareClassRoomInfo.className }}
                </div>
                <div class="share-qrcode-img__wrap">
                  <vue-qr
                    v-if="shareClassRoomInfo.QRCodeUrl"
                    :logo-src="shareClassRoomInfo.logoSrc"
                    :text="shareClassRoomInfo.QRCodeUrl"
                    :size="130"
                    :margin="0"
                  />
                </div>
                <div class="share-qrcode-tip">
                  {{ translateTip.share }}
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import i18next from 'i18next';
import Constant from '@/util/Constant';
import Util from '@/util/Util';
import BaseComponent from '@core/BaseComponent';
import VueQr from 'vue-qr';
const { ClipboardItem } = window;

export default {
  name: 'ShareDialogPC',
  components: { VueQr },
  extends: BaseComponent,

  data() {
    return {
      isShow: false,
      isPad: false,
      activeName: '',
      lastSaveTime: 0.0,
      shareClassRoomInfo: {
        userName: '',
        teacherName: '',
        classId: '',
        className: '',
        startTime: '',
        endTime: '',
        shareUrl: '',
        QRCodeUrl: '',
        logoSrc: '',
        xcxQRCodeBase64: '',
      },
      roomInfo: {},
      roleInfo: {},
    };
  },
  computed: {
    showMiniProgram() {
      return  this.shareClassRoomInfo.xcxQRCodeBase64 !== null
        && typeof this.shareClassRoomInfo.xcxQRCodeBase64 === 'string'
        && this.shareClassRoomInfo.xcxQRCodeBase64.length > 0;
    },
    copyText() {
      return [
        i18next.t('{{arg_0}} 邀请你参加直播课', { arg_0: this.shareClassRoomInfo.userName }),
        `${this.roomInfo.roomName}: ${this.shareClassRoomInfo.className}`,
        `${this.roomInfo.roomID}: ${this.shareClassRoomInfo.classId}`,
        `${i18next.t('时间')}: ${i18next.t('{{arg_0}} 至 {{arg_1}}', { arg_0: this.shareClassRoomInfo.startTime, arg_1: this.shareClassRoomInfo.endTime })}`,
        `${this.roleInfo.teacher}: ${this.shareClassRoomInfo.teacherName}`,
        `${i18next.t('链接')}: ${this.shareClassRoomInfo.shareUrl}`,
        i18next.t('点击打开链接或复制这段话打开【{{room}}】参加直播课', { room: this.roomInfo.room }),
      ].join('\n');
    },
    translateTip() {
      return {
        share: i18next.t('微信扫码分享{{room}}邀请', { arg_0: this.roomInfo.room }),
        content: i18next.t('点击打开链接或复制这段话打开【{{room}}】参加直播课', { arg_0: this.roomInfo.room }),
      };
    },
  },
  mounted() {
    const { roleInfo, roomInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    this.roomInfo = roomInfo;
    this.isPad = TCIC.SDK.instance.isPad();
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Joined_Class, true).then(() => {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      TCIC.SDK.instance.getUserInfo(TCIC.SDK.instance.getUserId())
        .then((userInfo) => {
          this.shareClassRoomInfo.userName = userInfo.nickname;
        });
      TCIC.SDK.instance.getUserInfo(classInfo.teacherId)
        .then((userInfo) => {
          this.shareClassRoomInfo.teacherName = userInfo.nickname;
        });
      this.shareClassRoomInfo.classId = `${classInfo.classId}`;
      this.shareClassRoomInfo.className = classInfo.className;
      this.shareClassRoomInfo.startTime = Util.formatTime(classInfo.startTime, 'yyyy-MM-DD HH:mm');
      this.shareClassRoomInfo.endTime = Util.formatTime(classInfo.endTime, 'HH:mm');
      this.shareClassRoomInfo.logoSrc = 'https://main.qcloudimg.com/raw/be5d8bc407204d0e1dea30bacd6d006b.png';  // 默认logo
      const shareInfo = TCIC.SDK.instance.getState(Constant.TStateShareClassroomInfo);    // 主动获取
      if (typeof shareInfo === 'object') {
        this.shareClassRoomInfo = Object.assign(this.shareClassRoomInfo, shareInfo);
      }
    });
    this.addLifecycleTCICStateListener(Constant.TStateShareClassroomInfo, (shareInfo) => {
      if (typeof shareInfo === 'object') {
        this.shareClassRoomInfo = Object.assign(this.shareClassRoomInfo, shareInfo);
      }
    });
  },

  methods: {
    // 隐藏组件
    async hide() {
      await this.updateComponent({
        display: 'none',
        transform: 'scale(1)',
      });
      this.isShow = false;
    },

    // 显示组件
    async show() {
      // 避免重复点击
      if (this.isShow) {
        return;
      }
      // 1. 由于分享内容是动态设置的，需要先将组件显示出来，才能获取到邀请窗口的高度
      await this.updateComponent({
        left: '-150%',
        top: '-150%',
        width: '400%',
        height: '400%',
        display: 'block',
      });
      this.isShow = true;
      // 2. 获取组件的scale
      const scale = this.getInviteDialogScale();
      // 3. 设置scale
      this.setScale(scale);
      this.activeName = this.showMiniProgram ? 'first' : 'second';
    },

    // 获取组件的缩放值
    getInviteDialogScale() {
      // const bodyRect = TCIC.SDK.instance.getAppClientRect();
      const bodyRect = document.getElementById('app').getBoundingClientRect();
      const rect = this.$refs['invite-dialog-ref'].getBoundingClientRect();
      if (rect.height > (bodyRect.height - 100)) { // 如果分享窗口高度 > 应用的高度 - 100，则需要进行缩放
        return ((bodyRect.height - 100) / rect.height).toFixed(2);
      }
      return 1;
    },

    // 设置组件的scale
    setScale(inviteDialogScale) {
      // 更新邀请组件
      // 小屏幕和大屏幕加载不同的组件或使用不同的布局
      if (TCIC.SDK.instance.isMobile() && !TCIC.SDK.instance.isPad()) {
        // 只有小屏的移动端才使用mobile的分享(这个不需要缩放)
      } else {
        // 加载pc和pad分享组件
        this.updateComponent({
          transform: `scale(${inviteDialogScale})`,
        });
      }
    },

    onCopySuccess() {
      window.showToast(i18next.t('复制成功'), 'success');
    },

    onCopyError() {
      window.showToast(i18next.t('复制失败'), 'error');
    },

    copyMiniProgramQRImg() {
      if (!this.showMiniProgram) {
        return;
      }
      const canvas = document.createElement('canvas');
      const img = document.getElementById('qrcode');
      canvas.width = img.width;
      canvas.height = img.height;
      const context = canvas.getContext('2d');
      context.drawImage(img, 0, 0, img.width, img.height);
      if (canvas.toBlob) {
        canvas.toBlob((blob) => {
          const clipboardItem = new ClipboardItem({ [blob.type]: blob });
          navigator.clipboard.write([clipboardItem]).then(() => {
            window.showToast(i18next.t('复制成功'), 'success');
          }, (err) => {
            window.showToast(i18next.t('复制失败'), 'error');
          });
        });
      }
    },

    saveMiniProgramQRImg() {
      const nowTime = new Date().getTime();
      if (!this.showMiniProgram || nowTime - this.lastSaveTime < 800) {
        // 防止频繁点击导致保存多张图片，每800ms只响应一次
        return;
      }
      this.lastSaveTime = nowTime;
      try {
        const isPng = this.shareClassRoomInfo.xcxQRCodeBase64.indexOf('png') > 0;
        const fileName = i18next.t('{{arg_0}}_小程序{{arg_1}}', { arg_0: TCIC.SDK.instance.getClassInfo().className, arg_1: isPng ? '.png' : '.jpg' });
        const timestamp = new Date().getTime();
        TCIC.SDK.instance.saveSnapshot(`xcxQRCode_${timestamp}`, this.shareClassRoomInfo.xcxQRCodeBase64, fileName, 1024 * 20, true).then(() => {
          window.showToast(i18next.t('图片保存成功'), 'sucess');
        }, (error) => {
          if (error.errcode) {
            window.showToast(i18next.t('图片保存失败'), 'error');
          }
        });
      } catch (error) {
        window.showToast(i18next.t('下载失败'), 'error');
      }
    },
  },
};
</script>

<style lang="less">
.invite-share-dialog-component {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);

  .share-dialog__wrap {
    width: 452px;
    overflow: hidden;

    .share-dialog__inner {

      background: #ffffff;
      border-radius: 10px;
      overflow: hidden;

      .invite-dialog-header__wrap {
        position: relative;
        text-align: center;
        height: 40px;
        line-height: 40px;
        border: 1px solid #dadee7;

        .invite-dialog-title__text {
          font-size: 14px;
          font-weight: bold;
          color: #000000;
        }

        .invite-dialog-close__btn {
          position: absolute;
          top: 14px;
          right: 18px;
          display: inline-block;
          width: 12px;
          height: 12px;
          cursor: pointer;
          background: url("@/assets/images/share/close-btn.png") no-repeat center center;
          background-size: contain;
          position: absolute;
          color: #35373f;
        }
      }

      .invite-dialog-body__wrap {
        padding: 0 24px 24px;

        // 覆盖el-tabs的样式
        .el-tabs__header {
          margin: 10px 0px 20px 0px;

          .el-tabs__nav-scroll {
            display: flex;
            justify-content: center;

            .el-tabs__active-bar{
              height: 3px;
            }

            .el-tabs__item {
              font-weight: 400;
              color: #000000;
              // line-height: 16px;
              font-size: 14px;
            }

            .is-active {
              font-weight: 600;
            }
          }

          .el-tabs__nav-wrap:after {
            background-color: transparent !important;
          }
        }

        .share-content-text__wrap {
          padding: 24px 24px;
          background: rgba(218, 222, 231, 1);
          border-radius: 4px;

          .share-content__item {
            display: flex;
            line-height: 20px;
            font-size: 14px;
            margin-bottom: 8px;

            &:last-child {
              margin: 0;
            }

            .share-label__text {
              width: 50px;
              color: #464754;
            }

            .share-content__text {
              flex: 1;
              color: #000000;

              .share-class-link {
                font-size: 14px;
                line-height: 20px;
              }
            }

            .share-copy-classNo__btn {
              cursor: pointer;
              margin-left: 10px;
              padding: 2px 6px;
              // box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.5);
              border-radius: 2px;
              border: 1px solid #7880a3;
              height: 16px;
              font-size: 12px;
              color: #7880a3;
              line-height: 16px;
            }
          }
        }

        .mini-program-qrcode_wrap {
          text-align: center;
          margin-bottom: 0;

          .mini-program {
            padding: 15px;
          }

          .qrcode__image {
            width:150px;
            height:150px;
            margin: 15px;
          }
        }

        .share-class-title__text {
          height: 18px;
          font-size: 18px;
          font-weight: 500;
          color: #333;
          line-height: 18px;
          margin-bottom: 10px;
        }

        .share-qrcode-tip {
          height: 18px;
          font-size: 14px;
          color: #1E2330;
          line-height: 20px;
          font-weight: 400;
          line-height: 18px;
        }

        .share-content-qrcode_wrap {
          text-align: center;
          margin-bottom: 24px;

          .share-qrcode-img__wrap {
            display: inline-block;
            margin: 16px auto 12px;
            padding: 10px;
            background: #ffffff;
            box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.05), 0 1px 2px 0 #dadee7;
            border-radius: 4px;
            width: 150px;
            height: 150px;

            .qrcode-img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }

      .invite-mini-program-footer__wrap {
        padding: 20px 20px 0px 20px;
        text-align: center;

        .copy-mini-program__btn {
          cursor: pointer;
          display: inline-block;
          width: 120px;
          height: 40px;
          background: #FFFFFF;
          border-radius: 2px;
          border: 1px solid #DADEE7;
          color: #000;
        }

        .save-image__btn {
          width: 120px;
          height: 40px;
          background: #0067ED;
          color: #fff;
        }

        .width__btn {
          width: 180px;
        }
      }

      .invite-dialog-footer__wrap {
        padding: 24px 24px 0px 24px;
        text-align: center;

        .invite-dialog-copy__btn {
          cursor: pointer;
          display: inline-block;
          width: 240px;
          height: 36px;
          line-height: 36px;
          font-size: 12px;
          background: #006eff;
          border-radius: 4px;
          color: #fff;
        }
      }
    }
  }

}
</style>

