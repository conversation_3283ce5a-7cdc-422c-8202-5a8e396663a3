<template>
  <div class="network-detector-component">
    <div class="network-detector-wrapper">
      <div v-if="state === 'detecting'" class="component-header">
        <div class="header-title">
          <div class="status-brief">
            <div class="monitor">
              <div class="magnifier" />
            </div>
            <span>{{ $t('网络检测中') }} {{ alreadyDetectedCount }} / {{ Object.keys(this.CONFIG).length }} </span>
          </div>
        </div>
        <div class="header-close" @click="cancelDetect">
          <i class="el-icon-close icon" />
        </div>
      </div>
      <div v-else class="component-header">
        <div v-if="exceptionCount" class="header-title">
          <p class="status-brief">
            <i class="icon icon-network-status el-icon--left break" />
            <span>{{ $t('网络状态异常') }}</span>
          </p>
          <p v-if="exceptionCount" class="desc">
            {{ $t('检测出') }}<span class="count"> {{ exceptionCount }} </span>{{ $t('个异常问题') }}
          </p>
        </div>
        <div v-else class="header-title">
          <p class="status-brief" v-if="qualityResult">
            <i class="icon icon-network-status el-icon--left" :class="[qualityResult.icon]" />
            <span>{{ qualityResult.tips }}</span>
          </p>
        </div>
        <div class="header-close" @click="hide">
          <i class="el-icon-close icon" />
        </div>
      </div>
      <div class="component-content">
        <ul>
          <li>
            <div class="key">
              <span>{{ $t('网络权限') }}</span>
            </div>
            <div class="value">
              <span v-if="networkPermission === null"><i class="icon-loading" />{{ $t('检测中') }}</span>
              <span v-else-if="networkPermission" class="success">{{ $t('正常') }}</span>
              <span v-else class="error">{{ $t('无权限') }}</span>
            </div>
          </li>
          <li v-for="item in Object.keys(CONFIG)" v-bind:key="item">
            <div class="key">
              <span>{{CONFIG[item]['cost']}}{{ item }}</span>
            </div>
            <div class="value">
              <span v-if="!CONFIG[item]['cost']"><i class="icon-loading" />{{ $t('检测中') }}</span>
              <span v-else-if="(CONFIG[item]['cost'] - 0) > 1000" class="error">{{ $t('网络不佳') }}</span>
              <span v-else class="success">{{ CONFIG[item]['cost'] }}ms</span>
            </div>
          </li>
          <!-- <li>
            <div class="key">
              <span>{{ $t('网络连接情况') }}</span>
            </div>
            <div class="value">
              <span v-if="networkStatus === null"><i class="icon-loading" />{{ $t('检测中') }}</span>
              <span v-else-if="networkStatus" class="success">{{ $t('正常') }}</span>
              <span v-else class="error">{{ $t('连接超时') }}</span>
            </div>
          </li>
          <li>
            <div class="key">
              <span>{{ $t('服务器连接情况') }}</span>
            </div>
            <div class="value">
              <span v-if="serveStatus === null"><i class="icon-loading" />{{ $t('检测中') }}</span>
              <span v-else-if="serveStatus === -1" class="error">{{ $t('连接超时') }}</span>
              <span v-else :class="serveResult.class">{{ serveResult.label }}</span>
            </div>
          </li>
          <li>
            <div class="key">
              <span>{{ $t('媒体服务器连接情况') }}</span>
            </div>
            <div class="value">
              <span v-if="mediaServeStatus === null"><i class="icon-loading" />{{ $t('检测中') }}</span>
              <span v-else-if="mediaServeStatus === -1" class="error">{{ $t('连接超时') }}</span>
              <span v-else :class="mediaServeResult.class">{{ mediaServeResult.label }}</span>
            </div>
          </li> -->
          <!-- 新增域名耗时检测 -->
          <!-- <li>
            <div class="key">
              <span>class.qcloudclass.com</span>
            </div>
            <div class="value">
              <span v-if="domain_01 === null"><i class="icon-loading" />{{ $t('检测中') }}</span>
              <span v-else-if="domain_01 === -1" class="error">{{ $t('连接超时') }}</span>
              <span v-else :class="domain_01.class">{{ domain_01.label }}</span>
            </div>
          </li>
          <li>
            <div class="key">
              <span>class-cf.qcloudclass.com</span>
            </div>
            <div class="value">
              <span v-if="domain_02 === null"><i class="icon-loading" />{{ $t('检测中') }}</span>
              <span v-else-if="domain_02 === -1" class="error">{{ $t('连接超时') }}</span>
              <span v-else :class="domain_02.class">{{ domain_02.label }}</span>
            </div>
          </li>
          <li>
            <div class="key">
              <span>tcic-api.qcloudclass.com</span>
            </div>
            <div class="value">
              <span v-if="domain_03 === null"><i class="icon-loading" />{{ $t('检测中') }}</span>
              <span v-else-if="domain_03 === -1" class="error">{{ $t('连接超时') }}</span>
              <span v-else :class="domain_03.class">{{ domain_03.label }}</span>
            </div>
          </li>
          <li>
            <div class="key">
              <span>tcic-os-api.qcloudclass.com</span>
            </div>
            <div class="value">
              <span v-if="domain_04 === null"><i class="icon-loading" />{{ $t('检测中') }}</span>
              <span v-else-if="domain_04 === -1" class="error">{{ $t('连接超时') }}</span>
              <span v-else :class="domain_04.class">{{ domain_04.label }}</span>
            </div>
          </li>
          <li>
            <div class="key">
              <span>res.qcloudclass.com</span>
            </div>
            <div class="value">
              <span v-if="domain_05 === null"><i class="icon-loading" />{{ $t('检测中') }}</span>
              <span v-else-if="domain_05 === -1" class="error">{{ $t('连接超时') }}</span>
              <span v-else :class="domain_05.class">{{ domain_05.label }}</span>
            </div>
          </li>
          <li>
            <div class="key">
              <span>res.qcloudtiw.com</span>
            </div>
            <div class="value">
              <span v-if="domain_06 === null"><i class="icon-loading" />{{ $t('检测中') }}</span>
              <span v-else-if="domain_06 === -1" class="error">{{ $t('连接超时') }}</span>
              <span v-else :class="domain_06.class">{{ domain_06.label }}</span>
            </div>
          </li>
          <li>
            <div class="key">
              <span>log.qcloudtiw.com</span>
            </div>
            <div class="value">
              <span v-if="domain_07 === null"><i class="icon-loading" />{{ $t('检测中') }}</span>
              <span v-else-if="domain_07 === -1" class="error">{{ $t('连接超时') }}</span>
              <span v-else :class="domain_07.class">{{ domain_07.label }}</span>
            </div>
          </li>
          <li>
            <div class="key">
              <span>tcic-prod-1257307760.qcloudclass.com</span>
            </div>
            <div class="value">
              <span v-if="domain_08 === null"><i class="icon-loading" />{{ $t('检测中') }}</span>
              <span v-else-if="domain_08 === -1" class="error">{{ $t('连接超时') }}</span>
              <span v-else :class="domain_08.class">{{ domain_08.label }}</span>
            </div>
          </li>
          <li>
            <div class="key">
              <span>tcic-source.qcloudclass.com</span>
            </div>
            <div class="value">
              <span v-if="domain_09 === null"><i class="icon-loading" />{{ $t('检测中') }}</span>
              <span v-else-if="domain_09 === -1" class="error">{{ $t('连接超时') }}</span>
              <span v-else :class="domain_09.class">{{ domain_09.label }}</span>
            </div>
          </li>
          <li>
            <div class="key">
              <span>yun.tim.qq.com</span>
            </div>
            <div class="value">
              <span v-if="domain_10 === null"><i class="icon-loading" />{{ $t('检测中') }}</span>
              <span v-else-if="domain_10 === -1" class="error">{{ $t('连接超时') }}</span>
              <span v-else :class="domain_10.class">{{ domain_10.label }}</span>
            </div>
          </li>
          <li>
            <div class="key">
              <span>api.my-imcloud.com</span>
            </div>
            <div class="value">
              <span v-if="domain_11 === null"><i class="icon-loading" />{{ $t('检测中') }}</span>
              <span v-else-if="domain_11 === -1" class="error">{{ $t('连接超时') }}</span>
              <span v-else :class="domain_11.class">{{ domain_11.label }}</span>
            </div>
          </li>
          <li>
            <div class="key">
              <span>report-log-lv1.api.qcloud.com</span>
            </div>
            <div class="value">
              <span v-if="domain_12 === null"><i class="icon-loading" />{{ $t('检测中') }}</span>
              <span v-else-if="domain_12 === -1" class="error">{{ $t('连接超时') }}</span>
              <span v-else :class="domain_12.class">{{ domain_12.label }}</span>
            </div>
          </li> -->
          <!-- <li v-if="!isWeb">
            <div class="key">
              <span>{{ $t('域名解析耗时') }}</span>
            </div>
            <div class="value">
              <span v-if="DNSLatency === null"><i class="icon-loading" />{{ $t('检测中') }}</span>
              <span
                v-else-if="DNSLatency === -1"
                class="error"
              >{{ $t('超时') }}</span>
              <span
                v-else
                class="success"
              >{{ DNSLatency }}ms</span>
            </div>
          </li> -->
        </ul>
      </div>
      <div class="component-footer">
        <template v-if="state === 'finishedv2'">
          <el-button v-clipboard:copy="networkTips" v-clipboard:success="onCopySuccess" v-clipboard:error="onCopyError"
            size="medium" type="primary" class="btn-crystal">
            {{ $t('复制结果') }}
          </el-button>
          <el-button size="medium" type="primary" class="btn-crystal" @click="detectv2">
            {{ $t('重新检测') }}
          </el-button>
        </template>
        <template v-else>
          <el-button size="medium" type="primary" class="btn-crystal" @click="cancelDetect">
            {{ $t('取消检测') }}
          </el-button>
        </template>
      </div>
    </div>
    <div id="js-test-cont" style="display: none"></div>
  </div>
</template>
<script>
import i18next from 'i18next';
import BaseComponent from '@/component/core/BaseComponent';
import {
  NetworkMap,
  GetNetworkTips,
} from '@/util/NetworkQualityDefinition';
import Lodash from 'lodash';

export default {
  name: 'NetworkDetectorComponent',
  components: {},
  extends: BaseComponent,
  data() {
    return {
      state: 'reset',  // reset / detecting / finished
      qualityStatus: 'unknown',
      qualityResult: null,
      networkStatus: null,
      networkPermission: null,
      mediaServeStatus: null,
      mediaServeResult: null,
      serveStatus: null,
      serveResult: null,
      DNSLatency: null,
      isWeb: TCIC.SDK.instance.isWeb(),
      businessServerTask: {
        interval: null,
        count: 0,
        maxCount: 3,
        results: [],
      },
      mediaServerTask: {
        interval: null,
        count: 0,
        maxCount: 3,
        results: [],
      },
      CONFIG: {
        'class.qcloudclass.com': { path: '/latest/static/libs/vue/vue_2.7.10.js', type: 'script' },
        'class-cf.qcloudclass.com': { path: '/latest/static/libs/vue/vue_2.7.10.js', type: 'script' },
        'tcic-api.qcloudclass.com': { path: '/v2/member/heartbeat' },
        'tcic-os-api.qcloudclass.com': { path: '/v2/member/heartbeat' },
        'res.qcloudclass.com': { path: '/Web/rtc-beauty-plugin.js', type: 'script' },
        'res.qcloudtiw.com': { path: '/board/third/cos/5.1.0/cos.min.js', type: 'script' },
        'log.qcloudtiw.com': { path: '/config/tiwlogger-server-config' },
        'tcic-prod-1257307760.qcloudclass.com': { path: '/doc/0nmot3jnt3gvpv6j3nfc_tiw/h5/data/img0.jpg', type: 'img' },
        'tcic-source.qcloudclass.com': { path: '/uploads/2120ea9b-1fdb-44b4-be38-9c688a90be45/3923193/1676867746_Xrtl9pQ7.jpg', type: 'img' },
        'yun.tim.qq.com': {},
        'api.my-imcloud.com': {},
        'report-log-lv1.api.qcloud.com': {},
      },
      alreadyDetectedCount: 0,
      exceptionCount: 0,
    };
  },
  computed: {
    // alreadyDetectedCount() {
    //   // let count = 0;
    //   // if (this.networkStatus !== null) count += 1;
    //   // if (this.networkPermission !== null) count += 1;
    //   // if (this.mediaServeStatus !== null) count += 1;
    //   // if (this.serveStatus !== null) count += 1;
    //   // if (this.DNSLatency !== null) count += 1;
    //   let count = 0;
    //   for (let item = 0; item < Object.keys(this.CONFIG).length; item++) {
    //     const key = Object.keys(this.CONFIG)[item];
    //     console.log(this.CONFIG[key]['result'], 'xingchenhe2222');
    //     if (this.CONFIG[key]['result'] != undefined) {
    //       count += 1;
    //     }
    //   }
    //   return count;
    // },
    // exceptionCount() {
    //   let count = 0;
    //   if (!this.networkPermission) count += 1;
    //   if (!this.networkStatus) count += 1;
    //   if (this.mediaServeStatus === - 1) count += 1;
    //   if (this.serveStatus === - 1) count += 1;
    //   if (!this.isWeb) {
    //     if (this.DNSLatency === - 1) count += 1;
    //   }
    //   return count;
    // },
    networkTips() {
      const keys = Object.keys(this.CONFIG);
      const res = [];
      for (let index = 0; index < keys.length; index++) {
        const element = keys[index];
        const cost = this.CONFIG[element]['cost'];
        res.push(`${element} cost ${cost} ms. ${cost > 1000 ? i18next.t('异常') : ''}`);
      }
      const classInfo = TCIC.SDK.instance.getClassInfo();
      if (classInfo) {
        const cid = TCIC.SDK.instance.getCid();
        const uid = TCIC.SDK.instance.getUid();
        return [
          `RoomId11: ${cid ? `${cid}(${classInfo.classId})` : classInfo.classId}`,
          `UserId: ${uid ? `${uid}(${TCIC.SDK.instance.getUserId()})` : TCIC.SDK.instance.getUserId()}`,
          ...res,
        ].join('\n');
      }
      return '';
    },
  },
  watch: {
    state(newVal, oldVal) {
      if (newVal !== oldVal && newVal === 'reset') {
        this.reset();
      }
    },
    alreadyDetectedCount(newVal) {
      if ((newVal === 4 && this.isWeb) || newVal === 5) {
        this.state = 'finished';
        // 网络检测此处只有延时，没有丢包
        // this.serveStatus = 300;
        // this.mediaServeStatus = 500;
        const mediaRtt = this.mediaServeStatus;
        const serveRtt = TCIC.SDK.instance.getApiNetworkQuality(this.serveStatus);

        this.qualityStatus = NetworkMap[Math.max(mediaRtt, serveRtt)];
        this.qualityResult = GetNetworkTips(this.qualityStatus);

        this.mediaServeResult = GetNetworkTips(NetworkMap[mediaRtt]);
        this.serveResult = GetNetworkTips(NetworkMap[serveRtt]);
      }
    },
  },
  mounted() {
    this.makeSureClassJoined(this.onJoinClass);
  },
  methods: {
    testSpeed(domain) {
      return new Promise((resolve) => {
        setTimeout(async () => {
          const t1 = +new Date();
          const res = await this.fetchWithTimeout(domain);
          this.alreadyDetectedCount = this.alreadyDetectedCount + 1;
          const t2 = +new Date();
          const cost = t2 - t1;
          this.CONFIG[domain]['cost'] = cost;
          console.log(domain, cost, res, 'fetchWithTimeout', this.CONFIG[domain]);
          if (cost > 1000) {
            this.exceptionCount = this.exceptionCount + 1;
          }
          // const e1 = document.getElementById(domain);
          // const e2 = e1.nextElementSibling;
          // const parent = e1.parentElement;
          // const anaBtn = e2.nextElementSibling;
          // e2.innerText = cost + 'ms';
          // const isError = (typeof res === 'Error' || (res.status && res.status === 'error'));
          // if (isError) {
          //   this.CONFIG[domain]['result'] = 'error';
          //   // parent.classList.add('error');
          //   // anaBtn.classList.remove('hide');
          // } else {
          //   if (cost > 5000 && cost < 10000) {
          //     this.CONFIG[domain]['result'] = 'timeout';
          //     // parent.classList.add('warning')
          //     // anaBtn.classList.remove('hide');
          //   } else if (cost < 5000) {
          //     this.CONFIG[domain]['result'] = cost;
          //     // parent.classList.add('success')
          //   }
          // }
          resolve();
        }, 0);
      });
    },
    run() {
      const task = [];
      for (const domain in this.CONFIG) {
        task.push(this.testSpeed(domain));
      }
      Promise.all(task).then(() => {
        this.state = 'finishedv2';
        console.log("fetchWithTimeout", JSON.stringify(this.CONFIG));
      });
    },
    fetchWithTimeout(domain, options, timeout = 10000) {
      const testCont = document.getElementById('js-test-cont');
      let url = `https://${domain}`;
      const path = this.CONFIG[domain].path || '';
      url = url + path;
      const tagType = this.CONFIG[domain].type;

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout')), timeout);
      });
      const fetchPromise = new Promise((resolve, reject) => {
        if (tagType) {
          const ele = document.createElement(tagType);
          ele.id = domain;
          console.log(url, 'xingchenhe22334 ok', tagType);
          ele.onload = () => {
            resolve({
              url: url,
              status: 'ok',
            });
          };
          ele.onerror = (e) => {
            console.log(url, 'xingchenhe22334 err', tagType);
            resolve({
              url: url,
              status: 'error',
            });
          };
          ele.setAttribute('src', url);
          testCont.appendChild(ele);
        } else {
          fetch(url).then((resp) => {
            console.log(url, 'xingchenhe22334 ok');
            resolve(resp);
          }).catch((e) => {
            console.log(url, 'xingchenhe22334 err');
            resolve(new Error('Fetch error'));
          });
        }
      });
      return Promise.race([
        fetchPromise,
        timeoutPromise
      ]);
    },
    reset() {
      this.networkStatus = null;
      this.networkPermission = null;
      this.mediaServeStatus = null;
      this.serveStatus = null;
      this.DNSLatency = null;
      this.businessServerTask = {
        interval: null,
        count: 0,
        maxCount: 3,
        results: [],
      };
      this.mediaServerTask = {
        interval: null,
        count: 0,
        maxCount: 1,  // 媒体服务器取值与信号格对齐，只取最新
        results: [],
      };
      this.alreadyDetectedCount = 0;
      this.exceptionCount = 0;
    },
    onJoinClass() {
      this.initData();
      this.bindEvent();
      // this.run();
    },

    initData() {
    },

    bindEvent() {
    },

    // 复制成功时的回调函数
    onCopySuccess() {
      window.showToast(i18next.t('复制成功'), 'success');
    },

    // 复制失败时的回调函数
    onCopyError() {
      window.showToast(i18next.t('复制失败请重试'));
    },

    toggle(state) {
      if (state) {
        this.show();
        this.detect();
        this.detectv2();
      } else {
        this.reset();
        this.hide();
      }
    },

    async checkBusinessServerConnection() {
      if (this.state !== 'detecting') {
        clearTimeout(this.businessServerTask.interval);
      } else {
        const rtt = await TCIC.SDK.instance.checkBusinessServerConnection();
        this.businessServerTask.count += 1;
        if (rtt !== - 1) {
          this.businessServerTask.results.push(rtt);
        }
        if (this.businessServerTask.count >= this.businessServerTask.maxCount) {
          if (rtt === - 1) {
            this.serveStatus = - 1;
          } else {
            this.serveStatus = Lodash.mean(this.businessServerTask.results);
          }
        } else {
          clearTimeout(this.businessServerTask.interval);
          this.businessServerTask.interval = setTimeout(async () => {
            await this.checkBusinessServerConnection();
          }, 2000);
        }
      }
    },

    async checkMediaServerConnection() {
      if (this.state !== 'detecting') {
        clearTimeout(this.mediaServerTask.interval);
      } else {
        const statistics = await TCIC.SDK.instance.checkMediaServerConnection();
        this.mediaServerTask.count += 1;
        if (statistics && statistics.rtt !== - 1) {
          this.mediaServerTask.results.push(statistics.networkQuality);
        }

        if (this.mediaServerTask.count >= this.mediaServerTask.maxCount) {
          if (statistics && statistics.rtt === - 1) {
            this.mediaServeStatus = - 1;
          } else {
            this.mediaServeStatus = Math.floor(Lodash.mean(this.mediaServerTask.results));
          }
        } else {
          clearTimeout(this.mediaServerTask.interval);
          this.mediaServerTask.interval = setTimeout(async () => {
            await this.checkMediaServerConnection();
          }, 2000);
        }
      }
    },
    async cancelDetect() {},
    async detectv2() {
      this.reset();
      this.detect();
      this.state = 'detecting';
      for (let item = 0; item < Object.keys(this.CONFIG).length; item++) {
        const key = Object.keys(this.CONFIG)[item];
        this.CONFIG[key]['result'] = null;
      }
      this.run();
    },
    async detect() {
      this.reset();
      this.state = 'detecting';
      // 接口反应太快..做个延时
      setTimeout(async () => {
        try {
          this.networkPermission = await TCIC.SDK.instance.checkNetworkPermission();
          this.networkStatus = await TCIC.SDK.instance.checkNetworkConnection();
          await this.checkBusinessServerConnection();
          await this.checkMediaServerConnection();
          // if (!this.isWeb) {
          //   this.DNSLatency = await TCIC.SDK.instance.checkDNSParsing();
          // }
        } catch (e) {
          window.showToast(i18next.t('网络状态检测失败，请稍后重试'));
          this.toggle(false);
        }
      }, 1000);
    },
  },
};
</script>
<style lang="less">
.network-detector-component {
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;

  .network-detector-wrapper {
    display: flex;
    flex-direction: column;
    text-align: left;
    font-size: 12px;
    user-select: text;
    -webkit-user-select: text;
    color: #fff;
    width: 400px;
    position: relative;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    background: rgba(28, 33, 49, 1);

    .component-header {
      border-bottom: 1px solid rgba(184, 184, 184, 0.1);
      height: 80px;

      .desc {
        font-size: 12px;
        text-indent: 35px;
        padding-top: 5px;
        color: rgba(138, 144, 153, 1);
      }

      .status-brief {
        font-size: 18px;
        color: #fff;
        vertical-align: sub;
      }

      .count {
        color: rgba(250, 100, 0, 1);
      }


      .icon-network-status {
        width: 24px;
        height: 24px;
        vertical-align: sub;

        &.poor {
          background-image: url(./assets/icon-network-status-poor.svg);
          background-repeat: no-repeat;
          background-size: contain;
        }

        &.good {
          background-image: url(./assets/icon-network-status-good.svg);
          background-repeat: no-repeat;
          background-size: contain;
        }

        &.break {
          background-image: url(./assets/icon-network-status-break.svg);
          background-repeat: no-repeat;
          background-size: contain;
        }
      }

      .monitor {
        width: 50px;
        height: 50px;
        vertical-align: middle;
        background-image: url(./assets/icon-monitor.svg);
        background-repeat: no-repeat;
        position: relative;
        display: inline-block;
      }

      .magnifier {
        width: 32px;
        height: 32px;
        background-image: url(./assets/icon-magnifier.svg);
        background-repeat: no-repeat;
        background-size: contain;
        position: absolute;
        bottom: 0;
        right: 0;
        animation: 4s rectangle infinite;
      }

      @keyframes rectangle {
        0% {
          transform: translate(0px, 0px);
        }

        25% {
          transform: translate(-10px, 0);
        }

        50% {
          transform: translate(-10px, -10px);
        }

        75% {
          transform: translate(0, -10px);
        }

        100% {
          transform: translate(0, 0);
        }
      }
    }

    .component-content {
      padding: 24px;
      flex: auto;

      .icon-loading {
        width: 17px;
        height: 17px;
        vertical-align: middle;
        display: inline-block;
        background-image: url(./assets/icon-loading.svg);
        background-repeat: no-repeat;
        background-size: contain;
        animation: loading-rotate 1s linear infinite;
        margin-right: 5px;
      }
    }

    .component-footer {
      border-top: 1px solid rgba(184, 184, 184, 0.1);
      flex: 1;
      display: flex;
      align-items: center;
      align-content: center;
      justify-content: center;

      .el-button+.el-button {
        margin-left: 20px;
      }

      button.btn-crystal {
        padding: 10px 14px;
        background: rgba(0, 0, 0, 0.1);
        color: #eee;
        border: none;
        transition: background-color 0.3s;
        font-size: 16px;
        margin-top: 16px;
        margin-bottom: 16px;

        &:hover {
          background: rgba(0, 0, 0, 0.3);
          color: #fff;

          .icon {
            opacity: 1;
          }
        }

        .icon {
          width: 16px;
          height: 16px;
          vertical-align: top;
          opacity: 0.8;
        }

      }
    }

    ul {
      display: flex;
      flex-direction: column;
      height: 100%;
      justify-content: space-around;

      li {
        width: 100%;
        font-size: 16px;
        display: flex;
        justify-content: space-between;
        line-height: 28px;

        .key {
          color: #8A9099;
        }

        .value {
          width: 30%;
          color: #fff;
          text-align: right;

          .success {
            color: rgba(39, 190, 76, 1)
          }

          .error {
            color: rgba(250, 100, 0, 1)
          }
        }
      }
    }
  }

}
</style>
