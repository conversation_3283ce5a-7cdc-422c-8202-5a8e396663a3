<template>
  <div class="mobile-bigclass-layout">
    <template v-if="isVideoOnlyClass">
      <big-class-portrait
        v-if="isPortrait"
        v-bind="{teacherVideo, studentVideos}"
      />
      <!-- 这里暂时放到和 big-class-landscape 同一级，如果后续横屏也需要自定义，则放到 big-class-landscape 下，与竖屏保持一致 -->
      <big-class-landscape-video-only
        v-else
        v-bind="{teacherVideo, studentVideos}"
      />
    </template>
    <template v-else>
      <big-class-portrait
        v-if="isPortrait"
        v-bind="{teacherVideo, studentVideos}"
      />
      <big-class-landscape
        v-else
        v-bind="{teacherVideo, studentVideos}"
      />
    </template>

    <!-- 非竖屏课时，老师不支持切换 -->
    <SwitchOrientationComponent v-if="!isWeb && !(!isPortraitClass && isTeacher) && canSwitchOridentation" :class-name="isPortrait ? 'is-portrait' : ''" />
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick, watch, provide, inject } from 'vue';
import _ from 'lodash';
import Constant from '@/util/Constant';
import SwitchOrientationComponent from '@vueComponent/videowrap-component/SwitchOrientationComponent';
import BigClassPortrait from './bigClass/bigClassPortrait.vue';
import BigClassLandscape from './bigClass/bigClassLandscape.vue';
import BigClassLandscapeVideoOnly from './bigClass/bigClassLandscapeVideoOnly.vue';
import i18next from 'i18next';

// State
const isPortrait = ref(TCIC.SDK.instance.isPortrait());
const isVideoOnlyClass = ref(TCIC.SDK.instance.isVideoOnlyClass());
const isWeb = ref(TCIC.SDK.instance.isWeb());
 // 非竖屏课时，老师不支持切换, 如果要切换，需要修改sdk setDeviceOrientation方法，那里拦截了
const isPortraitClass = ref(TCIC.SDK.instance.isPortraitClass());
const isTeacher = ref(TCIC.SDK.instance.isTeacher());
const teacherVideo = ref(null);
const studentVideos = ref([]);

// 小班纯视频课注入false, 不支持切换横竖屏
const canSwitchOridentation = inject('canSwitchOridentation', true);

// Watchers
watch(isPortrait, (newVal) => {
  // 解决横竖屏切换时PPT页面回到第一页问题
  nextTick(() => {
    const board = TCIC.SDK.instance.getBoard();
    board.refresh();
  });
});

// Methods
const handleAVAdd = (info) => {
  const classInfo = TCIC.SDK.instance.getClassInfo();
  if (classInfo.teacherId === info.userId) {
    TCIC.SDK.instance.loadComponent('teacher-component', {
      left: '0',
      top: '0',
      display: 'block',
      position: 'relative',
    }).then(() => {
      teacherVideo.value = info;
    });
  } else {
    TCIC.SDK.instance.loadComponent('student-component', {
      left: '0',
      top: '0',
      display: 'block',
      position: 'relative',
    }, null, info.userId)
      .then((ele) => {
        if (ele) {
          studentVideos.value.push(info);
        }
      });
  }
};

const handleAVRemove = (info) => {
  const classInfo = TCIC.SDK.instance.getClassInfo();
  if (info.userId === TCIC.SDK.instance.getUserId()) {
    window.showToast(i18next.t('你已下台，暂时无法参与音视频互动~'));
  }
  if (classInfo.teacherId === info.userId) {
    const teacherDom = TCIC.SDK.instance.getComponent('teacher-component');
    if (teacherDom) {
      teacherVideo.value = null;
      // 大班及 1v1 小班课老师离开时不移除节点
      // TCIC.SDK.instance.removeComponent('teacher-component');
    }
  } else {
    const studentDom = TCIC.SDK.instance.getComponent('student-component', info.userId);
    if (studentDom) {
      const idx = studentVideos.value.findIndex(student => student.userId === info.userId);
      TCIC.SDK.instance.removeComponent('student-component', info.userId);
      studentVideos.value.splice(idx, 1);

      setTimeout(() => {
        const screenPlayerComponent = TCIC.SDK.instance.getComponent('screen-player-component');
        if (screenPlayerComponent && !TCIC.SDK.instance.isWeb()) {
          screenPlayerComponent.getVueInstance().onResize();
        }
      }, 100);
    }
  }
};
const showQuickMsg = () => nextTick(() => {
  TCIC.SDK.instance.updateComponent('quickmsg-show-component', {
    display: 'block',
    height: 'calc(100% - 40px - env(safe-area-inset-bottom))',
  });
});

// 给自定义 UI 用
provide('showQuickMsg', showQuickMsg);

// Lifecycle hooks
onMounted(() => {
  const layoutType = _.get(TCIC.SDK.instance.getState(Constant.TStateBigClassLayout), 'mobile');
  const isCustomLayout = layoutType === 'custom';
  // 自定义布局下，默认不展示 quickmsg，让用户自己决定
  if (!isCustomLayout) {
    showQuickMsg();
  }
});

// 父组件负责维护 video列表和创建/销毁 dom, 子组件只负责根据列表插到父元素里面
TCIC.SDK.instance.on(TCIC.TMainEvent.AV_Add, handleAVAdd);
TCIC.SDK.instance.on(TCIC.TMainEvent.AV_Remove, handleAVRemove);

// Orientation change listener
TCIC.SDK.instance.subscribeState(TCIC.TMainState.Device_Orientation, (orientation) => {
  console.log('orientation change', orientation);
  isPortrait.value = orientation === TCIC.TDeviceOrientation.Portrait;
  const screenPlayerComponent = TCIC.SDK.instance.getComponent('screen-player-component');
  if (screenPlayerComponent?.getVueInstance().isScreenShareOpen) {
    TCIC.SDK.instance.reportLog('setSubStreamEncoderParam', {
      resMode: orientation,
      layout: 'bigClass',
    });
    TCIC.SDK.instance.setSubStreamEncoderParam({
      resMode: orientation,
    });
  }
});

// 大班课默认始终展示老师
handleAVAdd({
  userId: TCIC.SDK.instance.getClassInfo().teacherId,
  useName: TCIC.SDK.instance.getClassInfo().teacherName,
  stage: true,
});


onBeforeUnmount(() => {
  TCIC.SDK.instance.off(TCIC.TMainEvent.AV_Add, handleAVAdd);
  TCIC.SDK.instance.off(TCIC.TMainEvent.AV_Remove, handleAVRemove);
});
</script>

<style lang="less">
.header-component{
  --header-background-color: transparent;
}
.mobile-bigclass-layout {
  height: 100%;
  .footer-component-default {
    overflow: visible;
    height: 40px;
  }
  .vcp-player{
    background-color: transparent;
  }
}
</style>
