<template>
  <Component
    :is="customLayout"
    v-if="layout === 'custom' && !!customLayout"
    :videos="JSON.stringify({teacherVideo, studentVideos})"
  />
  <div
    v-else-if="isVideoOnlyClass"
    class="big-class-portrait"
  >
    <bigClassPortraitVideoOnly
      v-bind="{teacherVideo, studentVideos}"
    />
  </div>
  <div
    v-else
    class="big-class-portrait"
  >
    <bigClassPortraitDefault
      v-if="layout === 'videoTopLayout'"
      v-bind="{teacherVideo, studentVideos}"
    />
    <bigClassPortraitBoard
      v-else
      v-bind="{teacherVideo, studentVideos}"
    />
  </div>
</template>

<script setup>
import { ref, watch, onBeforeMount } from 'vue';
import bigClassPortraitDefault from './portrait/bigClassPortraitDefault.vue';
import bigClassPortraitBoard from './portrait/bigClassPortraitBoard.vue';
import bigClassPortraitVideoOnly from './portrait/bigClassPortraitVideoOnly.vue';
import _ from 'lodash';
import Constant from '@/util/Constant';

// videoTopLayout | boardTopLayout | custom
const layout = ref(TCIC.SDK.instance.getState(Constant.TStateBigClassLayout, 'videoTopLayout'));
const customLayout = ref(null);
const isVideoOnlyClass = TCIC.SDK.instance.isVideoOnlyClass();

onBeforeMount(() => {
  layout.value = _.get(TCIC.SDK.instance.getState(Constant.TStateBigClassLayout), 'mobile', 'videoTopLayout');
  customLayout.value = _.get(TCIC.SDK.instance.getState(Constant.TStateBigClassLayout), 'customLayoutName', '');
  TCIC.SDK.instance.reportLog('layout-component:bigClassPortrait', layout.value);
});

const props = defineProps({
  teacherVideo: Object,
  studentVideos: Array,
});
</script>

<style scoped>
.big-class-portrait {
  width: 100%;
  height: 100%;
}
</style>
