<template>
  <div class="big-class-landscape">
    <div
      class="bigclass-left"
    >
      <BoardWrap />
      <div
        ref="footerArea"
        class="footer-area"
      />
    </div>
    <div
      ref="videoArea"
      class="bigclass-right"
    />
  </div>
</template>
<script>
import BoardWrap from './components/BoardWrap.vue';

export default {
  name: 'BigClassLandscape',
  components: {
    BoardWrap,
  },
  inject: ['showQuickMsg'],
  props: {
    teacherVideo: Object,
    studentVideos: Array,
  },
  data() {
    return {};
  },
  watch: {
    // 当两者变化时更新布局
    teacherVideo() {
      this.initVideos();
    },
    studentVideos() {
      this.initVideos();
    },
  },
  mounted() {
    this.initLayout();
    this.initVideos();
    this.showQuickMsg();
  },
  methods: {
    initLayout() {
      this.$nextTick(() => {
        TCIC.SDK.instance.getComponent('quickmsg-show-component').getVueInstance().quickMsgVisible = true;
        TCIC.SDK.instance.loadComponent('footer-component', {
          left: '0',
          top: 'calc(100% - 40px)',
          zIndex: 10,
          width: '100%',
          height: '40px',
          display: 'block',
        })
          .then((ele) => {
            this.$refs.footerArea.appendChild(ele);
            const footerVue = TCIC.SDK.instance.getComponent('footer-component').getVueInstance();
            footerVue.isOneOnOneVideoClassOrOneOnOneBigClass = true;
            footerVue.disableQuickIM = false;
          });
      });
    },
    initVideos() {
      this.$nextTick(() => {
        if (this.teacherVideo) {
          TCIC.SDK.instance.updateComponent('teacher-component', {
            left: '0',
            top: '0',
            width: '160px',
            height: '80px',
            display: 'block',
            position: 'relative',
          }).then(() => {
            const ele = TCIC.SDK.instance.getComponent('teacher-component');
            if (ele) {
              this.$refs.videoArea.appendChild(ele);
            }
          });
        }
        this.studentVideos.forEach((info) => {
          TCIC.SDK.instance.updateComponent('student-component', {
            left: '0',
            top: '0',
            width: '160px',
            height: '80px',
            display: 'block',
            position: 'relative',
          }, info.userId).then(() => {
            const studentDom = TCIC.SDK.instance.getComponent('student-component', info.userId);
            if (studentDom) {
              this.$refs.videoArea.appendChild(studentDom);
            }
          });
        });
      });
    },
  },
};
</script>
<style lang="less">
.big-class-landscape {
  width: 100%;
  height: 100%;
  display: flex;
  .bigclass-left{
    width: calc(100% - 163px);
    height: 100%;
    position: relative;
  }
  .bigclass-right{
    width: 160px;
    margin-left: 3px;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 3px;
    position: relative;
    student-component{
      width: 160px!important;
      height: 80px!important;
    }
  }
}
</style>
