<template>
  <div class="big-class-landscape">
    <div
      ref="videoArea"
      class="bigclass-video"
      :style="{
        '--video-width': `${videoSize.videoWidth}px`,
        '--video-height': `${videoSize.videoHeight}px`,
      }"
    >
      <div
        ref="footerArea"
        class="footer-area"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick, computed, inject } from 'vue';
import { useElementSize } from '@vueuse/core';
import { useVideoSize } from '../../hooks/useVideoSize.js';
// Refs
const videoArea = ref(null);
const footerArea = ref(null);
const videoCount = ref(props.studentVideos.length + 1);
const videoSize = useVideoSize(videoArea, videoCount);
const showQuickMsg = inject('showQuickMsg');

// Props
const props = defineProps({
  teacherVideo: Object,
  studentVideos: Array,
});

// Watchers
watch(
  () => [props.teacherVideo, props.studentVideos],
  () => {
    initVideos();
    videoCount.value = props.studentVideos.length + 1; // teacher 常驻，所以加一
  },
  { deep: true },
);

// Lifecycle Hooks
onMounted(() => {
  initLayout();
  initVideos();
  showQuickMsg();
  TCIC.SDK.instance.loadComponent('quickmsg-show-component').then((ele) => {
    ele.getVueInstance().quickMsgVisible = true;
  });
});

// Methods
const initLayout = () => {
  nextTick(() => {
    TCIC.SDK.instance.getComponent('quickmsg-show-component').getVueInstance().quickMsgVisible = true;
    const screenPlayerComponent = TCIC.SDK.instance.getComponent('screen-player-component');
    videoArea.value.appendChild(screenPlayerComponent);
    TCIC.SDK.instance.loadComponent('footer-component', {
      left: '0',
      top: 'calc(100% - 40px)',
      zIndex: 10,
      width: '100%',
      height: '40px',
      display: 'block',
    }).then((ele) => {
      footerArea.value.appendChild(ele);
      const footerVue = TCIC.SDK.instance.getComponent('footer-component').getVueInstance();
      footerVue.isOneOnOneVideoClassOrOneOnOneBigClass = true;
      footerVue.disableQuickIM = false;
    });
  });
};

const initVideos = () => {
  nextTick(() => {
    if (props.teacherVideo) {
      TCIC.SDK.instance.updateComponent('teacher-component', {
        left: '0',
        top: '0',
        width: 'var(--video-width)',
        height: 'var(--video-height)',
        display: 'block',
        position: 'relative',
      }).then(() => {
        const ele = TCIC.SDK.instance.getComponent('teacher-component');
        if (ele) {
          videoArea.value.appendChild(ele);
        }
      });
    }

    props.studentVideos.forEach((info) => {
      TCIC.SDK.instance.updateComponent('student-component', {
        left: '0',
        top: '0',
        width: 'var(--video-width)',
        height: 'var(--video-height)',
        display: 'block',
        position: 'relative',
      }, info.userId).then(() => {
        const studentDom = TCIC.SDK.instance.getComponent('student-component', info.userId);
        if (studentDom) {
          videoArea.value.appendChild(studentDom);
        }
      });
    });
  });
};
</script>

<style lang="less">
.big-class-landscape {
  width: 100%;
  height: 100%;
  .bigclass-video{
    width: calc(100% - env(safe-area-inset-left));
    margin-left: env(safe-area-inset-left);
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
  }
}
</style>
