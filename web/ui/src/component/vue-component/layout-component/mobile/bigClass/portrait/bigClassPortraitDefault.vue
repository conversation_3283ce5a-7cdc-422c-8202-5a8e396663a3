<template>
  <div class="big-class-portrait">
    <div
      ref="videoArea"
      class="video-area"
      :style="{ '--video-width': videoWidth }"
    />
    <div
      v-show="boardMounted"
      ref="tabArea"
      :class="['tab-area', {
        'show-reply': !!replyMsg
      }]"
    >
      <el-tabs
        v-model="activeName"
        @tab-click="handleClick"
      >
        <el-tab-pane
          v-for="item in tabs"
          :key="item.name"
          :ref="item.name"
          :name="item.name"
          :label="renderLabel(item)"
        >
          <div
            v-if="item.name === 'board'"
          >
            <div
              class="board-body"
            >
              <BoardWrap />
            </div>
            <div
              ref="boardFooter"
              class="board-footer"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div
      ref="footerArea"
      class="footer-area"
    />
    <IMFooter
      @update-reply-msg="setReplyMsg"
    />
  </div>
</template>
<script>
import baseComponent from '@core/BaseComponent';
// TODO: 改成从 IMFooter 引入
import IMFooter from '@/component/vue-component/im-component/IMFooter.vue';
import BoardWrap from '../components/BoardWrap.vue';
export default {
  name: 'BigClassPortrait',
  components: {
    IMFooter,
    BoardWrap,
  },
  extends: {
    baseComponent,
  },
  props: {
    teacherVideo: Object,
    studentVideos: Array,
  },
  data() {
    return {
      boardMounted: false,
      activeName: 'board',
      unreadCount: 0,
      replyMsg: null,
      tabs: [
        {
          name: 'board',
          label: '互动白板',
        },
        {
          name: 'chat',
          label: '聊天',
        },
      ],
    };
  },
  computed: {
    videoWidth() {
      return `calc(100% / ${this.videoCount})`;
    },
    videoCount() {
      return this.studentVideos.length + (this.teacherVideo === null ? 0 : 1);
    },
  },
  watch: {
    teacherVideo() {
      this.initVideos();
    },
    studentVideos() {
      this.initVideos();
    },
  },
  mounted() {
    this.initLayout();
    TCIC.SDK.instance.subscribeState(TCIC.TMainState.Message_Unread_Count, this.messageUnreadCountChangeHandler);
    this.initVideos();
  },
  methods: {
    setReplyMsg(msg) {
      this.replyMsg = msg;
    },
    initLayout() {
      this.$nextTick(() => {
        console.log('initLayout', this.$refs);
        this.boardMounted = true;

        TCIC.SDK.instance.loadComponent('footer-component', {
          left: '0',
          top: '0',
          width: '100%',
          height: '40px',
          zIndex: 11,
          display: 'block',
        })
          .then((ele) => {
            this.$refs.boardFooter[0].appendChild(ele);
            const footerVue = TCIC.SDK.instance.getComponent('footer-component').getVueInstance();
            footerVue.isOneOnOneVideoClassOrOneOnOneBigClass = true;
            // 展示 quickIM 会导致未读消息数量不生效
            footerVue.disableQuickIM = true;
          })
          .catch((err) => {
            console.log('initLayout', err);
          });
        TCIC.SDK.instance.loadComponent('portrait-im-component', {
          left: '0',
          top: '0',
          width: '100%',
          height: '100%',
          display: 'block',
        })
          .then((ele) => {
            this.$refs.chat[0].$el.appendChild(ele);
            if (!this.portraitIMVue) {
              const portraitIMVue = TCIC.SDK.instance.getComponent('portrait-im-component').getVueInstance();
              this.portraitIMVue = portraitIMVue;
            }
            this.portraitIMVue.showIMFooter = false;
          });
      });
    },

    initVideos() {
      this.$nextTick(() => {
        if (this.teacherVideo) {
          TCIC.SDK.instance.updateComponent('teacher-component', {
            left: '0',
            top: '0',
            width: 'var(--video-width)',
            height: '100%',
            display: 'block',
            position: 'relative',
          }).then(() => {
            const ele = TCIC.SDK.instance.getComponent('teacher-component');
            if (ele) {
              this.$refs.videoArea.appendChild(ele);
            }
          });
        }
        this.studentVideos.forEach((info) => {
          TCIC.SDK.instance.updateComponent('student-component', {
            left: '0',
            top: '0',
            width: 'var(--video-width)',
            height: '100%',
            display: 'block',
            position: 'relative',
          }, info.userId).then(() => {
            const studentDom = TCIC.SDK.instance.getComponent('student-component', info.userId);
            if (studentDom) {
              this.$refs.videoArea.appendChild(studentDom);
            }
          });
        });
      });
    },
    renderLabel(item) {
      if (item.name === 'chat') {
        return `${item.label}${!this.unreadCount ? '' : `(${this.unreadCount})`}`;
      }
      return item.label;
    },
    messageUnreadCountChangeHandler(count) {
      this.unreadCount = count;
    },
    handleClick(tab) {
      if (tab.name === 'chat') {
        this.portraitIMVue.notifyVisibilityChange(true);
        TCIC.SDK.instance.getComponent('quickmsg-show-component').getVueInstance().quickMsgVisible = false;
      } else {
        this.portraitIMVue.notifyVisibilityChange(false);
        TCIC.SDK.instance.getComponent('quickmsg-show-component').getVueInstance().quickMsgVisible = true;
      }
      if (tab.name === 'board') {
        const tEduBoard = TCIC.SDK.instance.getBoard();
        tEduBoard.refresh();
      }
    },
  },
};
</script>

<style lang="less">
.big-class-portrait{
  height: 100%;
  .portrait-im-component{
    background-color: transparent!important;
    .im-component-body {
      padding-bottom: 0;
    }
  }
  .video-area {
    width: 100%;
    height: 30%;
    gap: 2px;
    position: relative;
    display: flex;
    student-component {
      order: 2;
    }
    teacher-component {
      order: 1;
    }
  }
  .tab-area{
    height: calc(70% - 93px);
    &.show-reply{
      height: calc(70% - 124px);
    }
  }
  .el-tabs__nav-scroll {
    display: flex;
    justify-content: center;
  }

  .tab-area .el-tabs__item.is-active{
      color: rgba(255,255,255,0.9)
  }

  .tab-area .el-tabs__item{
      color: rgba(255,255,255,0.55)
  }

  .tab-area  .el-tabs__nav-wrap::after{
      background-color: #3A3C42;
  }

  .tab-area .el-tabs__active-bar{
      background-color: rgba(255,255,255,0.9);
  }
  .el-tabs, .el-tab-pane {
    height: 100%;
  }
  .el-tab-pane>div {
    height: 100%;
  }
  .el-tabs__content {
    height: calc(100% - 55px);
  }
  .board-body {
    height: calc(100% - 80px);
    position: relative;
  }
  .board-footer {
    height: 40px;
    position: relative;
    .quick-im-component{
      display: none;
    }
    .footer-component.small-screen.is-none {
      display: flex !important; // 覆盖样式
    }
  }
  .footer-area {
    height: 40px;
    display: none;
    position: relative;
  }
}
</style>
