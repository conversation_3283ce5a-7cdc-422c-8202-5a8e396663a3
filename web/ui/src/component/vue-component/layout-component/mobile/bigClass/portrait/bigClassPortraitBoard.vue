<template>
  <div class="big-class-portrait-board">
    <div class="class-header">
      <div
        class="board-area"
        :style="{width: videoShow ? 'calc(100% - 115px)' : '100%'}"
      >
        <BoardWrap />
      </div>
      <div
        class="video-area"
        :style="{transform: videoShow ? 'translateX(0)' : 'translateX(115px)'}"
      >
        <div
          class="video-toggle"
          @click="toggleShow"
        >
          <i :class="videoShow ? 'close' : ''" />
        </div>
        <div
          ref="videoAreaRef"
          class="video-area-inner"
        />
      </div>
    </div>
    <div
      ref="footerAreaRef"
      class="footer-area"
    />
    <div
      class="im-area"
    >
      <PortraitIMWrap />
    </div>
  </div>
</template>
<script setup>
import { computed, onMounted, ref, watch, nextTick } from 'vue';
import BoardWrap from '../components/BoardWrap.vue';
import PortraitIMWrap from '../components/PortraitIMWrap.vue';

const videoAreaRef = ref();
const footerAreaRef = ref();

const videoShow = ref(false);

const props = defineProps({
  teacherVideo: Object,
  studentVideos: Array,
});

const toggleShow = () => {
  videoShow.value = !videoShow.value;
};

onMounted(() => {
  nextTick(() => {
    TCIC.SDK.instance.loadComponent('footer-component', {
      left: '0',
      top: '0',
      zIndex: 11,
      width: '100%',
      height: '40px',
      display: 'block',
    })
      .then((ele) => {
        footerAreaRef.value.appendChild(ele);
        const footerVue = TCIC.SDK.instance.getComponent('footer-component').getVueInstance();
        footerVue.isOneOnOneVideoClassOrOneOnOneBigClass = true;
        // 展示 quickIM 会导致未读消息数量不生效
        footerVue.disableQuickIM = true;
      })
      .catch((err) => {
        console.log('initLayout', err);
      });
  });
  initVideos({ teacherVideo: props.teacherVideo, studentVideos: props.studentVideos });
});

const initVideos = ({ teacherVideo, studentVideos }) => {
  console.warn('bigClass-video-sync::initVideos', teacherVideo, studentVideos);
  const promiseArr = [];
  if (teacherVideo) {
    promiseArr.push(TCIC.SDK.instance.updateComponent('teacher-component', {
      left: '0',
      top: '0',
      width: '115px',
      height: '80px',
      display: 'block',
      position: 'relative',
    }).then(() => {
      const ele = TCIC.SDK.instance.getComponent('teacher-component');
      if (ele) {
        videoAreaRef.value?.appendChild(ele);
      }
    }));
  }
  studentVideos?.forEach((info) => {
    promiseArr.push(TCIC.SDK.instance.updateComponent('student-component', {
      left: '0',
      top: '0',
      width: '115px',
      height: '80px',
      display: 'block',
      position: 'relative',
    }, info.userId).then(() => {
      const studentDom = TCIC.SDK.instance.getComponent('student-component', info.userId);
      if (studentDom) {
        videoAreaRef.value?.appendChild(studentDom);
      }
    }));
  });
  return Promise.all(promiseArr);
};

watch(
  [() => props.teacherVideo,
    () => props.studentVideos,
  ],
  ([teacherVideo, studentVideos]) => {
    initVideos({ teacherVideo, studentVideos });
  },
  {
    deep: true,
  },
);

</script>

<style lang="less">
.big-class-portrait-board{
  height: 100%;
  display: flex;
  flex-direction: column;
  .portrait-im-component{
    background-color: transparent!important;
  }
  .class-header{
    height: calc(100vw * 9 / 16);
    .board-area{
      width: 100%;
      height: 100%;
      position: relative;
    }
    .video-area {
      width: 130px;
      height: calc(100vw * 9 / 16);
      z-index: 10;
      top: 0;
      right: 0;
      position: absolute;
      display: flex;
      align-items: center;
      .video-toggle{
        height: 40px;
        width: 15px;
        background: #222329;
        border-radius: 2px 0 0 2px;
        i{
          background-repeat: no-repeat;
          background-image: url(@/component/vue-component/videowrap-component/assets/icon_right.svg);
          background-position: center;
          margin-top: 12.5px;
          transform: rotate(180deg);
          transition: transform .3s;
          width: 15px;
          height: 15px;
          display: block;
          &.close{
            transform: rotate(0);
          }
        }
      }
      .video-area-inner{
        display: flex;
        flex-direction: column;
        height: 100%;
        background: var(--primary-color);
        student-component {
        order: 2;
        }
        teacher-component {
          order: 1;
        }
      }

    }
  }
  .im-area{
    flex: 1;
    position: relative;
  }
  .board-footer {
    height: 40px;
    position: relative;
    .quick-im-component{
      display: none;
    }
    .footer-component.small-screen.is-none {
      display: flex !important; // 覆盖样式
    }
  }
  .footer-area {
    height: 40px;
    display: block;
    position: relative;
  }
}
</style>
