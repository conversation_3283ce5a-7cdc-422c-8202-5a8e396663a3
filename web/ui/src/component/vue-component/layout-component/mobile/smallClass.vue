<template>
  <!-- 小班课1v1 1v0 复用大班课布局 -->
  <MobileBigClassLayout v-if="isOneOnOneClass || isOneOnZeroClass" />
  <div v-else>unsupported</div>
</template>

<script setup>
import { ref, provide } from 'vue';
import MobileBigClassLayout from './bigClass.vue';
const isOneOnOneClass = TCIC.SDK.instance.isOneOnOneClass();
const isOneOnZeroClass = TCIC.SDK.instance.getClassInfo().maxRtcMember === 0;
provide('canSwitchOridentation', false);
</script>
<style lang="less" scoped>
</style>
