import { computed, nextTick, onMounted, ref, watch } from 'vue';

import { useElementSize } from '@vueuse/core';

export const useVideoSize = (containerRef, videoCountRef) => {
    const videoSize = ref({ videoWidth: 0, videoHeight: 0 });
    const updateVideoSize = () => {
      nextTick(() => {
        const { width: containerWidth, height: containerHeight } = useElementSize(containerRef);
        console.log('containerWidth', containerWidth.value, 'containerHeight', containerHeight.value, videoCountRef.value);
        if (videoCountRef.value <= 1) {
          // 撑满高度
          videoSize.value = {
            videoWidth: (containerHeight.value) * 16 / 9,
            videoHeight: containerHeight.value,
          };
        } else {
          // 撑满宽度
          const videoWidth = containerWidth.value / videoCountRef.value;
          const videoHeight = videoWidth * 9 / 16;
          videoSize.value = {
            videoWidth,
            videoHeight,
          };
        }
      });
    };
    onMounted(() => {
      // fix 获取 container 宽高为 0 的问题
      updateVideoSize();
    });
    watch(videoCountRef, updateVideoSize);
    return videoSize;
};

