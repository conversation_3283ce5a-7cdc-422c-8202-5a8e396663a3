<template>
  <div
    v-if="isShow"
    ref="imagePreview"
    class="inComponent inMoreComponent image-preview fill-parent"
    :class="mobileAnimationClass"
    :style="{ 'z-index': zIndex }"
  >
    <!-- 手机端 -->
    <div
      v-if="isSmallScreen"
      class="image-preview__mobile-wrap fill-parent"
    >
      <!-- 横屏 -->
      <div
        v-if="isLandscape"
        class="fill-parent"
      >
        <img
          ref="centerImage"
          v-loading="loading"
          :src="imageSrc"
          element-loading-background="transparent"
          element-loading-customClass="el-loading"
          :element-loading-text="$t('图片加载中')"
          class="landscape-image"
          @load="loading = false"
          @error="loading = false"
        >
        <div class="landscape-toolbar">
          <el-button
            type="primary"
            icon="el-icon-arrow-left"
            class="back-button"
            @click="close()"
            @touchstart="close()"
          />
          <button
            class="download-button"
            @click="isMobileDevice && !isDownloading ? '' : download()"
            @touchstart="isMobileDevice && !isDownloading ? download() : ''"
          >
            <i class="download-icon" />
          </button>
        </div>
      </div>
      <!-- 竖屏 -->
      <div
        v-else

        class="fill-parent"
        style="display: flex;"
      >
        <img
          ref="centerImage"
          v-loading="loading"
          element-loading-background="transparent"
          element-loading-customClass="el-loading"
          :element-loading-text="$t('图片加载中')"
          :src="imageSrc"
          class="portrait-image"
          @load="loading = false"
          @error="loading = false"
        >
        <el-button
          type="primary"
          icon="el-icon-arrow-left"
          class="back-button portrait-back-button"
          @click="close()"
        />
        <div
          class="portrait-download"
          :style="downloadStyle"
          @click="isMobileDevice && !isDownloading? '' : download()"
          @touchstart="isMobileDevice && !isDownloading ? download() : ''"
        >
          <div style="height:8px;" />
          <span class="download-text">{{ $t('下载图片') }}</span>
        </div>
      </div>
    </div>

    <!-- PC、Pad端 -->
    <div
      v-else
      class="image-preview__pc-wrap"
    >
      <div
        ref="pcContent"
        v-clickoutside="close"
        class="image-preview__pc-content"
      >
        <div
          v-loading="loading"
          element-loading-background="transparent"
          element-loading-customClass="el-loading"
          :element-loading-text="$t('图片加载中')"
        >
          <img
            ref="centerImage"
            :src="imageSrc"
            :style="imageStyle"
            class="center-image"
            @load="loading = false"
            @error="loading = false"
          >
        </div>
        <div
          style="text-align: center;"
          @click="clickToolbarOutside"
        >
          <div
            id="image-preview-toolbar"
            class="toolbar"
          >
            <el-tooltip
              class="tooltip"
              effect="dark"
              :content="$t('添加到白板')"
              placement="bottom"
            >
              <i
                v-if="showAddBoard"
                class="add-button"
                @click="isMobileDevice ? '' : addToBoard()"
                @touchstart="isMobileDevice ? addToBoard() : ''"
              />
            </el-tooltip>
            <el-tooltip
              class="tooltip"
              effect="dark"
              :content="$t('下载')"
              placement="bottom"
            >
              <i
                class="download-button"
                @click="isMobileDevice && !isDownloading ? '' : download()"
                @touchstart="isMobileDevice && !isDownloading ? download() : ''"
              />
            </el-tooltip>
            <span class="split-line" />
            <el-tooltip
              class="tooltip"
              effect="dark"
              :content="$t('关闭')"
              placement="bottom"
            >
              <i
                class="close-button"
                @click="isMobileDevice ? '' : close()"
                @touchstart="isMobileDevice ? close() : ''"
              />
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18next from 'i18next';
import Lodash from 'lodash';
import BaseComponent from '@core/BaseComponent';
import { PopupManager } from 'element-ui/lib/utils/popup';
import Clickoutside from 'element-ui/src/utils/clickoutside';
import ImageCache from './ImageCache.js';
const TEduBoard = window.TEduBoard;

export default {
  name: 'ImagePreview',
  directives: { Clickoutside },
  extends: BaseComponent,
  data() {
    return {
      isTeacher: false,
      isShow: false,
      inclose: false,
      thumbUrl: this.thumb,
      srcUrl: this.src,
      srcLocalUrl: '',
      zIndex: 2,
      imageStyle: 'width: 66.667%;height: 66.667%',
      deviceOrientation: TCIC.TDeviceOrientation.Portrait,
      isMobileDevice: false, // 是否是移动设备
      isDownloading: false, // 是否在下载
      imgId: 0, // 当前图片本地id
      loading: true,
      serverImgSeq: 0,
      delayTimer: null,
      showAddBoard: true,
    };
  },
  computed: {
    imageSrc() {
      if (this.thumbUrl.length > 0 && this.srcLocalUrl.length === 0) {
        return this.thumbUrl;
      }
      if (this.srcLocalUrl.length > 0) {
        return this.srcLocalUrl;
      }
      return this.srcUrl;
    },
    isLandscape() {
      return this.deviceOrientation === TCIC.TDeviceOrientation.Landscape;
    },
    mobileAnimationClass() {
      if (!this.isSmallScreen) {
        return '';
      }
      if (this.inclose) {
        return 'mobile-close-animation';
      }
      return this.isShow ? 'mobile-show-animation' : 'mobile-close-animation';
    },
    downloadStyle() {
      const left = document.body.clientWidth / 2 - 64;
      return `left:${left}px;`;
    },
  },
  mounted() {
    document.body.appendChild(this.$el);
    this.makeSureClassJoined(this.onJoinClass);

    this.isMobileDevice = TCIC.SDK.instance.isMobile();
    this.isDownloading = false;
    this.addLifecycleTCICStateListener(
      TCIC.TMainState.Device_Orientation,
      (orientation) => {
        this.deviceOrientation = orientation;
      },
    );
    TCIC.SDK.instance
      .promiseState(TCIC.TMainState.Board_Create, true)
      .then(() => {
        this.teduBoard = TCIC.SDK.instance.getBoard();
        // 白板已经创建
        this.initBoardEvent();
      });
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Back_Pressed, () => {
      if (!this.inclose) {
        this.close();
      }
    });
  },
  destroyed() {
    if (this.$el && this.$el.parentNode) {
      this.$el.parentNode.removeChild(this.$el);
    }
  },

  methods: {
    onJoinClass() {
      this.isTeacher = TCIC.SDK.instance.isTeacher();
    },

    initBoardEvent() {
      this.teduBoard = TCIC.SDK.instance.getBoard();
      this.teduBoard.on(TEduBoard.EVENT.TEB_ADDELEMENT, this.onAddElement);
    },

    onAddElement(id, userData) {
      console.log('onAddElement id:', id, userData);
    },

    updateImageStyle(imgWidth, imgHeight) {
      const ratio = 0.66666667;
      const h = (imgHeight * (document.body.clientWidth * ratio)) / Math.max(1.0, imgWidth);
      let style = '';
      if (h > document.body.clientHeight * ratio) {
        const height = Math.ceil(document.body.clientHeight * ratio);
        const width = Math.ceil((imgWidth * height) / Math.max(1.0, imgHeight));
        style = `height:${height}px;width: ${width}px;`;
      } else {
        const width = Math.ceil(document.body.clientWidth * ratio);
        style = `width:${width}px;height: auto;`;
      }
      if (this.imageStyle !== style) {
        this.imageStyle = style;
      }
    },

    loadImageSize(src) {
      const img = new Image();
      const that = this;
      img.onload = function () {
        that.updateImageStyle(img.width, img.height);
      };
      img.src = src;
    },

    loadSrc() {
      if (this.srcUrlIsBase64()) {
        this.loadImageSize(this.srcUrl);
        return;
      }
      const that = this;
      ImageCache.getImage(this.srcUrl).then(
        (blob) => {
          // 缓存读取成功
          that.srcLocalUrl = window.URL.createObjectURL(blob);
          that.loadImageSize(that.srcLocalUrl);
        },
        (e) => {
          ImageCache.getImgBlobByXHR(this.srcUrl).then((blob) => {
            that.srcLocalUrl = window.URL.createObjectURL(blob);
            that.loadImageSize(that.srcLocalUrl);
          }, (error) => {
            that.loadImageSize(that.srcUrl);
          });
        },
      );
    },
    addToBoard() {
      if (this.loading) {
        window.showToast(i18next.t('正在加载，请稍后'), 'error');
        return;
      }
      if (!this.teduBoard) return;
      const imgUrl = this.srcUrl;
      this.teduBoard.addElement(
        TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_IMAGE,
        imgUrl,
        { left: '2px', top: '2px' }, // 解决图片靠边后框选失败的问题
      );
      this.close();
    },
    async saveImageByBase64(base64) {
      try {
        let ext = base64.indexOf('data:image/png') >= 0 ? '.png' : '.jpg';
        if (base64.indexOf('data:image/gif') >= 0) {
          ext = '.gif';
        }
        const fileName = i18next.t('{{arg_0}} _图片{{ext}}', { arg_0: TCIC.SDK.instance.getClassInfo().className, ext });
        const timestamp = new Date().getTime();
        const res = await TCIC.SDK.instance.saveSnapshot(`downloadImage_${timestamp}`, base64, fileName, 1024 * 500, true);
        this.isDownloading = false;
        window.showToast(i18next.t('图片下载成功'), 'succcess');
        // console.log('downloadImage success', res);
      } catch (err) {
        this.isDownloading = false;
        let errMsg = i18next.t('图片下载失败');
        if (TCIC.SDK.instance.isMobileNative() && err.errcode) {
          if (err.errcode === -1) {
            errMsg = i18next.t('图片下载失败，请授予相册访问权限');
          } else if (err.errcode === -3) {
            errMsg = i18next.t('数据解码失败，请重试');
          } else if (err.errcode === -4) {
            errMsg = i18next.t('图片下载失败，无法写入相册');
          }
        }
        window.showToast(errMsg, 'error');
        console.error('downloadImage error', err);
        TCIC.SDK.instance.reportLog(
          'downloadImage',
          `saveImageByBase64 error, base64: ${base64.slice(0, 30)}, errcode: ${err?.errcode}, errmsg: ${err?.errmsg || err?.message}`,
        );
      }
    },
    download: Lodash.throttle(
      async function () {
        if (this.loading) {
          window.showToast(i18next.t('正在加载，请稍后'), 'error');
          return;
        }
        if (this.isDownloading) {
          return;
        }
        this.isDownloading = true;
        if (this.srcUrlIsBase64()) {
          this.saveImageByBase64(this.srcUrl);
        } else if (this.srcLocalUrl.length > 0) {
          const blob = await ImageCache.getImgBlobByXHR(this.srcLocalUrl);
          ImageCache.blobToBase64(blob).then((base64) => {
            this.saveImageByBase64(base64);
          });
        } else {
          const image = new Image();
          image.crossOrigin = '';
          image.src = this.srcUrl;
          image.onload = () => {
            // 回调函数返回base64值
            const base64 = this.image2Base64(image);
            this.saveImageByBase64(base64);
          };
          image.onerror = (err) => {
            this.isDownloading = false;
            window.showToast(i18next.t('图片下载失败'), 'error');
            console.error('downloadImage error', err);
            TCIC.SDK.instance.reportLog(
              'downloadImage',
              `downloadImage error, url: ${this.srcUrl}, errmsg: ${err?.message}`,
            );
          };
        }
      },
      2000,
      {
        leading: true,
        trailing: false,
      },
    ),
    close() {
      this.delayTimer = setTimeout(() => {
        // 延时清除返回键状态(避免触发)
        this.delayTimer = null;
        TCIC.SDK.instance.ignoreBackPressed(false);
      }, 500);
      this.$emit('close-preview-img', this.imgId);
      this.inclose = true;
      if (!this.isSmallScreen) {
        if (this.$refs && this.$refs.pcContent) {
          this.$refs.pcContent.style = 'animation:closeFrames 0.3s; -webkit-animation:closeFrames 0.3s;'; // closeFrames
          setTimeout(() => {
            if (this.$refs && this.$refs.pcContent) {
              this.$refs.pcContent.style = '';
              this.isShow = false;
              this.inclose = false;
            }
          }, 290);
        }
      } else {
        setTimeout(() => {
          this.isShow = false;
          this.inclose = false;
        }, 290);
      }
    },
    show() {
      this.isDownloading = false;
      this.zIndex = PopupManager.nextZIndex();
      this.isShow = true;
      this.loading = true;
      const index = this.srcUrl.toLowerCase().indexOf('http');
      this.showAddBoard = this.isTeacher && index === 0 && TCIC.SDK.instance.isClassLayoutHasDoc();
      if (this.thumbUrl && this.thumbUrl.length > 0) {
        // 加载缩略图，获取宽高比
        this.loadImageSize(this.thumbUrl);
      }
      if (this.srcLocalUrl.length === 0) {
        this.loadSrc();
      }
      if (this.delayTimer) {
        clearTimeout(this.delayTimer);
        this.delayTimer = null;
      }
      TCIC.SDK.instance.ignoreBackPressed(true);
    },
    // src为原图，thumb为缩略图
    showWithUrl(src, thumb = '') {
      if (this.inclose) {
        // 正在关闭中
        return;
      }
      if (src !== this.srcUrl || thumb !== this.thumbUrl) {
        this.srcUrl = src;
        this.thumbUrl = thumb;
        if (this.srcLocalUrl.length > 0) {
          window.URL.revokeObjectURL(this.srcLocalUrl);
          this.srcLocalUrl = '';
        }
      }
      this.show();
    },
    srcUrlIsBase64() {
      const base64Data = this.srcUrl.replace(/^data:image\/\w+;base64,/, '');
      return base64Data.length !== this.srcUrl.length;
    },
    image2Base64(img) {
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext('2d');
      ctx.drawImage(img, 0, 0, img.width, img.height);
      return canvas.toDataURL('');
    },
    clickToolbarOutside(e) {
      const toolbar = document.getElementById('image-preview-toolbar');
      // 计算toolbar在window中的位置
      let actualLeft = toolbar.offsetLeft;
      let actualTop = toolbar.offsetTop;
      let current = toolbar.offsetParent;
      while (current !== null) {
        actualLeft += current.offsetLeft;
        actualTop += current.offsetTop;
        current = current.offsetParent;
      }
      const toolbarW = toolbar.clientWidth;
      const toolbarH = toolbar.clientHeight;
      if (
        e.clientX < actualLeft
        || e.clientX > actualLeft + toolbarW
        || e.clientY < actualTop
        || e.clientY > actualTop + toolbarH
      ) {
        this.close();
      }
    },
    setLocalPerViewImgId(id) {
      this.imgId = id;
    },
    setComponentShowState(flag) {
      this.isShow = flag;
    },
    setServerImgId(id) {
      this.serverImgSeq = id;
    },
    getServerImgId() {
      return this.serverImgSeq;
    },
    getComponentShowState() {
      return this.isShow;
    },
    setPerImgHide() {
      this.hide();
    },
  },
};
</script>
<style lang="less">
.fill-parent {
  width: 100%;
  height: 100%;
}
.mobile-show-animation {
  animation: mobileShowFrames 0.3s;
  -webkit-animation: mobileShowFrames 0.3s;
  @keyframes mobileShowFrames {
    0% {
      left: 100%;
    }
    100% {
      left: 0;
    }
  }

  @-webkit-keyframes mobileShowFrames {
    0% {
      left: 100%;
    }
    100% {
      left: 0;
    }
  }
}

.mobile-close-animation {
  animation: mobileCloseFrames 0.3s;
  -webkit-animation: mobileCloseFrames 0.3s;
  @keyframes mobileCloseFrames {
    0% {
      left: 0;
    }
    100% {
      left: 100%;
    }
  }

  @-webkit-keyframes mobileCloseFrames {
    0% {
      left: 0;
    }
    100% {
      left: 100%;
    }
  }
}
.image-preview {
  position: absolute;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  pointer-events: none;
  /* Safari and Chrome */
  .image-preview__mobile-wrap {
    background-color: #000;
    text-align: center;
    .landscape-image {
      height: 100%;
      width: auto;
    }
    .portrait-image {
      width: 100%;
      height: auto;
      align-self: center;
    }
    .portrait-download {
      width: 128px;
      height: 42px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 6px;
      text-align: center;
      position: absolute;
      bottom: 99px;
    }
    .portrait-back-button {
      position: absolute;
      top: 6px;
      left: 1px;
      margin-top: env(safe-area-inset-top);
    }
    .download-icon {
      width: 24px;
      height: 24px;
      display: inline-block;
      background-image: url("./assets/icon-preview-mobile__download.svg");
    }
    .download-text {
      font-size: 16px;
      font-weight: 500;
      color: #ffffff;
    }
    .download-text:before {
      content: "";
      background-image: url("./assets/icon-preview-mobile__download.svg");
      background-repeat: no-repeat;
      background-size: 24px 24px;
      vertical-align: text-bottom;
      display: inline-block;
      width: 32px;
      height: 24px;
    }

    .back-button {
      background-color: transparent;
      border: 0;
      .el-icon-arrow-left {
        font-size: 24px;
      }
    }

    .landscape-toolbar {
      position: absolute;
      width: 100%;
      height: 40px;
      top: 0;
      display: flex;
      justify-content: space-between;
      margin-left: 20px;
      margin-right: 20px;
      background: linear-gradient(
        180deg,
        rgba(20, 24, 29, 0.4) 0%,
        rgba(0, 0, 0, 0) 100%
      );

      .download-button {
        width: 54px;
        height: 40px;
        margin-right: 20px;
        text-align: center;
        display: inline-block;
      }
    }
  }

  .image-preview__pc-wrap {
    align-self: center;
    text-align: center;
    width: 100%;
    pointer-events: none;
    .error-tips {
      background-color: #000;
      opacity: 0.4;
      color: #fff;
      text-align: center;
      font-size: 18px;
    }
    .image-preview__pc-content {
      display: inline-block;
      animation: showFrames 0.3s;
      -webkit-animation: showFrames 0.3s; /* Safari and Chrome */

      .center-image {
        border-radius: 4px;
      }

      @keyframes showFrames {
        0% {
          transform: scale(0.75);
        }
        100% {
          transform: scale(1);
        }
      }

      @-webkit-keyframes showFrames {
        0% {
          transform: scale(0.75);
        }
        100% {
          transform: scale(1);
        }
      }

      @keyframes closeFrames {
        0% {
          transform: scale(1);
          opacity: 1;
        }
        100% {
          transform: scale(0.75);
          opacity: 0;
        }
      }

      @-webkit-keyframes closeFrames {
        0% {
          transform: scale(1);
          opacity: 1;
        }
        100% {
          transform: scale(0.75);
          opacity: 0;
        }
      }

      .toolbar {
        width: auto;
        height: 72px;
        margin-top: 16px;
        background: linear-gradient(
          141deg,
          rgba(21, 27, 48, 0.7) 0%,
          rgba(28, 33, 49, 0.9) 100%
        );
        box-shadow: 0px 0px 3px 0px rgba(32, 32, 32, 0.4);
        border-radius: 10px;
        display: inline-block;
        text-align: center;

        .tooltip {
          margin: 6px;
        }
        .add-button {
          width: 40px;
          height: 40px;
          margin: 16px 0px 16px 20px;
          background-image: url("./assets/icon-preview__add.svg");
          display: inline-block;
          opacity: 0.85;
          &:hover {
            opacity: 1;
          }
        }

        .download-button {
          width: 40px;
          height: 40px;
          margin: 16px 20px;
          background-image: url("./assets/icon-preview__download.svg");
          display: inline-block;
          opacity: 0.85;
          &:hover {
            opacity: 1;
          }
        }

        .split-line {
          margin-bottom: 24px;
          width: 1px;
          height: 24px;
          background-color: #fff;
          opacity: 0.3;
          display: inline-block;
        }

        .close-button {
          width: 40px;
          height: 40px;
          margin: 16px 20px;
          background-image: url("./assets/icon-preview__close.svg");
          display: inline-block;
          opacity: 0.85;
          &:hover {
            opacity: 1;
          }
        }
      }
    }
  }
}
</style>
