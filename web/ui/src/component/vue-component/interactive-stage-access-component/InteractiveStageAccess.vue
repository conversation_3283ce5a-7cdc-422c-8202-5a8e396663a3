<template>
  <div
    v-if="isAssistant && canAssistantQuickStageUp && isClassStarted"
    class="interactive-stage-access-component"
  >
    <!-- 连麦入口 -->
    <div class="stage-access-entrance">
      <!-- 开始连麦 -->
      <el-tooltip
        v-if="!isOnStage"
        class="item"
        effect="dark"
        :content="toolContent"
        placement="top"
      >
        <div
          class="stage-access"
          @click="onStageAction(Stage_Up)"
        >
          <i />
          <div
            v-if="stageConnecting"
            class="s-stage-loading"
          >
            <span />
            <span />
            <span />
          </div>
        </div>
      </el-tooltip>

      <!-- 结束连麦 -->
      <el-tooltip
        v-if="isOnStage"
        class="item"
        effect="dark"
        :content="toolContent"
        placement="top"
      >
        <div
          class="stage-access hang"
          @click="onStageAction(Stage_Down)"
        >
          <i />
        </div>
      </el-tooltip>
    </div>
  </div>
</template>
<script>
import i18next from 'i18next';
import BaseComponent from '@/component/core/BaseComponent';

export default {
  name: 'StageAccessComponent',
  extends: BaseComponent,
  data() {
    return {
      isAssistant: false, // 是否助教
      canAssistantQuickStageUp: false, // 是否显示助教上台快速入口
      isClassStarted: false, // 是否已经开始上课
      isOnStage: false, // 权限列表中stage
      stageConnecting: false,
      Stage_Up: TCIC.TMemberActionType.Stage_Up,
      Stage_Down: TCIC.TMemberActionType.Stage_Down,
      roomInfo: {},
    };
  },
  computed: {
    toolContent() {
      return this.isOnStage ? i18next.t('下台') : i18next.t('上台');
    },
  },
  mounted() {
    this.roomInfo = TCIC.SDK.instance.getNameConfig().roomInfo;
    this.makeSureClassJoined(() => {
      this.show(); // 展示本组件
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      if (TCIC.SDK.instance.isBigRoom()) {
        // 大班课1v0，不显示助教上台快速入口
        this.canAssistantQuickStageUp = TCIC.SDK.instance.getClassInfo().maxRtcMember !== 0;
      } else {
        this.canAssistantQuickStageUp = TCIC.SDK.instance.isInteractClass() || TCIC.SDK.instance.isUnitedRTCClass();
      }
      if (this.isAssistant) {
        // 上台相关的状态监听
        this.isOnStage = TCIC.SDK.instance.getState(TCIC.TMainState.Stage_Status, false);
        this.addLifecycleTCICStateListener(TCIC.TMainState.Stage_Status, (status) => {
          this.isOnStage = status;
        });
        this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, (status) => {
          this.isClassStarted = status !== TCIC.TClassStatus.Not_Start;
        });
      }
    });
  },
  methods: {
    onStageAction(acctionType) {
      if (!this.isAssistant) {
        // 仅限助教操作
        return;
      }
      if (acctionType === TCIC.TMemberActionType.Stage_Up) {
        const classInfo = TCIC.SDK.instance.getClassInfo();
        if (classInfo.maxRtcMember === 0) {
          window.showToast(i18next.t('当前{{arg_0}}设定上台人数为0，成员无法上台', { arg_0: this.roomInfo.room }));
          return;
        }
      }
      if (this.stageConnecting) {
        // 正在链接中
        return;
      }
      this.stageConnecting = true;
      const classInfo = TCIC.SDK.instance.getClassInfo();
      const param = {
        classId: classInfo.classId,
        classType: classInfo.classType,
        userId: TCIC.SDK.instance.getUserId(),
        actionType: acctionType,
      };
      TCIC.SDK.instance.memberAction(param)
        .catch((err) => {
          window.showToast(err.errorMsg, 'error');
        })
        .finally(() => {
          this.stageConnecting = false;
        });
    },
  },
};
</script>
<style lang="less">
.interactive-stage-access-component {
  pointer-events: none;
  // 提示
  .stage-access-tips {
    position: absolute;
    top: 204px;
    left: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 62px;
    padding: 0 30px;
    transform: translateX(-50%);

    label {
      position: relative;
      color: #fff;
      font-size: 20px;
      z-index: 9;
      letter-spacing: 1px;

      i {
        position: relative;
        margin-right: 10px;
        color: #13a449;
        font-size: 24px;
      }
    }
  }

  // 连麦入口
  .stage-access-entrance {
    position: absolute;
    right: 44px;
    bottom: 66px;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 80px;
    pointer-events: auto;

    // 连麦中
    .s-stage-loading {
      position: absolute;
      display: flex;
      justify-content: center;
      bottom: 6px;
      span {
        display: inline-block;
        width: 4px;
        height: 4px;
        margin: 0 2px;
        border-radius: 50%;
        background: #fff;
        animation: stageLoad 1s ease infinite;
        &:nth-child(1) {
          animation-delay: 0.25s;
        }
        &:nth-child(2) {
          animation-delay: 0.5s;
        }
        &:nth-child(3) {
          animation-delay: 0.75s;
        }
      }
    }

    .stage-access {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 48px;
      background: linear-gradient(130deg, #50e083 0%, #27be4c 100%);
      box-shadow: 0 4px 12px 0 rgba(56, 204, 98, 0.32);
      border-radius: 50%;
      cursor: pointer;

      &.hang {
        background: linear-gradient(134deg, #ff824b 0%, #ff5910 100%);
        box-shadow: 0 4px 12px 0 rgba(250, 100, 0, 0.32);
      }

      &.dynamic {
        flex-direction: column;

        i {
          background-size: 70%;
          background-position: center 4px;
        }
      }

      i {
        display: flex;
        width: 100%;
        height: 100%;
        background: url('./assets/ic_stage.svg') no-repeat center;
      }
    }
  }
}
</style>
