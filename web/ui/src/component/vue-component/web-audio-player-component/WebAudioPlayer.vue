<!-- eslint-disable radix -->
<template>
  <div
    ref="player"
    class="audio-player-component"
    :class="{foldup: foldUp}"
  >
    <div
      class="board-back-bg audio-container"
    >
      <div class="filter-blur-bg" />
      <div class="drag-header drag-module-header__wrap" />

      <!-- 老师端 -->
      <template v-if="isMC">
        <div id="webAudioPlayWrap" />

        <template v-if="!foldUp">
          <div class="audio-title">
            <label
              class="title-text"
              :title="audio.name"
            >{{ audio.name }}</label>
            <div class="audio-ops">
              <span
                class="ic-audio-icon ic-audio-up"
                :class="{unfold: foldUp}"
                @click="foldUp = !foldUp"
              ><i /></span>
              <span
                class="ic-audio-icon ic-audio-close"
                @click="onHide"
              ><i /></span>
            </div>
          </div>
          <div
            v-loading="loading"
            class="content-wraper"
            :element-loading-text="loadingTip"
            element-loading-background="rgba(0, 0, 0, 0.3)"
          >
            <div
              class="audio-content"
              :class="{loading:loading}"
            >
              <i
                v-if="audio.playing"
                class="ic-audio ic_radio_pause"
                @click="pauseMusic"
              />
              <i
                v-else
                class="ic-audio ic_radio_play"
                @click="resumeMusic"
              />
              <div class="audio-progress">
                <label class="audio-time">{{ audio.process | timeFormat }}</label>
                <div
                  class="slider-wrapper"
                  @mousedown="onMouseDown"
                >
                  <el-slider
                    v-model="audio.process"
                    class="audio-slider"
                    :max="audio.duration"
                    :show-tooltip="false"
                    @stop.prevent
                    @change="seekMusic"
                    @input="sliderStop"
                    @mousedown.native="isChange = true"
                    @mouseup.native="isChange = false"
                  />
                </div>
                <label class="audio-time">{{ audio.duration | timeFormat }}</label>
              </div>

              <!-- 音量控制 -->
              <!-- <el-popover
                popper-class="board-back-bg volume-popover"
                placement="bottom"
                trigger="click"
              >
                <i
                  slot="reference"
                  class="ic-audio ic_radio_volume"
                  :class="btnActive? 'active':''"
                  @click="btnActive = true"
                />
                <el-slider
                  v-model="audio.volume"
                  class="video-slider-vertical"
                  :show-tooltip="false"
                  vertical
                  :max="volumeUint"
                  :min="0"
                  height="80px"
                  @change="setMusicVolume"
                />
              </el-popover> -->
            </div>
          </div>
        </template>
        <template v-else>
          <!--收起-->
          <div class="audio-title">
            <i
              v-if="audio.playing"
              class="ic-audio ic_radio_pause"
              @click="pauseMusic"
            />
            <i
              v-else
              class="ic-audio ic_radio_play"
              @click="resumeMusic"
            />
            <label class="title-text">{{ audio.name }}</label>
            <span class="title-time">{{ audio.process | timeFormat }}/{{ audio.duration | timeFormat }}</span>
            <div class="audio-ops">
              <span
                class="ic-audio-icon ic-audio-up"
                :class="{unfold: foldUp}"
                @click="foldUp = !foldUp"
              ><i /></span>
              <span
                class="ic-audio-icon ic-audio-close"
                @click="onHide"
              ><i /></span>
            </div>
          </div>
        </template>
      </template>

      <div
        v-else
        class="student-audio"
      >
        <template v-if="!foldUp">
          <div class="audio-title">
            <label class="title-text">{{ audio.name }}</label>
            <div class="audio-ops">
              <span
                class="ic-audio-icon ic-audio-up"
                :class="{unfold: foldUp}"
                @click="foldUp = !foldUp"
              ><i /></span>
            </div>
          </div>
          <div class="audio-progress student-progress">
            <label class="audio-time">{{ audio.process | timeFormat }}</label>
            <div class="slider-wrapper">
              <el-slider
                v-model="audio.process"
                class="audio-slider"
                :max="audio.duration"
                :show-tooltip="false"
              />
              <div class="overlay" />
            </div>
            <label class="audio-time">{{ audio.duration | timeFormat }}</label>
          </div>
        </template>
        <template v-else>
          <!--学生端收起-->
          <div class="audio-title student-audio-title">
            <label class="title-text">{{ audio.name }}</label>
            <div class="audio-ops">
              <span class="title-time">{{ audio.process | timeFormat }}/{{ audio.duration | timeFormat }}</span>
              <span
                class="ic-audio-icon ic-audio-up"
                :class="{unfold: foldUp}"
                @click="foldUp = !foldUp"
              ><i /></span>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
<script>
import i18next from 'i18next';
import BaseComponent from '@/component/core/BaseComponent';
function video(audios) {
  return audios.duration;
}
export default {
  name: 'AudioPlayerComponent',
  filters: {
    timeFormat(ms) {
      const duration = parseInt(ms, 10);
      let minute = parseInt(duration / 60, 10);
      let sec = `${(duration % 60)}`;
      const isM0 = ':';
      if (minute === 0) {
        minute = '00';
      } else if (minute < 10) {
        minute = `0${minute}`;
      }
      if (sec.length === 1) {
        sec = `0${sec}`;
      }
      return minute + isM0 + sec;
    },
  },
  extends: BaseComponent,
  data() {
    return {
      taskId: 'webAudioPlayer',
      isTeacher: null,
      isAssistant: null,
      isMC: false, // 是否主持人
      sequence: '', // 播流成功后的ID
      timer: '',
      foldUp: false, // 音频按钮
      audioElement: null,
      volumeUint: 200,
      receivedRoleType: '',
      audio: {
        id: 0,
        name: '',        // 文件名
        duration: 0,     // 音频时长
        process: 0,      // seconds
        volume: 100,     // 滑块最大值200，默认100居中
        playing: false,  // 播放状态
        status: 0,       // 播放组件状态: 1播放，2暂停，0停止
      },
      isChange: '',
      loading: false,

      btnActive: false,
      teacherJoined: false,
      isDragging: false,
      lastMouseDown: 0,
      roomInfo: {},
    };
  },
  computed: {
    roleType() {
      let tempType = '';
      if (this.isTeacher) tempType = 'teacher';
      if (this.isAssistant) tempType = 'assistant';
      return tempType;
    },
    loadingTip() {
      return i18next.t('音频{{arg_0}}加载中...', { arg_0: this.roomInfo.courseware });
    },
  },
  watch: {
    // teacherJoined(val) {
    //   if (val && this.audio && this.audio.status === 1) {
    //     console.info('音视频课件-webAudioPlayer teacherJoined');
    //     this.onShow();
    //   }
    // },
  },
  mounted() {
    const { roomInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roomInfo = roomInfo;
    this.makeSureClassJoined(this.onJoinClass);
    this.toggleComponentDrag(true);
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Task_Updated, this.onTaskUpdate);
    this.addLifecycleTCICEventListener(TCIC.TTrtcEvent.PlayStream_Changed, this.onPlayStreamChanged);
    console.info('音视频课件-webAudioPlayer mounted');
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
  methods: {
    async play(file) {
      // 有播放中的音视频文件直接拦截提醒（音频切音频相对容易，视频切音频跨文件考虑的要多些）
      try {
        this.isMC = true;
        TCIC.SDK.instance.setState(TCIC.TMainState.Is_Web_Audio_Vod_MC, true);

        await this.stopMusic();
        setTimeout(() => {
          this.show();
          this.createAudioElement(file);
        }, 500);
      } catch (err) {
        this.hide();
        console.error('音视频课件-加载音频资源失败', err);
        window.showToast(i18next.t('加载音频资源失败'));
      }
    },
    createAudioElement(file) {
      const { audio } = this;
      audio.id = file.id;
      audio.name = file.name;

      const webAudioPlayWrap = document.getElementById('webAudioPlayWrap');
      this.loading = true;
      this.audioElement = document.createElement('audio');
      this.audioElement.setAttribute('id', audio.id);
      this.audioElement.setAttribute('src', file.path);
      this.audioElement.setAttribute('crossOrigin', 'anonymous');
      this.audioElement.setAttribute('autoplay', true);
      this.audioElement.setAttribute('loop', true);
      this.audioElement.addEventListener('loadedmetadata', () => {
        this.startMusic();
        this.loading = false;
      });
      this.audioElement.addEventListener('error', (err) => {
        webAudioPlayWrap.removeChild(webAudioPlayWrap.firstElementChild);
        this.audioElement = null;
        this.hide();
        console.error('音视频课件-加载音频资源失败', err);
        window.showToast(i18next.t('加载音频资源失败'));
      });

      webAudioPlayWrap.appendChild(this.audioElement);
    },
    // 开始播放
    async startMusic() {
      await TCIC.SDK.instance.startCaptureStream(this.audio.id);
      this.getAudioPropsByLoad();
      this.getCurrentTimeByPlay();
      this.updateTask();
      TCIC.SDK.instance.setState(TCIC.TMainState.Web_Audio_Play, 0);
      console.info('音视频课件-audio-startMusic');
    },
    // 停止播放
    async stopMusic() {
      if (!this.audio.status) return;
      if (!this.isMC) return;

      try {
        const webAudioPlayWrap = document.getElementById('webAudioPlayWrap');
        this.audioElement.pause();
        clearInterval(this.timer);
        this.resetAudioData();
        await this.updateTask();
        await TCIC.SDK.instance.stopCaptureStream();
        webAudioPlayWrap.removeChild(webAudioPlayWrap.firstElementChild);
        this.audioElement = null;
        this.hide();
        TCIC.SDK.instance.setState(TCIC.TMainState.Web_Audio_Play, 2);
        console.info('音视频课件-audio-stopMusic');
      } catch (err) {
        console.error('音视频课件-停止播放音频资源失败', err);
        window.showToast(i18next.t('停止播放音频资源失败'));
      }
    },
    // 暂停播放
    pauseMusic() {
      clearInterval(this.timer);
      this.audio.playing = false;
      this.audio.status = 2;
      this.audioElement.pause();
      this.updateTask();
    },
    // 恢复播放
    async resumeMusic() {
      this.audioElement.play();
      this.getCurrentTimeByPlay();
      await this.updateTask();
    },
    getAudioPropsByLoad() {
      this.audio.duration = video(this.audioElement);
      this.audio.volume = this.audioElement.volume * this.volumeUint;
    },
    getCurrentTimeByPlay() {
      const { audio } = this;
      audio.playing = true;
      audio.status = 1;
      clearInterval(this.timer);
      this.timer = setInterval(() => {
        this.audio.process = this.audioElement.currentTime;
        this.updateTask();
      }, 1000);
    },
    resetAudioData() {
      this.audio = this.$options.data().audio;
      this.isMC = false;
      TCIC.SDK.instance.setState(TCIC.TMainState.Is_Web_Audio_Vod_MC, false);
    },
    /**
     * 设置背景音乐进度,单位毫秒
     */
    seekMusic(pst) {
      // 滑动结束时触发
      this.audioElement.currentTime = pst;
      this.resumeMusic();
      // this.updateTask();
    },
    async sliderStop(e) {
      // 滑动时实时触发，process变化时触发
      if (this.isChange) {
        this.pauseMusic();
        this.isChange = false;
      }
    },
    /**
     * 设置背景音乐的音量大小
     */
    setMusicVolume(volume) {
      console.info('音视频课件-setVolume', volume / this.volumeUint);
      this.audioElement.volume  = volume / this.volumeUint;
    },

    onShow() {
      if (TCIC.SDK.instance.isLiveClass() && !TCIC.SDK.instance.isTeacherOrAssistant()) {
        // 学生直播课不展示音频控件
        return;
      }
      this.show();
    },

    onHide() {
      this.loading = false;     // 清除加载状态
      this.stopMusic();
    },

    async updateTask() {
      if (!TCIC.SDK.instance.isTeacherOrAssistant()) return;
      const userId = TCIC.SDK.instance.getUserId();
      const content = JSON.stringify({
        ...this.audio,
        roleType: this.roleType,
      });
      return TCIC.SDK.instance.updateTask(this.taskId, content, -1, 0, userId).then(task => task)
        .catch((error) => {
          window.showToast(error.errorMsg);
        });
    },
    // 音频通知
    onTaskUpdate(taskInfo) {
      if (taskInfo.taskId === this.taskId) console.info('音视频课件-onTaskUpdate webAudio1', taskInfo, this.sequence, !!this.sequence);
      if (taskInfo.taskId !== this.taskId) return;

      if (taskInfo.status === 0) {
        console.info('音视频课件-onTaskUpdate-webAudio-刷新页面等场景，用户生命周期结束');
        this.closeComponentAfterRemoveStream();
        return;
      }

      if (taskInfo.content) {
        this.audio = JSON.parse(taskInfo.content);
        if (this.isTeacher && this.audio.roleType === 'teacher') return;
        if (this.isAssistant && this.audio.roleType === 'assistant') return;

        if (this.audio) {
          console.info('音视频课件-onTaskUpdate webAudio2', taskInfo.content, this.sequence, !!this.sequence);
          if (this.audio.status === 0) {
            this.closeComponentAfterRemoveStream();
            return;
          }
          this.onShow();
          if (!this.sequence) {
            this.sequence = TCIC.SDK.instance.bindRemoteVideoDom(
              taskInfo.bindingUser,
              TCIC.TTrtcVideoStreamType.Vod,
              null,
            );
            TCIC.SDK.instance.setState(TCIC.TMainState.Web_Audio_Play, 0);
          }
        }
      } else {
        this.hide();
      }
    },
    closeComponentAfterRemoveStream() {
      this.hide();
      this.sequence = '';
      TCIC.SDK.instance.setState(TCIC.TMainState.Web_Audio_Play, 2);
    },
    onPlayStreamChanged(info) {
      if (Object.is('remove', info.status) && this.sequence) this.closeComponentAfterRemoveStream();
      console.info('音视频课件-onPlayStreamChanged', this.sequence, info);
    },

    onJoinClass() {
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      // 老师是否已经进入课堂
      const permissions = TCIC.SDK.instance.getPermissionList();
      this.permissionListUpdateHandler(permissions);
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Permission_Update, this.permissionListUpdateHandler);

      document.addEventListener('mouseup', (event) => {
        if (this.isDragging) {
          this.isDragging = false;
          this.lastMouseUp = + new Date();
        }
      });
    },

    permissionListUpdateHandler(permissionList) {
      const teacherId = TCIC.SDK.instance.getClassInfo().teacherId;
      this.teacherJoined = permissionList.find(permission => permission.userId === teacherId);
    },

    onMouseDown() {
      this.isDragging = true;
      this.lastMouseDown = + new Date();
    },

  },

};
</script>

<style lang="less">
.ic_radio_pause {
  background: url('./assets/ic_radio_pause.svg') no-repeat center;

  &:hover {
    background: url('./assets/ic_radio_pause_hover.svg') no-repeat center;
  }
}

.ic_radio_play {
  background: url('./assets/ic_radio_play.svg') no-repeat center;

  &:hover {
    background: url('./assets/ic_radio_play_hover.svg') no-repeat center;
  }
}

.ic_radio_volume {
  background: url('./assets/ic_radio_volume.svg') no-repeat center;

  &:hover {
    background: url('./assets/ic_radio_volume_hover.svg') no-repeat center;
  }
}

//学生端
.student-audio {
  padding: 0 16px;
  .audio-progress {
    &.student-progress {
      margin: 24px 0 24px 0 !important;
    }
  }
}

.drag-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 24px;
  cursor: move;
  z-index: 2001;
}

//学生端-收起
.student-audio-title {
  .audio-progress {
    &.student-progress {
      margin: 24px 0 24px 0 !important;
    }
  }
}

//音量弹出框
.volume-popover {
  min-width: inherit !important;
  width: 60px !important;
  height: 112px;

  .video-slider-vertical {
    .el-slider__runway {
      background: #dfdfdf;
      margin: 0;

      &:before {
        top: inherit;
      }

      .el-slider__bar {
        top: inherit;
      }
    }
  }
}


/* Audio 播放器 */
.audio-player-component {

  position: absolute;
  left: calc(100% - 620px);
  top: 200px;
  width: 388px;
  height: 128px;
  transform-origin: top;

  &.foldup {
    height: 76px
  }

  .loading {
    opacity: .2
  }

  .audio-container {
    //left: calc(100% - 500px);
    //top: 200px;
    color: #fff;
    display: flex;
    flex-direction: column;
    //width: 388px;
    //min-height: 76px;
    width: 100%;
    height: 100%;
    padding: 24px 0 0 0;
    opacity: 1;
    transform-origin: top;
    //&:hover {
    //  cursor: move;
    //}
    .audio-title {
      padding: 0 16px;
      position: relative;
      z-index: 9;
      display: flex;
      align-items: center;

      .title-text {
        display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        font-size: 20px;
        line-height: 28px;
        color: #8A9099;
      }

      .title-time {
        margin-left: 12px;
        font-size: 16px;
        line-height: 28px;
        color: #8A9099;
        flex-shrink: 0;
      }

      .audio-ops {
        display: flex;
        align-items: center;
        margin-left: auto;
        flex-shrink: 0;

        .ic-audio-icon {
          display: flex;
          width: 20px;
          height: 20px;
          overflow: hidden;
          background: #030910;
          border-radius: 3px;
          cursor: default;

          &.ic-audio-up {
            margin-left: 12px;

            i {
              background: url('./assets/ic_menu_up.svg') no-repeat center;
            }

            &.unfold {
              i {
                transform: rotate(180deg);
                transition: all .5s;
              }
            }
          }

          &.ic-audio-close {
            margin-left: 16px;

            i {
              background: url('assets/ic_menu_close.svg') no-repeat center;
            }
          }

          &:hover {
            i {
              opacity: .8;
            }
          }

          i {
            display: inline-block;
            width: 20px;
            height: 20px;
            transition: all .5s;
            opacity: 0.4;
          }
        }
      }

      .ic_radio_volume,
      .ic_radio_pause,
      .ic_radio_play {
        height: 24px;
        margin-right: 12px;

        &:hover {
          cursor: default;
        }
      }
    }

    .el-loading-spinner .circular {
      width: 21px;
      height: 21px;
    }

    .audio-content {
      padding: 0 16px;
      position: relative;
      z-index: 9;
      display: flex;
      align-items: center;
      margin: 12px 0;
    }

    .audio-progress {
      display: flex;
      align-items: center;
      flex: 1;
      margin: 0 18px;

      .slider-wrapper {
        flex: 1;
        margin: 0 8px;
        position: relative;

        .overlay {
          position: absolute;
          top: -10px;
          right: 0;
          bottom: -10px;
          left: 0;
          background: rgba(255, 255, 255, 0.01);
          z-index: 1;
        }
      }

      .audio-slider {
        flex: 1;
        margin: 0 8px;
        z-index: 0;
        position: relative;

        .el-slider__runway .el-slider__bar,
        .el-slider__runway:before {
          height: 6px;
        }
      }

      .audio-time {
        font-size: 20px;
        color: #fff;
        flex-shrink: 0;
        width: 54px;
        word-break: keep-all;
      }
    }

    .ic-audio {
      display: flex;
      flex-shrink: 0;
      width: 24px;
      height: 52px;

      &:hover {
        cursor: default;
      }

      &.ic_radio_volume,
      &.ic_radio_pause {
        background-size: 100%;
      }

      &.ic_radio_volume {
        &.active {
          background: url('./assets/ic_radio_volume_hover.svg') no-repeat center;
          background-size: 100%;
        }
      }
    }
  }
}

</style>
