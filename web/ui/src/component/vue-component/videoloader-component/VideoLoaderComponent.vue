<template>
  <div
    id="video-loader-wraper"
    ref="wraper"
    class="video-loader-component"
  />
</template>
<script>
import BaseComponent from '@/component/core/BaseComponent';
import Constant from '@/util/Constant';
import VideoLoader from './VideoLoader.js';

export default {
  name: 'VideoLoaderComponent',
  components: {},
  extends: BaseComponent,
  data() {
    return {
      videoLoader: null,
      loadVideoInfos: [],
    };
  },
  watch: {
  },
  mounted() {
    this.makeSureClassJoined(this.onJoinClass);
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.AV_Add, this.onAvAdd);
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.AV_Remove, this.onAvRemove);
    this.addLifecycleTCICStateListener(Constant.TStateVideoLoaderPage, this.onPageUpdate);
    this.addLifecycleTCICStateListener(Constant.TStateVideoMaxLoadCount, this.onMaxLoadUpdate);
    const isPreviewVideo = TCIC.SDK.instance.getState(Constant.TStateLocalAVBeforeClassBegin);
    this.videoLoader = new VideoLoader(isPreviewVideo);
    this.videoLoader.addVideo(TCIC.SDK.instance.getUserId(), VideoLoader.MASK_SELF);
    this.videoLoader.setLoadCallback(this.onVideoLoad);
    this.videoLoader.setRemoveCallback(this.onVideoRemove);
    TCIC.SDK.instance.promiseState(Constant.TStateDeviceDetect, false).then(() => {
      this.videoLoader.setReady();
    });
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start).then(() => {
      this.videoLoader.startClass();
    });
    // 加载组件加载前上抛的学生
    TCIC.SDK.instance.getPermissionList()
      .forEach((permission) => {
        if (permission.stage) {
          this.onAvAdd(permission);
        }
      });
  },
  methods: {
    onJoinClass() {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      this.videoLoader.addVideo(classInfo.teacherId, VideoLoader.MASK_TEACHER);
      // 如果是 1v0 纯视频课堂，或者大班课纯视频课堂，需要先加载老师组件
      if (
          TCIC.SDK.instance.isVideoOnlyClass()
          && (classInfo.maxRtcMember === 0 || TCIC.SDK.instance.isBigRoom())
        ) {
        this.videoLoader.addVideo(classInfo.teacherId, VideoLoader.MASK_STAGE);
      }
    },
    onPageUpdate(page) {
      this.videoLoader && this.videoLoader.setLoadVideoPage(page);
    },
    onMaxLoadUpdate(num) {
      this.videoLoader && this.videoLoader.updateMaxLoad(num);
    },
    onAvAdd(info) {
      this.videoLoader.addVideo(info.userId, VideoLoader.MASK_STAGE);
      TCIC.SDK.instance.setState(Constant.TStateVideoLoaderCount, this.videoLoader.getLoadVideoCount());
    },
    onAvRemove(info) {
      this.videoLoader.removeVideo(info.userId, VideoLoader.MASK_STAGE);
      TCIC.SDK.instance.setState(Constant.TStateVideoLoaderCount, this.videoLoader.getLoadVideoCount());
    },
    onVideoLoad(userId, isTeacher) {
      const tagName = isTeacher ? 'teacher-component' : 'student-component';
      const label = isTeacher ? 'default' : userId;
      let { TConstantVideoWidth: videoWidth, TConstantVideoHeight: videoHeight } = Constant;
      // 1v1视频课堂防止布局抖动
      if (TCIC.SDK.instance.isPortraitClass() && TCIC.SDK.instance.isVideoOnlyClass()) {
        videoWidth = 1;
        videoHeight = 1;
      }
      TCIC.SDK.instance.loadComponent(tagName, {
        position: 'relative',
        width: `${videoWidth}px`,
        height: `${videoHeight}px`,
        display: 'inline-block',
        style: 'overflow: visible;',
      }, 'video-loader-wraper', label).then((ele) => {
        if (ele === null) {
          console.warn(`XDBG-VideoLoaderComponent::mounted=>load student component fail: ${userId}`);
          return;
        }
        console.log(`XDBG-VideoLoaderComponent::add video ${userId}/${isTeacher}`);
        this.loadVideoInfos.push({ userId, isTeacher, ele });
        TCIC.SDK.instance.notify(Constant.TEventAddVideoComponent, { userId, isTeacher });
      });
    },
    onVideoRemove(userId, isTeacher) {
      const tagName = isTeacher ? 'teacher-component' : 'student-component';
      const label = isTeacher ? 'default' : userId;
      TCIC.SDK.instance.removeComponent(tagName, label).then(() => {
        console.log(`XDBG-VideoLoaderComponent::remove video ${userId}/${isTeacher}`);
        TCIC.SDK.instance.notify(Constant.TEventRemoveVideoComponent, { userId, isTeacher });
      });
      if (isTeacher) {
        this.loadVideoInfos = this.loadVideoInfos.filter(info => !info.isTeacher);
      } else {
        this.loadVideoInfos = this.loadVideoInfos.filter(info => info.userId !== userId);
      }
    },
    // 获取当前加载的所有视频组件
    getCurLoadedVideoInfos() {
      return this.loadVideoInfos;
    },
  },
};
</script>
<style lang="less">

</style>
