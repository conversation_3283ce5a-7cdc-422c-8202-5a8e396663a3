
function VideoLoader(preloadSelf = true) {
  this.in_user_arr = [];
  this.in_preload_self = preloadSelf;
  this.in_load_ready = false;     // 是否准备就绪可以加载(设备检测完成)
  this.in_class_started = false;
  this.in_max_load = 51;    // 最大加载数量(不包含锁定用户)
  this.in_cur_page = 0;     // 当前页数
  this.in_load_callback = (userId, isTeacher) => {
    console.warn(`XDBG-VideoLoader::load=>${userId}, isTeacher: ${isTeacher}`);
  };
  this.in_remove_callback = (userId, isTeacher) => {
    console.warn(`XDBG-VideoLoader::remove=>${userId}, isTeacher: ${isTeacher}`);
  };
};


VideoLoader.MASK_TEACHER = 1;     // 是否老师
VideoLoader.MASK_SELF = 2;        // 是否自己
VideoLoader.MASK_STAGE = 4;       // 是否在台上

/**
 * 添加或更新待加载的视频用户信息
 * @param userId {string} 用户id
 * @param mask {number} 类型
 */
VideoLoader.prototype.addVideo = function (userId, mask) {
  const info = this.in_get_user_video(userId);
  console.log(`XDBG-VideoLoader::addVideo=>${userId}, ${info?.mask}, ${mask}`);
  if (info) {   // 添加类型
    info.mask |= mask;
  } else {
    this.in_user_arr.push({ userId, mask, load: false, lock: false });
  }
  this.in_update_loading();
};

/**
 * 移除待加载的视频用户信息或属性
 * @param userId {string} 用户id
 * @param mask {number} 类型
 */
VideoLoader.prototype.removeVideo = function (userId, mask) {
  console.log(`XDBG-VideoLoader::removeVideo=>${userId}, ${mask}`);
  const info = this.in_get_user_video(userId);
  if (info) {
    info.mask &= ~mask;
    this.in_update_loading();
  }
};

/**
 * 锁定用户(始终加载)
 * @param userId {string} 用户id
 */
VideoLoader.prototype.lockVideo = function (userId) {
  console.log(`XDBG-VideoLoader::lockVideo=>${userId}`);
  const info = this.in_get_user_video(userId);
  if (info) {
    info.lock = true;
    this.in_update_loading();
  }
};

/**
 * 解锁用户(按需加载)
 * @param userId {string} 用户id
 */
VideoLoader.prototype.unlockVideo = function (userId) {
  console.log(`XDBG-VideoLoader::unlockVideo=>${userId}`);
  const info = this.in_get_user_video(userId);
  if (info) {
    info.lock = false;
    this.in_update_loading();
  }
};

/**
 * 设置准备状态
 */
VideoLoader.prototype.setReady = function () {
  console.log('XDBG-VideoLoader::setReady=>enter');
  this.in_load_ready = true;
  this.in_update_loading();
};

/**
 * 更新课堂状态
 */
VideoLoader.prototype.startClass = function () {
  console.log('XDBG-VideoLoader::startClass=>enter');
  this.in_class_started = true;
  this.in_update_loading();
};

/**
 * 更新最大加载数量
 * @param maxLoadCount {number} 最大加载数量
 */
VideoLoader.prototype.updateMaxLoad = function (maxLoadCount) {
  if (this.in_max_load !== maxLoadCount) {
    console.log(`XDBG-VideoLoader::updateMaxLoad=>${maxLoadCount}`);
    this.in_max_load = maxLoadCount;
    this.in_update_loading();
  }
};

/**
 * 设置加载回调
 * @param callback  加载回调
 */
VideoLoader.prototype.setLoadCallback = function (callback) {
  this.in_load_callback = callback;
};

/**
 * 设置移除回调
 * @param callback  加载回调
 */
VideoLoader.prototype.setRemoveCallback = function (callback) {
  this.in_remove_callback = callback;
};

/**
 * 获取当前加载视频数量
 * @returns 当前加载视频数量
 */
VideoLoader.prototype.getLoadVideoCount = function () {
  return this.in_user_arr.length;
};

/**
 * 设置当前加载视频页
 */
VideoLoader.prototype.setLoadVideoPage = function (num) {
  if (this.in_cur_page !== num) {
    console.log(`XDBG-VideoLoader::setLoadVideoPage=>${num}/${this.in_cur_page}`);
    this.in_cur_page = num;
    this.in_update_loading();
  }
};

// 查找指定用户的视频信息
VideoLoader.prototype.in_get_user_video = function (userId) {
  return this.in_user_arr.find(info => info.userId === userId);
};

// 更新所有用户视频加载状态
VideoLoader.prototype.in_update_loading = function () {
  if (!this.in_load_ready) {
    return;
  }
  if (!this.in_class_started) {   // 课前
    this.in_user_arr.forEach((info) => {
      const isLoad = !!(this.in_preload_self && (info.mask & VideoLoader.MASK_SELF));
      this.in_update_user(info, isLoad);
    });
  } else {
    let loadIdx = -1;
    const lockArr = this.in_user_arr.filter(info => info.lock);
    lockArr.forEach(info => this.in_update_user(info, true));  // 被锁定则直接加载

    const startIdx = this.in_max_load * this.in_cur_page;
    for (let i = 0; i < this.in_user_arr.length; i ++) {
      const info = this.in_user_arr[i];
      if (info.lock) {    // 跳过已加载的
        continue;
      } else {
        if (info.mask & VideoLoader.MASK_STAGE) {
          loadIdx += 1;
          if (loadIdx >= startIdx && loadIdx < startIdx + this.in_max_load) {
            this.in_update_user(info, true);
            continue;
          }
        }
        this.in_update_user(info, false);
      }
    }
    // 移除需要删除的元素
    this.in_user_arr = this.in_user_arr.filter(info => info.mask);
  }
};

// 加载视频组件
VideoLoader.prototype.in_update_user = function (info, isLoad) {
  if (info.load !== isLoad) {
    info.load = isLoad;
    if (isLoad) {
      console.log(`XDBG-VideoLoader::in_update_user=>load: ${info.userId} ${info.mask}`);
      this.in_load_callback && this.in_load_callback(info.userId, TCIC.SDK.instance.isTeacher(info.userId));
    } else {
      console.log(`XDBG-VideoLoader::in_update_user=>remove: ${info.userId}`);
      this.in_remove_callback && this.in_remove_callback(info.userId, !!(info.mask & VideoLoader.MASK_TEACHER));
    }
  }
};

export default VideoLoader;
