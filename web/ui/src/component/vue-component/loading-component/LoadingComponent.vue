<template>
  <div :class="['loading-component', {'mobile-layout': isMobile, 'bg-transparent': bgTransparent}]">
    <div
      class="el-loading-mask"
      style="background-color: transparent;"
    >
      <div class="el-loading-spinner">
        <div class="loading-icon" />
        <p class="el-loading-text">
          {{ text }}
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import Constant from '@/util/Constant';
import BaseComponent from '@core/BaseComponent';

export default {
  extends: BaseComponent,

  props: {},

  data() {
    return {
      isMobile: false,
      text: '',
      bgTransparent: false,
      needTransparent: false,
      textMap: new Map(),
      seqId: 0,
      curSeqId: 0,
    };
  },
  computed: {},

  mounted() {
    this.isMobile = TCIC.SDK.instance.isMobile();
    TCIC.SDK.instance.registerState(Constant.TStateIsLoading, '正在加载', false);
  },

  beforeDestroy() {
  },

  methods: {
    onComponentVisibilityChange(visible) {
      TCIC.SDK.instance.setState(Constant.TStateIsLoading, visible);
    },

    showText(text, isTransparent = false) {
      this.seqId = this.seqId + 1;
      this.curSeqId = this.seqId;
      this.textMap.set(this.seqId, {
        text, transparent: isTransparent || this.needTransparent,
      });
      this.text = text;
      this.bgTransparent = isTransparent || this.needTransparent;
      // this.show();
      return this.seqId;
    },

    setBackgroundTransparent(transparent) {
      this.needTransparent = transparent;
      this.bgTransparent = this.bgTransparent || transparent;
    },

    hideText(seq) {
      this.textMap.delete(seq);
      if (this.curSeqId === seq) {
        const sortArr = Array.from(this.textMap.keys()).sort();
        if (sortArr.length > 0) {
          this.curSeqId = sortArr[sortArr.length - 1];
          const info = this.textMap.get(this.curSeqId);
          this.text = info.text;
          this.bgTransparent = info.isTransparent;
        }
      }

      if (this.textMap.size === 0) {
        this.hide();
      }
    },
  },
};
</script>

<style lang="less">
.loading-component {
  width: 100%;
  height: 100%;
  background: var(--primary-color, rgba(34, 34, 34, 0.5));

  &.bg-transparent {
    background: transparent;
  }

  .loading-icon {
    width: 50px;
    height: 50px;
    margin: 10px auto;
    background-image: url('../../../assets/icon-class-loading.gif');
    background-size: 50px 50px;
  }
}
</style>
