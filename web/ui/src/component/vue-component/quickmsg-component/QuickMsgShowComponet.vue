<template>
  <div
    v-if="showLiveCommenting && active && !deivceState"
    class="quickmsg-show-component"
  >
    <transition-group
      name="quick-im-component-msg-list"
      tag="ul"
      class="quick-im-component-msg-list"
    >
      <QuickMsgComponent
        v-for="(msg, index) in filteredList"
        :key="msg.key"
        :msg="msg"
        :index="index"
        class="quick-im-component-msg-item"
      />
    </transition-group>
  </div>
</template>
<script>
import QuickMsgComponent from '../im-component/QuickMsg.vue';
import BaseComponent from '@/component/core/BaseComponent';
import Constant from '@/util/Constant';
export default {
  name: 'EdupolicyComponent',
  components: {
    QuickMsgComponent,
  },
  extends: BaseComponent,
  data() {
    return {
      quickMsgVisible: true, // im和弹幕互斥显示
      isStudent: false, // 是否学生
      recvMsgList: [],
      showMsgList: [],
      msgList: [],                // 消息列表
      showLiveCommenting: true,   // 显示弹幕消息
      hideTimer: 0,
      active: false,
      deviceOrientation: TCIC.TDeviceOrientation.Portrait,
      deivceState: TCIC.SDK.instance.getState(Constant.TStateDeviceDetect),
      hasSideIM: false,  // 有侧边栏
      isInitMsg: false,
      classId: '', // 识别并过滤系统机器人帐号信息
    };
  },

  computed: {
    filteredList() {
      if (!this.hasSideIM) return this.showMsgList;
      // hasSideIM 学生端屏蔽所有气泡消息
      if (this.isStudent) return [];
      // hasSideIM 非学生端仅保留举手气泡消息
      return this.showMsgList.filter(msg => msg.msgType === 'hand_up_tips');
    },
  },

  mounted() {
    setInterval(() => {
      if (this.quickMsgVisible) {
        // 只在消息盒子没有显示的时候显示弹幕
        if (this.recvMsgList.length > 0) {
          // 每次取消息缓存中的一条加入显示队列
          this.showMsgList.push(this.recvMsgList[0]);
          this.recvMsgList.shift();
          if (this.showMsgList.length > 5) {
            // 最多显示5条
            this.showMsgList.shift();
          }
          // 有消息显示则清空隐藏定时器
          if (this.hideTimer) {
            clearTimeout(this.hideTimer);
            this.hideTimer = 0;
          }
        } else {
          // 没有消息显示，定时隐藏所有消息
          if (!this.hideTimer) {
            this.hideTimer = setTimeout(() => {
              this.showMsgList = [];
            }, 6000);
          }
        }
      } else {
        // 打开消息盒子，则清空消息缓存并定时隐藏所有弹幕
        this.recvMsgList = [];
        if (!this.hideTimer) {
          this.hideTimer = setTimeout(() => {
            this.showMsgList = [];
          }, 6000);
        }
      }
    }, 300);
    // 设备检测完成后才显示
    this.addLifecycleTCICStateListener(Constant.TStateDeviceDetect, this.deviceDetectHandler);
    this.addLifecycleTCICStateListener(Constant.TStateChatTipsEnable, (show) => {
      this.active = show;
    });
    this.addLifecycleTCICStateListener(Constant.TStateShowLiveCommenting, (show) => {
      this.showLiveCommenting = show;
    });
    this.makeSureClassJoined(() => {
      this.studentShowJoinExitMsg = TCIC.SDK.instance.isFeatureAvailable('StudentShowJoinExitMsg');
      this.showJoinExitMsg = TCIC.SDK.instance.isFeatureAvailable('ShowJoinExitMsg');
      this.isStudent = !TCIC.SDK.instance.isTeacher() && !TCIC.SDK.instance.isSupervisor();
      const classInfo = TCIC.SDK.instance.getClassInfo();
      this.classId = classInfo.classId;
      const lastActive = window.localStorage.getItem(`${classInfo.classId}_${TCIC.SDK.instance.getUserId()}_quick_im_active`);
      if (lastActive) {
        this.active = (lastActive === 'true');
      }
    });
    this.addLifecycleTCICStateListener(Constant.TStateShowChatBox, (isVisible) => {
      this.quickMsgVisible = !isVisible;
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.deviceOrientation = orientation;
    });
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Recv_IM_Msgs, (msgs) => {
      if (this.quickMsgVisible) {
        const currentUserId = TCIC.SDK.instance.getUserId();
        if (Array.isArray(msgs) && this.recvMsgList.length <= 1000) {
          msgs.forEach((msg) => {
            if (!msg.dataExt?.IsPrivateMsg || (msg.dataExt?.PrivateInfo?.From?.ID === currentUserId || msg.dataExt?.PrivateInfo?.To?.ID === currentUserId)) {
              this.recvMsgList.push(msg);
            }
          });
        }
      }
    });
    if (!this.isSmallScreen) {
      this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Layout, (layout) => {
        this.hasSideIM = TCIC.SDK.instance.getClassLayoutMainFrame(layout).sideIM;
      });
    }
  },

  methods: {
    deviceDetectHandler(state) {
      this.deivceState = state;
    },
  },
};
</script>
<style lang="less">
.quickmsg-show-component {
  height: 100%;
  transition: 0.5s all;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-end;
  pointer-events: none;
  & * * {
    pointer-events: none!important;
  }
  .quick-im-component-handup-body * * {
    pointer-events: auto!important;
  }
  .quick-im-component-msg-body * * {
    pointer-events: auto!important;
  }
  .quick-im-component-msg-list {
    padding-left: 28px;
    pointer-events: none!important;
    padding-bottom: 0px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-end;
    max-width: 535px;
    .quick-im-component-msg-item {
      transition: all 0.25s;
    }

    &-enter, &-leave-to {
      opacity: 0;
      transform: translateY(30px);
    }
  }
  @media screen and (orientation: portrait) {
  .quick-im-component-msg-list{
    padding-left: 0;
  }
  }
}
</style>
