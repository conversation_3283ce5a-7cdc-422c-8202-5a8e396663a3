<template>
  <div class="privacy-policy-container">
    <Box
      class="privacy-policy-component"
      @hide="hide"
    >
      <template #title>
        {{ $t('隐私政策') }}
      </template>
      <template #content>
        <div class="privacy-policy-content">
          <iframe
            :src="privacyPolicyUrl"
            class="privacy-policy-frame"
          />
        </div>
      </template>
    </Box>
  </div>
</template>

<script>
import BaseComponent from '@core/BaseComponent';
import Box from '@/component/ui-component/box-component/Box';

export default {
  components: { Box },
  extends: BaseComponent,
  props: {},
  data() {
    return {
      privacyPolicyUrl: [
        // 隐私政策目前固定放在 1.7.2.mp 这个版本上，不需要按版本区分
        'https://class.qcloudclass.com/1.7.2.mp/mp-privacy-policy.html',
        `?mpAppId=${encodeURIComponent(TCIC.SDK.instance.getFromMpAppId())}`,
        '&standalone=1',
        `&lng=${TCIC.SDK.instance.getLanguage()}`,
      ].join(''),
    };
  },
  mounted() {
  },
  methods: {
  },
};
</script>
<style lang="less">
.privacy-policy-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.privacy-policy-component {
  flex-shrink: 0;
  width: 640px;
  max-width: 90vw;

  .privacy-policy-content {
    width: 100%;
    height: 400px;
    max-height: 60vh;
  }

  .privacy-policy-frame {
    border: none;
    width: 100%;
    height: 100%;
    background: #fff;
  }
}
</style>
