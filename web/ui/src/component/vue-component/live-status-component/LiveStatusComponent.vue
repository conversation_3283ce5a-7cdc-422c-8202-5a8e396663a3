<template>
  <div
    v-if="isJoinClass"
    class="live-status"
  >
    <div
      v-if="classStatus != 1"
      class="live-status__status"
    >
      <div
        class="live-status__over"
        :style="{backgroundImage: 'url(' + cover + ')'}"
      />
      <div class="live-status__content">
        <div class="live-status__tips">
          {{ msgTips }}
        </div>
        <div class="live-status__time">
          {{ timeTips }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18next from 'i18next';
import BaseComponent from '@/component/core/BaseComponent';
import Util from '@/util/Util';

const defaultCover = './static/assets/class_cover.png';

export default {
  extends: BaseComponent,
  data() {
    return {
      classStatus: TCIC.TClassStatus.Not_Start,
      timeTips: '',
      msgTips: i18next.t('直播未开始'),
      isJoinClass: false,
      cover: defaultCover,
      shareCover: '',
      roomInfo: null,
      roleInfo: null,
    };
  },
  mounted() {
    this.roleInfo = TCIC.SDK.instance.getRoleInfo().roleInfo;
    this.roomInfo = TCIC.SDK.instance.getRoleInfo().roomInfo;
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Joined_Class, true).then(() => {
      this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, () => {
        this.updateStatus();
      });
      this.isJoinClass = true;
    });
  },
  methods: {
    updateStatus() {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      this.classStatus = classInfo.status;
      if (classInfo.cover !== null && classInfo.cover.length !== 0) {
        this.cover = classInfo.cover;
        console.log('liveStatus cover', this.cover);
      }
      this.shareCover = classInfo.shareCover;
      if (classInfo.status === TCIC.TClassStatus.Has_Ended) {
        this.msgTips = i18next.t('直播已结束');
        this.timeTips = Util.formatTime(classInfo.endTime, 'YYYY-MM-DD HH:mm');
      } else if (classInfo.status === TCIC.TClassStatus.Not_Start) {
        this.msgTips = i18next.t('直播未开始');
        const nowDate = new Date();
        const today0Timestamp = new Date().setHours(24, 0, 0, 0) / 1000;
        const todayRemain = today0Timestamp - classInfo.startTime;
        if (todayRemain > 0) {
          const startRemain = classInfo.startTime - nowDate.getTime() / 1000;
          if (startRemain > 0) {
            this.timeTips = i18next.t('倒计时 {{arg_0}}', { arg_0: Util.formatTime(today0Timestamp + startRemain, 'HH:mm:ss') });
            setTimeout(() => {
              this.updateStatus();
            }, 1000);
          } else {
            // this.timeTips = i18next.t('已过{{arg_0}}时间', { arg_0: this.roomInfo.startRoom });
            this.timeTips = '';
          }
        } else {
          const tomorrow0Timestamp =  new Date(new Date().setDate(nowDate.getDate() + 1)).setHours(24, 0, 0, 0) / 1000;
          const tomorrowRemain = tomorrow0Timestamp - classInfo.startTime;
          if (tomorrowRemain > 0) {
            this.timeTips = i18next.t('明天 {{arg_0}}', { arg_0: Util.formatTime(classInfo.startTime, 'HH:mm') });
          } else {
            this.timeTips = Util.formatTime(classInfo.startTime, 'YYYY-MM-DD HH:mm');
          }
        }
      }
    },
  },
};
</script>

<style lang="less">

.live-status {
  width: 100%;
  height: 100%;
  .live-status__over {
    background-size: cover;
    width: 100%;
    height: 100%;
  }
  .live-status__number {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: auto;
    left: 18px;
    top: 18px;
    color: #FFFFFF;
    line-height: 20px;
    background: rgba(0, 0, 0, 0.45);
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    button {
      width: 24px;
      height: 24px;
    }
    &.landscape {
      height: 48px;
      border-radius: 24px;
      font-size: 14px;
      padding: 0 12px;
    }
    &.portrait {
      height: 24px;
      border-radius: 12px;
      font-size: 12px;
      padding: 0 10px;
    }
    .live-status__sep {
      background: white;
      width: 1px;
      height: 15px;
      margin: 0px 12px;
    }
  }
  .live-status__status {
    width: 100%;
    height: 100%;
    .live-status__content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      .live-status__tips {
        font-size: 18px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
        line-height: 18px;
        text-align: center;
      }
      .live-status__time {
        margin-top: 8px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 13px;
        text-align: center;
      }
    }
  }
  .live-status__landscape-btn {
    position: absolute;
    right: 18px;
    bottom: 9px;
    background: rgba(0, 0, 0, 0.45);
  }
  button {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    border-radius: 20px;
    padding: 0;
    outline: none;
    width: 40px;
    height: 40px;
    i {
      width: 24px;
      height: 24px;
    }
    .live-status__landscape {
      background: url('./assets/ic_landscape.svg') no-repeat center;
    }
    .live-status__portrait {
      background: url('./assets/ic_portrait.svg') no-repeat center;
    }
  }
}
</style>
