<template>
  <div
    v-if="isStudent && isLiveClass"
    class="invite-stage-dialog-component"
  >
    <el-dialog
      v-if="showDialog"
      destroy-on-close
      :modal="true"
      :visible.sync="showDialog"
      custom-class="invite-stage-dialog"
      @close="onCloseDialog"
    >
      <div class="dialog-content">
        <span>{{ translateTip.inviteStage }}</span>
      </div>
      <div class="footer-btns">
        <el-button
          @click="onReject"
        >
          {{ $t('取消') }}
        </el-button>
        <el-button
          type="primary"
          @click="onApprove"
        >
          {{ $t('上台') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import i18next from 'i18next';
import Constant from '@/util/Constant';
import BaseComponent from '@core/BaseComponent';
import { throws } from 'assert';

export default {
  name: 'InviteStageDialog',
  extends: BaseComponent,
  data() {
    return {
      isStudent: false, // 是否学生
      isLiveClass: false,  // 是否是直播课
      showDialog: false,
      showTimer: null,
      inviteTask: null,
      inviteTaskId: '',
      roleInfo: {},
    };
  },
  computed: {
    translateTip() {
      return {
        inviteStage: i18next.t('{{arg_0}}正在邀请你上台', { arg_0: this.roleInfo.teacher }),
      };
    },
  },
  watch: {
    showDialog(newValue) {
      TCIC.SDK.instance.setState(Constant.TStateLiveTeacherInvitingMe, newValue);
    },
  },
  mounted() {
    const { roleInfo, roomInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    this.makeSureClassJoined(() => {
      const selfId = TCIC.SDK.instance.getUserId();
      this.inviteTaskId = `${Constant.TConstantLiveInviteTaskId}-${selfId}`;
      this.isStudent = !(TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isSupervisor());
      this.isLiveClass = TCIC.SDK.instance.isLiveClass();
      if (this.isLiveClass && this.isStudent) {
        this.addLifecycleTCICEventListener(TCIC.TMainEvent.Task_Updated, this.onTaskUpdate);
        TCIC.SDK.instance.getTasks(0).then((result) => {
          result.tasks.forEach((taskInfo) => {
            this.onTaskUpdate(taskInfo);
          });
        });
      }
    });
  },
  methods: {
    onReset() {
      this.showDialog = false;
      if (this.showTimer) {
        window.clearTimeout(this.showTimer);
      }
      this.showTimer = null;
      this.inviteTask = null;
    },
    onTaskUpdate(taskInfo) {
      if (taskInfo.taskId !== this.inviteTaskId) {
        // 不属于自己连麦请求，不处理
        return;
      }
      const item = JSON.parse(taskInfo.content);
      if (item) {
        this.inviteTask = taskInfo;
        switch (item.status) {
          case Constant.TConstantLiveInviteCreate: {
            if (this.showDialog) {
              return;
            }

            this.showDialog = true;
            this.showTimer = window.setTimeout(this.onCancel, item.timeout * 1000);
            break;
          }
          case Constant.TConstantLiveInviteCancel: {
            this.onReset();
            break;
          }
          default:
            break;
        }
      } else {
        if (this.showDialog) {
          this.onReset();
        }
      }
    },
    onCloseDialog() {
      this.showDialog = false;
      this.onAction(Constant.TConstantLiveInviteCancel);
    },
    onCancel() {
      this.onAction(Constant.TConstantLiveInviteCancel);
    },
    onReject() {
      this.onAction(Constant.TConstantLiveInviteReject);
    },
    onApprove() {
      const hadInvited = TCIC.SDK.instance.getState(TCIC.TMainState.Invite_Stage_Status, true);
      if (hadInvited) {
        // 刚邀请过不处理
        return;
      }
      TCIC.SDK.instance.setState(TCIC.TMainState.Invite_Stage_Status, true);
      TCIC.SDK.instance.setCommandStatus(TCIC.TCommandID.Stage, TCIC.TMainState.Ask_Stage_Status, TCIC.TCommandStatus.Approve);
      const joinedTrtc = TCIC.SDK.instance.getState(TCIC.TMainState.Joined_TRTC, false);
      if (joinedTrtc) {
        this.onAction(Constant.TConstantLiveInviteApprove);
      } else {
        TCIC.SDK.instance.promiseState(TCIC.TMainState.Joined_TRTC, true).then(() => {
          this.onAction(Constant.TConstantLiveInviteApprove);
        });
      }
    },
    onAction(statue) {
      if (this.inviteTask) {
        const item = JSON.parse(this.inviteTask.content);
        item.status = statue;
        TCIC.SDK.instance.updateTask(this.inviteTaskId, JSON.stringify(item))
          .then((task) => {
            // 更新连麦状态并隐藏
            console.log('===>>> : 刷新 : 连麦邀请 成功 : ');
          })
          .catch((error) => {
            // 更新定时器失败
            console.log('===>>> : 刷新 : 发起连麦邀请 失败 : ');
          });
      }

      this.onReset();
    },
  },
};
</script>

<style lang="less">
.invite-stage-dialog-component {
  width: 100%;
  height: 100%;
  .invite-stage-dialog {
    .el-dialog__header {
      .el-dialog__title {
        font-weight: bold;
        font-size: 16px;
        color: #000000;
      }
    }
    width: 420px;
    max-width: 80%;
    border-radius: 4px;
    .el-dialog__body {
      padding: 24px 16px;
    }
    .dialog-content {
      margin-bottom: 30px;
      span {
        font-size: 12px;
        color: #000000;
        line-height: 16px;
        word-break: break-word;
      }
    }
    .footer-btns {
      text-align: right;
      .el-button {
        padding: 8px 19px;
        font-size: 12px;
        background: #fff;
        border-color: #D5D5D5;
        color: #333333;
      }

      .el-button:hover {
        border-color: #0067ED;
        color: #0067ED;
      }

      .el-button--primary {
        background-color: #006EFF;
        border-color: #006EFF;
        color: #FFFFFF;
      }

      .el-button--primary:hover {
        background-color: #4D9AFF;
        border-color: #4D9AFF;
        color: #FFFFFF;
      }
    }
  }
}
</style>
