<template>
  <!--底部缩略图-->
  <div
    v-if="thumbnailImages.length > 0"
    class="thumbnail thumbnail__scroll-now"
  >
    <ul>
      <li
        v-for="(item, index) in thumbnailImages"
        :key="item"
        :class="{'thumbnail__active' : index === currentPage}"
        :style="{width: thumbnailWidth + 'px', height: thumbnailHeight + 'px'}"
        @click="onChangePage(index)"
      >
        <span
          class="thumbnail__text-page"
        >{{ getPageIndexText(index) }}</span>
        <img
          v-lazy="item"
          @error="onLoadThumbnailError"
        >
      </li>
    </ul>
  </div>
</template>
<script>
import Util from '../../../util/Util';
import i18next from 'i18next';
import BaseComponent from '../../core/BaseComponent';
import errorThumbnail from './assets/thumbnail__error.png';
import errorPortraitThumbnail from './assets/thumbnail__portrait-error.png';
import DocumentUtil from '@/util/Document';

const TEduBoard = window.TEduBoard;
export default {
  extends: BaseComponent,
  data() {
    return {
      currentPage: 0,
      totalPage: 0,
      thumbnailImages: [],
      boardInfoList: [],
      thumbnailWidth: 160,
      thumbnailHeight: 90,
      isPortrait: false,
    };
  },
  mounted() {
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Board_Create, true).then(() => {
      this.teduBoard = TCIC.SDK.instance.getBoard();
      // 白板已经创建
      this.initEvent();
      this.updateThumbnail();
      this.updatePage();
    });
  },
  beforeDestroy() {
    this.unInitEvent();
  },
  methods: {
    getPageIndexText(index) {
      if (this.isPortrait) {
        return String(index + 1);
      }
      return i18next.t('第{{arg_0}}页', { arg_0: index + 1 });
    },
    initEvent() {
      // 监听白板翻页
      this.teduBoard = TCIC.SDK.instance.getBoard();
      this.teduBoard.on(TEduBoard.EVENT.TEB_SWITCHFILE, this.updateThumbnail);
      this.teduBoard.on(TEduBoard.EVENT.TEB_GOTOBOARD, this.updatePage);
    },
    onComponentVisibilityChange(visible) {
      // 更新缩略图，保证第一次加载失败的缩略图可以重新加载
      this.updateThumbnail();
      // 当组件可见时，将选中的缩略图滚动到中间
      this.scrollActiveThumbnailToView(false);
    },
    scrollActiveThumbnailToView(animate) {
      // 当组件可见时，将选中的
      const activeItem = document.getElementsByClassName('thumbnail__active')[0];
      if (activeItem) {
        activeItem.scrollIntoView({
          behavior: animate ? 'smooth' : 'auto',
          block: 'end',
          inline: 'center',
        });
      }
    },
    updateThumbnail() {
      if (!this.teduBoard) return;
      const fileId = this.teduBoard.getCurrentFile();
      const fileInfo = this.teduBoard.getFileInfo(fileId);
      console.log('updateThumbnail', fileInfo, this.isFileHasPage(fileInfo));
      if (this.isFileHasPage(fileInfo)) {
        if (!fileInfo.ratio) {
          // 宽高比不存在，默认值为16:9
          fileInfo.ratio = '16:9';
        }
        const wh = fileInfo.ratio.split(':');
        const ratio = wh[0] / wh[1];
        // 判断横竖屏课件
        this.isPortrait = ratio < 1;
        this.thumbnailWidth = ratio * this.thumbnailHeight;
        // 静态转码文件或动态转码文件
			  const thumbnailImages =	this.teduBoard.getThumbnailImages(fileId);
        if (thumbnailImages && thumbnailImages.length > 0) {
          this.thumbnailImages = thumbnailImages;
          this.boardInfoList = fileInfo.boardInfoList;
          this.updatePage();
        }
      } else {
        this.thumbnailImages = [];
      }
    },
    updatePage() {
      const fileId = this.teduBoard.getCurrentFile();
      const fileInfo = this.teduBoard.getFileInfo(fileId);
      if (this.isFileHasPage(fileInfo)) {
        // 静态转码文件或动态转码文件
        this.currentPage = fileInfo.currentPageIndex;
        setTimeout(() => {
          this.scrollActiveThumbnailToView(true);
        }, 50);
      }
    },
    unInitEvent() {
      if (this.teduBoard) {
        this.teduBoard.off(TEduBoard.EVENT.TEB_SWITCHFILE, this.updateThumbnail);
        this.teduBoard.off(TEduBoard.EVENT.TEB_GOTOBOARD, this.updatePage);
      }
    },
    async onChangePage(index) {
      await DocumentUtil.addSnapshotMark('onChangePage');
      const boardId = this.boardInfoList[index].boardId;
      this.currentPage = index;
      this.teduBoard.gotoBoard(boardId);
    },
    isFileHasPage(fileInfo) {
      return Util.isFileHasPage(fileInfo);
    },
    onLoadThumbnailError(event) {
      const img = event.srcElement;
      img.src = this.isPortrait ? errorPortraitThumbnail : errorThumbnail;
    },
  },
};
</script>

<style lang="less">
  @--color-primary: #006EFF;
  @--color-public: #14181D;
  @--color-disable: #b9bbbc;
  @--color-back: #1C2131;

  .light .thumbnail {
    --bg-color: #FAFAF5;
  }

	.thumbnail {
		display: flex;
		height: 129px;
		background: var(--bg-color, @--color-back);
		overflow: auto;
		padding: 10px 16px;
    &::-webkit-scrollbar {
      height: 29px;
      background: #414551;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #FFF;
      border-radius: 14px;
      border: #414551 10px solid;
    }
		/*超过一屏的时候加样式"thumbnail__scroll-now"*/
		&.thumbnail__scroll-now {
			justify-content: flex-start;
			& >ul {
				justify-content: flex-start;
			}
		}
		& >ul {
			display: flex;
			justify-content: center;
			li {
				position: relative;
				width: 160px;
				height: 90px;
				margin-right: 16px;
				border-radius: 4px;
				background: #ddd;
        overflow: hidden;
        >img {
          width: 100%;
        }
				&:last-child {
					margin-right: 0
				}
				&.thumbnail__active {
					box-shadow: 0 0 0 2px @--color-primary;
					.thumbnail__text-page {
            background: @--color-primary
          }
				}
				&:hover {
					cursor: pointer
				}
				.thumbnail__text-page {
					position: absolute;
					bottom: 4px;
					left: 4px;
          margin-right: 4px;
					display: flex;
					align-items: center;
					padding: 6px;
					background: rgba(@--color-back, .7);
					border-radius: 4px;
					font-size: 12px;
					color: #DCEBEB;
					font-weight: 300;
				}
			}
		}
	}
</style>
