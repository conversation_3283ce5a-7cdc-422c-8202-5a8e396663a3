<template>
  <el-popover
    :value="showEndTips && !hasShowEndTips"
    trigger="manual"
    effect="dark"
    placement="bottom-start"
    popper-class="class-duration-popper"
  >
    <div class="class-end-tip">
      {{ $t('距离结束还有') }}
      <span>{{ showTipTime }}</span>
      {{ $t('分钟') }}
    </div>
    <div :class="['class-duration-sub-component header__span--duration', {'small-screen': isMobile}]" slot="reference">
      <template v-if="duration <= 0">
        <i class="un-course" />
        <span class="class-duration-type">{{ classTime }}</span>
      </template>
      <template v-else>
        <i />
        <template v-if="hasShowEndTips">
          <span class="class-duration-type">{{ $t('倒计时') }}: &nbsp;</span>
          <span
            v-if="duration > 0"
            class="class-duration-time"
          >{{ countDownTime }}</span>
          <span
            v-if="duration > 0 "
            class="class-duration-num"
          > ({{ getUserNumberText(onlineNumber) }})</span>
        </template>
        <template v-else>
          <span class="class-duration-type">{{ classTime }}</span>
          <span
            v-if="duration > 0"
            class="class-duration-time"
          >{{ getFormatTime(duration) }}</span>
          <span
            v-if="duration > 0 "
            class="class-duration-num"
          > ({{ getUserNumberText(onlineNumber) }})</span>
        </template>
      </template>
    </div>
  </el-popover>
</template>
<script>
import i18next from '@util/i18nextByKey';
import Util from '@util/Util';
import BaseComponent from '@core/BaseComponent';
import Constant from "@util/Constant";


const SHOW_TIPS_TIME = 3;

export default {
  name: 'ClassDurationSubComponent',
  extends: BaseComponent,
  data() {
    return {
      roomInfo: null,
      onlineNumber: 0,
      showTipTime: SHOW_TIPS_TIME,
      countDownTime: '00:00',
      classStatus: TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status),
      showEndTips: false,
      isTeacher: TCIC.SDK.instance.isTeacher(),
      duration: 0, // 秒级
      classTime: i18next.t('正在进入...'),
      startTime: null,
      canDelayTime: true, // 是否可以拖堂
      tomorrowTime: 0,
      isLiveClass: false, // 是否为直播课
      isMobile: false,
      hasShowEndTips: false,
    };
  },
  mounted() {
    this.roomInfo = TCIC.SDK.instance.getRoleInfo().roomInfo;
    this.classTime = i18next.t('正在进入{{arg_0}}...', { arg_0: this.roomInfo.name });
    this.isMobile = TCIC.SDK.instance.isMobile();
    const classInfo = TCIC.SDK.instance.getClassInfo();
    this.canDelayTime = classInfo.endDelayTime !== -1;

    this.makeSureClassJoined(() => {
      this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Duration, (duration) => {
        if (typeof duration !== 'undefined') {
          this.duration = duration;
          const classStatus = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Not_Start);
          // TCIC.SDK.instance.setState(Constant.TStateImmerseMode, false);
          // console.log('canDelayTime', classInfo.endTime - Math.floor(new Date().getTime() / 1000) - SHOW_TIPS_TIME * 60);
          if (
            classStatus === TCIC.TClassStatus.Already_Start
            && !this.canDelayTime
            && this.isTeacher
            && Math.abs(classInfo.endTime - Math.floor(new Date().getTime() / 1000) - SHOW_TIPS_TIME * 60) <= 2
          ) {
            if (!this.hasShowEndTips) {
              TCIC.SDK.instance.setState(Constant.TStateImmerseMode, false);
              setTimeout(() => {
                this.showEndTips = true;
                setTimeout(() => {
                  this.showEndTips = false;
                  this.hasShowEndTips = true;
                }, 3000);
              }, 500);
            }
          }
          if (this.hasShowEndTips) {
            this.countDownTime = Util.formatDuration(Math.abs(classInfo.endTime - Math.floor(new Date().getTime() / 1000)), 'mm:ss');
          }
          this.updateClassTime();
        }
      });
      this.addLifecycleTCICStateListener(TCIC.TMainState.Member_Count, (onlineNumber) => {
        this.onlineNumber = onlineNumber;
      });
      this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, (classStatus) => {
        this.classStatus = classStatus;
        setTimeout(() => {
          this.updateClassTime();
        }, 0);
      });
      this.startTime = TCIC.SDK.instance.getClassInfo().startTime;
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);  // 获取明天凌晨时间点
      this.tomorrowTime = Math.round(+ tomorrow / 1000);
    });
  },
  methods: {
    updateClassTime() {
      const classStatus = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Not_Start);
      this.startTime = TCIC.SDK.instance.getClassInfo().startTime;
      // fix: 阿卡索问题,已经上课,但是duration是负值,显示状态不对.
      if (this.duration < 0 && classStatus !== TCIC.TClassStatus.Already_Start) {
        if (!this.startTime) return;
        // 20241218 产品需求改为仅展示上课状态，无需展示上课时间
        // https://tapd.woa.com/tapd_fe/70068217/iteration/card/1070068217002053841?q=f86a7628e8e353b879513b5697ec34ae&dialog_preview_id=story_1070068217121061400
        this.classTime = i18next.t('roomStatusDesc.notStart.simple', this.roomInfo);
        // if (this.startTime < this.tomorrowTime || (+ new Date() / 1000) >= this.tomorrowTime) {
        //   if (this.isSmallScreen) {
        //     this.classTime = i18next.t('roomStatusDesc.notStart.simple', this.roomInfo);
        //   } else {
        //     const i18nArgs = { arg_0: Util.formatDuration(-this.duration, 'HH:mm:ss'), ...this.roomInfo };
        //     this.classTime = i18next.t('roomStatusDesc.notStart.today', i18nArgs);
        //   }
        // } else if (this.startTime < this.tomorrowTime + 24 * 60 * 60) {
        //   const i18nArgs = { arg_0: i18next.t('明天') + Util.formatTime(this.startTime, 'HH:mm'), ...this.roomInfo };
        //   this.classTime = i18next.t('roomStatusDesc.notStart.otherday', i18nArgs);
        // } else if (this.startTime < this.tomorrowTime + 48 * 60 * 60) {
        //   const i18nArgs = { arg_0: i18next.t('后天') + Util.formatTime(this.startTime, 'HH:mm'), ...this.roomInfo };
        //   this.classTime = i18next.t('roomStatusDesc.notStart.otherday', i18nArgs);
        // } else {
        //   const i18nArgs = { arg_0: Util.formatTime(this.startTime, 'MM-DD HH:mm'), ...this.roomInfo };
        //   this.classTime = i18next.t('roomStatusDesc.notStart.otherday', i18nArgs);
        // }
      } else if (this.duration === 0) {
        if (classStatus === - 1) {
          this.classTime = i18next.t('roomStatusDesc.entering', this.roomInfo);
        } else if (classStatus === TCIC.TClassStatus.Has_Ended) {
          this.classTime = i18next.t('roomStatusDesc.hasEnded', this.roomInfo);
        } else if (classStatus === TCIC.TClassStatus.Has_Expired) {
          this.classTime = i18next.t('roomStatusDesc.hasExpired', this.roomInfo);
        } else if (classStatus === TCIC.TClassStatus.Already_Start) {
          this.classTime = i18next.t('roomStatusDesc.hasStarted', this.roomInfo);
        } else {
          if (this.isSmallScreen) {
            this.classTime = i18next.t('roomStatusDesc.notStart.simple', this.roomInfo);
          } else {
            this.classTime = i18next.t('roomStatusDesc.hasDelayed', this.roomInfo);
          }
        }
      } else if (classStatus === TCIC.TClassStatus.Has_Ended) {
        this.classTime = i18next.t('roomStatusDesc.hasEnded', this.roomInfo);
        this.duration = 0;
      } else {
        this.isLiveClass = TCIC.SDK.instance.isLiveClass(); // 课程状态
        this.classTime = i18next.t('roomStatusDesc.hasStartedWithExtraInfo', this.roomInfo);
      }
    },
    toggleTheme() {
      // 调试代码
      if (document.body.classList.contains('dark')) {
        document.body.classList.replace('dark', 'light');
      } else {
        document.body.classList.replace('light', 'dark');
      }
    },
    getFormatTime(time) {
      return Util.formatDuration(time, 'HH:mm:ss');
    },
    getUserNumberText(num) {
      return i18next.t('{{arg_0}}人在线', { arg_0: num });
    },
  },
};
</script>
<style lang="less">
.dark .class-duration-popper.el-popper{
  margin-top: 12px!important;
  background: #343948;
  border: none;
}
.class-end-tip{
  font-size: 12px;
  color: #fff;
  +  .popper__arrow{
    display: block!important;
  }
}
.class-duration-sub-component {
  min-width: 200px;

  i {
    display: flex;
    width: 8px;
    height: 8px;
    margin-right: 8px;
    border-radius: 8px;
    background-color: #13A449;

    &.un-course {
      background-color: #FA6400;
    }
  }
  &.small-screen {
    .class-end-tip span {
      font-size: 16px;
    }
    .class-duration-type {
      color: #969FB4;
    }
    .un-course {
      width: 5px;
      height: 5px;
      margin-right: 5px;
    }

  }
  &.header__span--duration {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: var(--text-color, #FFFFFF);
  }
  @media (max-width: 530px) {
    i {
      margin-right: 2px;
      width: 6px;
      height: 6px;
    }
    &.header__span--duration {
      font-size: 10px;
    }

  }
}
</style>
