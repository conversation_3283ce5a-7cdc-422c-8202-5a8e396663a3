<template>
  <div class="timer-tool-sub-component">
    <!--V1.3.4暂时去掉提示，有文字:content="component.label"-->
    <el-tooltip
      :manual="interactiveIsTouchEvent()"
      class="item"
      placement="bottom"
      popper-class="tooltip-no-content"
    >
      <button
        class="header__button button--secondary"
        :class="{active: component.active}"
        @click="open"
      >
        <IconTimer class="header__i i--menu icon-timer-tool" />{{ $t('定时器') }}
      </button>
    </el-tooltip>
  </div>
</template>

<script>
import BaseComponent from '@/component/core/BaseComponent';
import IconTimer from '../assets/svg-component/ic_tool_timer.svg';

export default {
  name: 'TimerToolSubComponent',
  components: {
    IconTimer,
  },
  extends: BaseComponent,
  props: {
    component: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      isMobile: TCIC.SDK.instance.isMobile(),
      componentInstance: null,
    };
  },
  mounted() {
  },
  beforeDestroy() {
  },
  methods: {
    // 随堂测
    async open() {
      this.componentInstance = this.componentInstance
          || TCIC.SDK.instance.getComponent('timer-tool-component')
          || await TCIC.SDK.instance.loadComponent('timer-tool-component');
      TCIC.SDK.instance.updateComponent('timer-tool-component', {
        display: 'block',
        zIndex: 1000,
      });
    },
  },
};
</script>
<style lang="less">
.timer-tool-sub-component {
  .icon-timer-tool {
    background-size: 90% !important;
  }
}
</style>
