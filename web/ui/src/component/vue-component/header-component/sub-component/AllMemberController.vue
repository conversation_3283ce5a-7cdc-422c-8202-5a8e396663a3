<template>
  <div class="whole-header-sub-component">
    <el-tooltip
      :manual="interactiveIsTouchEvent()"
      class="item"
      :disabled="(isMobile && !btnDisabled) || visible"
      :content="!isClassStarted ? translateTip.roomNotStart : $t('全员操作')"
      placement="bottom"
      :hide-after="2000"
    >
      <el-popover
        ref="popover"
        popper-class="header-component-popover more-popover inComponent submenu-popover all-member-controller-component"
        placement="bottom"
        trigger="click"
        @show="onPopOverShow"
        @hide="onPopOverHide"
      >
        <ul class="header-more-box flex-column">
          <li
            v-if="showNotice"
            class="header-menu-li pointer"
            data-user-event="ALLMemberController-notice"
            @click="displayNotice"
          >
            <IconAlarm class="header__i i--menu whole-ic-notice" />{{ $t('公告') }}
          </li>
          <li
            v-if="!isLive && canMemberStageUp"
            class="header-menu-li "
            data-user-event="ALLMemberController-muteall"
          >
            <MuteAll />
          </li>
          <li
            v-if="!isCoTeaching"
            class="header-menu-li pointer"
            data-user-event="ALLMemberController-prize"
            @click="dispatchTrophy"
          >
            <IconPrize class="header__i i--menu whole-ic-prize" />{{ $t('全员奖励') }}
          </li>
          <!--<li class="header-menu-li pointer" @click="resetVideoPosition">
            <i class="header__i i&#45;&#45;menu whole-ic-homing"></i>
            全员归位
          </li>-->
          <!--          <li class="header-menu-li" @click="allStageDown">-->
          <!--            <i class="header__i i&#45;&#45;menu whole-ic-down"></i>-->
          <!--            全员下台-->
          <!--          </li>-->
        </ul>
        <button
          slot="reference"
          class="header__button button--secondary"
          data-user-event="ALLMemberController-navwhole"
          :disabled="btnDisabled"
          :class="{active: visible}"
        >
          <div class="tool-item-wrap">
            <IconWhole class="header__i i--menu ic_nav_whole" style="padding: 0!important" />
            <span class="header__btn-text">{{ $t('全员操作') }}</span>
          </div>
        </button>
      </el-popover>
    </el-tooltip>
  </div>
</template>

<script>

import i18next from 'i18next';
import BaseComponent from '@/component/core/BaseComponent';
import MuteAll from '@/component/vue-component/member-list-component/sub-component/MuteAll';
import Constant from '@/util/Constant';
import IconWhole from '../assets/svg-component/ic_nav_whole.svg';
import IconAlarm from '../assets/svg-component/ic_nav_alarm.svg';
import IconPrize from '../assets/svg-component/ic_all_prize.svg';
import Lodash from 'lodash';
export default {
  name: 'AllMemberControllerSubComponent',
  components: {
    MuteAll,
    IconWhole,
    IconAlarm,
    IconPrize,
  },
  extends: BaseComponent,
  props: {
    component: {
      type: Object,
      default: () => null,
    },
  },

  data() {
    return {
      isMobile: TCIC.SDK.instance.isMobile(),
      disabled: true,
      value: true,
      isClassStarted: false,
      visible: false,
      // 是否是直播课
      isLive: false,
      // 是否是双师课
      isCoTeaching: false,
      // 是否是1v1课
      isOneOnOneClass: false,
      // 是否是老师
      isTeacher: false,
      // 是否是助教
      isAssistant: false,
      // 成员是否能上台
      canMemberStageUp: true,
      localComponent: this.component,
      roomInfo: {},
      roleInfo: {},
    };
  },
  computed: {
    btnDisabled() {
      return !this.isClassStarted;
    },
    showNotice() {
      return this.isTeacher || this.isAssistant;    // 非双师课堂的老师才展示公告
    },
    translateTip() {
      return {
        roomNotStart: i18next.t('开始{{arg_0}}后才可开启全员操作', { arg_0: this.roomInfo.startRoom }),
      };
    },
  },
  watch: {
    'localComponent.active'(val, oldVal) {
      if (val !== oldVal && !val) {
        this.onHide();
      }
    },
  },

  mounted() {
    const { roleInfo, roomInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    this.roomInfo = roomInfo;
    this.isClassStarted = (TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Not_Start) === TCIC.TClassStatus.Already_Start);
    this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, (status) => {
      this.isClassStarted = (status === TCIC.TClassStatus.Already_Start);
    });
    this.makeSureClassJoined(this.onJoinClass);
  },

  beforeDestroy() {
  },

  methods: {
    onJoinClass() {
      this.isLive = TCIC.SDK.instance.isLiveClass();
      this.isCoTeaching = TCIC.SDK.instance.isCoTeachingClass();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      this.isOneOnOneClass = TCIC.SDK.instance.isOneOnOneClass();
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.canMemberStageUp = TCIC.SDK.instance.getClassInfo().maxRtcMember !== 0;
    },
    // 全员归位
    resetVideoPosition: Lodash.throttle(() => {
      TCIC.SDK.instance.notify(Constant.TStateResetVideoPosition, { reset: true }, true);
    }, 2000, {
      leading: true,
      trailing: false,
    }),

    // 全员奖杯
    dispatchTrophy: Lodash.throttle(() => {
      if (TCIC.SDK.instance.getState(TCIC.TMainState.Member_Count) === 0) {
        // 用 Lodash.throttle 时，this为空
        const { roleInfo, roomInfo } = TCIC.SDK.instance.getNameConfig();
        window.showToast(i18next.t('{{arg_0}}中无{{arg_1}}，不可全员奖励', { arg_0: roomInfo.room, arg_1: roleInfo.student }));
        return;
      }
      const trophyComponent = TCIC.SDK.instance.getComponent('trophy-component');
      trophyComponent.getVueInstance().distributeToEveryone();
    }, 2000, {
      leading: true,
      trailing: false,
    }),

    // 公告
    displayNotice: Lodash.throttle(() => {
      TCIC.SDK.instance.getComponent('notice-dialog').getVueInstance()
        .show();
    }, 2000, {
      leading: true,
      trailing: false,
    }),
    // 全员下台
    // allStageDown: Lodash.throttle(function () {
    //   // this.stageDown();
    // }, 1000, {
    //   leading: true,
    //   trailing: false,
    // }),

    // 下台
    // stageDown() {
    //   const classInfo = TCIC.SDK.instance.getClassInfo();
    //   const param = {
    //     classId: classInfo.classId,
    //     classType: classInfo.classType,
    //     userId: user.userId,
    //     actionType: TCIC.TMemberActionType.Stage_Down,
    //   };
    //   TCIC.SDK.instance.memberAction(param)
    //       .catch((err) => {
    //         window.showToast(err.errorMsg, 'error');
    //       });
    // },


    onHide() {
      this.$refs.popover.doClose();
    },
    onPopOverShow() {
      this.visible = true;
      this.localComponent.active = true;
      const notice = document.querySelector('li.header-menu-li.pointer');
      const muteAll = document.querySelector('div.mute-all-sub-component');
      notice && window.tbm.updateTarget('header', [window.tbm.generateNode(notice)], 'notice');
      muteAll && window.tbm.updateTarget('header', [window.tbm.generateNode(muteAll)], 'muteall');
    },
    onPopOverHide() {
      this.visible = false;
      this.localComponent.active = false;
      window.tbm.updateTarget('header', [], 'notice');
      window.tbm.updateTarget('header', [], 'muteall');
    },
  },
};
</script>
<style lang="less">
.submenu-popover {
  .whole-ic-prize {
    background-image: url('../assets/svg-component/ic_all_prize.svg');
  }

  .whole-ic-notice {
  }

  .whole-ic-homing {
    background-image: url('../assets/ic_all_homing.svg');
  }

  .whole-ic-down {
    background-image: url('../assets/ic_all_down.svg');
  }
}

.whole-header-sub-component {

  .inactive {
    background: rgb(255, 255, 255)
  }
}

.all-member-controller-component {
  .header-menu-li {
    &.pointer {
      cursor: pointer;
      opacity: 0.85;
    }

    &:hover {
      opacity: 1;
    }
  }

}
</style>
