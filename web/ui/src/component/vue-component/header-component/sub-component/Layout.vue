<template>
  <div class="layout-sub-component">
    <el-tooltip
      :manual="interactiveIsTouchEvent()"
      :disabled="isMobile || localComponent.active"
      class="item"
      :content="$t('布局')"
      placement="bottom"
    >
      <el-popover
        ref="popover"
        placement="bottom"
        :width="localComponent.width"
        popper-class="header-component-popover inComponent submenu-popover layout-popover"
        trigger="click"
        @show="onPopoverShow"
        @hide="onPopoverHide"
      >
        <ClassLayout
          ref="classlayout"
          @hide="onHide"
        />
        <button
          slot="reference"
          :ref="localComponent.name"
          class="header__button button--secondary"
          :class="{active: localComponent.active}"
        >
          <div class="tool-item-wrap">
            <el-badge
              :value="localComponent.badge"
              :max="99"
              class="badge"
              :hidden="localComponent.badge === 0"
            >
              <IconLayout :class="`header__i i--menu ic-nav-layout`" style="padding: 0!important"/>
            </el-badge>
            <span class="header__btn-text">{{ $t('布局') }}</span>
          </div>
        </button>
      </el-popover>
    </el-tooltip>
  </div>
</template>

<script>
import BaseComponent from '@/component/core/BaseComponent';
import ClassLayout from '@/component/vue-component/class-layout-component/ClassLayout.vue';
import IconLayout from '../assets/svg-component/ic_layout.svg';

export default {
  name: 'LayoutSubComponent',
  components: {
    ClassLayout,
    IconLayout,
  },
  extends: BaseComponent,
  props: {
    component: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      isMobile: TCIC.SDK.instance.isMobile(),
      localComponent: this.component,
    };
  },
  mounted() {
    console.debug('[layout-component] loaded => ', this.component);
  },
  beforeDestroy() {
  },
  methods: {
    onHide() {
      this.$refs.popover.doClose();
    },
    onPopoverShow() {
      this.localComponent.active = true;
      this.$nextTick(() => {
        window.tbm.updateTarget(
          'header',
          Array.from(this.$refs.classlayout.$el.querySelectorAll('li.header-menu-li'))
            .map(item => window.tbm.generateNode(item)),
          'layout',
        );
      });
    },
    onPopoverHide() {
      this.localComponent.active = false;
      window.tbm.updateTarget('header', [], 'layout');
    },
  },
};
</script>
<style lang="less">
.layout-popover {
  .header-menu-li {
    cursor: pointer;

    &:hover,
    &.active {
      color: #006eff !important;
    }
  }
}
</style>
