<template>
  <div
    class="fullscreen-sub-component"
    @click="requestFullscreen"
  >
    <IconFull class="header__i icon-fullscreen" />
  </div>
</template>

<script>
import screenfull from 'screenfull';
import IconFull from '../assets/svg-component/ic_fullscreen.svg';

export default {
  deta() {
    return {

    };
  },
  components: {
    IconFull,
  },
  methods: {
    requestFullscreen() {
      // document.documentElement.requestFullscreen();
      if (!screenfull.isEnabled) {
        window.showToast('当前浏览器不支持全屏');
        return;
      }
      screenfull.toggle();
    },
  },
};
</script>

<style lang="less">
.fullscreen-sub-component {
  display: flex;
  align-items: center;
  .header__i.icon-fullscreen {
    width: 24px;
    height: 24px;
    margin: 8px;
    background-repeat: no-repeat;
    background-position: center;
  }
}
</style>
