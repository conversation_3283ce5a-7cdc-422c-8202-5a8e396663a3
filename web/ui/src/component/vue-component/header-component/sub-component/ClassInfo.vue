<template>
  <div class="class-info-sub-component">
    <el-tooltip
      :disabled="!classNameTooltipEnable"
      :content="className"
      placement="bottom"
    >
      <span
        ref="classNameRef"
        class="header__className"
      >{{ className }}</span>
    </el-tooltip>
    <MixedPopper
      ref="popper"
      :type="isSmallScreen ? 'drawer' : 'popover'"
      :title="isSmallScreen ? className : ''"
      :visible.sync="classInfoVisible"

      :popover-placement="isSmallScreen ? 'right-start' : 'top-start'"
      :popover-width="popoverWidth"
      :popover-trigger="isMobile ? 'manual' : 'hover'"
      :popover-visible-arrow="false"
      :popover-offset="offset"
      :popover-close-delay="closeDelay"
      :popover-popper-class="`common-tooltip class-info-tooltip ${isSmallScreen ? 'mobile' : ''}`"

      :drawer-show-header-split-line="false"
      drawer-custom-class="class-tips-drawer"

      @popover-after-enter="onPopupShow()"
      @popover-after-leave="onPopupHide()"
      @popover-show="handleSelfPopoverShow"
    >
      <div>
        <template v-if="ready">
          <div class="header__tips">
            <div class="class-tips">
              <table>
                <tr>
                  <td>{{ roomInfo.roomID }}</td>
                  <td>
                    <span>{{ classInfo.roomId }}</span>
                    <span
                      v-clipboard:copy="classInfo.roomId"
                      v-clipboard:success="onCopySuccess"
                      v-clipboard:error="onCopyError"
                      class="btn-copy"
                    >
                      <i class="icon icon-copy el-icon--left" />
                    </span>
                  </td>
                </tr>
                <tr>
                  <td>{{ $t('用户ID') }}</td>
                  <td>
                    <span>{{ classInfo.userId }}</span>
                  </td>
                </tr>
                <tr>
                  <td>{{ $t('UI版本') }}</td>
                  <td>
                    <span>{{ classInfo.uiVersion }}</span>
                  </td>
                </tr>
                <tr>
                  <td>{{ $t('TCIC版本') }}</td>
                  <td>
                    <span>{{ classInfo.tcicVersion }}</span>
                  </td>
                </tr>
                <tr v-if="classInfo.sdkVersion">
                  <td>{{ $t('SDK版本') }}</td>
                  <td>
                    <span>{{ classInfo.sdkVersion }}</span>
                  </td>
                </tr>
                <tr v-if="classInfo.platform">
                  <td>{{ $t('客户端') }}</td>
                  <td>
                    <span>{{ classInfo.platform }}</span>
                  </td>
                </tr>
                <!-- <tr v-if="classInfo.appVersion">
                  <td>APP</td>
                  <td>
                    <span>{{ classInfo.appVersion }}</span>
                  </td>
                </tr> -->
              </table>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="header__tips">
            {{ translateTip.roomLoading }}
          </div>
        </template>
      </div>
      <div
        v-if="ready"
        slot="footer"
        class="btn-wrapper mobile-drawer__btn-group"
      >
        <el-button
          v-clipboard:copy="classTips"
          v-clipboard:success="onCopySuccess"
          v-clipboard:error="onCopyError"
          size="medium"
          :type="isSmallScreen ? 'default' : 'primary'"
          class="main-button"
        >
          <i
            v-if="isSmallScreen"
            class="icon icon-copy el-icon--left"
          />
          {{ $t('全部复制') }}
        </el-button>
        <el-button
          size="medium"
          :type="isSmallScreen ? 'default' : 'primary'"
          class="main-button"
          @click="showFeedbackModal"
        >
          <i
            v-if="isSmallScreen"
            class="icon icon-feedback el-icon--left"
          />
          {{ $t('反馈问题') }}
        </el-button>
        <el-button
          v-if="!isMobileNative"
          size="medium"
          :type="isSmallScreen ? 'default' : 'primary'"
          class="main-button"
          @click="selfRepair"
        >
          {{ $t('设备检测') }}
        </el-button>
      </div>
      <div
        slot="reference"
        :class="['icon-wrapper', {'small-screen': isSmallScreen}]"
      >
        <button
          :class="['icon-btn']"
          @click="triggerPopover"
        >
          <IconInfo
            :class="['icon-classname']"
            name="info"
          />
        </button>
        <i class="question-icon" />
      </div>
    </MixedPopper>
    <!-- 网络状态 -->
    <NetworkQuality v-if="isSmallScreen && !isRtmpMode" />
    <ForceRefresh v-if="!isMobileNative && isSmallScreen" />

    <span
      v-if="!isInDetectPage &&showInvalidPanel"
      class="c-detected-error-info"
      @click="()=>showInvalidPanel=false"
    >{{ $t("您的浏览器部分功能缺失，可能引起连麦异常") }}</span>
    <div ref="detectEl" />
    <el-dialog
      :title="$t('提交反馈')"
      :visible.sync="feedbackDialog"
      append-to-body
      custom-class="feedback-dialog"
      :width="isSmallScreen ? isPortrait ? '80%' : '50%' : '400px'"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      modal
      destroy-on-close
    >
      <div>
        <div style="display: flex">
          <span style="color: red; margin-right: 10px">*</span>
          <el-input
            v-model="content"
            :placeholder="$t('请简单描述你遇到的问题')"
            clearable
          />
        </div>
        <div style="margin: 10px 0" />

        <div style="display: flex">
          <span style="color: red; margin-right: 10px">*</span>
          <el-input
            v-model="personalInfo"
            :placeholder="$t('输入联系方式，如：QQ、微信、邮箱或手机号')"
            clearable
          />
        </div>
        <template v-if="isElectron">
          <el-checkbox
            v-model="agreement"
            class="pt10"
          >
            {{ $t('发送系统信息') }}
          </el-checkbox>
          <p
            v-if="agreement"
            class="agreement"
          >
            {{ translateTip.agreement }}
          </p>
        </template>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          size="mini"
          :disabled="!agreement && content.length === 0"
          @click="submitFeedback"
        >
          {{ $t('提 交') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="''"
      :visible.sync="detectVisible"
      width="420px"
      destroy-on-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      :center="true"
      modal
      append-to-body
    >
      <!-- <span
        v-if="isSafeInput"
        class="message"
        v-html="message"
      /> -->
      <!-- <div
        v-for="(option, index) in options"
        :key="index"
        class="option"
      >
        <el-checkbox v-model="option.value">
          {{ option.text }}
        </el-checkbox>
      </div> -->
      <div
        class="exception"
      >
        <el-steps
          :active="detectStep"
        >
          <!--
            :description="`${detectStep == index?'检测中..':detectFailed.include(item.id)?'异常':'正常'}`" -->
          <el-step
            v-for="(item,index) in detectAction"
            :key="item.id"
            :title="item.text"
            :description="procesHandler(item,index).text"
            :status="procesHandler(item,index).status"
          />
        </el-steps>
        <!-- <div v-if="detectFailed.length">
          {{ detectMsg }}
        </div> -->
        <div
          ref="detectWrap"
          :style="{minHeight: '100px',
                   marginTop:'10px'}"
        />
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          :disabled="detectHideBtnDisabled"
          type="primary"
          @click="detectHideHandler"
        >
          {{ $t('确定') }}
        </el-button>
        <el-button
          type="secondary"
          @click="reloadHandler"
        >
          {{ $t('刷新') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import i18next from 'i18next';
import BaseComponent from '@/component/core/BaseComponent';
import Version from '@/pages/class/version';
import Constant from '@/util/Constant';
import NetworkQuality from './NetworkQuality';
import { UAParser } from 'ua-parser-js';
import { headerEventHub } from '../HeaderEventHub';
import MixedPopper from '@/component/ui-component/mixed-popper-component/MixedPopper';
import DetectUtil from '@vueComponent/device-detect-component/DetectUtil';
import ForceRefresh from './ForceRefresh';
import IconInfo from '../assets/svg-component/warning-icon.svg';


const sleep = time => new Promise(resolve => setTimeout(resolve, time));

const confirmHandler = function (content) {
  return new Promise((reolve, reject) => {
    TCIC.SDK.instance.showMessageBox(
      i18next.t('提示'),
      // eslint-disable-next-line quotes, max-len
      `${content}`,
      [i18next.t('是'), i18next.t('否')],
      (index) => {
        if (index === 0) {
          reolve();
        } else {
          reject();
        }
      },
    );
  });
};

export default {
  name: 'ClassInfoSubComponent',
  components: {
    NetworkQuality,
    MixedPopper,
    ForceRefresh,
    IconInfo,
  },
  extends: BaseComponent,
  data() {
    return {
      test: 1,
      ready: false,
      className: '',
      classTips: null,
      classInfo: null,
      content: '',
      offset: -20,
      isMobile: TCIC.SDK.instance.isMobile(),
      isElectron: TCIC.SDK.instance.isElectron(),
      isMobileNative: TCIC.SDK.instance.isMobileNative(),
      closeDelay: TCIC.SDK.instance.isMobile() ? 4000 : 500,
      popoverTask: null,
      classNameTooltipEnable: false,
      systemInfo: null,
      feedbackDialog: false,
      agreement: true,
      isSubmitting: false,
      loadingReqId: 0,
      personalInfo: '',
      feedbackId: -1,
      roomInfo: {},
      isPortrait: false,
      classInfoVisible: false,
      isRtmpMode: false,
      detectVisible: false,
      detectHideBtnDisabled: true,
      showInvalidPanel: false,
      showValidPanel: false,
      isInDetectPage: TCIC.SDK.instance.getState(Constant.TStateDeviceDetect),
      detectStep: 0,
      detectAction: [{
          id: 'camera',
          text: i18next.t('麦克风'),
          val: {
            action: async (detectWrap) => {
              try {
               await DetectUtil.startMicTest(detectWrap);
                await sleep(2000);
                TCIC.SDK.instance.reportEvent(
                  'detectedResult',
                  {
                    type: '麦克风',
                  },
                  +new Date(),
                );
              } catch (err) {
                console.log('detectWrap: err is:', err);
                return false;
              }
              await DetectUtil.stopDetectMicrophone();
              return true;
            },
          },
        }, {
          id: 'mic',
          text: i18next.t('摄像头'),
          val: {
            action: async (detectWrap) => {
              try {
                await DetectUtil.startCameraTest(detectWrap);
                await sleep(2000);
                await confirmHandler(i18next.t('摄像头画面是否正常？'));
              } catch (err) {
                TCIC.SDK.instance.reportEvent(
                  'detectedResultError',
                  {
                    type: '摄像头',
                    err,
                  },
                  +new Date(),
                );
                await DetectUtil.stopCameraTest();
                return false;
              }
              await DetectUtil.stopCameraTest();
              return true;
            },
          },
        }, {
          id: 'speaker',
          text: i18next.t('扬声器'),
          val: {
            action: async () => {
              try {
                await DetectUtil.startSpeakerTest('https://res.qcloudclass.com/assets/detect.mp3');
                await sleep(1000);
                await confirmHandler(i18next.t('能否听到播放的声音？'));
              } catch (err) {
                TCIC.SDK.instance.reportEvent(
                  'detectedResult',
                  {
                    type: '扬声器',
                    err,
                  },
                  +new Date(),
                );
                console.error(err);
                await DetectUtil.stopSpeakerTest();
                return false;
              }
              await DetectUtil.stopSpeakerTest();
              return true;
            },
          },
        }],
      detectFailed: [],
    };
  },
  computed: {
    translateTip() {
      return {
        roomLoading: i18next.t('{{arg_0}}信息加载中...', { arg_0: this.roomInfo.name }),
        agreement: i18next.t('系统可能会将{{arg_0}}帐号及系统信息上报，我们将依照隐私权政策和服务条款的规定，使用你提供的信息解决技术问题。', { arg_0: this.roomInfo.name }),
      };
    },
    popoverWidth() {
      if (!this.ready) {
        return 120;
      }
      return this.isSmallScreen ? Math.min(window.innerWidth, window.innerHeight) : 400;
    },
  },
  watch: {
    feedbackDialog(flag) {
      this.$nextTick(() => {
        if (flag) {
          this.feedbackId = window.tbm.pushWindow('feedback');
          window.tbm.pushTarget('app', ['back', 'problem', 'contact', 'commit'], this.feedbackId);
          window.tbm.updateTarget('app', [window.tbm.generateNode(document.querySelector('.feedback-dialog .el-dialog__headerbtn'))], 'back', this.feedbackId);
          const nodes = [];
          const subNames = ['problem', 'contact'];
          Array.from(document.querySelectorAll('.feedback-dialog input'))
            .forEach((item, i) => window.tbm.updateTarget('app', [window.tbm.generateNode(item, () => item.focus())], subNames[i], this.feedbackId));
          window.tbm.updateTarget('app', nodes, 'input', this.feedbackId);
          window.tbm.updateTarget('app', [window.tbm.generateNode(document.querySelector('.dialog-footer button'))], 'commit', this.feedbackId);
          window.tbm.activeWindow(this.feedbackId, 'feedback', 'back');
        } else {
          window.tbm.removeWindow(this.feedbackId);
        }
      });
    },
  },
  mounted() {
    this.roomInfo = TCIC.SDK.instance.getRoleInfo().roomInfo;
    this.isRtmpMode = TCIC.SDK.instance.isRtmpMode();
    this.makeSureClassJoined(this.onJoinClass);
    this.code = parseInt(Math.random() * 9999, 10);
    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.isPortrait = orientation === TCIC.TDeviceOrientation.Portrait;
    });
    headerEventHub.$on('header-left-popover-show', this.handlePopoverShow);
    this.autoDetect();
  },
  beforeDestroy() {
    headerEventHub.$off('header-left-popover-show', this.handlePopoverShow);
  },
  methods: {
    deviceDetectHandler(state) {
      this.isInDetectPage = state;
    },
    autoDetect() {
      if (this.isMobileNative) {
        return;
      }
      this.addLifecycleTCICStateListener(Constant.TStateDeviceDetect, this.deviceDetectHandler);
    DetectUtil.supportDetect().then((detectedObj) => {
      this.detectedObj = detectedObj;
      console.log('classInfo detected', detectedObj);
      const detail = detectedObj.detail;
      if (detectedObj.result && !detectedObj.detail) {
        return;
      }
      const {
          isBrowserSupported,
          isWebRTCSupported,
          isMediaDevicesSupported,
          isH264EncodeSupported,
          isVp8EncodeSupported,
          isH264DecodeSupported,
      } = detail;
      if (!isBrowserSupported || !isWebRTCSupported || !isMediaDevicesSupported) {
        this.showInvalidPanel = true;
      }
      if (!isH264EncodeSupported && !isVp8EncodeSupported) {
          // 无法推流
          this.showInvalidPanel = true;
      }
      if (!isH264DecodeSupported) {
          // 无法观看
          this.showInvalidPanel = true;
      }
      TCIC.SDK.instance.reportEvent(
                'autoDetectResult',
                {
                  detectedObj,
                },
                +new Date(),
              );
    });
    },
    procesHandler(item, index) {
      if (Number(this.detectStep) === Number(index)) {
        return {
          status: 'process',
          text: '检测中..',
        };
      }
      if (Number(this.detectStep) <= Number(index)) {
        return {
          status: 'wait',
          text: '待检测',
        };
      }
      const isFailed = this.detectFailed.includes(item.id);

      return isFailed ?  {
        status: 'error',
          text: '异常',
      } : {
        status: 'success',
          text: '正常',
      } ;
    },
    detectHideHandler() {
      if (this.detectHideBtnDisabled) {
        return;
      }
      this.detectVisible = false;
    },
    onPopupShow() {
      const btns = [];
      Array.from(document.querySelectorAll('.btn-wrapper button.el-button.main-button')).forEach(item => btns.push(window.tbm.generateNode(item)));
      window.tbm.updateTarget('classinfo', btns, 'buttons');
    },
    onPopupHide() {
      window.tbm.clearTarget('classinfo');
    },
    onJoinClass() {
      const { classId, className } = TCIC.SDK.instance.getClassInfo();
      const cid = TCIC.SDK.instance.getCid();
      const uid = TCIC.SDK.instance.getUid();
      const tcicVersion = TCIC.SDK.instance.getVersion();
      const mainVersion = tcicVersion.split('_')[0];
      const clientInfo = TCIC.SDK.instance.getClientInfo();
      const clientVersion = clientInfo ? clientInfo.sdkVersion : null;
      const env = TCIC.SDK.instance.getParams('env');
      const parser = new UAParser(navigator.userAgent);
      let platform = '';
      if (TCIC.SDK.instance.isWeb()) {
        platform = parser.getBrowser().name;
      } else if (TCIC.SDK.instance.isIOS()) {
        platform = 'ios';
        if (TCIC.SDK.instance.isPad()) {
          platform = 'ipad';
        }
      } else if (TCIC.SDK.instance.isAndroid()) {
        platform = 'android';
        if (TCIC.SDK.instance.isPad()) {
          platform = 'pad';
        }
      } else if (TCIC.SDK.instance.isWindows()) {
        platform = 'pc';
      } else if (TCIC.SDK.instance.isMac()) {
        platform = 'mac';
      }


      this.classInfo = {
        roomId: `${cid ? `${cid}(${classId})` : classId}`,
        userId: `${uid ? `${uid}(${TCIC.SDK.instance.getUserId()})` : TCIC.SDK.instance.getUserId()}`,
        uiVersion: `${Version.getBuildVersion()}${env ? `-${env}` : ''}`,
        tcicVersion: `${tcicVersion}${env ? `-${env}` : ''}`,
        sdkVersion: clientVersion,
        platform,
      };

      console.log(' this.classInfo:', clientInfo, this.classInfo);
      let classTips = '';
      Object.keys(this.classInfo).forEach((key) => {
        classTips += `${key}: ${this.classInfo[key]}\n`;
      });
      this.classTips = classTips;

      this.ready = true;

      this.className = className;
      this.$nextTick(() => {
        if (this.$refs.classNameRef && this.$refs.classNameRef.scrollWidth > 120) {
          this.classNameTooltipEnable = true;
        }
      });

      if (!this.isSmallScreen) {
        const node = window.tbm.generateNode(this.$refs.popper.$refs.popover.$el, () => {
          if (this.$refs.popper.$refs.popover.showPopper) {
            this.$refs.popper.$refs.popover.doClose();
          } else {
            this.$refs.popper.$refs.popover.doShow();
          }
        });
        window.tbm.updateTarget('header', [node], 'info');
      }
    },

    // 复制成功时的回调函数
    onCopySuccess() {
      window.showToast(i18next.t('复制成功'), 'success');
    },

    // 复制失败时的回调函数
    onCopyError() {
      window.showToast(i18next.t('复制失败请重试'));
    },

    showLoading() {
      const loading = TCIC.SDK.instance.getComponent('loading-component').getVueInstance();
      this.loadingReqId = loading.showText(i18next.t('正在收集系统信息，请稍后...'));
    },

    hideLoading() {
      if (this.loadingReqId) {
        TCIC.SDK.instance.getComponent('loading-component').getVueInstance()
          .hideText(this.loadingReqId);
        this.loadingReqId = 0;
      }
    },

    getSystemInfo() {
      this.showLoading();
      TCIC.SDK.instance.setState(Constant.TStateIsGettingSystemInfo, true);
      setTimeout(async () => {
        try {
          this.systemInfo = await TCIC.SDK.instance.getSystemInfo();
        } finally {
          this.hideLoading();
          TCIC.SDK.instance.setState(Constant.TStateIsGettingSystemInfo, false);
        }
      }, 100);
    },
    reloadHandler() {
      setTimeout(() => {
        window.customReload();
      }, 1000);
    },
    selfRepair() {
          this.detectStep = 0;
          this.detectHideBtnDisabled = true;
          TCIC.SDK.instance.showMessageBox(
            '',
            i18next.t('开始设备检测？'),
              [i18next.t('确定'), i18next.t('取消')],
              async (index) => {
                if (index === 0) {
                  TCIC.SDK.instance.reportEvent(
                    'selfRepair',
                    {
                      classInfo: this.classTips,
                      personalInfo: this.personalInfo,
                    },
                    +new Date(),
                  );
                  // 初始化监测模块
                  // const detect = new RTCDetect();
                  // 获得当前环境监测结果
                  /**
                   * {
                        "result": true,
                        "detail": {
                            "isBrowserSupported": true,
                            "isWebRTCSupported": true,
                            "isMediaDevicesSupported": true,
                            "isH264EncodeSupported": true,
                            "isVp8EncodeSupported": true,
                            "isH264DecodeSupported": true,
                            "isVp8DecodeSupported": true
                        }
                    }
                  */
                  this.detectVisible = true;
                  this.$nextTick(async () => {
                    const detectWrap = this.$refs.detectWrap;
                    for (let i = 0 ; i < this.detectAction.length; i++) {
                      const item = this.detectAction[i];
                      const { action } = item.val;
                      const result = await action(detectWrap);
                      if (!result) {
                        this.detectFailed.push(item.id);
                      }
                      TCIC.SDK.instance.reportEvent(
                      'detectedResult',
                      {
                        detectedObj: result,
                      },
                      +new Date(),
                    );
                      this.detectStep += 1;
                    }
                    this.detectHideBtnDisabled = false;
                  });
                }
              },
        );
      },
    async showFeedbackModal() {
      this.classInfoVisible = false;
      if (this.isElectron) {
        this.systemInfo = null;
        this.feedbackDialog = true;
        this.getSystemInfo();
      } else {
        this.feedbackDialog = true;
      }
    },

    submitFeedback() {
      if (!this.content.trim() || !this.personalInfo.trim()) {
        window.showToast(i18next.t('输入内容不能为空'));
        return;
      }
      if (this.isSubmitting) {
        return;
      }
      this.isSubmitting = true;

      this.$copyText(this.code).then(
        (e) => {
          console.log(e);
        },
        (e) => {
          console.error(e);
        },
      );
      TCIC.SDK.instance.reportEvent(
        'feedback',
        {
          classInfo: this.classTips,
          content: this.content,
          systemInfo: (this.agreement && this.systemInfo) || {},
          personalInfo: this.personalInfo,
        },
        this.code,
        +new Date(),
      );

      this.feedbackDialog = false;
      setTimeout(() => {
        this.isSubmitting = false;
      }, 1000);
      this.$notify({
        title: i18next.t('反馈成功, 问题ID #{{arg_0}}', { arg_0: this.code }),
        position: 'top-left',
        duration: 15000,
        offset: 30,
      });
      this.code = parseInt(Math.random() * 9999, 10);
    },

    handleSelfPopoverShow() {
      headerEventHub.$emit('header-left-popover-show', 'ClassInfo');
    },

    handlePopoverShow(type) {
      if (type !== 'ClassInfo') {
        this.$refs.popper.$refs.popover.doClose();
      }
    },

    triggerPopover() {
      if (this.isMobile && !this.isSmallScreen) {
        clearTimeout(this.popoverTask);
        this.$refs.popper.$refs.popover.doShow();
        this.popoverTask = setTimeout(() => {
          this.$refs.popper.$refs.popover.doClose();
        }, 3000);
      }
    },
  },
};
</script>
<style lang="less" scoped>
.c-detected-error-info{
  position: absolute;
    left: 0px;
    top: 50px;
    background: red;
    padding: 10px;
    border-radius: 10px;
    min-width: 200px;
}
.class-info-sub-component {
  -webkit-app-region: no-drag;
  position: relative;
  max-width: 170px;
  height: 30px;
  display: flex;
  align-items: center;

  .icon-wrapper {
    width: 16px;
    margin-left: 6px;
    .icon-btn {
      display: block;
      .icon-classname {
        display: block;
        width: 16px;
        height: 16px;
        cursor: pointer;
      }
    }
    &.small-screen {
      margin-left: 2px;
      // .icon-classname {
      //   background-image: url('../assets/icon-down.png');
      // }
    }
  }
}

.class-info-tooltip.el-popover {
  background: #1c2131;
  border: none;
  border-radius: 8px;
  padding: 16px 20px;
  &:not(.mobile) {
    top: 54px !important;
    max-width: 400px;
  }
  &.mobile{
    top: calc(env(safe-area-inset-top) + 45px)!important;
  }

  .btn-wrapper {
    text-align: center;
  }

  .btn-copy {
    padding: 0 3px;
    border: none;
    vertical-align: middle;
    width: 16px;
    height: 16px;
    display: inline-block;
    cursor: pointer;

    &:hover {
      .icon {
        opacity: 1;
      }
    }

    .icon {
      width: 16px;
      height: 16px;
      vertical-align: top;
      opacity: 0.8;
      transition: opacity 0.3s;
    }

    .icon-copy {
      background-image: url(../assets/icon-copy.svg);
      background-repeat: no-repeat;
      background-size: contain;
    }
  }

  &.el-popper[x-placement^='bottom'] {
    .popper__arrow:after {
      border-bottom-color: rgba(0, 0, 0, 0.7) !important;
    }
  }
}

.class-tips-drawer {
  .class-tips {
    margin-top: 8px;
  }

  .icon {
    width: 16px;
    height: 16px;
    vertical-align: middle;
  }

  button.btn-copy {
    padding: 0 3px;
    background: rgba(0, 0, 0, 0.1);
    color: #eee;
    border: none;
    transition: background-color 0.3s;
    vertical-align: middle;

    span {
      display: inline-block;
    }

    &:hover {
      background: rgba(0, 0, 0, 0.3);
      color: #fff;

      .icon {
        opacity: 1;
      }
    }

    .icon {
      width: 16px;
      height: 16px;
      vertical-align: top;
      opacity: 0.8;
    }

    .icon-copy-outline {
      background-image: url(../assets/icon-copy.svg);
      background-repeat: no-repeat;
      background-size: contain;
    }
  }

  .icon-copy {
    background-image: url(../assets/icon-copy.svg);
    background-repeat: no-repeat;
    background-size: contain;
  }

  .icon-feedback {
    background-image: url(../assets/icon-feedback.svg);
    background-repeat: no-repeat;
    background-size: contain;
  }
}


.feedback-dialog {
  .el-dialog__header {
    padding: 10px 20px 8px;
  }

  .el-dialog__title {
    font-size: 14px;
  }

  .el-dialog__body {
    padding: 15px 20px;
  }

  .pt10 {
    padding-top: 10px;
  }

  .agreement {
    font-size: 12px;
    padding-top: 5px;
  }
}

.class-tips {
  text-align: left;
  white-space: pre-wrap;
  font-size: 14px;
  user-select: text;
  -webkit-user-select: text;
  color: #fff;
  padding-bottom: 10px;

  .class-tips-drawer & {
    padding: 0 16px;
  }

  table {
    width: 100%;

    tr {
      height: 36px;
      line-height: 20px;

      td {
        color: var(--pop-text-color, #fff);
        font-size: 14px;
        user-select: text;
        -webkit-user-select: text;

        &:first-child {
          width: 100px;
          color: #8a9099;
        }

        span {
          user-select: text;
          -webkit-user-select: text;
        }
      }
    }

    .value {
      text-align: right;
    }
  }
}
</style>
