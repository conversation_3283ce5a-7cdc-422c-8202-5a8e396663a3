<template>
  <div class="setting-sub-component">
    <el-tooltip
      :disabled="isMobile || component.active"
      class="item"
      :content="$t('设置')"
      placement="bottom"
    >
      <MixedPopper
        ref="popper"
        :type="isSmallScreen ? 'drawer' : 'popover'"
        :visible.sync="localComponent.active"
        :title="$t('设置')"

        popover-placement="bottom-end"
        :popover-popper-class="`header-component-popover content-fixed popover__setting inComponent ${isCoTeachingClass ? 'co-teaching-class' : ''}`"
        popover-trigger="click"

        drawer-btt-height="calc(100% - 45px)"
        drawer-custom-class="setting-component"

        @popover-show="onPopoverShow"
        @popover-hide="onPopoverHide"
      >
        <Box
          v-if="!isSmallScreen"
          :class="['setting-component', { 'small-screen': isSmallScreen }]"
          @hide="hidePopover"
        >
          <template #title>
            {{ $t('设置') }}
          </template>
          <template #content>
            <CTSettingComponent
              v-if="isCoTeachingClass"
              ref="ctsettingComponent"
              :component="localComponent"
              @on-button-update="onCTSettingButtonUpdate"
              @hide="hidePopover"
            />
            <SettingComponent
              v-else
              ref="settingComponent"
              :component="localComponent"
            />
          </template>
        </Box>
        <template v-else>
          <CTSettingComponent
            v-if="isCoTeachingClass"
            ref="ctsettingComponent"
            :component="localComponent"
            @on-button-update="onCTSettingButtonUpdate"
            @hide="localComponent.active = false"
          />
          <SettingComponent
            v-else
            ref="settingComponent"
            :component="localComponent"
          />
        </template>

        <template #reference>
          <button
            :ref="localComponent.name"
            class="header__button button--secondary header__right-button"
            :class="{ active: localComponent.active }"
          >
            <div class="tool-item-wrap">
              <el-badge
                is-dot
                class="badge"
                :hidden="!showRedDot"
              >
                <IconSetting class="header__i i--menu ic_nav_setting" style="padding: 0!important"/>
              </el-badge>
              <span class="header__btn-text">{{ $t('设置') }}</span>
            </div>
          </button>
        </template>
      </MixedPopper>
    </el-tooltip>
  </div>
</template>

<script>
import BaseComponent from '@/component/core/BaseComponent';
import SettingComponent from '../../setting-component/Setting.vue';
import CTSettingComponent from '../../co-teaching-components/settting-component/CTSettingComponent.vue';
import MixedPopper from '@/component/ui-component/mixed-popper-component/MixedPopper';
import Box from '@/component/ui-component/box-component/Box';
import IconSetting from '../assets/svg-component/ic_settings.svg';
import Lodash from 'lodash';

export default {
  name: 'SettingSubComponent',
  components: {
    SettingComponent,
    IconSetting,
    CTSettingComponent,
    MixedPopper,
    Box,
  },
  extends: BaseComponent,
  props: {
    component: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      isMobile: TCIC.SDK.instance.isMobile(),
      isCoTeachingClass: TCIC.SDK.instance.isCoTeachingClass(),
      isClassStarted: false,
      localComponent: this.component,
      isPortrait: false,
      showRedDot: false, // 红点标记
    };
  },
  watch: {
    'localComponent.active'(active) {
      if (this.showRedDot && active) {
        this.showRedDot = false;
        localStorage.setItem(`click_setting_${TCIC.SDK.instance.getUserId()}`, Date.now());
      }
    },
  },
  mounted() {
    this.addLifecycleTCICStateListener(
      TCIC.TMainState.Class_Status,
      (status) => {
        this.isClassStarted = status !== TCIC.TClassStatus.Not_Start;
      },
    );
    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.isPortrait = orientation === TCIC.TDeviceOrientation.Portrait;
    });

    // 红点标记
    if (TCIC.SDK.instance.isAIDenoiseSupported()) {
      const val = localStorage.getItem(`click_setting_${TCIC.SDK.instance.getUserId()}`);
      if (!val) {
        this.showRedDot = true;
        console.log('show setting red dot');
      }
    }
  },
  beforeDestroy() {
  },
  methods: {
    hidePopover() {
      this.$refs.popper.$refs.popover.doClose();
    },
    showSetting() {
      const settingComponent = TCIC.SDK.instance.getComponent('setting-component');
      TCIC.SDK.instance.updateComponent('setting-component', {
        top: this.isPortrait ? '45px' : 0,
        height: this.isPortrait ? 'calc(100% - 45px)' : '100%',
        display: 'block',
      });
      this.$emit('icon-click');

      // 红点标记
      if (this.showRedDot) {
        this.showRedDot = false;
        localStorage.setItem(`click_setting_${TCIC.SDK.instance.getUserId()}`, Date.now());
      }
    },
    onPopoverShow() {
      this.localComponent.active = true;
      this.updateTbmTarget(true);
      this.$refs.ctsettingComponent?.init(); // ctsetting用旧逻辑，将设备检测中的值同步至setting组件中
      window.tbm.setEndTarget('setting');
    },
    onPopoverHide() {
      this.localComponent.active = false;
      window.tbm.clearTarget('setting');
      window.tbm.setEndTarget(null);
    },
    onCTSettingButtonUpdate() {
      this.updateTbmTarget();
    },
    updateTbmTarget: Lodash.throttle(function () {
      this.$nextTick(() => {
        if (!this.localComponent.active) return;      // 未显示时忽略
        if (this.$refs.settingComponent?.$el) {
          // TODO 这是要干什么。。。settingComponent的结构不一样，querySelector要改
        } else if (this.$refs.ctsettingComponent?.$el) {
          const subTargets = ['camera', 'cameraMirror', 'microphone', 'button'];
          const sections = Array.from(this.$refs.ctsettingComponent.$el.querySelectorAll('input,button'));
          const count = Math.min(subTargets.length, sections.length);
          for (let i = 0; i < count; i++) {
            window.tbm.updateTarget(
              'setting',
              this.getSectionButtons(sections[i])
                .map(item => window.tbm.generateNode(item)),
              subTargets[i],
            );
          }
          window.tbm.updateTarget(
            'setting',
            Array.from(this.$refs.ctsettingComponent.$el.querySelectorAll('button.audition-button'))
              .map(item => window.tbm.generateNode(item)),
            'button',
          );
        }
      });
    }, 500, {
      leading: false,
      trailing: true,
    }),
    checkValid(dom) {   // 检测按钮是否可用
      const validClassArr = ['arrows-left-able', 'arrows-right-able', 'icon-sub-able', 'icon-add-able'];
      if (dom) {
        if (dom.style.display === 'none') return false;     // 不可见时直接返回不可用
        const domClasses = Array.from(dom.classList);
        for (let i = 0; i < domClasses.length; i++) {
          if (validClassArr.includes(domClasses[i])) return true;
        }
      }
      return false;
    },
    getSectionButtons(dom) {
      return Array.from(dom.querySelectorAll('i.base-icon'))
        .filter(item => this.checkValid(item));
    },
  },
};
</script>
<style lang="less">
@setting-color: var(--text-color, #fff);
.setting-sub-component {
  .header-mobile-component & {
    .ic_nav_setting {
      width: 24px;
      height: 24px;
      background-size: 24px;
    }
  }

  .ic_nav_setting {
  }

  table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;

    tr {
      cursor: pointer;
      width: 100%;
      color: @setting-color;
    }

    td {
      padding: 5px;
      float: left;

      img {
        width: 25px;
        height: auto;
      }
    }
  }
}

.setting-vedio-model {
  padding-left: 16px;
  padding-bottom: 24px;
  display: flex;

  .setting-tips-class-text {
    font-size: 16px;
    line-height: 22px;
    font-weight: 400;
    color: @setting-color;
  }

  .setting-tips-class-icon-sel {
    cursor: pointer;
    display: inline-block;
    margin: 4px 8px 8px 4px;
    width: 16px;
    height: 16px;
    background-repeat: no-repeat;
    background-position: center;
    background-image: url('../assets/ic_information_hover.svg');
  }

  .setting-tips-class-icon-nosel {
    display: inline-block;
    margin: 4px 8px 8px 4px;
    width: 16px;
    height: 16px;
    background-repeat: no-repeat;
    background-position: center;
    background-image: url('../assets/ic_information_normal.svg');
  }

  .el-switch {
    &.vedio-model-switch {
      height: 24px;

      .el-switch__label {
        color: @setting-color;

        &.is-active {
          color: @setting-color;
        }

        span {
          font-size: 16px;
        }
      }

      &.is-checked .el-switch__core {
        position: absolute;
        left: 174px;
        border-width: 2px;
        width: 40px !important;
        height: 22px !important;
        border-color: #006EFF !important;
        background-color: #006EFF !important;

        &:after {
          top: 0px;
          left: 36px;
          width: 18px;
          height: 18px;
          background-color: #FFFFFF;
        }
      }

      .el-switch__core {
        position: absolute;
        left: 174px;
        border-width: 2px;
        width: 40px !important;
        height: 22px !important;
        border-color: #FFFFFF !important;
        background-color: #FFFFFF !important;

        &:after {
          top: 0px;
          left: 0px;
          width: 18px;
          height: 18px;
          background-color: rgba(0, 0, 0, 0.14);
        }
      }
    }
  }
}

.vedio-info-tooltip.el-popover {
  background: #14181D;
  border: none;
  position: absolute;
  top: 250px;
  left: 0px;
}

.vedio-info-message {
  width: 191px;
  height: 51px;

  .tips-class-text {
    width: 146px;
    height: 24px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: @setting-color;
    line-height: 17px;
  }
}

.co-teaching-class {
  background: none !important;
  box-shadow: none !important;
}
</style>
