<template>
  <div class="document-sub-component">
    <el-tooltip
      :manual="interactiveIsTouchEvent()"
      :disabled="isMobile || localComponent.active"
      class="item"
      :content="roomInfo.courseware"
      placement="bottom"
    >
      <DocumentComponent
        :hide-document="onHide"
        :component="component"
      />
    </el-tooltip>
  </div>
</template>

<script>
import BaseComponent from '@/component/core/BaseComponent';
import DocumentComponent from '../../document-component/Document.vue';
import i18next from 'i18next';

export default {
  name: 'DocumentSubComponent',
  components: {
    DocumentComponent,
  },
  extends: BaseComponent,
  props: {
    component: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      isMobile: TCIC.SDK.instance.isMobile(),
      localComponent: this.component,
      roomInfo: {},
    };
  },
  mounted() {
    this.roomInfo = TCIC.SDK.instance.getRoleInfo().roomInfo;
  },
  methods: {
    onHide() {
      this.localComponent.active = false;
    },
    togglePopover() {
      this.localComponent.active = !this.localComponent.active;
    },
  },
};
</script>
<style lang="less">
.header__list.overlay .icon-document {
  opacity: .3;
}
.document-sub-component {
  .icon-document {
    width: 24px;
    height: 24px;
    background-position: 50%;
    background-size: contain;
    background-repeat: no-repeat;
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
    &.mobile {
      width: 24px;
      height: 24px;
      margin: 8px;
      background-color: inherit;
    }
  }

  .badge {
    .el-badge__content {
      background-color: #FA6400;
      border: 0;
    }
  }
}
</style>
