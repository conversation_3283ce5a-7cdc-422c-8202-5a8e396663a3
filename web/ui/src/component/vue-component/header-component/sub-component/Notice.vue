<template>
  <div
    class="notice-sub-component"
  >
    <el-tooltip
      :disabled="isMobile || localComponent.active"
      class="item"
      :content="$t('公告')"
      placement="bottom"
    >
      <button
        class="header__button button--secondary header__right-button"
        :class="{active: localComponent.active}"
        @click="showNotice"
      >
        <div class="tool-item-wrap">
          <el-badge
            :value="localComponent.badge"
            :max="99"
            class="badge"
            :hidden="localComponent.badge === 0"
          >
            <IconNotice class="header__i i--menu icon-notice" style="padding: 0!important"/>
          </el-badge>
          <span class="header__btn-text">{{ $t('公告') }}</span>
        </div>
      </button>
    </el-tooltip>
  </div>
</template>

<script>
import BaseComponent from '@core/BaseComponent';
import IconNotice from '../assets/svg-component/ic_nav_notice.svg';

export default {
  name: 'NoticeSubComponent',
  components: {
    IconNotice,
  },
  extends: BaseComponent,
  props: {
    component: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      isMobile: TCIC.SDK.instance.isMobile(),
      isSmallScreen: TCIC.SDK.instance.isMobile() && !TCIC.SDK.instance.isPad(),
      isPortrait: false,
      localComponent: this.component,
    };
  },
  mounted() {
    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.isPortrait = orientation === TCIC.TDeviceOrientation.Portrait;
    });
  },
  methods: {
    showNotice() {
      TCIC.SDK.instance.getComponent('notice-dialog').getVueInstance()
        .show();
      this.$emit('icon-click');
    },
  },
};
</script>
<style lang="less">
.notice-sub-component {
  .icon-notice {
  }

  .header-mobile-component & {
    .icon-notice {
      width: 24px;
      height: 24px;
    }
  }
}
</style>
