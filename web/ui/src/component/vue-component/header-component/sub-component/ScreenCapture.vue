<template>
  <div class="screen-capture-sub-component">
    <el-tooltip
      :manual="interactiveIsTouchEvent()"
      :disabled="(isMobile && !btnDisabled) || localComponent.active"
      :content="!isClassStarted ? translateTip.roomStartContent
        : (isVodPlaying ? translateTip.vodPlayContent : (isSubCamera ? $t('请先关闭辅助摄像头后再使用屏幕共享') : $t('屏幕共享')))"
      class="item"
      placement="bottom"
    >
      <el-popover
        v-if="isElectron"
        ref="popover"
        :placement="localComponent.placement"
        :width="localComponent.width"
        popper-class="header-component-popover popover__screens-sources"
        trigger="manual"
        @show="onPopOverShow"
        @hide="onPopOverHide"
      >
        <ShareSources
          ref="screen-sources"
          @hide="onHide"
        />
        <button
          slot="reference"
          :ref="localComponent.name"
          :disabled="btnDisabled"
          class="header__button button--secondary"
          :class="{active: localComponent.active}"
          @click="showScreenShare"
        >
          <div class="tool-item-wrap">
            <el-badge
              :value="localComponent.badge"
              :max="99"
              class="badge"
              :hidden="localComponent.badge === 0"
            >
              <IconScreenShare :class="`header__i i--menu icon-screen-capture`" style="padding: 0!important"/>
            </el-badge>
            <span class="header__btn-text">{{ $t('屏幕共享') }}</span>
          </div>
        </button>
      </el-popover>
      <div v-else>
        <button
          :ref="localComponent.name"
          :disabled="btnDisabled"
          class="header__button button--secondary"
          :class="{active: localComponent.active}"
          @click="toggleScreenCapture"
        >
          <div class="tool-item-wrap">
            <el-badge
              :value="localComponent.badge"
              :max="99"
              class="badge"
              :hidden="localComponent.badge === 0"
            >
              <IconScreenShare :class="`header__i i--menu icon-screen-capture`" style="padding: 0!important"/>
            </el-badge>
            <span class="header__btn-text">{{ $t('屏幕共享') }}</span>
          </div>
        </button>
      </div>
    </el-tooltip>
    <ScreenCaptureDialog
      ref="screen-capture"
      :title="$t('您尚未打开屏幕共享权限')"
      @status-update="screenCaptureStatusUpdate"
    />
  </div>
</template>

<script>
import i18next from 'i18next';
import IconScreenShare from '../assets/svg-component/ic_screen_share.svg';
import BaseComponent from '@core/BaseComponent';
import DetectUtil from '@vueComponent/device-detect-component/DetectUtil';
import ScreenCaptureDialog from '@vueComponent/device-detect-component/ScreenCaptureDialog.vue';
import ShareSources from '@vueComponent/share-sources-component/ShareSources.vue';
import Constant from '@/util/Constant';

export default {
  name: 'ScreenCaptureSubComponent',
  components: {
    ShareSources,
    ScreenCaptureDialog,
    IconScreenShare,
  },
  extends: BaseComponent,
  props: {
    component: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      isElectron: TCIC.SDK.instance.isElectron(),
      isMobile: TCIC.SDK.instance.isMobile(),
      isClassStarted: false,
      isVodPlaying: false,
      isSubCamera: false,
      localComponent: this.component,
      roomInfo: {},
      roleInfo: {},
    };
  },
  computed: {
    btnDisabled() {
      /**
       * 上课前禁止屏幕分享的限制
       * !this.isClassStarted
       */
      if (!this.isClassStarted) {
        return true;
      }
      return  this.isVodPlaying || this.isSubCamera;
    },
    translateTip() {
      return {
        roomStartContent: i18next.t('开始{{arg_0}}后才可使用屏幕共享', { arg_0: this.roomInfo.startRoom }),
        vodPlayContent: i18next.t('请先关闭视频{{arg_0}}后再使用屏幕共享', { arg_0: this.roomInfo.courseware }),
      };
    },
  },
  mounted() {
    const { roleInfo, roomInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    this.roomInfo = roomInfo;
    this.updateStatus();
    this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, (status) => {
      console.log('classstatus change:: inCapture', TCIC.TMainState.Class_Status, status);
      this.updateStatus();
    });
    this.addLifecycleTCICStateListener(Constant.TStateVodPlayerVisible, (status) => {
      this.updateStatus();
    });
    this.addLifecycleTCICStateListener(Constant.TStateStartSubCamera, (status) => {
      this.updateStatus();
    });
  },
  methods: {
    updateStatus() {
      // 没有上课或者正在播放视频，不启用屏幕共享
      const classStatus = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Not_Start);
      this.isClassStarted = (classStatus === TCIC.TClassStatus.Already_Start);
      this.isVodPlaying = TCIC.SDK.instance.getState(Constant.TStateVodPlayerVisible, false);
      this.isSubCamera = TCIC.SDK.instance.getState(Constant.TStateStartSubCamera, false);
    },
    checkOtherScreenSharing() {
      const sdk = TCIC.SDK.instance;
      // 检查当前是否有其他有正在分享
      const selfId = sdk.getUserId();
      const permissionList = sdk.getPermissionList();
      const shareItem = permissionList.find(suer =>  suer.screen !== 0 && suer.screenState < 2);
      return shareItem && shareItem.userId !== selfId;
    },
    toggleScreenCapture() {
      if (!this.isElectron && !TCIC.SDK.instance.isScreenShareSupported()) {
        TCIC.SDK.instance.showMessageBox('', i18next.t('当前浏览器不支持屏幕共享，请升级浏览器或使用客户端'), [i18next.t('确认')], () => {
        });
        return false;
      }
      if (!TCIC.SDK.instance.isFeatureAvailable('WebScreenShareAvailable')) {
        TCIC.SDK.instance.showMessageBox('', i18next.t('当前设备或应用不支持屏幕共享'), [i18next.t('确认')], () => {
        });
        return false;
      }
      // 1.6.3 支持学生屏幕分享，房间内只支持一个人进行屏幕分享
      if (this.checkOtherScreenSharing()) {
        window.showToast(i18next.t('其他人正在进行屏幕共享，请稍候再试'));
        return;
      }
      const isTeacher = TCIC.SDK.instance.isTeacher();
      const isScreenShareOpen = TCIC.SDK.instance.getState(TCIC.TMainState.Screen_Share) < 2;
      TCIC.SDK.instance.reportLog('toggleScreenShare', `[HeaderSubComponent][ScreenCapture] toggleScreenCapture, isTeacher ${isTeacher}, isScreenShareOpen ${isScreenShareOpen}`);

      if (isScreenShareOpen) {
        // 关闭分享不用判断是否在台上
        TCIC.SDK.instance.loadComponent('screen-component').then((dom) => {
          dom.getVueInstance().toggleScreenShare();
        });
        return;
      }
      if (isTeacher) { // 老师分享
        const isOnStage = TCIC.SDK.instance.getState(TCIC.TMainState.Stage_Status, false);
        if (!isOnStage) {
          window.showToast(i18next.t('请先上台再开始屏幕共享'));
          return;
        }
        TCIC.SDK.instance.loadComponent('screen-component').then((dom) => {
          dom.getVueInstance().toggleScreenShare();
        });
      } else {
        const selfId = TCIC.SDK.instance.getUserId();
        const component = TCIC.SDK.instance.getComponent('student-component', selfId);
        if (component) {
          TCIC.SDK.instance.loadComponent('screen-component')
            .then((dom) => {
              dom.getVueInstance().toggleScreenShare();
            });
        } else {
          const component = TCIC.SDK.instance.getComponent('student-component', selfId);
          if (component) {
            TCIC.SDK.instance.loadComponent('screen-component').then((dom) => {
              dom.getVueInstance().toggleScreenShare();
            });
          } else {
            window.showToast(i18next.t('请先上台再开始屏幕共享'));
            return;
          }
        }
      }
    },

    /**
     * 打开屏幕共享
     */
    showScreenShare() {
      const selfPermission = TCIC.SDK.instance.getPermission(TCIC.SDK.instance.getUserId());
      if (!selfPermission.stage) {
        window.showToast(i18next.t('请先上台再开始屏幕共享'));
        return;
      }
      if (this.isElectron && TCIC.SDK.instance.isMac()) {
        this.detectScreenCapture(() => {
          this.$refs.popover.doShow();
        });
      } else {
        this.$refs.popover.doShow();
      }
    },

    /**
     * 检测屏幕分享
     */
    detectScreenCapture(callback) {
      DetectUtil.detectScreenCaptureSupported()
        .then((support) => {
          console.debug('detectScreenCaptureSupported', support);
          if (support) {
            callback && callback();
          } else {
            this.$refs['screen-capture'].show();
          }
        });
    },

    screenCaptureStatusUpdate() {
      this.$refs['screen-capture'].hide();
    },

    onPopOverShow() {
      this.localComponent.active = true;
      this.$refs['screen-sources'].notifyVisibilityChange(true);
    },

    onPopOverHide() {
      this.localComponent.active = false;
      this.$refs['screen-sources'].notifyVisibilityChange(false);
    },
    onHide() {
      if (this.isElectron) {
        this.$refs.popover.doClose();
      }
    },
  },
};
</script>
<style lang="less">

.el-popover.popover__screens-sources {
  padding:0;
}
</style>
