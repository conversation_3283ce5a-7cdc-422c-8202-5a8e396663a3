<template>
  <div class="tool-box-sub-component">
    <el-tooltip
      :manual="interactiveIsTouchEvent()"
      :disabled="(isMobile && !btnDisabled) || visible"
      :content="!isClassStarted ? translateTip.content : $t('工具箱')"
      class="item"
      placement="bottom"
    >
      <el-popover
        ref="popover"
        v-model="visible"
        placement="bottom"
        popper-class="header-component-popover more-popover inComponent submenu-popover"
        trigger="manual"
        @show="onPopOverShow"
        @hide="onPopOverHide"
      >
        <ul
          v-if="visible"
          class="header-more-box flex-column"
        >
          <template v-for="item in renderMenu">
            <li
              v-if="item.isSub"
              :key="item.name"
              class="header-menu-li"
            >
              <!-- popover组件 -->
              <el-popover
                v-if=" item.type === 'popover' "
                placement="bottom-end"
                :width="item.width"
                popper-class="header-component-popover"
                trigger="click"
                @show="() => { item.active = true }"
                @hide="() => { item.active = false }"
              >
                <Component
                  :is="item.name"
                  :component="item"
                />
                <button
                  slot="reference"
                  :ref="item.name"
                  class="header__button button--secondary"
                  :class="{active: item.active}"
                >
                  <el-badge
                    :value="item.badge"
                    :max="99"
                    class="badge"
                    :hidden="item.badge === 0"
                  >
                    <i :class="`header__i i--menu icon-${item.name}`" />
                  </el-badge>
                </button>
              </el-popover>
              <!-- 自定义组件 -->
              <Component
                :is="item.name"
                v-else
                :component="item"
              />
            </li>
            <li
              v-else
              :key="item.name"
              class="header-menu-li"
            >
              <button
                :ref="item.name"
                class="header__button button--secondary"
                :class="{active: item.active}"
              >
                <el-badge
                  :value="item.badge"
                  :max="99"
                  class="badge"
                  :hidden="item.badge === 0"
                >
                  <i :class="`header__i i--menu icon-${item.name}`" />
                </el-badge>
              </button>
            </li>
          </template>
        </ul>
        <button
          slot="reference"
          :disabled="btnDisabled"
          class="header__button button--secondary"
          :class="{active: visible}"
          @click="visible = !visible"
        >
          <div class="tool-item-wrap">
            <IconTool class="header__i i--menu ic_nav_tool" style="padding: 0!important" />
            <span class="header__btn-text">{{ $t('工具箱') }}</span>
          </div>
        </button>
      </el-popover>
    </el-tooltip>
  </div>
</template>

<script>
import Quiz from './Quiz';
import TimerTool from './TimerTool';
import ClockTool from './ClockTool';
import SeizeAnswer from './SeizeAnswer.vue';
import RandomChooseTool from './RandomChoose';
import SubCameraPreview from './SubCameraPreview';
import PopoverComponent from '@core/PopoverComponent';
import IconTool from '../assets/svg-component/ic_tools.svg';
import i18next from 'i18next';

export default {
  name: 'ToolBoxSubComponent',
  components: {
    SubCameraPreview,
    IconTool,
    Quiz,
    TimerTool,
    ClockTool,
    SeizeAnswer,
    RandomChooseTool,
  },
  extends: PopoverComponent,
  props: {
    component: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      isMobile: TCIC.SDK.instance.isMobile(),
      isClassStarted: false,
      isTeacher: false,
      visible: false,
      localComponent: this.component,
      roomInfo: {},
      roleInfo: {},
    };
  },
  computed: {
    btnDisabled() {
      return !this.isClassStarted;
    },
    renderMenu() {
      let menu = this.localComponent.children.filter(item => item.enable === true);
      // sub-camera-preview 在非electron不显示
      /**
       * todo: finlaywu 改为尊享版才出现辅助摄像头
       * 先下掉辅助摄像头
       */
      // if (!TCIC.SDK.instance.isElectron()) {
      menu = menu.filter(item => item.name !== 'sub-camera-preview');
      // }
      if (TCIC.SDK.instance.isLiveClass()) {    // 公开课隐藏答题工具
        menu = menu.filter(item => item.name !== 'quiz');
      }
      return menu;
    },
    translateTip() {
      return {
        content: i18next.t('开始{{arg_0}}后才可使用工具箱', { arg_0: this.roomInfo.startRoom }),
      };
    },
  },
  watch: {
    'component.active'(val, oldVal) {
      if (val !== oldVal && !val) {
        this.onHide();
      }
    },
  },
  mounted() {
    const { roleInfo, roomInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    this.roomInfo = roomInfo;
    this.initPopoverComponents();
    this.isClassStarted = (TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Not_Start) === TCIC.TClassStatus.Already_Start);
    this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, (status) => {
      this.isClassStarted = (status === TCIC.TClassStatus.Already_Start);
    });
  },
  beforeDestroy() {
  },
  methods: {
    initPopoverComponents() {
      this.localComponent.children.forEach((item) => {
        if (item.type === 'popover' && item.enable === true && item.hiddenWhenMoreBtnShow === true) {
          this.addPopoverComponent(item);
        }
      });
    },
    getMoreBtn() {
      this.visible = true;
    },
    onHide() {
      this.$refs.popover.doClose();
    },
    onPopOverShow() {
      this.visible = true;
      this.localComponent.active = true;
    },
    onPopOverHide() {
      this.visible = false;
      this.localComponent.active = false;
    },
  },
};
</script>
<style lang="less">
// 定时器、计时器、答题器去掉提示
.tooltip-no-content {
  &.el-tooltip__popper {
    min-width: inherit;
    padding: 0;
  }
}

.more-popover {
  position: relative;

  .header-more-box {
    // width: 260px;
    display: flex;

    li {
      min-width: 60px;
      text-align: center;
      color: #fff;

      //.header__text {
      //  display: block;
      //  margin-top: 2px;
      //}
    }
  }
}
</style>
