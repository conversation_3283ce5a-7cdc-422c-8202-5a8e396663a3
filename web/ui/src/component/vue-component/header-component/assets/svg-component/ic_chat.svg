<svg width="24" height="24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g filter="url(#prefix__filter0_b_246_22180)">
    <mask id="prefix__a" fill="#fff">
      <path fill-rule="evenodd" clip-rule="evenodd"
        d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12c0 2.265.753 4.355 2.023 6.032l-.992 2.291A1.2 1.2 0 004.132 22H12z" />
    </mask>
    <path
      d="M12 22v1.5h.001V22zm-7.977-3.968l1.377.596.348-.804-.53-.698-1.195.906zm-.992 2.291l-1.377-.596 1.377.596zM20.5 12a8.5 8.5 0 01-8.5 8.5l.001 3c6.35 0 11.499-5.15 11.499-11.5h-3zM12 3.5a8.5 8.5 0 018.5 8.5h3C23.5 5.649 18.351.5 12 .5v3zM3.5 12A8.5 8.5 0 0112 3.5v-3C5.649.5.5 5.649.5 12h3zm1.719 5.126A8.456 8.456 0 013.5 12h-3c0 2.604.867 5.009 2.327 6.937l2.392-1.811zm-.812 3.793l.993-2.291-2.753-1.192-.993 2.291 2.753 1.192zm-.275-.419a.3.3 0 01.275.42l-2.753-1.193C.882 21.51 2.19 23.5 4.132 23.5v-3zm7.868 0H4.132v3H12v-3zm0 0v3-3z"
      fill="var(--icon-color, #D1D9EC)" mask="url(#prefix__a)" />
  </g>
  <g filter="url(#prefix__filter1_b_246_22180)">
    <path fill-rule="evenodd" clip-rule="evenodd"
      d="M7.148 12.211a5.002 5.002 0 009.704 0l-1.455-.363a3.501 3.501 0 01-6.793 0l-1.456.363z"
      fill="var(--icon-color, #A5FE33)" />
  </g>
  <mask id="prefix__b" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="7" y="11" width="10" height="5">
    <path fill-rule="evenodd" clip-rule="evenodd"
      d="M7.148 12.211a5.002 5.002 0 009.704 0l-1.455-.363a3.501 3.501 0 01-6.793 0l-1.456.363z"
      fill="var(--icon-second-color, #D1D9EC)" />
  </mask>
  <g mask="url(#prefix__b)">
    <path d="M16 15l-4-4 2-1.5 4.5 3.5-2.5 2z" fill="var(--icon-third-color, #006CFF)" />
  </g>
  <defs>
    <filter id="prefix__filter0_b_246_22180" x="-6" y="-6" width="36" height="36" filterUnits="userSpaceOnUse"
      color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feGaussianBlur in="BackgroundImageFix" stdDeviation="4" />
      <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_246_22180" />
      <feBlend in="SourceGraphic" in2="effect1_backgroundBlur_246_22180" result="shape" />
    </filter>
    <filter id="prefix__filter1_b_246_22180" x="-.852" y="3.848" width="25.703" height="20.152"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feGaussianBlur in="BackgroundImageFix" stdDeviation="4" />
      <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_246_22180" />
      <feBlend in="SourceGraphic" in2="effect1_backgroundBlur_246_22180" result="shape" />
    </filter>
  </defs>
</svg>