<template>
<svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 26 26" fill="none">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M4.25 13.5V12H5.75V13.5C5.75 17.5041 8.99594 20.75 13 20.75C17.0041 20.75 20.25 17.5041 20.25 13.5V12H21.75V13.5C21.75 17.9943 18.3616 21.6973 14 22.1935V25H12V22.1935C7.63843 21.6973 4.25 17.9943 4.25 13.5Z" fill="var(--icon-color, #D1D9EC)"/>
    <mask id="mask0_4704_9566" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="4" y="12" width="18" height="13">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M4.25 13.5V12H5.75V13.5C5.75 17.5041 8.99594 20.75 13 20.75C17.0041 20.75 20.25 17.5041 20.25 13.5V12H21.75V13.5C21.75 17.9943 18.3616 21.6973 14 22.1935V25H12V22.1935C7.63843 21.6973 4.25 17.9943 4.25 13.5Z" fill="#E4E9EE"/>
    </mask>
    <g mask="url(#mask0_4704_9566)">
        <path d="M19.5 10H24.5L26 14L21.5 24L14 25.5V19.5L18 17.5L19.5 10Z" fill="var(--icon-second-color, #006CFF)"/>
        <path d="M23.5 17.5L13 13L19.5 10L23.5 11V17.5Z" fill="var(--icon-third-color, #A5FE33)"/>
    </g>
    <rect x="8.75" y="2.75" width="8.5" height="15" rx="4.25" stroke="var(--icon-color, #D1D9EC)" stroke-width="1.5"/>
    <path v-if="off" d="M2 2L24 24" stroke="#F24D43" stroke-width="3"/>
</svg>
</template>

<script setup>
const props = defineProps({
    off: {
        type: Boolean,
        default: false,
    },
});
</script>

<style lang="less">

</style>
