<template>
  <div
    v-if="readyState"
    ref="headerDiv"
    :class="['header-component', { 'mobile': isMobile, 'small-screen': isSmallScreen, 'header-fold': !showHeader }]"
  >
    <HeaderPC
      v-if="!isSmallScreen"
      ref="header"
    />
    <HeaderMobile
      v-if="isSmallScreen && !isShowLiveHeader"
      ref="header"
      :class="isSmallScreen ? 'mobile-layout' : ''"
    />
    <HeaderMobileLive
      v-if="isSmallScreen && isShowLiveHeader"
      ref="header"
    />
    <div class="preload-number-font" />
  </div>
</template>

<script>
import i18next from 'i18next';
import BaseComponent from '@core/BaseComponent';
import Constant from '@util/Constant';
import VConsole from 'vconsole';
import HeaderMobile from './HeaderMobile';
import HeaderMobileLive from './HeaderMobileLive';
import HeaderPC from './HeaderPC';
import { agreeDesktopUpgrade } from '../../../pages/class/desktopUpgrade';

export default {
  name: 'HeaderComponent',
  components: {
    HeaderPC,
    HeaderMobile,
    HeaderMobileLive,
  },
  extends: BaseComponent,
  data() {
    return {
      isTeacher: false,
      isAssistant: false,
      isSupervisor: false,
      isMobilePortrait: false,
      isMobile: false,
      readyState: false,
      isDeviceDetect: null,
      isLive: TCIC.SDK.instance.isLiveClass(),
      roomInfo: {},
      roleInfo: {},
      showHeader: true,
      isMouseOver: false,
      enableHeaderFold: true, // 是否开启header自动折叠
    };
  },
  computed: {
    isShowLiveHeader() {
      if (TCIC.SDK.instance.isUnitedClass()) {
        return false;
      }
      // 以下是兼容170以下的老版本
      return this.isLive || this.isMobilePortrait;
    },
  },
  watch: {
    enableHeaderFold(val) {
      if (!val) {
        this.showHeader = true;
      }
    },
  },
  async mounted() {
    this.initVConsoleContainer();
    // 监听加入课堂事件
    const { roleInfo, roomInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    this.roomInfo = roomInfo;
    this.readyState = true;
    this.isMobile = TCIC.SDK.instance.isMobile();
    this.makeSureClassJoined(this.onJoinClass);
    if (this.isSmallScreen) {
      this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
        this.isMobilePortrait = orientation === TCIC.TDeviceOrientation.Portrait;
      });
    }

    this.addLifecycleTCICStateListener(Constant.TStateImmerseMode, this.onImmerseModeChange);
    this.$nextTick(() => {
      const targetDom = this.$refs.headerDiv;
      if (targetDom && !this.isMobile) {
        targetDom.addEventListener('mouseenter', this.handleMouseEnter);
        targetDom.addEventListener('mouseleave', this.handleMouseLeave);
      }
    });

    const agree = await agreeDesktopUpgrade();
    if (agree) {
      return;
    }
  },
  beforeDestroy() {
    const targetDom = this.$refs.headerDiv;
    if (targetDom && !this.isMobile) {
      targetDom.removeEventListener('mouseenter', this.handleMouseEnter);
      targetDom.removeEventListener('mouseleave', this.handleMouseLeave);
    }
  },

  methods: {
    onImmerseModeChange(enable) {
      if (!TCIC.SDK.instance.checkPermission(TCIC.TResourcePath.Board, TCIC.TPermissionFlag.Read)) {
        return;
      }
      if (this.enableHeaderFold) {
        this.showHeader = (!(enable && !this.isMouseOver));
      }
    },
    handleMouseEnter() {
      this.isMouseOver = true;
    },
    handleMouseLeave() {
      this.isMouseOver = false;
    },
    onJoinClass() {
      this.isLive = TCIC.SDK.instance.isLiveClass();
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      this.isSupervisor = TCIC.SDK.instance.isSupervisor();
      // 绑定事件
      this.bindEvent();

      if (TCIC.SDK.instance.isMobile()) {
        // 移动端进课堂后
        this.afterClassBegin();
      }
    },

    bindEvent() {
      // 成员离开
      // this.addLifecycleTCICEventListener(TCIC.TMainEvent.Member_Exit, this.memberExitHandler);

      // 设备检测
      this.addLifecycleTCICStateListener(Constant.TStateDeviceDetect, this.deviceDetectHandler);
    },

    // 开始上课后提示
    afterClassBegin() {
      if (this.isTeacher || this.isAssistant || this.isSupervisor) {
        // 学生才提示，老师和助教可以直接请求上台，巡课不能上台
        return;
      }

      // TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start).then(() => {
      //   const classInfo = TCIC.SDK.instance.getClassInfo();
      //   const permission = TCIC.SDK.instance.getPermission(TCIC.SDK.instance.getUserId());
      //   if (!permission.stage) {
      //     // 没在台上
      //     if (!TCIC.SDK.instance.isLiveClass() && !TCIC.SDK.instance.isUnitedLiveClass() && !this.isSupervisor) {
      //       // 非直播课 & 非巡课老师
      //     if (classInfo.maxRtcMember !== 0 && !this.isTeacher && !this.isAssistant) {
      //         // 学生才提示举手，助教可以直接请求上台
      //         const teacherId = TCIC.SDK.instance.getClassInfo().teacherId;
      //         const count = TCIC.SDK.instance.getPermissionList().filter(item => item.userId !== teacherId && item.stage).length;
      //         if (count >= classInfo.maxRtcMember) {
      //           window.showToast(i18next.t('当前台上人数已满，可举手申请上台~'));
      //         } else {
      //           window.showToast(i18next.t('你不在台上，可举手申请上台，打开音视频互动~'));
      //         }
      //       }
      //     }
      //   }
      // });
    },

    startClass() {
      const canStartClass = (TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isAssistant())
        && TCIC.SDK.instance.getClassInfo().status === TCIC.TClassStatus.Not_Start;
      console.log(`setClass->start class, canStartClass: ${canStartClass}`);
      if (canStartClass) {
        TCIC.SDK.instance
          .startClass()
          .then(() => {
            console.log('setClass->start class success');
          })
          .catch((error) => {
            console.error(`setClass->start class fail: ${error.errorCode}|${error.errorMsg}`);
            window.showToast(i18next.t('{{arg_0}}失败: {{arg_1}}', { arg_0: this.roomInfo.startRoom, arg_1: error.errorMsg }), 'error');
            TCIC.SDK.instance.reportEvent('handle_start_class', error, -1);
          });
      }
    },

    stopClass() {
      let canStopClass = (TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isAssistant())
        && TCIC.SDK.instance.getClassInfo().status === TCIC.TClassStatus.Already_Start;
      if (TCIC.SDK.instance.getParams('noEndClass')) {
        canStopClass = false;
      }
      console.log(`setClass->stop class, canStopClass: ${canStopClass}`);
      if (canStopClass) {
        // 老师上课中
        TCIC.SDK.instance.showMessageBox(
          i18next.t('teacherEndRoomConfirm.title', this.roomInfo),
          i18next.t('teacherEndRoomConfirm.message', this.roomInfo),
          [
            this.roomInfo.endRoom,
            this.roomInfo.leaveRoom,
          ],
          (index) => {
            if (index === 1) {
              TCIC.SDK.instance.unInitialize();
            } else if (index === 0) {
              // 先下课
              TCIC.SDK.instance
                .endClass()
                .then(() => {
                  console.log('setClass->stop class');
                  TCIC.SDK.instance.unInitialize();
                })
                .catch((error) => {
                  console.error(`setClass->stop class fail: ${error.errorCode}|${error.errorMsg}`);
                  window.showToast(i18next.t('{{arg_0}}失败: {{arg_1}}', { arg_0: this.roomInfo.endRoom, arg_1: error.errorMsg }), 'error');
                  TCIC.SDK.instance.reportEvent('handle_end_class', error);
                });
            }
          },
        );
      }
    },

    memberExitHandler(uid) {
      if (TCIC.SDK.instance.isTeacher()) {
        // 老师端监听学员退出事件
        if (uid !== TCIC.SDK.instance.getUserId()) {
          // 不是自己
          TCIC.SDK.instance.getUserInfo(uid).then((result) => {
            window.showToast(i18next.t('{{arg_0}} 离开了{{arg_1}}', { arg_0: result.nickname, arg_1: this.roomInfo.name }));
          });
        }
      } else if (uid === TCIC.SDK.instance.getClassInfo().teacherId) {
        // 学生端显示老师离开事件
        // 课堂结束后不提示
        if (TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status) !== TCIC.TClassStatus.Has_Ended) {
          window.showToast(i18next.t('{{arg_0}} 暂时离开了{{arg_1}}', { arg_0: this.roleInfo.teacher, arg_1: this.roomInfo.name }));
        }
      }
    },

    deviceDetectHandler(state) {
      if (this.isDeviceDetect === state) return;
      this.isDeviceDetect = state;
      if (!state) {
        if (!TCIC.SDK.instance.isMobile()) {
          // 非移动端完成设备测试后
          this.afterClassBegin();
        }
      }
    },
    initVConsoleContainer() {
      const domId = 'qc_vconsole';
      try {
        let cont = document.getElementById(domId);
        if (!cont) {
          cont = document.createElement('div');
          cont.setAttribute('id', domId);
          cont.style.display = 'none';
          document.body.appendChild(cont);
        }
      } catch (e) {
        console.error(e);
      }
    },
    checkPress() {
      this.pressBeginTime = +new Date();
      this.hideVconsole();
    },
    checkShowVConsole() {
      const now = +new Date();
      const gap = now - this.pressBeginTime;
      // 按住10秒,展示vconsole
      if (gap < 10 * 1000) {
        return;
      }
      this.toggleVconsole();
    },
    hideVconsole() {
      const domId = 'qc_vconsole';
      const cont = document.getElementById(domId);
      if (cont && cont.style) {
        cont.style.display = 'none';
      }
    },
    toggleVconsole() {
      const domId = 'qc_vconsole';
      const cont = document.getElementById(domId);
      let showStatus = false;
      if (cont && cont.style && cont.style.display) {
        const curr = cont.style.display;
        if (curr === 'none') {
            cont.style.display = 'block';
            if (!this.vc) {
              this.vc = new VConsole({
                target: cont,
              });
            }
          showStatus = true;
        } else {
          cont.style.display = 'none';
        }
      }
      return showStatus;
    },
  },
};
</script>
<style lang="less">
@keyframes header-fold-up {
  0% {
    height: 0;
  }
  100% {
    height: 80px;
  }
}

@keyframes header-fold-down {
  100% {
    height: 0;
  }
  0% {
    height: 80px;
  }
}
.light {
  --icon-second-color: rgba(0, 0, 0, 0.8);
  --icon-third-color: rgba(0, 0, 0, 0.8);
  .header-component-default .header-component:not(.mobile) .button--secondary{
    &:hover, &.active {
      --icon-second-color: #fff;
      --icon-third-color: #fff;
    }
  }
}
.header-component-default {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: auto;
  opacity: 1;
  display: block;
  font-size: 13px;
  z-index: 10;
  overflow: visible !important;

  .header__className {
    text-overflow: ellipsis;
    max-width: 120px;
    overflow: hidden;
    word-break: keep-all;
    white-space: nowrap;
  }

  .header-component {
    height: 100%;
    width: 100%;
    margin-top: 0;
    pointer-events: none;
    animation: header-fold-up 0.1s ease-in;
    &.header-fold {
      animation: header-fold-down 0.1s ease-in;
      height: 0;
      margin-top: -100px;
    }

    &.mobile-layout {
      opacity: 0.8;
      pointer-events: none;

      .header__logo {
        margin-right: 10px;
      }
    }

    // V1.3.4统一菜单小图标
    .button--secondary {
      -webkit-app-region: no-drag;
      border: none;
      color: var(--btn--secondary--text-color, #fff);
      font-size: 11px;
      outline: none;
      cursor: pointer;
      display: flex;
      justify-content: space-around;
      min-width: 52px;
      width: 100%;
    }

    &:not(.mobile) .button--secondary {
      border-radius: 2px;
      padding: 4px 0;

      &:hover,
      &.active {
        background: var(--active-bg-color, #3D7EFD);
        --icon-color: #fff;
        color: var(--active-color,#fff);
        // backdrop-filter: blur(4px);
        border-radius: 10px;
        box-sizing: content-box;
        .tool-item-wrap span {
          --text-color: #fff;
        }
      }

      // &.active {
      //   background: #006eff !important;
      // }
    }

    &.mobile .button--secondary {
      padding: 2.5px 0;
    }

    .header__i.i--menu {
      width: 24px;
      height: 24px;
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
      vertical-align: middle;
    }

    &.small-screen {
      .header__i.i--menu {
        background-size: 50%;
      }

      .button--secondary {
        min-width: 40px;
      }
    }

    .header__i {
      display: inline-block;
    }
  }

  .header__logout {
    span {
      display: block;
      width: 30px;
      height: 30px;
      background-image: url('./assets/icon-back.png');
      background-repeat: no-repeat;
    }
  }
}

// V1.3.4统一下拉二级菜单列表样式
.el-popover {
  &.submenu-popover {
    width: auto !important;
    min-width: 114px;
    padding: 0 16px;

    .header-more-box {
      &.flex-column {
        flex-direction: column;
      }

      .header-menu-li {
        display: flex;
        align-items: center;
        width: auto;
        height: 58px;
        font-size: 16px;
        line-height: 24px;
        color: var(--text-color, #fff);
        text-align: left;
        border-bottom: 1px solid rgba(#fff, 0.1);

        &:last-child {
          border-bottom: none;
        }

        .button--secondary {
          display: flex;
          align-items: center;
          height: 24px;
          padding: 0;
          font-size: 16px;
        }

        .header__i.i--menu {
          width: 24px;
          height: 24px;
          margin-right: 10px;
        }
      }
    }
  }
}

.el-popover {
  &.header-component-popover {
    background: #1c2131;
    border: 0;

    &.content-fixed {
      min-width: 0;
      width: 0;
      height: 0;
      padding: 0;
      transform: none;
    }

    &.el-popper[x-placement^='top'],
    &.el-popper[x-placement^='left'],
    &.el-popper[x-placement^='right'],
    &.el-popper[x-placement^='bottom'] {
      .popper__arrow {
        border-top-color: #000 !important;

        &::after {
          border-top-color: #000 !important;
        }
      }
    }
  }
}
</style>

<style lang="less">
/** 提前加载自定义字体，避免打开定时器，计时器的时候才加载导致闪一下 */
@font-face {
  font-family: 'Number';
  src: url('@assets/font/Akrobat-Bold.otf');
}

.preload-number-font {
  position: fixed;
  left: -10000;
  top: -10000;
}

.preload-number-font::after {
  content: '';
  font-family: Number;
}
</style>
