<template>
  <div
    ref="headerRef"
    :class="['header-pc-component', { 'device-pad': isPad, 'wide-text': isWideText }]"
    style="-webkit-app-region: drag"
  >
    <div class="header__left">
      <div
        v-if="isPad"
        class="header__logout"
      >
        <span @click="logout" />
      </div>
      <!-- 最大化/最小化/关闭按钮 -->
      <MacControlBar v-if="isElectron && isMac" />
      <div
        v-loading="!logo"
        element-loading-background="transparent"
        :style="logo ? `background-image:url(${logo})` : ''"
        class="header__logo"
      />
      <!-- 课堂信息 -->
      <ClassInfo />
      <!-- 网络状态 -->
      <NetworkQuality v-if="!isRtmpMode" />
      <ForceRefresh v-if="!isMobileNative" />
      <FeedbackQrPopover v-if="isShowFeedbackQr" />
      <MarqueeBanner
        v-if="isShowFeedbackQr"
        :text="$t('该平台专注提供线上音视频互动服务。为保护你的权益与安全，请警惕网络诈骗。')"
      />
      <ClassDuration v-if="isPad" />
    </div>
    <div class="course-time">
      <ClassDuration v-if="!isPad" />
    </div>
    <div
      ref="header-menu-wrapper"
      class="header__sub-operation"
    >
      <ul
        v-show="!isSimpleModeSharing"
        class="header__list"
        :class="{ overlay: overlay }"
      >
        <template v-for="(item, index) in renderMenu">
          <li
            v-if="item.isSub"
            :key="item.name"
            :class="'tool-' + item.name"
            :data-user-event="`HeaderPC-click_${item.name}`"
          >
            <Component
              :is="item.name"
              :ref="item.name"
              :component="item"
            />
          </li>
          <li
            v-else
            :key="item.name+index"
            :class="'tool-' + item.name"
            :data-user-event="`HeaderPC-click_${item.name}`"
          >
            <el-tooltip
              class="item"
              :disabled="!item.label"
              :content="$t(item.label)"
              placement="bottom"
            >
              <button
                :ref="item.name"
                class="header__button button--secondary"
                :class="[{ active: item.active }, item.class || '']"
                @click="item.action"
              >
                <div
                  class="tool-item-wrap"
                >
                  <el-badge
                    :value="item.badge"
                    :max="99"
                    class="badge"
                    :hidden="item.badge === 0"
                  >
                    <i
                      :class="`header__i i--menu`"
                      :style="{ 'background-image': `url(${item.icon})` }"
                    />
                  </el-badge>
                  <span class="header__btn-text">{{ item.label }}</span>
                </div>
              </button>
            </el-tooltip>
          </li>
        </template>
      </ul>
      <ul
        class="header__list"
      >
        <!-- 更多部分DOM开始 -->
        <li
          v-if="showMore"
          v-show="!isSimpleModeSharing"
          class="header__li-more"
          :data-user-event="`HeaderPC-clickmore`"
        >
          <More
            ref="more"
            class="inMoreComponent"
            :menu-item-width="menuItemWidth"
          />
        </li>
        <!-- 更多部分DOM结束 -->

        <li
          v-if="classPermission.status"
          ref="classBtnWrap"
          class="header__button--curriculum"
        >
          <el-button
            ref="classBtn"
            class="header__button--start"
            :type="isClassStarted ? 'warning' : 'primary'"
            :disabled="leaveClassShow ? false : isStartClassButtonDisabled"
            :data-user-event="`HeaderPC-${canStopClass ? `endRoom` : (canStartClass ? `startRoom` : `leaveRoom`) }`"
            @click="controlClass"
          >
            {{ leaveClassShow ? $t('离开') : canStopClass ? roomInfo.endRoom : (roomInfo.startRoom ) }}
          </el-button>
        </li>
        <li
          v-if="!(isTeacher || isAssistant)"
          ref="leaveBtnWrap"
          class="header__button--curriculum"
        >
          <el-button
            ref="leaveBtn"
            class="header__button--start"
            type="warning"
            @click="logout"
          >
            {{ $t('离开') }}
          </el-button>
        </li>
      </ul>
      <!-- 最大化/最小化/关闭按钮  -->
      <WindowControlBar v-if="isElectron && !isMac" />
    </div>
    <!-- 布局弹框 -->
  </div>
</template>

<script>
import i18next from 'i18next';
import Constant from '@util/Constant';
import DomObserver from '@util/DomObserver';
import Util from '@util/Util';
import Lodash from 'lodash';

import ClassInfo from './sub-component/ClassInfo';
import NetworkQuality from './sub-component/NetworkQuality';
import FeedbackQrPopover from './sub-component/FeedbackQrPopover';
import ForceRefresh from './sub-component/ForceRefresh';
import WindowControlBar from './sub-component/WindowControlBar';
import MacControlBar from './sub-component/MacControlBar';
import ScreenCapture from './sub-component/ScreenCapture';
import Layout from './sub-component/Layout';
import More from './sub-component/More';
import Member from './sub-component/Member';
import AllMemberController from './sub-component/AllMemberController';
import Setting from './sub-component/Setting';
import Message from './sub-component/Message';
import ClassDuration from './sub-component/ClassDuration';
import Document from './sub-component/Document';
import ToolBox from './sub-component/ToolBox';
import Invite from './sub-component/Invite';
import Notice from './sub-component/Notice';
import Camera from './sub-component/Camera';
import Speaker from './sub-component/Speaker';
import HeaderBase from './HeaderBase';
import MarqueeBanner from '@/component/vue-component/header-component/sub-component/MarqueeBanner.vue';

export default {
  name: 'HeaderPC',
  components: {
    MarqueeBanner,
    ClassInfo,
    NetworkQuality,
    FeedbackQrPopover,
    ForceRefresh,
    WindowControlBar,
    MacControlBar,
    ScreenCapture,
    Layout,
    ToolBox,
    More,
    Member,
    AllMemberController,
    Setting,
    Message,
    ClassDuration,
    Document,
    Invite,
    Notice,
    Speaker,
    Camera,
  },
  extends: HeaderBase,
  data() {
    return {
      isElectron: TCIC.SDK.instance.isElectron(),
      isMac: TCIC.SDK.instance.isMac(),
      isPad: TCIC.SDK.instance.isPad(),
      isRecordMode: TCIC.SDK.instance.isRecordMode(),
      isMobileNative: TCIC.SDK.instance.isMobileNative(),
      isStudent: TCIC.SDK.instance.isStudent(),
      classId: '', // 课堂ID
      classJoined: false,
      overlay: false, // 用于遮罩（设备检测期间可见不可点）
      foldMenuWidth: 0, // [菜单隐藏] 临界宽度
      menuWrapperWidth: 0, // [菜单隐藏] 实际菜单wrapper的宽度
      menuItemWidth: 60, // [菜单隐藏] 单个菜单宽度
      resizeTask: 0,
      resizeCount: 1,
      isDeviceDetect: true,
      isLoading: true,
      classType: false,
      showMore: false,
      classLayout: TCIC.TClassLayout.Top,
      hasSideIM: false,
      isStartClassButtonDisabled: false,
      maximize: false, // mac最大化和还原
      isStageUp: false, // 是否上台
      isSimpleModeSharing: false, // 是否在精简模式分享
      classPermission: {
        status: false,
        audio: false,
        layout: false,
      },
      roomInfo: null,
      roleInfo: null,
      isRtmpMode: false,
      isShowFeedbackQr: false,
      domObserver: new DomObserver(),
    };
  },
  computed: {
    leaveClassShow() {
      return this.canStopClass ? false : (!this.canStartClass);
    },
    defaultMenu() {
      if (!this.classJoined) return [];
      // 根据角色过滤菜单
      const role = (this.isTeacher && 'teacher') || (this.isSupervisor && 'supervisor') || (this.isAssistant && 'assistant') || 'student';
      let defaultMenu = this.menu.filter(item => item.enable === true && item.role[role] && item.role[role].classType.includes(this.classType));
      // 通过rbac权限过滤菜单(双师课布局不受权限控制)
      if (!TCIC.SDK.instance.isCoTeachingClass() && !TCIC.SDK.instance.checkPermission(TCIC.TResourcePath.Class_Layout, TCIC.TPermissionFlag.Read)) {
        defaultMenu = defaultMenu.filter(item => item.name !== 'layout');
      }
      if (!TCIC.SDK.instance.checkPermission(TCIC.TResourcePath.Document, TCIC.TPermissionFlag.Read)) {
        defaultMenu = defaultMenu.filter(item => item.name !== 'document');
      }
      if (!TCIC.SDK.instance.checkPermission(TCIC.TResourcePath.AV_Share, TCIC.TPermissionFlag.Read)) {
        defaultMenu = defaultMenu.filter(item => item.name !== 'screenCapture');
      }
      // 根据课堂类型过滤菜单
      if (TCIC.SDK.instance.isLiveClass()) {
        if (!TCIC.SDK.instance.isClassLayoutHasDoc()) {
          // 公开课纯视频布局, 隐藏“屏幕分享”和“课件”
          defaultMenu = defaultMenu.filter(item => item.name !== 'document' && item.name !== 'screenCapture');
        }
        if (TCIC.SDK.instance.isStudent() && !this.isStageUp) {
          // 学生端未上台时不显示"设置"
          defaultMenu = defaultMenu.filter(item => item.name !== 'setting');
        }
      } else if (TCIC.SDK.instance.isVideoOnlyClass()) {
        // 纯视频，隐藏"布局"和"课件""
        defaultMenu = defaultMenu.filter(item => item.name !== 'layout' && item.name !== 'document');
      } else if (TCIC.SDK.instance.isUnitedLiveClass()) {
        // 标准观看学生端没有"设置"菜单
        if (TCIC.SDK.instance.isStudent()) {
          defaultMenu = defaultMenu.filter(item => item.name !== 'setting');
        }
      }
      if (TCIC.SDK.instance.isBigRoom()
        && TCIC.SDK.instance.getClassInfo().maxRtcMember === 0
        && !TCIC.SDK.instance.isTeacher()
      ) {
        // 大班课1v0，非老师不显示屏幕共享
        defaultMenu = defaultMenu.filter(item => item.name !== 'screenCapture');
      }
      if (TCIC.SDK.instance.isClassLayoutFixed()) {
        // 如果课堂有固定布局，隐藏"布局"菜单
        defaultMenu = defaultMenu.filter(item => item.name !== 'layout');
      }
      if (this.hasSideIM) {
        // 有sideIM时菜单中不显示消息
        defaultMenu = defaultMenu.filter(item => item.name !== 'message');
      }
      return defaultMenu;
    },
    renderMenu() {
      let defaultMenu;
      if (!this.classJoined) return [];
      if (this.menuWrapperWidth === 0) return [];
      // 当前可以展示的item数量
      const btnWidth = (this.$refs.classBtnWrap?.clientWidth || 0) + (this.$refs.leaveBtnWrap?.clientWidth || 0);
      const menuAvailableWidth = this.menuWrapperWidth - (this.isElectron ? 120 : 0) - btnWidth;
      const availableCount = Math.floor(menuAvailableWidth / this.menuItemWidth);
      console.log(`pc header menuWrapperWidth ${this.menuWrapperWidth}, btnWidth ${btnWidth}, menuAvailableWidth ${menuAvailableWidth}`);
      console.log(`pc header menuItemWidth ${this.menuItemWidth}, menuItemCount ${this.defaultMenu.length}, availableCount ${availableCount}`);
      if (availableCount >= this.defaultMenu.length) {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.showMore = false;
        TCIC.SDK.instance.setState(Constant.TStateVideoTipsComponentDown, this.showMore);
        defaultMenu = this.defaultMenu;
      } else {
        // 如果需要隐藏，至少隐藏3个
        const minimumItemCount = 3;
        let count = availableCount - minimumItemCount;
        count = Math.min(count, this.defaultMenu.length - minimumItemCount);

        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.showMore = true;
        TCIC.SDK.instance.setState(Constant.TStateVideoTipsComponentDown, this.showMore);
        defaultMenu = this.defaultMenu.filter((item, index) => index < count || item.name === 'message');
        const hideMenu = this.defaultMenu.filter((item, index) => index >= count && item.name !== 'message');
        // eslint-disable-next-line vue/no-async-in-computed-properties
        this.$nextTick(() => {
          if (this.$refs.more) {
            this.$refs.more.update(hideMenu);
          }
        });
      }
      // eslint-disable-next-line vue/no-async-in-computed-properties
      this.$nextTick(() => this.updateTvBtn());
      return defaultMenu;
    },

    showHandUp() {
      return (
        !this.isTeacher
        && !this.isSupervisor // 学生
        // && this.classType !== 'live' // 直播课不显示
        && !this.isDeviceDetect // 设备检测阶段不显示
        && this.isClassStarted // 上课后才显示
        && !this.isRecordMode // 录制模式不显示
      );
    },
  },
  mounted() {
    const { roomInfo, roleInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    this.roomInfo = roomInfo;
    this.isRtmpMode = TCIC.SDK.instance.isRtmpMode();
    console.log(`[HeaderPC] Util`, Util.shouldShowQrcode());
    this.isShowFeedbackQr = Util.shouldShowQrcode();
    if (this.isShowFeedbackQr) {
      this.$nextTick(() => {
        this.observerWechatQrDom();
      });
    }
    // TODO 应该改成自动获取 menuItemWidth
    if (this.isPad) {
      this.menuItemWidth = 50;
    } else if (this.isWideText) {
      this.menuItemWidth = 72;
    }
    this.makeSureClassJoined(this.onJoinClass);
    document.addEventListener('touchend', this.onEventHandler, true);
    document.addEventListener('mousedown', this.onEventHandler, true);
    document.addEventListener('reload-header', this.onEventHandler, true);
  },

  beforeDestroy() {
    this.unbindEvent();
  },
  methods: {
    observerWechatQrDom() {
      this.domObserver.init({
        targetDomClassName: '#wechat-popover',
        useDomCheck: true,
        domChangeCallback: () => {
          this.domObserver.destroy();
          TCIC.SDK.instance.showErrorMsgBox({
              title: i18next.t('异常提醒'),
              message: i18next.t('未经许可, 不可修改页面样式'),
          });
        },
      });
    },
    i18next: i18next.t,
    onJoinClass() {
      this.classJoined = true;
      this.classLayout = TCIC.SDK.instance.getClassLayout();
      this.hasSideIM = TCIC.SDK.instance.getClassLayoutMainFrame(this.classLayout).sideIM;

      // 绑定事件
      this.bindEvent();

      // 初始化状态
      this.initState();

      // 初始化状态
      this.initData();

      // 初始化权限
      this.initPermission();
    },
    initPermission() {
      this.classPermission.status = TCIC.SDK.instance.checkPermission(TCIC.TResourcePath.Class_Status, TCIC.TPermissionFlag.Read);
      this.classPermission.audio = TCIC.SDK.instance.checkPermission(TCIC.TResourcePath.Class_Audio, TCIC.TPermissionFlag.Read);
      this.classPermission.layout = TCIC.SDK.instance.checkPermission(TCIC.TResourcePath.Class_Layout, TCIC.TPermissionFlag.Read);
    },
    initData() {
      const { classId } = this.classInfo;
      this.classId = classId;
      // classStatus 已经在 HeaderBase 里处理了
      // @TODO 待ClassSubType统一修改后，再统一使用枚举值修改
      this.classType = TCIC.SDK.instance.isInteractClass() ? 'interactive' : 'live';
      if (TCIC.SDK.instance.isUnitedClass()) {
        // 1.7.0 以上只有这2种
        this.classType = TCIC.SDK.instance.isUnitedRTCClass() || TCIC.SDK.instance.isTeacherOrAssistant() ? 'unitedrtc' : 'unitedlive';
      } else {
        if (TCIC.SDK.instance.isCoTeachingClass()) {
          this.classType = TCIC.TClassSubType.CoTeaching;
        }
        if (TCIC.SDK.instance.isOneOnOneClass()) {
          this.classType = TCIC.TClassSubType.OneOnOne;
        }
      }
      console.log(`[HeaderPC] classType ${this.classType}`);

      // 初始化菜单宽度，这里的假设前提是菜单的宽度是固定的
      this.foldMenuWidth = (this.defaultMenu.length + 2) * this.menuItemWidth + (this.isElectron ? 120 : 0);
      this.onResize();
    },

    initState() {
      // 设备检测阶段 或者 Loading 阶段，部分按钮不可操作
      this.setOverlay(TCIC.SDK.instance.getState(Constant.TStateDeviceDetect) || TCIC.SDK.instance.getState(Constant.TStateIsLoading));
      // 设备检测
      this.isDeviceDetect = TCIC.SDK.instance.getState(Constant.TStateDeviceDetect);
      TCIC.SDK.instance.promiseState(Constant.TStateDeviceDetect, false).then(() => {
        this.isDeviceDetect = false;
        this.$nextTick(() => this.updateTvBtn());
      });
      this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, (status) => {
        this.$nextTick(() => this.updateTvBtn());
      });
    },

    bindEvent() {
      // 监听课堂状态变化
      this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, (status) => {
        // classStatus 已经在 HeaderBase 里处理了
        // 课堂状态变化，header中间的文案会变化，更新菜单区域大小
        this.onResize();
      });

      // Loading 动画
      this.addLifecycleTCICStateListener(Constant.TStateIsLoading, this.onLoadingStateChange);

      // 设备检测阶段，部分按钮不可操作
      this.addLifecycleTCICStateListener(Constant.TStateDeviceDetect, this.onDeviceDetectStateChange);

      // 屏幕分享状态变化
      this.addLifecycleTCICStateListener(TCIC.TMainState.Screen_Share, this.onScreenShare);

      // 是否在倒计时中
      this.addLifecycleTCICStateListener(Constant.TStateClassStartCountDowning, (val) => {
        this.isStartClassButtonDisabled = val;
      });

      // 监听上下台状态
      this.addLifecycleTCICStateListener(TCIC.TMainState.Stage_Status, (val) => {
        this.isStageUp = val;
      });
      // 监听布局变更
      this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Layout, (val) => {
        this.classLayout = val;
        this.hasSideIM = TCIC.SDK.instance.getClassLayoutMainFrame(this.classLayout).sideIM;
      });
      // 窗口大小变更
      window.addEventListener('resize', this.onResize);
    },

    unbindEvent() {
      window.removeEventListener('resize', this.onResize);
      document.removeEventListener('touchend', this.onEventHandler, true);
      document.removeEventListener('mousedown', this.onEventHandler, true);
    },

    onScreenShare() {
      this.onResize();
      // 正在用精简模式做屏幕分时时，要更新菜
      if (TCIC.SDK.instance.isElectron()) {
        // 正在用精简模式做屏幕分时，要更新菜
        const isScreenShare = TCIC.SDK.instance.getState(TCIC.TMainState.Screen_Share, 2) !== 2;
        const advMode = TCIC.SDK.instance.isFeatureAvailable('ScreenShareAdvanceMode');
        if (isScreenShare && !advMode) {
          this.isSimpleModeSharing = true;
        } else {
          this.isSimpleModeSharing = false;
        }
        this.$nextTick(() => this.updateTvBtn());
      }
    },
    onResize() {
      // 窗口大小变更时，更新客户端width，来决定header的按钮的展示
      clearTimeout(this.resizeTask);
      this.$nextTick(() => {
        // 只有 menu部分 > 1/3的header宽度，才被认为菜单被正常加载了
        if (
          this.$refs['header-menu-wrapper'].clientWidth === 0
          || (this.isElectron && (this.$refs.headerRef.clientWidth / 3) * 2 < this.$refs['header-menu-wrapper'].clientWidth)
        ) {
          if (this.resizeCount < 10) {
            this.resizeCount += 1;
            this.resizeTask = setTimeout(() => {
              this.onResize();
            }, 150 * this.resizeCount);
          } else {
            this.resizeCount = 1;
          }
        } else {
          this.menuWrapperWidth = this.$refs['header-menu-wrapper'].clientWidth;
          this.resizeCount = 1;
          console.log(`pc header menuWrapperWidth ${this.menuWrapperWidth}`);
        }
      });
    },

    // 上下课
    controlClass() {
      if (this.canStopClass) {
        this.$parent.stopClass();
      } else if (this.canStartClass) {
        this.$parent.startClass();
      } else {
        this.logout();
      }
    },

    // 退出
    logout() {
      // 请求离开课堂
      TCIC.SDK.instance.leaveClass();
    },

    onDeviceDetectStateChange(state) {
      this.isDeviceDetect = state;
      this.setOverlay(state || this.isDeviceDetect);
    },

    onLoadingStateChange(state) {
      this.isLoading = state;
      this.setOverlay(state || this.isDeviceDetect);
    },

    setOverlay(overlay) {
      this.overlay = overlay;
    },

    toggleFullScreen() {},

    onEventHandler: Lodash.throttle(
      function (event) {
        let isInComponent = false;
        let isInMoreComponent = false; // more组件比较特殊，需要再进行一层判断，代表点击事件不在more里面。

        // 通过给子组件添加class "inComponent"，判断点击事件是否在子组件里面
        const path = event.path || (event.composedPath && event.composedPath());
        path.forEach((item) => {
          if (item.classList && item.classList.contains('inComponent')) {
            isInComponent = true;
          }
          if (item.classList && item.classList.contains('inMoreComponent')) {
            isInMoreComponent = true;
          }
        });

        Object.keys(this.$refs).forEach((key) => {
          const refItem = this.$refs[key];
          // eslint-disable-next-line no-underscore-dangle
          if (Array.isArray(refItem) && refItem[0]) {
            const ref = refItem[0];
            // eslint-disable-next-line no-underscore-dangle
            if (ref && ref._props) {
              // eslint-disable-next-line no-underscore-dangle
              const active = refItem.find(item => item._props.component.active === true);
              if (active && !isInComponent && !ref.$el.contains(event.target)) {
                active.onHide?.();
              }
            }
          }
        });

        if (!isInComponent && !isInMoreComponent) {
          if (this.$refs.more) {
            setTimeout(() => {
              this.$refs.more?.onMoreHide();
            }, 100);
          }
        }
      },
      100,
      {
        leading: true,
        trailing: false,
      },
    ),
    updateTvBtn: Lodash.throttle(
      function () {
        // 更新菜单栏的按钮事件
        const nodes = [];
        window.tbm.updateTarget('header', [window.tbm.generateNode(this.$el.querySelector('.header__logout span'))], 'back');
        // 更新主菜单按钮
        if (this.isSimpleModeSharing) {
          // 简单共享模式不显示菜单
          window.tbm.updateTarget('header', [], 'menu');
        } else {
          const menus = Array.from(document.querySelectorAll('ul.header__list li button.header__button'));
          menus.forEach((item) => {
            if (!item.disabled) nodes.push(window.tbm.generateNode(item));
          });
          window.tbm.updateTarget('header', nodes, 'menu');
        }
        // 注册上下课按钮
        if (this.$refs.classBtn) {
          window.tbm.updateTarget('header', [window.tbm.generateNode(this.$refs.classBtn.$el)], 'button');
        }
        if (this.$refs.leaveBtn) {
          window.tbm.updateTarget('header', [window.tbm.generateNode(this.$refs.leaveBtn.$el)], 'button');
        }
      },
      500,
      {
        leading: false,
        trailing: true,
      },
    ),
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less">
.course-time {
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.board-default {
  z-index: 99;
  position: absolute;
  text-align: initial;
}

.header-pc-component {
  height: 100%;
  width: 100%;
  position: relative;
  display: flex;
  justify-content: space-between;
  z-index: 2;
  background-color: #1c2131;
  color: #fff;
  /* overflow: hidden; */
  align-items: center;
  padding: 0 20px;
  pointer-events: auto !important;

  .header__list {
    display: flex;

    .el-tooltip {
      display: block;
    }

    li {
      display: inline-block;
    }

    .tool-item-wrap {
      height: 44px;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2px;
      span {
        white-space: nowrap;
        font-size: 12px;
        line-height: 17px;
        text-align: center;
        color: var(--text-color,#A3AEC7);
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.header__left {
  position: relative;
  flex: 0.8;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  z-index: 9;
}

.classid-info-board {
  width: 250px;
  text-align: center;
  padding: 5px 10px;
  top: 45px;
  left: -22px;
  background: rgba(0, 0, 0, 0.7);
}

.classid-info-board .el-button--mini {
  padding: 5px;
  margin-left: 5px;
}

.header-pc-component .header__logo {
  display: inline-block;
  vertical-align: middle;
  width: 21px;
  height: 21px;
  margin-right: 6px;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;

  .el-loading-spinner {
    margin-top: 0;
    top: 0;
    opacity: 0.5;

    .circular {
      width: 100%;
      height: 100%;
    }
  }
}

.header__left .header__button {
  background: none;
  color: #f7f7f7;
  border: 1px solid #f7f7f7;
  border-radius: 5px;
  height: 26px;
  padding: 0 6px;
  cursor: pointer;
}

.header-pc-component .header__list li {
  display: inline-block;
  padding: 0 6px;
  position: relative;
}

.header-pc-component .header__i.i--menu {
  width: 24px;
  height: 24px;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  vertical-align: middle;
}

.header__i {
  display: inline-block;
}

//#header-component-default {
//  z-index: -1 !important;
//}
.header-pc-component .header__info {
  color: #fff !important;
}

.header-pc-component .header__span--number {
  color: #fff;
}

.header__operation {
  margin-left: 8px;
}

.header__operation .header__button {
  background: none;
  color: #f7f7f7;
  border: 1px solid #f7f7f7;
  border-radius: 5px;
  height: 26px;
  margin-top: 10px;
  padding: 0 6px;
}

// 注意还要同步修改 menuItemWidth 。。。
.header-pc-component .header__sub-operation li {
  width: 60px;
  text-align: center;
  color: #fff;
  display: flex;
  align-content: center;
  align-items: center;
  justify-content: space-around;

  > div {
    width: 100%;
  }
}

.header-pc-component.wide-text .header__sub-operation li {
  width: 72px;
}

.header-pc-component.device-pad .header__sub-operation li {
  width: 50px;
}

.header-pc-component .header__sub-operation li .header__text {
  display: block;
  margin-top: 2px;
}

.header-pc-component .startclass {
  background-color: #2971ff;
  border: 1px solid #2971ff;
}

.header-pc-component .endclass {
  background-color: #fa3b3b;
  border: 1px solid #fa3b3b;
}

.header__startclass_span {
  display: inline-block;
  width: 4px;
  height: 4px;
  margin-bottom: 4px;
  border-radius: 2px;
  background-color: rgb(18, 177, 33);
}

.header-pc-component .header__sub-operation {
  position: relative;
  z-index: 9;
  flex: 1.15;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  height: 100%;
}

.header-copy-btn {
  padding: 4px 5px;
}

// 布局
.header-layout-board {
  width: 180px;
  text-align: center;
  padding: 5px;
  top: 45px;
  left: -5px;
}

.header-layout-board table {
  border-collapse: collapse;
  border-spacing: 0px;
  width: 100%;
}

.header-layout-board table tr {
  cursor: pointer;
  width: 100%;
}

.header-layout-board table td {
  padding: 5px;
  float: left;
}

.header-layout-board table td img {
  width: 25px;
  height: auto;
}

.header-pc-component .badge, .header-more-box .badge {
  .el-badge__content {
    background-color: #fa6400;
    border: 0;
  }
}

.header__button--curriculum {
  position: relative;
  width: auto !important;
  padding: 0 2px 0 16px !important;
  &:before {
    position: absolute;
    top: calc(50% - 12px);
    left: 0;
    width: 1px;
    height: 24px;
    background: #ffffff;
    opacity: 0.3;
    content: '';
  }
}

.header__button--start {
  -webkit-app-region: no-drag;
  padding: 7px 9px !important;
  //&.el-button--small {
  //  font-size: 16px;
  //  border-radius: 4px;
  //}
  &.el-button--warning {
    background: #fa6400;
    border-color: #fa6400;
    &.is-disabled {
      background: #fa6400;
      border-color: #fa6400;
      opacity: 0.5;
      &:hover {
        background: #fa6400;
        border-color: #fa6400;
        opacity: 0.5;
      }
    }
    &:hover {
      background: #fa6400;
      border-color: #fa6400;
      opacity: 0.9;
    }
  }
}

/* 遮罩层 Begin */
.header__list.overlay {
  position: relative;

  &:after {
    content: ' ';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
  }

  .header__i.i--menu {
    opacity: 0.3;
  }

  button.header__button--start {
    background: #666 !important;
    color: #999;
    border-color: #666 !important;
  }

  .header__button {
    opacity: 0.5;
  }
}
</style>
