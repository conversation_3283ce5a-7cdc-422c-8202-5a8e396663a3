<template>
  <div
    ref="wrap"
    class="po-camera"
  >
    <div class="component-header">
      <div class="header-title">
        {{ $t('辅助摄像头') }}
      </div>
      <div
        class="header-close"
        @click="hide"
      >
        <i class="el-icon-close icon" />
      </div>
    </div>
    <div class="po-camera-content component-content">
      <el-form
        label-position="left"
        label-width="94px"
      >
        <el-form-item :label="$t('摄像头')">
          <el-select
            ref="camera"
            v-model="currentDeviceIndex"
            popper-class="camera-select-dropdown"
            :placeholder="$t('请选择设备')"
            @change="changed"
          >
            <el-option
              v-for="(item, index) in devices"
              :key="item.deviceId"
              :label="item.deviceName"
              :value="index"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('分辨率')">
          <el-select
            ref="microphone"
            v-model="currentResolution"
            popper-class="camera-select-dropdown"
            :disabled="currentDeviceIndex === null"
            :placeholder="$t('请选择分辨率')"
            @change="changed"
          >
            <el-option
              v-for="item in resolutions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-checkbox
            v-model="mirror"
            :checked="mirror"
          >
            {{ $t('镜像模式') }}
          </el-checkbox>
        </el-form-item>
        <el-form-item label="">
          <div class="camera-preview">
            <video
              ref="preview"
              :class="['camera-show', {'mirror': mirror}]"
            />
          </div>
        </el-form-item>
      </el-form>
      <div class="camera-btn">
        <el-button
          type="default"
          @click="hide"
        >
          {{ $t('取消') }}
        </el-button>
        <el-button
          type="primary"
          :disabled="currentDeviceIndex === null || currentResolution === null || !previewSucess"
          @click="start"
        >
          {{ $t('开启') }}
        </el-button>
      </div>
    </div>
    <ScreenCaptureDialog
      ref="screen-capture"
      :title="$t('您尚未打开屏幕共享权限')"
      @status-update="screenCaptureStatusUpdate"
    />
  </div>
</template>
<script>
import i18next from 'i18next';
import Constant from '@/util/Constant';
import BaseComponent from '@/component/core/BaseComponent';
import ScreenCaptureDialog from '@vueComponent/device-detect-component/ScreenCaptureDialog.vue';
import Media from '@/util/Media';

export default {
  name: 'IntroductionComponent',
  components: {
    ScreenCaptureDialog,
  },
  extends: BaseComponent,
  data() {
    return {
      value: '',
      devices: [],
      resolutions: [i18next.t('标清'), i18next.t('高清'), i18next.t('全高清')],
      currentDeviceId: null,
      currentDeviceIndex: null,
      currentResolution: i18next.t('全高清'),
      previewElement: null,
      previewSucess: false, // 成功预览
      mirror: false,
      roomInfo: null,
    };
  },
  watch: {
    mirror(flag) {
      TCIC.SDK.instance.setState(Constant.TStateSubCameraMirror, flag);
    },
  },
  mounted() {
    const { roomInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roomInfo = roomInfo;
    this.addLifecycleTCICEventListener(TCIC.TTrtcEvent.Device_Changed, () => {
      if (this.isVisible) {
        this.init();
      }
    });
  },
  methods: {
    async init() {
      this.previewElement = this.$refs.preview;
      const devices = await TCIC.SDK.instance.getCameras();
      this.devices = devices;
      if (this.currentDeviceId) {
        // 当前预览的设备被拔掉，停止预览
        if (!devices.find(item => item.deviceId === this.currentDeviceId)) {
          // select选择器的更新和当前选项的更新需要在select更新之后
          this.$nextTick(() => {
            this.currentDeviceIndex = null;
            this.currentResolution = null;
            this.currentDeviceId = null;
          });
          this.stopPreview();
        } else {
          // select选择器的更新和当前选项的更新需要在select更新之后
          this.$nextTick(() => {
            this.currentDeviceIndex = devices.findIndex(item => item.deviceId === this.currentDeviceId);
          });
        }
      }
    },
    videoSize() {
      if (this.currentResolution === i18next.t('标清')) {
        return { width: 640, height: 360 };
      }
      if (this.currentResolution === i18next.t('高清')) {
        return { width: 1280, height: 720 };
      }
      if (this.currentResolution === i18next.t('全高清')) {
        return { width: 1920, height: 1080 };
      }
      return { width: 1920, height: 1080 };
    },
    getTTrtcVideoResolution() {
      if (this.currentResolution === i18next.t('标清')) {
        return TCIC.TTrtcVideoResolution.Resolution_640_360;
      }
      if (this.currentResolution === i18next.t('高清')) {
        return TCIC.TTrtcVideoResolution.Resolution_1280_720;
      }
      if (this.currentResolution === i18next.t('全高清')) {
        return TCIC.TTrtcVideoResolution.Resolution_1920_1080;
      }
      return TCIC.TTrtcVideoResolution.Resolution_1920_1080;
    },
    async show() {
      const isVodPlaying = TCIC.SDK.instance.getState(Constant.TStateVodPlayerVisible, false);
      const isSubCameraStarted = TCIC.SDK.instance.getState(Constant.TStateStartSubCamera, false);
      if (isVodPlaying) {
        window.showToast(i18next.t('关闭视频{{arg_0}}后才可使用辅助摄像头'), { arg_0: this.roomInfo.courseware });
        return ;
      } if (isSubCameraStarted) {
        window.showToast(i18next.t('请先关闭当前辅助摄像头'));
        return ;
      }
      const width = 487;
      const height = 472;
      this.updateComponent({
        left: `calc(50% - ${width}px / 2)`,
        top: `calc(50% - ${height}px / 2)`,
        width: `${width}px`,
        height: 'auto',
        display: 'block',
      });
      this.currentResolution = i18next.t('全高清');
      await this.init();
      this.startPreview();
    },
    hide() {
      this.currentDeviceIndex = null;
      this.currentResolution = null;
      this.stopPreview();
      this.updateComponent({
        display: 'none',
      });
    },
    screenCaptureStatusUpdate() {
      this.$refs['screen-capture'].hide();
    },
    async start() {
      if (TCIC.SDK.instance.isElectron() && TCIC.SDK.instance.isMac()) {
        // 判断是否有屏幕分享权限
        const hasPermission = await TCIC.SDK.instance.hasScreenCapturePermission();
        if (!hasPermission) {
          this.$refs['screen-capture'].show();
          return;
        }
      }
      if (this.currentDeviceIndex === null || this.currentResolution === null) {
        return;
      }
      const isVodPlaying = TCIC.SDK.instance.getState(Constant.TStateVodPlayerVisible, false);
      if (isVodPlaying) {
        window.showToast(i18next.t('关闭视频{{arg_0}}后才可使用辅助摄像头'), { arg_0: this.roomInfo.courseware });
        return ;
      }
      const resolution = this.getTTrtcVideoResolution();
      const componentInstance = TCIC.SDK.instance.getComponent('sub-camera-component').getVueInstance();
      componentInstance.startLocalRender(this.currentDeviceIndex, resolution);
      this.hide();
    },
    startPreview() {
      if (this.currentDeviceIndex === null || this.currentResolution === null) {
        return;
      }
      const currentDeviceId = this.devices[this.currentDeviceIndex].deviceId;
      this.currentDeviceId = currentDeviceId;
      this.previewSucess = false;
      const videoSize = this.videoSize();
      Media.mediaDevices.getUserMedia({
        video: {
          deviceId: currentDeviceId,
          aspectRatio: 1.777778,
          width: { min: videoSize.width },
          height: { min: videoSize.height },
        },
        audio: false,
      }).then((stream) => {
        this.previewSucess = true;
        this.previewElement.srcObject = stream;
        this.previewElement.play();
      })
        .catch((error) => {
          this.previewSucess = false;
          switch (error.name) {
            case 'NotAllowedError':
            case 'SecurityError':
              this.$message.error(i18next.t('摄像头打开失败，请检查系统权限设置'));
              break;
            case 'AbortError':
            case 'NotReadableError':
              this.$message.error(i18next.t('摄像头打开失败，可能已被占用或没有权限或不支持这种分辨率'));
              break;
            case 'OverconstrainedError':
              this.$message.error(i18next.t('摄像头打开失败，可能不支持这种分辨率'));
              break;
            case 'NotFoundError':
            case 'TypeError':
            // 以上异常理论上不应该发生，如果出现，说明硬件环境可能发生了变化，重新初始化
              this.init();
              this.$message.error(i18next.t('摄像头打开失败，该摄像头不可用'));
              break;
          }
        });
    },
    stopPreview() {
      const stream = this.previewElement.srcObject;
      if (stream) {
        const tracks = stream.getTracks();
        tracks.forEach((track) => {
          track.stop();
        });
        this.previewElement.srcObject = null;
      }
    },
    changed() {
      this.stopPreview();
      this.startPreview();
    },

  },
};
</script>
<style lang="less">
.po-camera {
  width: 100%;
  color: #fff;
  background: #1c2131;
  border-radius: 4px;
  box-shadow: 0px -3px 2px 0px rgba(0, 0, 0, 0.1);
  &-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    .el-form {
      .el-select > .el-input {
        width: 326px;
      }
      .el-form-item {
        .el-form-item__content {
          line-height: 0;
        }
        &:first-child {
          margin-bottom: 16px;
        }
      }
    }
    .camera-preview {
      width: 320px;
      height: 180px;
      border-radius: 6px;
      background: url('./assets/video_empty.png');
      object-fit: cover;
      .camera-show {
        width: 100%;
        height: 100%;
        &.mirror {
          transform: rotateY(180deg);
        }
      }
    }
    .camera-btn {
      display: flex;
      flex-direction: row;
      justify-content: center;
      margin-bottom: 22px;
      .el-button {
        min-width: 100px;
        height: 32px;
        margin: 2px 12px;
        padding: 0 10px;
        font-size: 16px;
        &--default {
          background: #31373d;
          border: none;
          &:hover {
            color: rgba(#fff, .8);
          }
          &.is-active, &:focus {
            color: #fff;
          }
        }
      }
    }
  }

  .component-content {
    .el-select .el-input.is-disabled .el-input__inner {
      background-color: #222;
    }
  }
}

.camera-select-dropdown {
  font-size: 16px;
  background: #191D2C !important;
  &.el-popper {
    margin-top: 6px!important;
  }
  .el-select-dropdown__item {
    position: relative;
    padding-left: 40px;
    font-size: 16px;
  }
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item.select,
  .el-select-dropdown__item:hover {
    color: #006EFF;
    background: none !important;
  }

  .el-select-dropdown__item.selected {
    color: #fff;
    font-weight: normal;
    &:after {
      position: absolute;
      top: 9px;
      left: 16px;
      display: flex;
      width: 16px;
      height: 16px;
      content: '';
      background: url("./assets/ic-select.svg") no-repeat center;
    }
    &:hover {
      color: #006EFF;
    }
  }
}
</style>
