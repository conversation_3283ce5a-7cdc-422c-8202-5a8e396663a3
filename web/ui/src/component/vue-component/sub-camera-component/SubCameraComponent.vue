<template>
  <div
    ref="wrap"
    class="sub-camera"
  >
    <div
      :class="['header', {'small' : isSmallScreen}]"
    >
      <div class="sub-camera__title">
        {{ $t('辅助摄像头') }}
      </div>
      <div v-if="isTeacher || isAssistant || isSupervisor">
        <button
          v-if="!isVideoLiveClass && !isUnitedVideoOnlyClass"
          class="fullscreen"
          @click="toggleFullScreen"
        />
        <button
          class="close"
          @click="close"
        />
      </div>
    </div>
    <div
      :class="['content-wrap', {'mirror': mirror}]"
    >
      <div
        ref="content"
        data-dom-name="subcamera-video-dom"
        class="content"
      />
    </div>
  </div>
</template>
<script>
import BaseComponent from '@/component/core/BaseComponent';
import Constant from '@/util/Constant';
import Drag from '@util/Drag';

export default {
  extends: BaseComponent,
  data() {
    return {
      taskId: 'sub_camera',
      isTeacher: false,
      isAssistant: false,
      isSupervisor: false,
      isShow: false,
      isVideoLiveClass: false,
      isUnitedVideoOnlyClass: false,
      remoteUserId: null,
      rectBeforeFUllScreen: null,
      isFullScreen: false,
      videoSequence: -1,
      currentRectRatio: null, // 相对于白板的偏移比例{
      minWidth: 492,
      minHeight: 277,
      mirror: false,
      bindingUserId: '',
    };
  },
  watch: {
    isShow(val) {
      TCIC.SDK.instance.setState(Constant.TStateShowSubCameraComponent, val);
    },
  },
  mounted() {
    // 获取初始的权限信息并初始化辅助摄像头状态
    this.onPermissionUpdate(TCIC.SDK.instance.getPermissionList());
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Permission_Update, this.onPermissionUpdate);
    this.addLifecycleTCICEventListener(Constant.TEventComponentUpdateLayout, this.boardLayoutUpdate);
    this.addLifecycleTCICStateListener(Constant.TStateHeaderVisible, flag => this.updateSmallLiveSubCameraLayout(this.isShow));
    this.addLifecycleTCICStateListener(TCIC.TMainState.Joined_TRTC, (joined) => {
      // 公开课退出连麦后如果正在使用辅助摄像头，则退出辅助摄像头
      const isStartSubCamera = TCIC.SDK.instance.getState(Constant.TStateStartSubCamera, false);
      if (!joined && isStartSubCamera) {
        this.stopLocalRender();
      }
    });
    this.makeSureClassJoined(() => {
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      this.isSupervisor = TCIC.SDK.instance.isSupervisor();
      this.isVideoLiveClass = TCIC.SDK.instance.isLiveClass() && !TCIC.SDK.instance.isClassLayoutHasDoc();
      this.isUnitedVideoOnlyClass = TCIC.SDK.instance.isVideoOnlyClass();
      const boardElement = document.getElementById('white-board');
      new Drag({
        dragEle: this.$el.parentNode,
        targetEles: [boardElement],
        pad: 15,
        lockRadio: true,
        enableDrag: false,
        enableResize: true,
        enableSimpleResize: true,
        onResize: (x, y, w, h, cursor, isReSize) => {
          if (Math.floor(w) < this.minWidth || Math.floor(h) < this.minHeight) {
            return;
          }
          console.log('resize', JSON.stringify(this.currentRect), x, y, w, h);
          this.setSelfRect({
            left: Math.floor(x),
            top: Math.floor(y),
            width: Math.floor(w),
            height: Math.floor(h),
          });
        },
        onStartResize: (drag, x, y) => {
        },
        onEndResize: (drag, position) => {
          this.updateTask();
        },
      });
      // 状态更新
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Task_Updated, this.onTaskUpdate);
      // 更新最初的状态
      TCIC.SDK.instance.getTasks(0).then((result) => {
        result.tasks.forEach((taskInfo) => {
          this.onTaskUpdate(taskInfo);
        });
      });
    });
    // 监听窗口尺寸变更
    window.addEventListener('resize', this.onWindowResize);
    // 监听辅助摄像头镜像
    this.addLifecycleTCICStateListener(Constant.TStateSubCameraMirror, mirror => this.mirror = mirror);
  },
  methods: {
    onWindowResize() {
      setTimeout(async () => {    // 延时刷新布局，避免获取尺寸不准
        this.updateLocalPosition();
      }, 200);
    },
    show() {
      this.isShow = true;
      this.updateVideoLiveSubCameraLayout();
      if (this.isVideoLiveClass) {
        return this.updateComponent(this.getVideoLiveComponentLayout());
      }
      if (this.isUnitedVideoOnlyClass) {
        return this.updateComponent(this.getVideoOnlyComponentLayout());
      }
      return this.updateComponent({
        position: 'absolute',
        left: `calc(50% - ${this.minWidth}px / 2)`,
        top: `calc(50% - ${this.minHeight}px / 2)`,
        width: `${this.minWidth}px`,
        height: `${this.minHeight}px`,
        display: 'block',
      });
    },
    hide() {
      this.isShow = false;
      if (!this.isTeacher) {
        const switchCom = TCIC.SDK.instance.getComponent('college-video-switch-component');
        if (switchCom) {
          switchCom.getVueInstance().updateStartSubCamera(false);
        }
      }
      return this.updateComponent({
        display: 'none',
      });
    },
    startLocalRender(deviceIndex, resolution) {
      if (!TCIC.SDK.instance.getState(Constant.TStateStartSubCamera, false)) {
        const content = this.$refs.content;
        TCIC.SDK.instance.setState(Constant.TStateStartSubCamera, true);
        TCIC.SDK.instance.startSubCamera(content, deviceIndex, resolution).then(() => {
          this.show().then(async () => {
            // TCIC.SDK.instance.setState(Constant.TStateStartSubCamera, true);
            this.bindingUserId = TCIC.SDK.instance.getUserId();
            const boardRect = await this.getBoardRect();
            if (boardRect.width > 0 && boardRect.height > 0) {
              this.toggleComponentDrag(true, '.sub-camera__title', boardRect);
            }
            this.updateTask();
          })
            .catch((err) => {
              TCIC.SDK.instance.setState(Constant.TStateStartSubCamera, false);
            });
        });
      }
    },
    stopLocalRender() {
      this.isFullScreen = false;
      this.bindingUserId = '';
      this.stopTask();
      this.hide();
      if (TCIC.SDK.instance.getState(Constant.TStateStartSubCamera, false)) {
        TCIC.SDK.instance.setState(Constant.TStateStartSubCamera, false);
        TCIC.SDK.instance.stopSubCamera().then(() => {
        });
      }
    },
    async updateLocalPosition() {   // 更新本地位置信息
      // 在下次渲染的时候获取宽高，保证获取的是最新的
      const boardRect = await this.getBoardRect();
      if (!boardRect || this.isUnitedVideoOnlyClass) {
        return ;
      }
      if ((this.isTeacher || this.isAssistant || this.isSupervisor) && !this.isVideoLiveClass && !this.isUnitedVideoOnlyClass) {
        // 限定拖动区域
        this.toggleComponentDrag(false, '.sub-camera__title');
        this.toggleComponentDrag(true, '.sub-camera__title', boardRect);
      }
      if (this.currentRectRatio) {
        // 更新展示位置
        const newRect = {
          left: Math.floor(this.currentRectRatio.left * boardRect.width + boardRect.left),
          top: Math.floor(this.currentRectRatio.top * boardRect.height + boardRect.top),
          width: Math.floor(this.currentRectRatio.width * boardRect.width),
          height: Math.floor(this.currentRectRatio.height * boardRect.height),
        };
        this.setSelfRect(newRect);
      }
    },
    async boardLayoutUpdate(target) {
      if (target.theTCICComponentName === 'board-component') {
        requestAnimationFrame(async () => {
          this.updateLocalPosition();
        });
      }
    },
    async toggleFullScreen(sync = true) {
      const boardRect = await this.getBoardRect();
      if (this.isFullScreen) {
        this.isFullScreen = false;
        this.toggleComponentDrag(true, '.sub-camera__title', boardRect);    // 退出全屏时开启拖动
        this.setSelfRect(this.rectBeforeFUllScreen).then(() => {
          sync && this.updateTask();
        });
      } else {
        this.isFullScreen = true;
        this.rectBeforeFUllScreen = this.getSelfRect();
        this.toggleComponentDrag(false, '.sub-camera__title');      // 全屏时关闭拖动(避免设置的位置被修改引起异常)
        this.setSelfRect(boardRect).then(() => {
          sync && this.updateTask();
        });
      }
    },
    close() {
      this.stopLocalRender();
    },
    getSelfRect() {
      return this.$refs.wrap.getBoundingClientRect();
    },
    getBoardRect() {
      return new Promise((resolve) => {
        const boardElement = document.getElementById('white-board');
        if (!this.isVideoLiveClass && !this.isUnitedVideoOnlyClass && boardElement) {
          const oriRect = boardElement.getBoundingClientRect();
          const boardRect = JSON.parse(JSON.stringify(oriRect));
          if (boardRect.width <= 0 || boardRect.height <= 0) {
            // 获取的白板区域错误，500ms后重试获取
            setTimeout(() => {
              this.getBoardRect().then((rect) => {
                resolve(rect);
              });
            }, 500);
          } else {
            // 调整比例到16：9
            const ratio = 16 / 9;
            if (boardRect.height * ratio > boardRect.width) {
              // 以宽为基准
              const newHeight = boardRect.width / ratio;
              boardRect.top += (boardRect.height - newHeight) / 2;
              boardRect.y = boardRect.top;
              boardRect.height = newHeight;
              boardRect.bottom = boardRect.top + boardRect.height;
            } else {
              // 以高为基准
              const newWidth = boardRect.height * ratio;
              boardRect.left += (boardRect.width - newWidth) / 2;
              boardRect.x = boardRect.left;
              boardRect.width = newWidth;
              boardRect.right = boardRect.left + boardRect.width;
            }
            resolve(boardRect);
          }
        } else {
          resolve({ top: 0, left: 0, width: 0, height: 0 });
        }
      });
    },
    setSelfRect(rect) {
      if (rect) {
        // 不能设置最小尺寸，否则在移动端上会超出白板范围
        // if (rect.height < this.minHeight) {
        //   rect.height = this.minHeight;
        // }
        // if (rect.width < this.minWidth) {
        //   rect.width = this.minWidth;
        // }
        return this.updateComponent({
          position: this.isVideoLiveClass ? 'relative' : 'absolute',
          left: `${rect.left}px`,
          top: `${rect.top}px`,
          width: `${rect.width}px`,
          height: `${rect.height}px`,
          display: this.isShow ? 'block' : 'none',
          zIndex: 250,
        });
      }
    },
    onComponentDragEnd() {
      if (this.isTeacher || this.isAssistant || this.isSupervisor) {
        this.updateTask();
      }
    },
    async onTaskUpdate(taskInfo) {
      if (taskInfo.taskId !== this.taskId) {
        // 不属于辅助摄像头任务
        return;
      }
      if (taskInfo.status === 0) {
        // 任务结束
        this.stopLocalRender(); // 远程关闭
        return;
      }
      const contentJson = JSON.parse(taskInfo.content);
      if (contentJson.userId === TCIC.SDK.instance.getUserId()) {
        // 本人任务更新
        return;
      }

      this.isShow = true;   // 收到任务时展示
      if (this.isVideoLiveClass && !this.isSmallScreen) {
        this.updateVideoLiveSubCameraLayout();
        this.updateComponent(this.getVideoLiveComponentLayout());
        return;
      }
      if (this.isVideoOnlyClass && !this.isSmallScreen) {
        this.updateComponent(this.getVideoOnlyComponentLayout());
        return;
      }
      if (TCIC.SDK.instance.isLiveClass() && this.isSmallScreen) {
        // 直播课堂
        this.updateSmallLiveSubCameraLayout(this.isShow);
        return;
      }
      const startSubCamera = TCIC.SDK.instance.getState(Constant.TStateStartSubCamera, false);
      if (taskInfo.taskId === 'sub_camera' && !this.isTeacher && !startSubCamera) {
        const switchCom = TCIC.SDK.instance.getComponent('college-video-switch-component');
        if (switchCom) {
          switchCom.getVueInstance().updateStartSubCamera(true);
        }
      }
      const boundsRatio = contentJson.bounds;
      this.isFullScreen = !!contentJson.fullScreen;
      this.currentRectRatio = boundsRatio;
      const boardRect = await this.getBoardRect();
      if (this.isFullScreen || this.isUnitedVideoOnlyClass) {
        this.rectBeforeFUllScreen = this.getSelfRect();
        this.toggleComponentDrag(false, '.sub-camera__title');      // 全屏时关闭拖动(避免设置的位置被修改引起异常)
        this.setSelfRect(boardRect);
      } else {
        this.toggleComponentDrag(true, '.sub-camera__title', boardRect);    // 退出全屏时开启拖动
        const left = Math.floor(boardRect.left + boundsRatio.left * boardRect.width);
        const top = Math.floor(boardRect.top + boundsRatio.top * boardRect.height);
        const width = Math.floor(boundsRatio.width * boardRect.width);
        const height = Math.floor(boundsRatio.height * boardRect.height);
        this.setSelfRect({ left, top, width, height });
      }
    },
    async updateTask() {
      if (!this.isVideoLiveClass && !this.isUnitedVideoOnlyClass) {
        // 获取组件基于白板的相对位置，按比例换算
        const boardRect = await this.getBoardRect();
        const selfRect = this.getSelfRect();
        this.currentRectRatio =  {
          left: (selfRect.left - boardRect.left) / boardRect.width,
          top: (selfRect.top - boardRect.top) / boardRect.height,
          width: selfRect.width / boardRect.width,
          height: selfRect.height / boardRect.height,
        };
        const content = {
          bounds: this.currentRectRatio,
          fullScreen: this.isFullScreen,
          userId: TCIC.SDK.instance.getUserId(),
        };
        TCIC.SDK.instance.updateTask(
          this.taskId,
          JSON.stringify(content),
          -1,
          false,
          this.bindingUserId,
        ).then((task) => {
          // 更新成功
        })
          .catch((error) => {
          // 更新失败
          });
      } else {    // 视频直播课没有白板
        TCIC.SDK.instance.updateTask(
          this.taskId,
          JSON.stringify({
            bounds: {
              left: 0,
              top: 0,
              width: this.minWidth,
              height: this.minHeight,
            },
            fullScreen: this.isFullScreen,
            userId: TCIC.SDK.instance.getUserId(),
          }),
          -1,
          false,
          this.bindingUserId,
        );
      }
    },
    stopTask() {
      TCIC.SDK.instance.stopTask(this.taskId).then((task) => {
      })
        .catch((error) => {
        });
    },
    onPermissionUpdate(permissionList) {
      TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Visible, true)
        .then(() => {  // 确保课堂可见才处理
          // 遍历权限列表，找到具有屏幕分享权限并且正在屏幕分享的人
          const sharerPermissionList = permissionList.filter(permission => permission.screen && permission.screenState < 2 && permission.userId !== this.selfUserId);
          if (sharerPermissionList.length <= 0) {
            // 没有人有分享权限
            this.stopRemoteRender();
            return;
          }
          const permission = sharerPermissionList[0];
          if (permission.screen !== 3) {
            // 没有人有分享辅助摄像头权限
            this.stopRemoteRender();
            return;
          }
          if (TCIC.SDK.instance.getUserId() === permission.userId) {
            // 自己分享，无需处理
            return;
          }
          if (permission.screenState < 2) {
            this.startRemoteRender(permission.userId);
          } else {
            this.stopRemoteRender();
          }
        });
    },
    stopRemoteRender() {
      if (this.videoSequence > 0) {
        this.hide();
        TCIC.SDK.instance.unbindRemoteVideoDom(
          this.remoteUserId,
          TCIC.TTrtcVideoStreamType.Sub,
          this.videoSequence,
        );
        this.remoteUserId = null;
        this.videoSequence = -1;
      }
    },
    startRemoteRender(userId) {
      if (this.videoSequence < 0) {
        this.remoteUserId = userId;
        this.videoSequence = TCIC.SDK.instance.bindRemoteVideoDom(
          userId,
          TCIC.TTrtcVideoStreamType.Sub,
          this.$refs.content,
          true,
        );
      }
    },
    updateVideoLiveSubCameraLayout() {  // 更新辅助摄像头的布局
      if (this.isVideoLiveClass) {
        const subCameraCom = TCIC.SDK.instance.getComponent('sub-camera-component');
        const videoWrap = TCIC.SDK.instance.getComponent('videowrap-component');
        if (subCameraCom && videoWrap) {
          const videoWrapVue = videoWrap.getVueInstance();
          if (subCameraCom.parentNode !== videoWrapVue.$refs.student) {
            videoWrapVue.$refs.student.appendChild(subCameraCom);
          }
        }
      }
    },
    getVideoLiveComponentLayout() {  //  获取组件的布局
      const wrap = TCIC.SDK.instance.getComponent('videowrap-component');
      let videoWidth = this.minWidth;
      let videoHeight = this.minHeight;
      if (wrap) {
        const videoWrapVue = wrap.getVueInstance();
        videoWidth = videoWrapVue.videoWidth;
        videoHeight = videoWrapVue.videoHeight;
      }
      return {
        top: '0',
        left: '0',
        position: 'relative',
        width: `${videoWidth}px`,
        height: `${videoHeight}px`,
        display: 'block',
        style: 'overflow: visible;margin:2px;',
      };
    },
    getVideoOnlyComponentLayout() {   // 获纯视频课布局
      return {
        top: '0',
        left: '0',
        position: 'relative',
        width: `${Constant.TConstantVideoWidth}px`,
        height: `${Constant.TConstantVideoHeight}px`,
        display: 'block',
        style: 'overflow: visible;margin:2px;',
      };
    },
    updateSmallLiveSubCameraLayout(show) {  // 更新公开课小屏布局
      if (!this.isSmallScreen || this.isUnitedVideoOnlyClass) return;  // 忽略非移动端
      this.updateComponent({
        left: '25%',
        top: '25%',
        position: 'absolute',
        width: `${Constant.TConstantVideoWidth * 1.5}px`,
        height: `${Constant.TConstantVideoHeight * 1.5}px`,
        display: show ? 'block' : 'none',
        style: 'overflow: visible;margin:2px;',
      });
      this.toggleComponentDrag(false, '.sub-camera__title');
      this.toggleComponentDrag(true, '.sub-camera__title');
    },
  },
};
</script>
<style lang="less">
.sub-camera {
  width: 100%;
  height: 100%;
  background: #1C2131;
  overflow: hidden;
  .header {
    position: absolute;
    background: #1C2131;
    width: 100%;
    height: 36px;
    box-shadow: 0px -3px 2px 0px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0 16px;
    z-index: 1000;
    &.small {
      background: rgba(28, 33, 49, 0.6);
    }
    .sub-camera__title {
      height: 36px;
      font-size: 14px;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 36px;
      flex-grow: 1;
    }
    button {
      width: 20px;
      height: 20px;
      &.fullscreen {
        background: url("./assets/fullscreen.svg") no-repeat;
        margin-right: 12px;
      }
      &.close {
        background: url("./assets/close.svg") no-repeat;
      }
    }
  }
  .content-wrap {
    width: 100%;
    height: 100%;
    overflow: hidden;
    &.mirror {
      transform: rotateY(180deg);
    }
    .content {
      margin: -15px;
      width: calc(100% + 30px);
      height: calc(100% + 30px);
      .vcp-player {
        width: 100% !important;
        height: 100% !important;
        video {
          width: 100% !important;
          height: 100% !important;
        }
      }
    }
  }
}
</style>
