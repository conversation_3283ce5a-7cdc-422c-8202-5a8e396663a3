<template>
  <div :class="['students-wrap-component', {'show-background' : isShowVideoList}]">
    <div
      v-show="isShowVideoList"
      class="wrap-content"
    >
      <div
        ref="studentList"
        class="student-list"
      >
        <div
          id="students-wrap"
          ref="wrap"
          class="students-wrap"
        />
      </div>
      <template v-if="isShowScroll">
        <div
          class="video-pack left"
          :class="{ 'disable' : !leftEnable }"
          @click.stop.prevent="scrollVideoH(-1)"
        >
          <i />
        </div>
        <div
          class="video-pack right"
          :class="{ 'disable' : !rightEnable }"
          @click.stop.prevent="scrollVideoH(1)"
        >
          <i />
        </div>
      </template>
    </div>
    <div
      :class="['video-hide', {'show' : !isShowVideoList}]"
      @click="toggleShow"
    />
  </div>
</template>

<script>
import i18next from 'i18next';
import BaseComponent from '@core/BaseComponent';
import Constant from '@/util/Constant';
import Lodash from 'lodash';

export default {
  extends: BaseComponent,
  props: {},
  data() {
    return {
      videoWidth: 160,    // 视频宽度
      videoHeight: 90,    // 视频高度
      studentComs: [],    // 学生组件列表
      isShowScroll: false,  // 是否显示滚动条
      leftEnable: false, // 左侧滚动按钮是否可点击
      rightEnable: false, // 右侧滚动按钮是否可点击
      isShowVideoList: true,  // 是否显示视频列表
      roleInfo: {},
      roomInfo: {},
    };
  },

  mounted() {
    const { roomInfo, roleInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    this.roomInfo = roomInfo;
    // 监听加入课堂事件
    this.makeSureClassJoined(this.onJoinClass);
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Task_Updated, this.onTaskUpdate);

    this.addLifecycleTCICEventListener(TCIC.TMainEvent.AV_Add, (info) => {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      if (!classInfo || classInfo.teacherId === info.userId) { // 忽略老师事件
        return;
      }
      if (info.userId === TCIC.SDK.instance.getUserId()) {  // 自己的视频，一开始就展示
        this.loadStudentComponent(info);
      } else {  // 其他人的视频，开始上课后，才展示远端视频
        TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start)
          .then(() => {
            this.loadStudentComponent(info);
          });
      }
    });

    this.addLifecycleTCICEventListener(TCIC.TMainEvent.AV_Remove, (info, reason) => {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      if (classInfo.teacherId === info.userId) { // 忽略老师事件
        return;
      }
      if (info.userId === TCIC.SDK.instance.getUserId()) {
        window.showToast(i18next.t('你已下台，暂时无法参与音视频互动~'));
      }
      TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start)
        .then(() => {
          this.removeStudentComponent(info);
        });
    });
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Permission_Update, this.sortStudents);
    // 监听组件尺寸变更
    const resizeObserver = new ResizeObserver((entries) => {
      this.onComponentResize(entries[0].contentRect);
    });
    resizeObserver.observe(this.$el);
  },
  beforeDestroy() {

  },
  methods: {
    // 进入课堂处理
    onJoinClass() {
      TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Not_Start).then(() => {
        // 未上课达到最大人数上线，则只展示视频框，不打开摄像头。上课后删除该视频框
        const myUserId = TCIC.SDK.instance.getUserId();
        const myPermission = TCIC.SDK.instance.getPermission(myUserId);
        this.loadStudentComponent(myPermission);
        TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start).then(() => {
          const permission = TCIC.SDK.instance.getPermission(myUserId);
          if (!permission.stage) {  // 上课后若不在台上则移除自己
            this.removeStudentComponent(myPermission);
          } else {    // 上课后时先关闭本地摄像头，待学生确认后再打开本地视频上行
            const myDom = TCIC.SDK.instance.getComponent('student-component', myUserId);
            if (myDom && myDom.getVueInstance()) {
              myDom.getVueInstance().enableCamera(false);
            }
          }
        });
      });
      TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start)
        .then(() => {
          const classInfo = TCIC.SDK.instance.getClassInfo();
          const permissionList = TCIC.SDK.instance.getPermissionList();
          permissionList
            .filter(info => info.userId !== classInfo.teacherId && info.stage)
            .forEach((info) => {
              this.loadStudentComponent(info);
            });
        });
    },
    // 尺寸变更事件
    onComponentResize(rect) {
      this.$nextTick(() => {
        this.updateStudentWrapStyle();
        this.updateScroll();
      });
    },
    // 任务更新
    onTaskUpdate(taskInfo) {
      if (TCIC.SDK.instance.isTeacher()) return;    // 老师忽略这个事件
      if (taskInfo.taskId === Constant.TConstantCollegeTaskEnableMicRequest) {
        TCIC.SDK.instance.setState(Constant.TStateCollegeEnableStudentMicRequest, taskInfo.content === 'true');
      }
    },
    // 加载学生组件
    loadStudentComponent(info) {
      if (this.studentComs.find(ele => ele.getAttribute('label') === info.userId)) {
        return;
      }
      TCIC.SDK.instance.loadComponent('student-component', {
        position: 'relative',
        width: `${this.videoWidth}px`,
        height: `${this.videoHeight}px`,
        display: 'inline-block',
        style: 'overflow: visible;',
      }, 'students-wrap', info.userId)
        .then((ele) => {
          if (ele === null) {
            console.warn(`VideoWrapComponent::mounted=>load student component fail: ${info.userId}`);
            return;
          } if (info.userId === TCIC.SDK.instance.getUserId()) { // 将自己放到第一个
            const wrap = this.$refs.wrap;
            const firstChild = wrap.firstChild;
            if (firstChild) {
              wrap.insertBefore(ele, firstChild);
            }
            // 设备检测完成，且开始上课后才显示该提示
            if (TCIC.SDK.instance.isInteractClass() && !TCIC.SDK.instance.isCollegeClass()) {
              // eslint-disable-next-line max-len
              const loadBeforeClassStart = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status) === TCIC.TClassStatus.Not_Start;
              TCIC.SDK.instance.promiseState(Constant.TStateDeviceDetect, false)
                .then(() => TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start))
                .then(() => {
                  const audioEnable = info.mic;
                  const videoEnable = info.camera;
                  const stageEnable = info.stage;
                  if (audioEnable && videoEnable) {
                    if (loadBeforeClassStart && TCIC.SDK.instance.getState(Constant.TStateLocalAVBeforeClassBegin)) {
                      const audioCapture = TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Capture);
                      const videoCapture = TCIC.SDK.instance.getState(TCIC.TMainState.Video_Capture);
                      if (audioCapture && videoCapture) {
                        window.showToast(i18next.t('你已被{{arg_0}}邀请上台，音视频将被打开~', { arg_0: this.roomInfo.teacher }));
                      } else if (audioCapture) {
                        window.showToast(i18next.t('你已被{{arg_0}}邀请上台，视频已关闭~', { arg_0: this.roomInfo.teacher }));
                      } else if (videoCapture) {
                        window.showToast(i18next.t('你已被{{arg_0}}邀请上台，麦克风已关闭~', { arg_0: this.roomInfo.teacher }));
                      } else {
                        window.showToast(i18next.t('你已被{{arg_0}}邀请上台，音视频已关闭~', { arg_0: this.roomInfo.teacher }));
                      }
                    } else {
                      window.showToast(i18next.t('你已被{{arg_0}}邀请上台，音视频将被打开~', { arg_0: this.roomInfo.teacher }));
                    }
                  } else if (audioEnable) {
                    window.showToast(i18next.t('你已上台，为了不干扰{{arg_0}}秩序，您的视频已被关闭，如有需要，请举手申请。', { arg_0: this.roomInfo.name }));
                  } else if (videoEnable) {
                    window.showToast(i18next.t('你已上台，为了不干扰{{arg_0}}秩序，您的麦克风已被关闭，如有需要，请举手申请。', { arg_0: this.roomInfo.name }));
                  } else if (stageEnable) {
                    window.showToast(i18next.t('你已上台，为了不干扰{{arg_0}}秩序，您的音视频已被关闭，如有需要，请举手申请。', { arg_0: this.roomInfo.name }));
                  }
                });
            }
            this.studentComs.unshift(ele);
          } else {
            this.studentComs.push(ele);
          }
          this.updateScroll();
        });
    },
    // 移除学生组件
    removeStudentComponent(info) {
      TCIC.SDK.instance.removeComponent('student-component', info.userId)
        .then(() => {
          this.updateScroll();
        });
      this.studentComs = this.studentComs.filter(ele => ele.getAttribute('label') !== info.userId);
    },
    // 水平滚动, 小于0向左，大于0向右
    scrollVideoH(direction) {
      const studentScroll = this.$refs.wrap;
      // 整页整页翻可能造成断续感，
      const len = studentScroll.offsetWidth;// - (studentScroll.offsetWidth % (this.videoWidth + 8));
      let scrollPos = direction > 0 ? studentScroll.scrollLeft + len : studentScroll.scrollLeft - len;
      if (scrollPos < 0) {
        scrollPos = 0;
      } else if (scrollPos > studentScroll.scrollWidth - studentScroll.offsetWidth) {
        scrollPos = studentScroll.scrollWidth - studentScroll.offsetWidth;
      }
      this.leftEnable = scrollPos > 0;
      this.rightEnable = scrollPos < studentScroll.scrollWidth - studentScroll.offsetWidth ;
      studentScroll.scrollLeft = scrollPos;
    },
    // 刷新滚动控制栏状态
    updateScroll() {
      const studentScroll = this.$refs.wrap;
      this.isShowScroll = (studentScroll.scrollWidth - studentScroll.clientWidth > 10);
      this.leftEnable = studentScroll.scrollLeft > 0;
      this.rightEnable = studentScroll.scrollLeft < studentScroll.scrollWidth - studentScroll.offsetWidth ;
    },
    updateStudentWrapStyle() {
      const parenetNode = this.$refs.studentList;
      const count = Math.floor(parenetNode.clientWidth / (this.videoWidth + 8));
      this.$refs.wrap.style = `width:${count * (this.videoWidth + 8)}px;`;
    },
    // 隐藏视频列表
    toggleShow() {
      this.isShowVideoList = !this.isShowVideoList;
      TCIC.SDK.instance.setState(Constant.TStateShowStudentsVideoWrap, this.isShowVideoList);
    },
    // 视频列表排序
    sortStudents: Lodash.throttle(function () {
      if (this.studentComs.length <= 1 || !this.isShowVideoList) {
        return;
      }
      this.studentComs.sort((item1, item2) => {
        const userid1 = item1.getAttribute('label');
        const userid2 = item2.getAttribute('label');
        const permission1 = TCIC.SDK.instance.getPermission(userid1);
        const permission2 = TCIC.SDK.instance.getPermission(userid2);
        const shareScreen1 = permission1.screen > 0 && (permission1.screenState === TCIC.TScreenState.Sharing || permission1.screenState === TCIC.TScreenState.Paused);
        const shareScreen2 = permission2.screen > 0 && (permission2.screenState === TCIC.TScreenState.Sharing || permission2.screenState === TCIC.TScreenState.Paused);
        // 第一优化级，是否自己
        if (userid1 === TCIC.SDK.instance.getUserId()) return -1;
        if (userid2 === TCIC.SDK.instance.getUserId()) return 1;
        // 第二优化级，屏幕分享
        if (shareScreen1 && !shareScreen2) {
          return -1;
        }
        if (shareScreen2 && !shareScreen1) {
          return 1;
        }
        // 进入第三优先级比较：视频是否打开
        // cameraState表示摄像头状态，0是未知，1是打开，2是关闭
        if (permission1.cameraState === 1 && permission2.cameraState !== 1) {
          return -1;
        }
        if (permission1.cameraState !== 1 && permission2.cameraState === 1) {
          return 1;
        }
        // 进入第四优先级比较：进入课堂顺序
        if (permission1.lastEnterTime && permission2.lastEnterTime) {
          return permission1.lastEnterTime  - permission2.lastEnterTime;
        }
        return 0;
      });
      // 更新UI布局
      this.layoutStudents();
    }, 200, {
      leading: true,
      trailing: false,
    }),
    // 刷新显示
    layoutStudents() {
      for (let i = 0; i < this.studentComs.length; i ++) {
        this.$refs.wrap.appendChild(this.studentComs[i]);
      }
    },
    /** ********* 大教学模式学生端接口封装 ************ */
    // 打开麦克风
    enableMic() {
      const nRet = this.getDeviceOperateAuth('mic');
      if (nRet !== 0) {
        return;
      }
      const stuCom = this.studentComs.find(ele => ele.getAttribute('label') === TCIC.SDK.instance.getUserId());
      if (stuCom) {
        stuCom.getVueInstance().enableMic(true);
      } else {
        window.showToast(i18next.t('你不在台上，可举手申请上台，打开音视频互动~'));
      }
    },
    // 关闭麦克风
    disableMic() {
      const stuCom = this.studentComs.find(ele => ele.getAttribute('label') === TCIC.SDK.instance.getUserId());
      if (stuCom) {
        stuCom.getVueInstance().enableMic(false);
      } else {
        window.showToast(i18next.t('你不在台上，可举手申请上台，打开音视频互动~'));
      }
    },
    // 打开摄像头
    enableCamera() {
      const nRet = this.getDeviceOperateAuth('camera');
      if (nRet !== 0) {
        return;
      }
      const stuCom = this.studentComs.find(ele => ele.getAttribute('label') === TCIC.SDK.instance.getUserId());
      if (stuCom) {
        stuCom.getVueInstance().enableCamera(true);
      } else {
        window.showToast(i18next.t('你不在台上，可举手申请上台，打开音视频互动~'));
      }
    },
    // 关闭摄像头
    disableCamera() {
      const stuCom = this.studentComs.find(ele => ele.getAttribute('label') === TCIC.SDK.instance.getUserId());
      if (stuCom) {
        stuCom.getVueInstance().enableCamera(false);
      } else {
        window.showToast(i18next.t('你不在台上，可举手申请上台，打开音视频互动~'));
      }
    },
    // 开启屏幕分享
    enableScreenShare() {
      const stuCom = this.studentComs.find(ele => ele.getAttribute('label') === TCIC.SDK.instance.getUserId());
      if (stuCom) {
        const isScreenShare = TCIC.SDK.instance.getState(TCIC.TMainState.Screen_Share) < 2;
        if (isScreenShare) {
          window.showToast(i18next.t('你有正在共享的内容，如有需要，请先停止共享'));
          return;
        }
        const permission = TCIC.SDK.instance.getPermission(TCIC.SDK.instance.getUserId());
        if (!permission.screen) {
          window.showToast(i18next.t('暂未开启您共享屏幕的权限'));
          return;
        }
        stuCom.getVueInstance().enableScreenShare()
          .catch((err) => {
            if (err.errorCode !== 0) {
              window.showToast(err.errorMsg);
            }
          });
      } else {
        window.showToast(i18next.t('你不在台上，可举手申请上台，打开音视频互动~'));
      }
    },
    // 关闭屏幕分享
    disableScreenShare() {
      const stuCom = this.studentComs.find(ele => ele.getAttribute('label') === TCIC.SDK.instance.getUserId());
      if (stuCom) {
        stuCom.getVueInstance().disableScreenShare();
      } else {
        window.showToast(i18next.t('你不在台上，可举手申请上台，打开音视频互动~'));
      }
    },
    // 打开自己的麦克风权限
    enableMyMicPermission(enable = true) {
      return TCIC.SDK.instance.memberAction({
        actionType: enable ? TCIC.TMemberActionType.Mic_Open : TCIC.TMemberActionType.Mic_Close,
        userId: TCIC.SDK.instance.getUserId(),
      }).then(() => TCIC.SDK.instance.setState(Constant.TStateCollegeRequestMicPermission, enable))
        .catch(err => console.error(err));
    },
    // 获取设备操作权限
    // devType: 设备类型: mic: 麦克风；camera: 摄像头
    // return: 1:全体禁言 2:没有麦克风权限 3:没有摄像头权限 4:上台人数已满
    getDeviceOperateAuth(devType) {
      let nRet = 0;
      const permissions = TCIC.SDK.instance.getPermissionList();
      const userId = TCIC.SDK.instance.getUserId();
      const permission = permissions.find(item => item.userId === userId);
      const muteAll = TCIC.SDK.instance.getState(TCIC.TMainState.Mute_All);
      if (devType === 'mic' && muteAll && !permission.mic) {
        nRet = 1;
        if (TCIC.SDK.instance.getState(Constant.TStateCollegeEnableStudentMicRequest)) {
          this.enableMyMicPermission();
        } else {
          window.showToast(i18next.t('{{arg_0}}已开启全员静音，你可以举手申请发言~', { arg_0: this.roleInfo.teacher }), 'error');
        }
      } else {
        const classInfo = TCIC.SDK.instance.getClassInfo();
        let stageCount = 0;
        let isStage = false;
        for (let i = 0; i < permissions.length; i++) {
          if (permissions[i].mic
            || permissions[i].camera
            || permissions[i].stage) {
            stageCount += 1;
            if (permissions[i].userId === userId) {
              isStage = true;
            }
          }
          if (devType === 'mic') {
            if (permissions[i].userId === userId
              && !permissions[i].mic
              && (permissions[i].stage || permissions[i].camera)) {
              nRet = 2;
              if (TCIC.SDK.instance.getState(Constant.TStateCollegeEnableStudentMicRequest)) {
                this.enableMyMicPermission();
              } else {
                window.showToast(i18next.t('{{arg_0}}关闭了你的麦克风权限，你可以举手申请打开', { arg_0: this.roleInfo.teacher?.toLowerCase() }));
              }
              return;
            }
          } else if (devType === 'camera') {
            if (permissions[i].userId === userId
              && !permissions[i].camera
              && (permissions[i].stage || permissions[i].mic)) {
              nRet = 3;
              window.showToast(i18next.t('{{arg_0}}关闭了你的摄像头权限，你可以举手申请打开', { arg_0: this.roleInfo.teacher?.toLowerCase() }));
              return;
            }
          }
        }
        if (classInfo.maxRtcMember === stageCount - 1 // 包含了老师所以减一
          && !isStage) {  // 上台人数已满后不允许上台
          nRet = 4;
          window.showToast(i18next.t('上台人数已满，后续版本将支持更多{{arg_0}}上台', { arg_0: this.roleInfo.student }));
          return;
        }
        return nRet;
      }
    },
  },
};
</script>


<style lang="less">

.students-wrap-component {
  width: 100%;
  height: 100%;

  &.show-background {
    background: rgba(48, 48, 48, 0.3);
  }

  .wrap-content {
    margin-left: 124px;
    margin-right: 124px;
    height: 100%;
    .student-list {
      width: 100%;
      height: 100%;
      white-space: nowrap;
      overflow-x: scroll;
      overflow-y: hidden;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;

      .students-wrap {
        overflow-x: hidden;
        scroll-behavior: smooth;

      }
      student-component {
        margin: 12px 4px;
      }

      &::-webkit-scrollbar {
        display: none;
      }
    }

    .disable {
      opacity: 0.3;
    }

    .video-pack {
      position: absolute;
      background: transparent;
      top: 0px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      padding: 0;
      border: 0;

      i {
        display: flex;
        width: 32px;
        height: 32px;
        margin: 41px 0;
      }


      &.left {
        left: 76px;
        i {
          background: url("./assets/icon_left.png") no-repeat;
          background-size: contain;
        }
        &:hover {
          cursor: pointer;
          i {
            background: url("./assets/icon_left_hover.png") no-repeat;
            background-size: contain;
          }
        }
      }

      &.right {
        right: 76px;
        i {
          background: url("./assets/icon_right.png") no-repeat;
          background-size: contain;
        }
        &:hover {
          cursor: pointer;
          i {
            background: url("./assets/icon_right_hover.png") no-repeat;
            background-size: contain;
          }
        }
      }

      &.left, &.right {
        position: absolute;
      }
    }
  }

  .video-hide {
    position: absolute;
    width: 56px;
    height: 20px;
    right: 0px;
    bottom: 0px;
    opacity: 0.8;
    background: url("./assets/icon_hide.png") no-repeat;
    background-size: contain;

    &.show {
      top: 0px;
      right: 0px;
      background: url("./assets/icon_show.png") no-repeat;
      background-size: contain;
    }

    &:hover {
      cursor: pointer;
      opacity: 1;
    }
  }
}
</style>
