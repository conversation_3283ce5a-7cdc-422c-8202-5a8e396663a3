<template>
  <div class="network-tips-component">
    <div
      v-if="showComponent"
      class="network-tips__body"
    >
      <div class="network-tips__content">
        <div class="network-tips__title">
          {{ title }}
        </div>
        <div class="network-tips__message">
          {{ message }}
        </div>
        <div class="network-tips__buttons">
          <button
            v-if="btnIgnre"
            class="btn-com ignore major"
            @click="onIgnore"
          >
            {{ $t('忽略') }}
          </button>
          <button
            v-if="btnCloseVideo"
            class="btn-com close-video minor"
            @click="onCloseVideo"
          >
            {{ translateTip.audioRoom }}
          </button>
          <button
            v-if="btnQuit"
            class="btn-com quit-class major"
            @click="onQuit"
          >
            {{ translateTip.exitRoom }}
          </button>
          <button
            v-if="btnReload"
            class="btn-com reload-class minor"
            @click="onReload"
          >
            {{ $t('重新进入') }}
          </button>
          <button
            v-if="btnCheck"
            class="btn-com check-network minor"
            @click="onCheck"
          >
            {{ $t('检测网络') }}
          </button>
        </div>
      </div>
      <div class="network-tips__line left" />
      <div class="network-tips__line right" />
      <div class="network-tips__tip left" />
      <div class="network-tips__tip right" />
    </div>
    <div
      v-if="showToast"
      :class="['network-tips__toast',
               {'network-tips__toast-small-screen' : isSmallScreen}]"
    >
      <div
        v-for="msg in toastMsg"
        :key="msg"
        :class="['toast-msg',{'toast-msg-small-screen' : isSmallScreen}]"
      >
        <i class="icon el-icon--left icon-network-warning" />
        {{ msg }}
        <button
          v-if="btnCloseVideo"
          :class="['btn_close_tips',{'btn_close_tips-small-screen' : isSmallScreen}]"
          @click="onCloseVideo"
        >
          {{ translateTip.audioRoom }}
        </button>
        <div
          :class="['btn_close',{'btn_close-small-screen' : isSmallScreen}]"
          @click="hiddenNetworkTips"
        >
          <i class="el-icon-close icon" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import i18next from 'i18next';
import BaseComponent from '@core/BaseComponent';
import Constant from '@/util/Constant';

export default {
  extends: BaseComponent,
  props: {},
  data() {
    return {
      showComponent: false,
      title: i18next.t('报告~网络好像失去连接了'),
      message: i18next.t('网络异常，请检查网络设置，或尝试重新进入'),
      enableWeakNetworkTips: true,      // 启用弱网提醒
      btnIgnre: false,
      btnCloseVideo: false,
      btnQuit: true,
      btnReload: true,
      btnCheck: true,
      showToast: false,
      showBadNetworkToast: false,
      toastMsg: [],
      toastTimer: null,
      toastInterval: null,
      lastToastTime: 0,
      dlgTime: 0,
      reportBadNetworkTime: 0,
      cpuToastCpuTime: 0,
      reportCpuTime: 0,
      badNetworkCount: 0,
      loadingReq: 0,
      roomInfo: {},
    };
  },
  computed: {
    translateTip() {
      return {
        audioRoom: i18next.t('音频{{arg_0}}', { arg_0: this.roomInfo.name }),
        exitRoom: i18next.t('退出{{arg_0}}', { arg_0: this.roomInfo.name }),
      };
    },
  },

  mounted() {
    this.roomInfo = TCIC.SDK.instance.getRoleInfo().roomInfo;
    this.makeSureClassJoined(this.onJoinClass);
  },
  beforeDestroy() {
  },
  methods: {
    onJoinClass() {
      this.addLifecycleTCICEventListener(TCIC.TStatisticsEvent.Network_Statistics, this.onStatistics);
      // @hotfix 国外网络检测可能存在问题，临时隐藏网络检测的提示 Temporary hiding for Turito
      // 禁用掉弱网提示，这个其实应该走后端的配置的
      this.enableWeakNetworkTips = false;
      // this.enableWeakNetworkTips = TCIC.SDK.instance.isFeatureAvailable('WeakNetworkTips');
    },
    onStatistics(statistic) {
      if (!TCIC.SDK.instance.isTeacher()
          && TCIC.SDK.instance.isLiveClass()
          && TCIC.SDK.instance.isMobile()
          && !TCIC.SDK.instance.isPad()) {
        // 学生移动端直播课隐藏网络弹窗
        return;
      }
      const curTime = new Date().getTime();
      const isClassStart = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status) === TCIC.TClassStatus.Already_Start;
      const isChecking = TCIC.SDK.instance.getState(Constant.TStateIsGettingSystemInfo, false);

      if (!isClassStart && statistic.networkQuality !== TCIC.TNetworkQuality.Down) {  // 没上课时不展示非断网的弱网提示
        return;
      }

      // 每 5 分钟最多显示一次 toast
      const TOAST_MIN_INTERVAL = 5 * 60 * 1000;
      // bad network 持续阈值（单位为采样次数，每 2 秒采样一次）
      const BAD_COUNT_THRESHOLD = 8;

      switch (statistic.networkQuality) {
        case TCIC.TNetworkQuality.Down:     // 无网络
          // @hotfix 国外网络检测可能存在问题，临时隐藏网络检测的提示 Temporary hiding for Turito
          // this.showLoading();
          // this.disconnect();
          TCIC.SDK.instance.reportLog(
            'network-quality',
            '[networkWarning] type: broken, error_message: network broken',
          );
          break;
        case TCIC.TNetworkQuality.Vbad:     // 极差
        case TCIC.TNetworkQuality.Bad:     // 差
          this.badNetworkCount += 1;
          this.hideLoading();
          if (this.badNetworkCount > BAD_COUNT_THRESHOLD
            && curTime - this.lastToastTime > TOAST_MIN_INTERVAL
          ) {
            this.lastToastTime = new Date().getTime();
            this.badNetworkCount = 0;
            if (statistic.networkQuality === TCIC.TNetworkQuality.Vbad) {
              // vbad
              this.showWeakConnect();
              TCIC.SDK.instance.reportLog(
                'network-quality',
                '[networkWarning] type: very-bad, error_message: network very bad',
              );
            } else {
              // bad
              this.showWeakNetworkToast();
              TCIC.SDK.instance.reportLog(
                'network-quality',
                '[networkWarning] type: bad, error_message: network bad',
              );
            }
          }
          if (curTime - this.reportBadNetworkTime > 10 * 60 * 1000) {
            TCIC.SDK.instance.reportEvent('network_statistics', statistic, -1);
            this.reportBadNetworkTime = curTime;
          }
          break;
        default:
          this.badNetworkCount = 0;
          this.hideLoading();
          this.showComponent = false;
          this.hiddenNetworkTips();
          break;
      }
      if (statistic.systemCpu >= 80 && curTime - this.cpuToastCpuTime > 5 * 60 * 1000 && !isChecking) {
        // this.showHighCPUWarnToast(); // 隐藏cpu堵车消息
        this.cpuToastCpuTime = curTime;
      }
      if (statistic.systemCpu >= 80 && curTime - this.reportCpuTime > 10 * 60 * 1000) {
        TCIC.SDK.instance.reportEvent('cpu_statistics', statistic, -1);
        this.reportCpuTime = curTime;
      }
    },

    // 重新进入课堂后依然断网
    stillDisconnected() {
      this.showComponent = true;
      this.title = i18next.t('尽力了，未发现网络连接');
      this.message = i18next.t('网络异常，请稍后重试');
      this.clearBtn();
      this.btnQuit = true;
    },

    // 持续断网
    disconnect() {
      this.showComponent = true;
      this.title = i18next.t('报告~网络好像失去连接了');
      this.message = i18next.t('网络异常，请检查网络设置，或尝试重新进入');
      this.clearBtn();
      this.btnQuit = true;
      this.btnReload = true;
      this.btnCheck = true;
    },

    // 弱网、卡顿
    weakConnect() {
      if (!this.enableWeakNetworkTips) {  // 禁用弱网提示时直接返回
        return;
      }
      const isAudioMode = TCIC.SDK.instance.getState(Constant.TStateAudioMode, false);
      this.showComponent = true;
      this.title = i18next.t('zzz...当前网络开小差啦~');
      this.clearBtn();
      this.btnIgnre = true;
      this.btnCheck = true;
      if (isAudioMode) {  // 已经是音频模式
        this.message = i18next.t('当前网络状况较差，请及时更换网络');
        this.btnCloseVideo = false;
      } else {
        this.message = i18next.t('当前网络状况较差，为保证{{arg_0}}效果，建议关闭视频', { arg_0: this.roomInfo.name });
        this.btnCloseVideo = true;
      }
    },

    showWeakConnect() {
      if (!this.enableWeakNetworkTips) {  // 禁用弱网提示时直接返回
        return;
      }
      const isAudioMode = TCIC.SDK.instance.getState(Constant.TStateAudioMode, false);
      if (isAudioMode) {  // 已经是音频模式
        this.message = i18next.t('当前网络状况较差，请及时更换网络');
        this.btnCloseVideo = false;
      } else {
        this.message = i18next.t('当前网络状况较差，为保证{{arg_0}}效果，建议关闭视频', { arg_0: this.roomInfo.startRoom });
        this.btnCloseVideo = true;
      }
      this.toastShow(this.message);
    },
    // 展示弱网提示
    showWeakNetworkToast() {
      if (!this.enableWeakNetworkTips) {  // 禁用弱网提示时直接返回
        return;
      }
      this.btnCloseVideo = false;
      this.toast([i18next.t('zzz...当前网络开小差啦~')]);
    },

    // 展示高CPU提示
    showHighCPUWarnToast() {
      this.toast([i18next.t('系统CPU堵车啦~'), i18next.t('请尝试退出部分应用，以保证{{arg_0}}效果', { arg_0: this.roomInfo.name })]);
    },

    // 展示网络toast
    toast(msgs) {
      if (this.toastTimer) {
        clearTimeout(this.toastTimer);
        this.toastTimer = null;
      }
      this.showToast = true;
      this.toastMsg = msgs;
      this.toastTimer = setTimeout(() => {
        this.showToast = false;
      }, 5000);
    },

    toastShow(msgs) {
      let timeCount = 10;
      this.toastInterval = setInterval(() => {
        const msgRes = `${msgs}(${timeCount}s)`;
        this.toastMsg = [msgRes];
        this.showToast = true;
        timeCount -= 1;
        if (timeCount < 0) {
          clearInterval(this.toastInterval);
          this.toastInterval = null;
          this.showToast = false;
        }
      }, 1000);
    },

    hiddenNetworkTips() {
      this.showToast = false;
      if (this.toastInterval) {
        clearInterval(this.toastInterval);
        this.toastInterval = null;
      }
    },

    // 忽略
    onIgnore() {
      this.showComponent = false;
      this.dlgTime = new Date().getTime();
    },

    // 关闭视频
    onCloseVideo() {
      TCIC.SDK.instance.setState(Constant.TStateAudioMode, true);
      TCIC.SDK.instance.setState(Constant.TStateNetWorkTipsAudio, true);
      this.dlgTime = new Date().getTime();
      this.showComponent = false;
      this.showToast = false;
      this.hiddenNetworkTips();
    },

    // 退出
    onQuit() {
      TCIC.SDK.instance.unInitialize();
    },

    // 重新进入
    onReload() {
      const loading = TCIC.SDK.instance.getComponent('loading-component')
        .getVueInstance();
      loading.showText(i18next.t('正在重新进入{{arg_0}}，请稍后...', { arg_0: this.roomInfo.name }));
      TCIC.SDK.instance.reloadClass();
      this.showComponent = false;
    },

    // 检测网络
    onCheck() {
      TCIC.SDK.instance.getComponent('network-detector-component').getVueInstance()
        .toggle(true);
      this.showComponent = false;
    },

    clearBtn() {
      this.btnIgnre = false;
      this.btnCloseVideo = false;
      this.btnQuit = false;
      this.btnReload = false;
      this.btnCheck = false;
    },

    showLoading(message) {
      if (!this.loadingReq) {
        const loading = TCIC.SDK.instance.getComponent('loading-component')
          .getVueInstance();
        this.loadingReq = loading.showText('', true);
      }
    },
    hideLoading() {
      if (this.loadingReq) {
        const loading = TCIC.SDK.instance.getComponent('loading-component')
          .getVueInstance();
        loading.hideText(this.loadingReq);
        this.loadingReq = 0;
      }
    },
  },
};
</script>

<style lang="less">
.network-tips-component {
  @--color-primary: #006EFF;
  @--color-public: #14181D;
  position: relative;

  .network-tips__body {
    .network-tips__content {
      margin-top: 35px;
      padding: 30px 10px;
      border: 5px solid #F1F1F1;
      border-radius: 12px;
      background: linear-gradient(316deg, #C5C5C5 0%, #DADADA 100%);

      .network-tips__title {
        font-size: 20px;
        font-weight: 600;
        color: #393939;
        line-height: 28px;
        text-align: center;
        margin-top: 10px;
      }
      .network-tips__message {
        font-size: 16px;
        font-weight: 400;
        color: #393939;
        text-align: center;
        margin: 10px 0;
      }
      .network-tips__buttons {
        margin-top: 24px;
        text-align: center;

        .btn-com {
          width: 96px;
          height: 38px;
          border-radius: 25px;
          margin: 0 8px;
          background: @--color-public;
          font-size: 16px;
          color: #FFFFFF;
          font-weight: 600;

          &.minor {
            color: #FFF;
            width: 104px;
            height: 46px;
            padding: 4px;
            background-image: linear-gradient(@--color-public, @--color-public), linear-gradient(148deg, #E7E7E7 0%, #A5A5A5 100%);
            background-clip: content-box, padding-box;

            &:hover {
              background-image: linear-gradient(@--color-primary, @--color-primary), linear-gradient(148deg, #E7E7E7 0%, #A5A5A5 100%);
            }
          }

          &.major {
            cursor: pointer;
            color: @--color-public;
            background: transparent;
            border: 2px solid @--color-public;

            &:hover {
              color: @--color-primary;
              border: 2px solid @--color-primary;
            }
          }
        }
      }
    }

    .network-tips__line {
      position: absolute;
      top: -38px;
      width: 1px;
      height: 48px;
      border: 1px solid #C5C5C5;

      &.left {
        left: 83px;
      }
      &.right {
        right: 83px;
      }
    }

    .network-tips__tip {
      position: absolute;
      top: 10px;
      width: 16px;
      height: 16px;
      border-radius: 16px;
      background: @--color-public;

      &.left {
        left: 76px;
      }
      &.right {
        right: 76px;
      }
    }
  }

  .network-tips__toast {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
    width: auto;
    text-align: center;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 24px;
    white-space: nowrap;

   .icon-network-warning {
    width: 24px;
    height: 24px;
    padding-right: 4px;
    background-image: url(./assets/icon-network-status-poor.svg);
    background-repeat: no-repeat;
    background-size: contain;
   }

    .toast-msg {
     display: flex;
     flex-direction: row;
     align-items: center;
     justify-content: center;
     flex-wrap: nowrap;
     font-size: 20px;
     font-weight: 500;
     color: #FFFFFF;
    }

    .btn_close_tips {
     width: 80px;
     height: 32px;
     background: #006EFF;
     border-radius: 4px;
     font-size: 16px;
     color: #FFFFFF;
     font-weight: 500;
     margin: 0 8px;
     margin-left: 30px;
    }

    .btn_close {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 2px;
      background: rgba(0, 0, 0, 0.05);
      color: rgba(#979797, .5);
    }
  }

  .network-tips__toast-small-screen {
     position: fixed;
     top: 50%;
     left: 50%;
     transform: translateX(-60%) translateY(-50%);
     width: auto;
     text-align: center;
     background: rgba(0, 0, 0, 0.3);
     border-radius: 6px;
     padding: 15px;
     white-space: normal;

    .icon-network-warning {
    width: 24px;
    height: 24px;
    padding-right: 4px;
    background-image: url(./assets/icon-network-status-poor.svg);
    background-repeat: no-repeat;
    background-size: contain;
   }
     .toast-msg-small-screen {
     display: flex;
     flex-direction: row;
     align-items: center;
     justify-content: center;
     flex-wrap: nowrap;
      font-size: 10px;
      font-weight: 100;
      color: #FFFFFF;
    }

    .btn_close_tips-small-screen {
     width: 80px;
     height: 26px;
     background: #006EFF;
     border-radius: 2px;
     font-size: 8px;
     color: #FFFFFF;
     font-weight: 500;
     margin: 0 4px;
     margin-left: 15px;
    }

    .btn_close-small-screen {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 16px;
      height: 16px;
      border-radius: 2px;
      background: rgba(0, 0, 0, 0.05);
      color: rgba(#979797, .5);
    }
    }
}
</style>
