/* eslint-disable function-paren-newline */
import i18next from 'i18next';
import BaseComponent from '@core/BaseComponent';
import Constant from '@/util/Constant';
import Lodash from 'lodash';
import Media from '@/util/Media';
import DetectUtil from './DetectUtil';
import { MediaPermissionsErrorType, requestMediaPermissions } from 'mic-check';


const detectVolumeInterval = 200;

export const DeviceDetectBaseComponent = {
  extends: BaseComponent,

  data() {
    return {
      deviceDetectReportAction: 'device-detect-base',
      deviceDetectStatus: 'active', // active / reset
      cameras: [],
      camera: null,
      mirror: null,
      microphones: [],
      microphone: null,
      speakers: [],
      speaker: null,
      audioPlayStatus: false,
      speakerTestMediaUrl: './static/assets/detect.mp3',
      speakerTestMediaDuration: 9000,
      speakerTestSto: null,
      detection: {
        browser: {
          available: null,
          tip: {
            detecting: i18next.t('检测中...'),
            available: i18next.t('当前浏览器可用'),
            unavailable: i18next.t('浏览器不兼容，请升级或使用其他浏览器'),
          },
        },
        'screen-capture': {
          available: null,
          tip: {
            detecting: i18next.t('检测中...'),
            available: i18next.t('屏幕共享功能可用'),
            unavailable: i18next.t('未授权屏幕共享权限，需配置后方可使用'),
          },
        },
        camera: {
          tip: {
            detecting: i18next.t('检测中...'),
            available: i18next.t('请确认是否看到摄像头画面'),
            unavailable: TCIC.SDK.instance.isMac() ? i18next.t('摄像头打开失败，请确认是否未被授权') : i18next.t('摄像头打开失败，请确认是否被其他应用程序占用'),
            nodevice: i18next.t('无可用摄像头'),
          },
          enumerating: false,
          available: null,
        },
        microphone: {
          volume: 0,
          volumeLevel: 0,
          beforeVolume: 0, // 禁用或启用前音量
          tip: {
            detecting: i18next.t('检测中...'),
            available: i18next.t('请说话测试麦克风音量大小'),
            unavailable: TCIC.SDK.instance.isMac() ? i18next.t('麦克风打开失败，请确认是否未被授权') : i18next.t('麦克风打开失败，请确认是否被其他应用程序占用'),
            nodevice: i18next.t('无可用麦克风'),
            mute: i18next.t('音量值设置为0时，将无法正常通话'),
            low: i18next.t('音量值设置过低，将影响通话体验'),
          },
          enumerating: false,
          available: null,
        },
        speaker: {
          volume: 0,
          beforeVolume: 0, // 禁用或启用前音量
          tip: {
            detecting: i18next.t('检测中...'),
            available: i18next.t('请点击试听确认扬声器正常工作'),
            unavailable: TCIC.SDK.instance.isMac() ? i18next.t('扬声器打开失败，请确认是否未被授权') : i18next.t('扬声器打开失败，请检查系统设置'),
            nodevice: i18next.t('无可用扬声器'),
            mute: i18next.t('音量值设置为0时，将无法正常通话'),
            low: i18next.t('音量值设置过低，将影响通话体验'),
          },
          enumerating: false,
          available: null,
        },
      },
      requestCamPermissionTip: false,
      requestMicPermissionTip: false,
      result: {
        browser: {
          available: false,
          reason: null,
        },
        camera: {
          available: false,
          reason: null,
        },
        microphone: {
          available: false,
          reason: null,
        },
        speaker: {
          available: false,
          reason: null,
        },
      },
      enableAIDenoise: false,
      isAIDenoiseSupported: false,
    };
  },

  computed: {
    cameraTooltip() {
      let tip = '';
      if (this.detection.camera.available === null) {
        // 检测中
        tip = this.detection.camera.tip.detecting;
      } else if (this.cameras.length === 0) {
        // 无设备
        tip = this.detection.camera.tip.nodevice;
        // ok 可用
      } else if (this.detection.camera.available) {
        tip = this.detection.camera.tip.available;
      } else {
        //  设备不可用-根据错误区分原因
        tip = this.detection.camera.tip.unavailable;
        const isWeb = TCIC.SDK.instance.isWeb();
        // todo
        const errCode = this.detection.camera.errCode;
        if (errCode) {
          switch (errCode) {
            case -1001: // 打开失败
              tip = i18next.t('摄像头打开失败，请检查系统设置');
              break;
            case -1002: // 找不到设备
              tip = i18next.t('找不到可用摄像头');
              break;
            case -1003: // 未授权
              tip = i18next.t('摄像头权限未开启，请前往开启');
              if (isWeb) {
                tip = i18next.t('摄像头权限未开启，');
                this.requestCamPermissionTip = true;
              }
              break;
            case -1004: // 设备被占用
              tip = i18next.t('摄像头被占用，请关闭其他软件');
              break;
            case 2: // 已关闭
              tip = i18next.t('摄像头已关闭，请开启');
              break;
            default:
              break;
          }
        }
      }
      return tip;
    },

    micTooltip() {
      let tip = '';
      if (this.detection.microphone.available === null) {
        // 检测中
        tip = this.detection.microphone.tip.detecting;
      } else if (this.microphones.length === 0) {
        // 无麦克风设备
        tip = this.detection.microphone.tip.nodevice;
      } else if (this.detection.microphone.available) {
        if (this.detection.microphone.volume === 0) {
          // 设备可用且音量为0
          tip = this.detection.microphone.tip.mute;
        } else if (this.detection.microphone.volume <= 80) {
          // 设备可用且音量低于 80
          tip = this.detection.microphone.tip.low;
        } else {
          // 麦克风设备可用
          tip = this.detection.microphone.tip.available;
        }
      } else {
        //  麦克风设备不可用
        tip = this.detection.microphone.tip.unavailable;
        const isWeb = TCIC.SDK.instance.isWeb();
        const errCode = this.detection.microphone.errCode;
        if (errCode) {
          switch (errCode) {
            case -1001: // 打开失败
              tip = i18next.t('麦克风打开失败，请检查系统设置');
              break;
            case -1002: // 找不到设备
              tip = i18next.t('找不到可用麦克风');
              break;
            case -1003: // 未授权
              tip = i18next.t('麦克风权限未开启，请前往开启');
              if (isWeb) {
                tip = i18next.t('麦克风权限未开启，');
                this.requestMicPermissionTip = true;
              }
              break;
            case -1004: // 设备被占用
              tip = i18next.t('麦克风被占用，请关闭其他软件');
              break;
            case 2: // 已关闭
              tip = i18next.t('麦克风已关闭，请开启');
              break;
            default:
              break;
          }
        }
      }
      return tip;
    },
    speakerToollip() {
      let tip = '';
      if (this.detection.speaker.available === null) {
        // 检测中
        tip = this.detection.speaker.tip.detecting;
      } else if (this.speakers.length === 0) {
        // 无扬声器设备
        tip = this.detection.speaker.tip.nodevice;
      } else if (this.detection.speaker.available) {
        if (this.detection.speaker.volume === 0) {
          // 设备可用且音量为0
          tip = this.detection.speaker.tip.mute;
        } else if (this.detection.speaker.volume <= 80) {
          // 设备可用且音量低于 80
          tip = this.detection.speaker.tip.low;
        } else {
          // 扬声器设备可用
          tip = this.detection.speaker.tip.available;
        }
      } else {
        // 扬声器设备不可用
        tip = this.detection.speaker.tip.unavailable;
      }
      return tip;
    },
  },

  watch: {
    mirror(val) {
      localStorage.setItem('mirror', val);
      const { Enable, Disable } = TCIC.TTrtcVideoMirrorType;
      TCIC.SDK.instance.setLocalVideoParams({ mirror: val ? Enable : Disable });
      return val;
    },
    audioPlayStatus(val) {
      clearTimeout(this.speakerTestSto);
      if (val) {
        this.detectSpeaker();
      } else {
        this.stopDetectSpeaker();
      }
    },
    enableAIDenoise(val) {
      this.toggleAIDenoise(val);
    },
  },

  methods: {
    toggleAIDenoise(val) {
      localStorage.setItem('aiDenoise', val);
      DetectUtil.enableAIDenoise(val);
    },
    reportDeviceDetectLog(msg) {
      TCIC.SDK.instance.reportLog(this.deviceDetectReportAction, msg);
    },
    updateTbmTarget() {
      // 各组件自己实现
    },
    initDeviceData() {
      if (localStorage.getItem('mirror')) {
        this.mirror = JSON.parse(localStorage.getItem('mirror'));
      } else {
        this.mirror = true;
      }
      console.log(this.deviceDetectReportAction, 'initDeviceData, mirror', this.mirror);
    },
    initDeviceListener() {
      console.log(this.deviceDetectReportAction, 'initDeviceListener');
      // 设备断通事件
      this.addLifecycleTCICEventListener(TCIC.TTrtcEvent.Device_Changed, this.deviceChangeListener);
      // 音量检测
      this.addLifecycleTCICEventListener(TCIC.TTrtcEvent.Volume_Update, (result) => {
        // 先过滤id，避免因防抖导致音量不展示
        if (result && result.userId === TCIC.SDK.instance.getUserId()) {
          this.volumeUpdateListener(result);
        }
      });
    },

    /**
     * 检测屏幕分享
     */
    async detectScreenCapture() {
      const support = await DetectUtil.detectScreenCaptureSupported();
      // DetectUtil.detectScreenCaptureSupported()第一次返回false的情况下，第二次调用会返回true;
      TCIC.SDK.instance.setState(Constant.TStateScreenPermissionGranted, support);
      this.detection['screen-capture'].available = support;
      this.result['screen-capture'] = {
        available: support,
        reason: '',
      };
      this.reportDeviceDetectLog(
        `[detectScreenCapture] result ${JSON.stringify(support)}`,
      );
      this.updateTbmTarget();
    },

    /**
     * 检测浏览器
     */
    async detectBrowser() {
      const checkResult = await DetectUtil.supportDetect();
      this.detection.browser.available = checkResult.result;
      this.result.browser = {
        available: !!checkResult.result,
        reason: `${JSON.stringify(checkResult.detail)}`,
      };
      this.reportDeviceDetectLog(
        `[detectBrowser] result ${JSON.stringify(checkResult)}`,
      );
    },

    // ---------------- camera start ----------------
    /**
     * @description 开始检测摄像头
     */
    async detectCamera(reason) {
      if (this.deviceDetectStatus === 'reset') {
        return;
      }
      const { camera } = this.detection;
      if (this.cameras.length === 0) {
        console.warn(this.deviceDetectReportAction, '没有枚举到摄像头设备');
        this.reportDeviceDetectLog(
          '[detectCamera] [warn] no cameras',
        );
        camera.available = false;
        return;
      }
      this.reportDeviceDetectLog(
        `[detectCamera] deviceId: ${this.camera?.deviceId}, deviceName: ${this.camera?.deviceName}, reason: ${reason}`,
      );
      camera.available = null;
      try {
        await DetectUtil.startCameraTest(this.$refs.camera);
        camera.available = true;
        this.result.camera = {
          available: true,
          reason: null,
        };
        this.reportDeviceDetectLog(
          `[detectCamera] [succ] deviceId: ${this.camera?.deviceId}, deviceName: ${this.camera?.deviceName}`,
        );

        // 检测成功，设置参数
        const { Enable, Disable } = TCIC.TTrtcVideoMirrorType;
        DetectUtil.setLocalVideoParams({ mirror: this.mirror ? Enable : Disable });
      } catch (e) {
        camera.available = false;
        const errCode = e && e.getCode ? e.getCode() : e?.errorCode;

        this.result.camera = {
          available: false,
          reason: `${e.name || ''} : ${e.message || ''}` || e,
        };
        camera.errCode = errCode;
        let needThrowError = true;
        if (e.errorMsg === 'user stop') {
          // 中断上一个，不算error
          needThrowError = false;
        }
        this.reportDeviceDetectLog(
          `[detectCamera] [error] name: ${e.name}, message: ${e.message}, errCode: ${errCode}, needThrowError ${needThrowError}`,
        );
        if (needThrowError) {
          if (e.message.indexOf('20000') > -1 || e.message.indexOf('超时') > -1) {
            this.showMessageBox();
          }
          throw e;
        }
      }
      this.updateTbmTarget();
    },

    /**
     * @description 重启浏览器提示
     */
    showMessageBox() {
      let tips = i18next.t('请尝试重启应用重新进入');
      if (!TCIC.SDK.instance.isWeb()) {
        tips = i18next.t('请尝试重启应用重新进入');
      }
      // TCIC.SDK.instance.showMessageBox(
      //   i18next.t('错误提示'),
      //   i18next.t(tips),
      //   [],
      //   (index) => {},
      //   {},
      //   '',
      //   false,
      // );
      window.showToast(tips);
    },

    /**
     * @description 停止检测摄像头
     */
    async stopDetectCamera() {
      this.reportDeviceDetectLog(
        `[stopDetectCamera] deviceId: ${this.camera?.deviceId}, deviceName: ${this.camera?.deviceName}`,
      );
      await DetectUtil.stopCameraTest();
    },

    /**
     * 摄像头变化
     */
    async onCameraChange(device, init, reason) {
      const detectDevice = device || this.camera;
      TCIC.SDK.instance.notify('user-event', {
        type: 'change',
        param: {
          action: `Device-Camera_${detectDevice?.deviceName}`,
          reason,
        },

      });
      this.reportDeviceDetectLog(
        `[onCameraChange] deviceId: ${detectDevice?.deviceId}, deviceName: ${detectDevice?.deviceName}, init: ${init}, reason: ${reason}`,
      );
      if (!TCIC.SDK.instance.getState(Constant.TStateDeviceDetect)) {
        // 不是设备检测状态，仅切换，不改 detection
        try {
          await DetectUtil.switchCamera(device.deviceId);
        } catch (e) {
          console.error(this.deviceDetectReportAction, 'switch camera error', e);
          this.$message.error(i18next.t('切换摄像头失败，请重试'));
          this.reportDeviceDetectLog(
            `[onCameraChange] [error] name: ${e.name}, message: ${e.message}`,
          );
        }
        return;
      }

      // 设备检测状态才走这里
      const { camera } = this.detection;
      camera.available = null;
      try {
        await this.stopDetectCamera();
        await DetectUtil.switchCamera(detectDevice.deviceId);
        // windows 端会有闪一下的情况，如果不做这个处理，如果原来是没有流的情况，就看不到视频了
        await this.detectCamera(reason || 'camera change');
        // detectCamera 里已经修改了 camera.available，可能是 false，这里不再重新赋值
        // camera.available = true;
      } catch (e) {
        console.error(this.deviceDetectReportAction, 'switch camera error', e);
        camera.available = false;
        this.$message.error(i18next.t('切换摄像头失败，请重试'));
        this.reportDeviceDetectLog(
          `[onCameraChange] [error] name: ${e.name}, message: ${e.message}`,
        );
      }
      this.updateTbmTarget();
    },
    // ---------------- camera end ----------------

    // ---------------- microphone start ----------------
    /**
     * @description 开始检测麦克风
     */
    async detectMicrophone(reason) {
      if (this.deviceDetectStatus === 'reset') {
        return;
      }
      const { microphone } = this.detection;
      if (this.microphones.length === 0) {
        console.warn(this.deviceDetectReportAction, '没有枚举到麦克风设备');
        this.reportDeviceDetectLog(
          '[detectMicrophone] [warn] no microphones',
        );
        microphone.available = false;
        return;
      }
      this.reportDeviceDetectLog(
        `[detectMicrophone] deviceId: ${this.microphone?.deviceId}, deviceName: ${this.microphone?.deviceName}, reason: ${reason}`,
      );
      microphone.available = null;
      try {
        await DetectUtil.startMicTest(this.$refs.microphone);
        microphone.available = true;
        this.result.microphone = {
          available: true,
          reason: null,
        };
        this.reportDeviceDetectLog(
          `[detectMicrophone] [succ] deviceId: ${this.microphone?.deviceId}, deviceName: ${this.microphone?.deviceName}`,
        );

        // 检测成功，开始检测音量
        this.detectMicrophoneVolume('mic succ');
      } catch (e) {
        microphone.available = false;
        this.result.microphone = {
          available: false,
          reason: `${e.name || ''} : ${e.message || ''}` || e,
        };
        const errCode = e && e.getCode ? e.getCode() : e?.errorCode;
        microphone.errCode = errCode;
        let needThrowError = true;
        if (e && e.getCode && (errCode === 0x1001 || errCode === -2)) {
          // 这里为什么不处理？
          needThrowError = false;
        }
        if (e.errorMsg === 'user stop') {
          // 中断上一个，不算error
          needThrowError = false;
        }
        this.reportDeviceDetectLog(
          `[detectMicrophone] [error] name: ${e?.name}, message: ${e?.message}, errCode: ${errCode}, needThrowError ${needThrowError}`,
        );
        if (needThrowError) {
          if (e.message.indexOf('20000') > -1 || e.message.indexOf('超时') > -1) {
            this.showMessageBox();
          }
          throw e;
        }
      }
      this.updateTbmTarget();
    },

    /**
     * @description 停止检测麦克风
     */
    async stopDetectMicrophone() {
      this.reportDeviceDetectLog(
        `[stopDetectMicrophone] deviceId: ${this.microphone?.deviceId}, deviceName: ${this.microphone?.deviceName}`,
      );
      await DetectUtil.stopDetectMicrophone();
      this.stopDetectMicrophoneVolume();
    },

    /**
     * 麦克风变化
     */
    async onMicrophoneChange(device, init, reason) {
      const detectDevice = device || this.microphone;
      TCIC.SDK.instance.notify('user-event', {
        type: 'change',
        param: {
          action: `Device-Mic_${detectDevice?.deviceName}`,
          reason,
        },
      });
      this.reportDeviceDetectLog(
        `[onMicrophoneChange] deviceId: ${detectDevice?.deviceId}, deviceName: ${detectDevice?.deviceName}, init: ${init}, reason: ${reason}`,
      );

      if (!TCIC.SDK.instance.getState(Constant.TStateDeviceDetect)) {
        // 不是设备检测状态，仅切换，不改 detection
        try {
          await DetectUtil.switchMic(device.deviceId);
        } catch (e) {
          console.error(this.deviceDetectReportAction, 'switch microphone error', e);
          this.$message.error(i18next.t('切换麦克风失败，请重试'));
          this.reportDeviceDetectLog(
            `[onMicrophoneChange] [error] name: ${e.name}, message: ${e.message}`,
          );
        }
        return;
      }

      // 设备检测状态才走这里
      const { microphone } = this.detection;
      microphone.available = null;
      try {
        await this.stopDetectMicrophone();
        // Electron必须先切换设备，再调用检测接口，否则流不会变。
        await DetectUtil.switchMic(detectDevice.deviceId);
        await this.detectMicrophone(reason || 'mic change');
        // detectMicrophone 里已经修改了 microphone.available，可能是 false，这里不再重新赋值
        // microphone.available = true;
      } catch (e) {
        console.error(this.deviceDetectReportAction, 'switch microphone error', e);
        this.$message.error(i18next.t('切换麦克风失败，请重试'));
        microphone.available = false;
        this.reportDeviceDetectLog(
          `[onMicrophoneChange] [error] name: ${e.name}, message: ${e.message}`,
        );
      }
      this.updateTbmTarget();
    },

    /**
     * @description 检测麦克风音量
     */
    async detectMicrophoneVolume(reason) {
      if (this.deviceDetectStatus === 'reset') {
        return;
      }
      const { microphone } = this.detection;
      this.reportDeviceDetectLog(
        `[detectMicrophoneVolume] deviceId: ${this.microphone?.deviceId}, deviceName: ${this.microphone?.deviceName}, reason: ${reason}`,
      );
      try {
        microphone.volume = await DetectUtil.getMicrophoneVolume();
        // PC 默认麦克风音量就设置成100
        if (TCIC.SDK.instance.isElectron()) {
          microphone.volume = 100;
        }
        this.reportDeviceDetectLog(
          `[detectMicrophoneVolume] [succ] deviceId: ${this.microphone?.deviceId}, deviceName: ${this.microphone?.deviceName}, volume ${microphone.volume}`,
        );
      } catch (e) {
        console.error(this.deviceDetectReportAction, 'detectMicrophoneVolume error', e);
        const errCode = e && e.getCode ? e.getCode() : e?.errorCode;
        this.$message.error(i18next.t('获取麦克风音频失败，请重试'));
        this.reportDeviceDetectLog(
          `[detectMicrophoneVolume] [error] name: ${e.name}, message: ${e.message}, errCode: ${errCode}`,
        );
      }
      return DetectUtil.enableVolumeEvaluation(detectVolumeInterval);
    },

    /**
     * @description 停止检测麦克风音量
     */
    stopDetectMicrophoneVolume() {
      // 降低间隔，用于展示音量
      return DetectUtil.enableVolumeEvaluation(500);
    },

    /**
     * 麦克风音量调整
     */
    async onMicrophoneVolumeChange(value) {
      const ava = this.detection?.microphone?.available;
      if (!ava) {
        console.warn('set microphone volume return, mic is not available');
        return;
      }
      try {
        await DetectUtil.setMicrophoneVolume(value);
      } catch (e) {
        console.error(this.deviceDetectReportAction, 'set microphone volume error', e);
        this.$message.error(i18next.t('调整麦克风音量失败，请重试'));
        this.reportDeviceDetectLog(
          `[onMicrophoneVolumeChange] [error] name: ${e.name}, message: ${e.message}`,
        );
      }
      this.updateTbmTarget();
    },
    async disableMicVolume() {
      if (this.detection.microphone.volume > 0) {
        this.detection.microphone.beforeVolume = this.detection.microphone.volume;
        this.detection.microphone.volume = 0;
        await this.onMicrophoneVolumeChange(0);
      } else {
        this.detection.microphone.volume = this.detection.microphone.beforeVolume;
        await this.onMicrophoneVolumeChange(this.detection.microphone.beforeVolume);
      }
    },

    /**
     * 处理音量变化事件
     */
    volumeUpdateListener: Lodash.debounce(function (result) {
      if (this.detection.microphone.volume) {
        this.detection.microphone.volumeLevel = Media.amplifier(result.volume, 22);
      } else {
        this.detection.microphone.volumeLevel = 0;
      }
    }, 50),
    // ---------------- microphone end ----------------

    // ---------------- speaker start ----------------
    /**
     * @description 开始检测扬声器
     */
    async detectSpeaker(reason) {
      if (this.deviceDetectStatus === 'reset') {
        return;
      }
      this.reportDeviceDetectLog(
        `[detectSpeaker] deviceId: ${this.speaker?.deviceId}, deviceName: ${this.speaker?.deviceName}, reason: ${reason}`,
      );
      try {
        await DetectUtil.stopSpeakerTest();
        await DetectUtil.startSpeakerTest(this.speakerTestMediaUrl);
        this.speakerTestSto = setTimeout(() => {
          this.audioPlayStatus = false;
        }, this.speakerTestMediaDuration);
        this.reportDeviceDetectLog(
          `[detectSpeaker] [succ] deviceId: ${this.speaker?.deviceId}, deviceName: ${this.speaker?.deviceName}`,
        );
      } catch (e) {
        console.error(this.deviceDetectReportAction, 'detectSpeaker error', e);
        const errCode = e && e.getCode ? e.getCode() : e?.errorCode;
        this.$message.error(i18next.t('检测扬声器失败，请检查设备并重试'));
        this.audioPlayStatus = false;
        this.reportDeviceDetectLog(
          `[detectSpeaker] [error] name: ${e.name}, message: ${e.message}, errCode: ${errCode}`,
        );
      }
      this.updateTbmTarget();
    },

    /**
     * @description 停止检测扬声器
     */
    stopDetectSpeaker() {
      this.reportDeviceDetectLog(
        `[stopDetectSpeaker] deviceId: ${this.speaker?.deviceId}, deviceName: ${this.speaker?.deviceName}`,
      );
      try {
        DetectUtil.stopSpeakerTest();
      } catch (e) {
        console.error(this.deviceDetectReportAction, 'stopDetectSpeaker error', e);
      }
    },

    /**
     * @description audio播放/暂停切换
     */
    toggleAudioPlay() {
      this.audioPlayStatus = !this.audioPlayStatus;
    },

    /**
     * 扬声器变化
     */
    async onSpeakerChange(device, reason) {
      const detectDevice = device || this.speaker;

      TCIC.SDK.instance.notify('user-event', {
        type: 'change',
        param: {
          action: `Device-Speaker_${detectDevice?.deviceName}`,
          reason,
        },
      });
      this.reportDeviceDetectLog(
        `[onSpeakerChange] deviceId: ${detectDevice?.deviceId}, deviceName: ${detectDevice?.deviceName}}, reason: ${reason}`,
      );
      const { speaker } = this.detection;
      speaker.available = null;
      try {
        if (this.audioPlayStatus) {
          this.toggleAudioPlay();
        }
        await this.stopDetectSpeaker();
        DetectUtil.switchSpeaker(detectDevice.deviceId);
        // this.$message.success('切换扬声器成功');
        speaker.available = true;
      } catch (e) {
        console.error(this.deviceDetectReportAction, 'switch speaker error', e);
        this.$message.error(i18next.t('切换扬声器失败，请重试'));
        speaker.available = false;
        // 停止测试
        this.stopDetectSpeaker();
        this.reportDeviceDetectLog(
          `[onSpeakerChange] [error] name: ${e.name}, message: ${e.message}`,
        );
      }
      this.updateTbmTarget();
    },

    /**
     * @description 检测扬声器音量
     */
    async detectSpeakerVolume() {
      if (this.deviceDetectStatus === 'reset') {
        return;
      }
      const { speaker } = this.detection;
      this.reportDeviceDetectLog(
        `[detectSpeakerVolume] deviceId: ${this.speaker?.deviceId}, deviceName: ${this.speaker?.deviceName}`,
      );
      try {
        speaker.volume = await DetectUtil.getSpeakerVolume();
        this.reportDeviceDetectLog(
          `[detectSpeakerVolume] [succ] deviceId: ${this.speaker?.deviceId}, deviceName: ${this.speaker?.deviceName}, volume ${speaker.volume}`,
        );
      } catch (e) {
        console.error(this.deviceDetectReportAction, 'detectSpeakerVolume error', e);
        const errCode = e && e.getCode ? e.getCode() : e?.errorCode;
        this.$message.error(i18next.t('获取扬声器音频失败，请重试'));
        this.reportDeviceDetectLog(
          `[detectSpeakerVolume] [error] name: ${e.name}, message: ${e.message}, errCode: ${errCode}`,
        );
      }
    },

    /**
     * 扬声器音量调整
     */
    async onSpeakerVolumeChange(value) {
      console.log(this.deviceDetectReportAction, 'set speaker volume', value);
      try {
        await DetectUtil.setSpeakerVolume(value);
      } catch (e) {
        console.error(this.deviceDetectReportAction, 'set speaker volume error', e);
        this.$message.error(i18next.t('调整扬声器音量失败，请重试'));
        this.reportDeviceDetectLog(
          `[onSpeakerVolumeChange] [error] name: ${e.name}, message: ${e.message}`,
        );
      }
    },
    async disableSpeakerVolume() {
      if (this.detection.speaker.volume > 0) {
        this.detection.speaker.beforeVolume = this.detection.speaker.volume;
        this.detection.speaker.volume = 0;
        await this.onSpeakerVolumeChange(0);
      } else {
        this.detection.speaker.volume = this.detection.speaker.beforeVolume;
        await this.onSpeakerVolumeChange(this.detection.speaker.beforeVolume);
      }
      this.updateTbmTarget();
    },
    // ---------------- speaker end ----------------

    /**
     * 处理设备事件
     */
    deviceChangeListener({ deviceId, type, state, name }) {
      this.reportDeviceDetectLog(
        `[deviceChangeListener] type: ${type}, deviceId: ${deviceId}, state: ${state}`,
      );
      switch (state) {
        case TCIC.TTrtcDeviceState.Add:
          this.deviceAddHandler({ deviceId, type });
          break;
        case TCIC.TTrtcDeviceState.Remove:
          this.deviceRemoveHandler({ deviceId, type });
          break;
        case TCIC.TTrtcDeviceState.Active:
          this.deviceActiveHandler({ deviceId, type });
          // this.showDeviceChangeToast({ deviceId, type, name });
          break;
        case TCIC.TTrtcDeviceState.Update:
          this.deviceUpdateHandler({ deviceId, type });
          break;
      }
    },
    // showDeviceChangeToast({ deviceId, type, name }) {
    //   console.log(`当前设备摄像头：${this.camera.deviceName} ${this.camera.deviceId}`, "hhhhaa");
    //   console.log(`当前设备麦克风：${this.microphone.deviceName} ${this.microphone.deviceId}`, "hhhhaa");
    //   console.log(`当前设备扬声器：${this.speaker.deviceName} ${this.speaker.deviceId}`, "hhhhaa");
    //   console.log(`当前变更：${name} ${deviceId}`, "hhhhaa");
    //   let show = false;
    //   if (this.camera) {
    //     if (type == TCIC.TTrtcDeviceType.Camera) {
    //       if (name != this.camera.deviceName) {
    //         show = true;
    //         console.log(`摄像头变更`, 'hhhhaa');
    //       }
    //     }
    //   };
    //   if (this.microphone) {
    //     if (type == TCIC.TTrtcDeviceType.Mic) {
    //       if (name != this.microphone.deviceName) {
    //         show = true;
    //         console.log(`麦克风变更`, 'hhhhaa');
    //       }
    //     }
    //   };
    //   if (this.speaker) {
    //     if (type == TCIC.TTrtcDeviceType.Speaker) {
    //       if (name != this.speaker.deviceName) {
    //         show = true;
    //         console.log(`扬声器变更`, 'hhhhaa');
    //       }
    //     }
    //   };
    //   if (show) {
    //     window.showToast(`当前使用的设备:${name}`);
    //   };
    // },
    deviceAddHandler({ deviceId, type }) {
      // 各组件自己实现
    },
    deviceRemoveHandler({ deviceId, type }) {
      // 各组件自己实现
    },
    deviceActiveHandler({ deviceId, type }) {
      // 各组件自己实现
    },
    deviceUpdateHandler({ deviceId, type }) {
      // 各组件自己实现
    },
    async requestCamPermission() {
      try {
        await requestMediaPermissions({ video: true, audio: false });
      } catch (e) {
        console.error('request requestCamPermission error', e);
      }
    },
    async requestMicPermission() {
      try {
        await requestMediaPermissions({ video: false, audio: true });
      } catch (e) {
        console.error('request requestMicPermission error', e);
      }
    },
  },
};
