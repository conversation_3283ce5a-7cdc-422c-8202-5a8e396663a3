<template>
  <div class="device-detect-wrapper">
    <div
      v-if="modal"
      class="detect-enter"
    >
      <div class="detect-item-camera">
        <div
          ref="camera"
          class="detect-preview__video"
        />
      </div>
      <div class="detect-item-microphone">
        <div
          v-show="false"
          ref="microphone"
        />
      </div>
      <div class="setting">
        <el-row :gutter="20">
          <el-col :span="24">
            <section class="section header">
              <span class="main-text">{{ $t('设备检测') }}</span>
              <a
                v-if="!isCollegeClass && isZh"
                class="link-primary ml10"
                href="javascript:"
                data-user-event="DeviceDetect-helper"
                @click="getHelp('device-detect')"
              >
                {{ $t('获取帮助') }}
                <i class="question-icon" />
              </a>
            </section>
            <section
              v-if="detectDevices.includes('screen-capture')"
              class="section"
            >
              <span class="label">{{ $t('屏幕共享') }}</span>
              <template v-if="detection['screen-capture'].available === null">
                <p class="main-text">
                  {{ detection['screen-capture'].tip.detecting }}
                </p>
              </template>
              <template v-else-if="detection['screen-capture'].available">
                <p class="main-text">
                  {{ detection['screen-capture'].tip.available }}<i
                    class="base-icon success-icon ml10"
                  />
                </p>
              </template>
              <template v-else>
                <p class="main-text">
                  {{ detection['screen-capture'].tip.unavailable }}<i
                    class="base-icon warning-icon ml10"
                  />
                </p>
                <el-button
                  class="plain"
                  plain
                  @click="showScreenCaptureGuide"
                >
                  {{ $t('开始配置屏幕共享权限') }}
                </el-button>
              </template>
            </section>
            <section
              v-if="detectDevices.includes('browser')"
              class="section"
            >
              <span class="label">{{ $t('浏览器') }}</span>
              <template v-if="detection.browser.available === null">
                <p class="main-text el-input">
                  {{ detection.browser.tip.detecting }}
                </p>
              </template>
              <template v-else-if="detection.browser.available">
                <p class="main-text el-input">
                  {{ detection.browser.tip.available }}
                </p>
                <i class="base-icon success-icon ml10" />
              </template>
              <template v-else>
                <p class="main-text el-input">
                  {{ detection.browser.tip.unavailable }}<i
                    class="base-icon warning-icon ml10"
                  />
                </p>
                <el-button
                  class="plain"
                  plain
                  @click="getHelp('download-browser')"
                >
                  {{ $t('下载浏览器') }}
                </el-button>
              </template>
            </section>
            <!--  网络检测 -->
            <section
              class="section"
            >
              <span class="label">{{ $t('网络状况') }}</span>
              <template v-if="isClassStarted">
                <p
                  class="el-input main-text"
                  :class="{ 'error-text': !networkDetecting && (networkStatus>=4) }"
                >
                  {{ networkStatusText }}
                  <button
                    v-if="!networkDetecting"
                    class="network-btn"
                    style="color: #006eff;"
                    @click="reDetectNetwork"
                  >
                    {{ $t('重新检测') }}
                  </button>
                </p>
                <el-tooltip
                  effect="dark"
                  :content="networkStatusTipsText"
                  placement="right"
                  :popper-class="(!networkDetecting && (networkStatus>=4)) ? 'network-error-tps': ''"
                  :value="networkTipsVisible"
                >
                  <i
                    v-loading="networkDetecting"
                    element-loading-spinner="el-icon-loading"
                    element-loading-background="transparent"
                    :class="{
                      'success-icon': !networkDetecting && networkStatus<4,
                      'warning-icon': !networkDetecting && (networkStatus>=4)
                    }"
                    class="ml10 base-icon"
                  />
                </el-tooltip>
              </template>
              <template v-if="!isClassStarted">
                <p class="el-input sub-text main-text">
                  {{ translateTip.netWorkContent }}
                </p>
              </template>
            </section>
            <!--  摄像头  -->
            <section class="section">
              <p class="label">
                {{ $t('摄像头') }}
              </p>
              <div class="content">
                <div class="section-item">
                  <el-select
                    ref="camera-select"
                    v-model="camera"
                    :class="[ detection.camera.available !== null && cameras.length === 0 ? 'is-error' : '']"
                    :popper-append-to-body="false"
                    :placeholder="$t(detection.camera.available !== null && cameras.length === 0 ? detection.camera.tip.nodevice : $t('请选择设备'))"
                    :no-data-text="$t(detection.camera.tip.nodevice)"
                    value-key="deviceId"
                    popper-class="device-detect-select"
                    :disabled="detection.camera.available === null"
                    @change="(arg)=>onCameraChange(arg,'','userChange')"
                  >
                    <el-option
                      v-for="item in cameras"
                      :key="item.value"
                      :label="item.deviceName"
                      :value="item"
                      icon="el-icon-circle-plus"
                    />
                  </el-select>
                  <el-tooltip
                    effect="dark"
                    :content="cameraTooltip"
                    placement="right"
                    :popper-class="detection.camera.available ? '': 'detect-error-tps'"
                  >
                    <!-- 使用插槽来支持自定义内容 -->
                    <div slot="content">
                      <div class="content">
                        <p>
                          {{ cameraTooltip }}<span
                            v-if="requestCamPermissionTip"
                            class="open-link"
                            @click="requestCamPermission"
                          >{{ $t("请前往开启") }}
                          </span>
                        </p>
                      </div>
                    </div>
                    <i
                      v-loading="detection.camera.available === null"
                      element-loading-spinner="el-icon-loading"
                      element-loading-background="transparent"
                      :class="{'success-icon': detection.camera.available, 'warning-icon': detection.camera.available === false}"
                      class="ml10 base-icon"
                    />
                  </el-tooltip>
                </div>
                <p class="sub-section pt10">
                  <el-checkbox
                    v-model="mirror"
                    :disabled="!detection.camera.available"
                    :checked="mirror"
                  >
                    {{ $t('镜像模式') }}
                  </el-checkbox>
                </p>
              </div>
            </section>
            <!--  摄像头/end  -->
            <!--  麦克风  -->
            <section
              v-if="detectDevices.includes('microphone')"
              class="section"
            >
              <p class="label">
                {{ $t('麦克风') }}
              </p>
              <div class="content">
                <div
                  class="section-item"
                >
                  <el-select
                    ref="microphone-select"
                    v-model="microphone"
                    :class="{
                      'is-error': (detection.microphone.available !== null && microphones.length === 0) || detection.microphone.volume === 0,
                      'is-warning': detection.microphone.volume <= 80 && detection.microphone.volume > 0,
                    }"
                    :popper-append-to-body="false"
                    :placeholder="$t(detection.microphone.available !== null && microphones.length === 0 ? detection.microphone.tip.nodevice : $t('请选择设备'))"
                    :no-data-text="$t(detection.microphone.tip.nodevice)"
                    value-key="deviceId"
                    popper-class="device-detect-select"
                    :disabled="detection.microphone.available === null"
                    @change="(arg)=>onMicrophoneChange(arg,'','userChange')"
                  >
                    <el-option
                      v-for="item in microphones"
                      :key="item.value"
                      :label="item.deviceName"
                      :value="item"
                    />
                  </el-select>
                  <el-tooltip
                    effect="dark"
                    :content="micTooltip"
                    placement="right"
                    :popper-class="detection.microphone.available ? '': 'detect-error-tps'"
                    :value="detection.microphone.volume <= 80"
                    :manual="detection.microphone.volume <= 80"
                  >
                    <!-- 使用插槽来支持自定义内容 -->
                    <div slot="content">
                      <div class="content">
                        <p>
                          {{ micTooltip }}<span
                            v-if="requestMicPermissionTip"
                            class="open-link"
                            @click="requestMicPermission"
                          >
                            {{ $t("请前往开启") }}
                          </span>
                        </p>
                      </div>
                    </div>
                    <i
                      v-loading="detection.microphone.available === null"
                      element-loading-spinner="el-icon-loading"
                      element-loading-background="transparent"
                      :class="{
                        'success-icon': detection.microphone.available,
                        'warning-icon': detection.microphone.available === false,
                        'warning-icon': detection.microphone.available && detection.microphone.volume === 0,
                        'hint-icon': detection.microphone.available && detection.microphone.volume > 0 && detection.microphone.volume <= 80,
                      }"
                      class="ml10 base-icon"
                    />
                  </el-tooltip>
                </div>
                <div class="sub-section">
                  <div class="volume">
                    <div class="mic-icon">
                      <IconMic />
                    </div>
                    <ul class="capacity">
                      <li
                        v-for="item in 21"
                        :key="item"
                        class="item"
                        :class="{active: item < detection.microphone.volumeLevel }"
                      />
                    </ul>
                  </div>
                  <p class="pt10 sub-text">
                    {{ $t('对着麦克风说话可以看到波动效果') }}
                  </p>
                  <div
                    class="volume volume-scrollbar"
                    @click.stop
                    @mousedown.stop
                    @drag.stop
                  >
                    <i
                      :class="[detection.microphone.volume > 0 ? 'speaker-icon' : 'speaker-icon disable']"
                      @click="disableMicVolume"
                    />
                    <div class="capacity">
                      <div class="slider-wrapper">
                        <el-slider
                          v-model="detection.microphone.volume"
                          :show-tooltip="false"
                          :format-tooltip="(item)=>{ return `${item}%`}"
                          @change="onMicrophoneVolumeChange"
                        />
                      </div>
                    </div>
                  </div>
                  <p
                    v-if="isRTCMode"
                    class="sub-section pt10"
                  >
                    <el-checkbox
                      v-model="enableAIDenoise"
                      :disabled="!isAIDenoiseSupported"
                      :checked="enableAIDenoise"
                    >
                      {{ $t(' AI 降噪') }}
                    </el-checkbox>
                  </p>
                </div>
              </div>
            </section>
            <!--  麦克风/end  -->
            <!--  扬声器  -->
            <section
              v-if="detectDevices.includes('speaker')"
              class="section"
            >
              <p class="label">
                {{ $t('扬声器') }}
              </p>
              <div class="content">
                <div class="section-item">
                  <el-select
                    ref="speaker-select"
                    v-model="speaker"
                    :class="{
                      'is-error': (detection.speaker.available !== null && speakers.length === 0) || detection.speaker.volume === 0,
                      'is-warning': detection.speaker.volume <= 80 && detection.speaker.volume > 0,
                    }"
                    :popper-append-to-body="false"
                    :placeholder="$t(detection.speaker.available !== null && speakers.length === 0 ? detection.speaker.tip.nodevice : $t('请选择设备'))"
                    :no-data-text="$t(detection.speaker.tip.nodevice)"
                    value-key="deviceId"
                    popper-class="device-detect-select"
                    :disabled="detection.speaker.available === null"
                    @change="(arg)=>onSpeakerChange(arg,'userChange')"
                  >
                    <el-option
                      v-for="item in speakers"
                      :key="item.value"
                      :label="item.deviceName"
                      :value="item"
                    />
                  </el-select>
                  <el-tooltip
                    effect="dark"
                    :content="speakerToollip"
                    placement="right"
                    :value="detection.speaker.volume <= 80"
                    :manual="detection.speaker.volume <= 80"
                  >
                    <i
                      v-loading="detection.speaker.available === null"
                      element-loading-spinner="el-icon-loading"
                      element-loading-background="transparent"
                      :class="{
                        'success-icon': detection.speaker.available,
                        'warning-icon': detection.speaker.available === false,
                        'warning-icon': detection.speaker.available && detection.speaker.volume === 0,
                        'hint-icon': detection.speaker.available && detection.speaker.volume > 0 && detection.speaker.volume <= 80,
                      }"
                      class="ml10 base-icon"
                    />
                  </el-tooltip>
                </div>
                <div class="sub-section volume-test">
                  <div
                    class="volume volume-scrollbar"
                    @click.stop
                    @mousedown.stop
                    @drag.stop
                  >
                    <i
                      :class="[detection.speaker.volume > 0 ? 'speaker-icon' : 'speaker-icon disable']"
                      @click="disableSpeakerVolume"
                    >
                      <IconSpeaker :isMute="detection.speaker.volume <= 0" />
                    </i>
                    <div class="capacity">
                      <div class="slider-wrapper">
                        <el-slider
                          v-model="detection.speaker.volume"
                          :show-tooltip="false"
                          :format-tooltip="(item)=>{ return `${item}%`}"
                          @change="onSpeakerVolumeChange"
                        />
                      </div>
                    </div>
                    <el-button
                      class="plain"
                      type="text"
                      size="mini"
                      @click="toggleAudioPlay"
                    >
                      {{ audioPlayStatus ? $t('暂停') : $t('试听') }}
                    </el-button>
                  </div>
                </div>
              </div>
            </section>
            <!--  扬声器/end  -->
            <section class="enter-section">
              <el-button
                v-loading="isDetecting"
                type="primary"
                size="mini"
                class="enter mt10"
                element-loading-spinner="el-icon-loading"
                element-loading-background="transparent"
                element-loading-text=""
                :disabled="isDetecting"
                data-user-event="DeviceDetect-enter"
                @click="enter"
              >
                {{ isDetecting ? $t('设备检测中...') : roomInfo.enterRoom }}
              </el-button>
            </section>
          </el-col>
        </el-row>
      </div>
    </div>
    <screen-capture-dialog
      ref="screen-capture"
      :title="$t('配置指引')"
      @status-update="screenCaptureStatusUpdate"
    />
  </div>
</template>

<script>

/*
*  Web 与 Electron 的检测流程有所不同
*  Electron的检测可以通过 TCIC.TTrtcEvent.Status_Update 事件通知知道流是否正常
*  Web通过接口调用是否报错保证
* */
import i18next from 'i18next';
import Constant from '@util/Constant';
import { DeviceDetectBaseComponent } from './DeviceDetectBase';
import DetectUtil from './DetectUtil';
import ScreenCaptureDialog from './ScreenCaptureDialog';
import Util from '@util/Util';
import Lodash from 'lodash';
import { IconMic, IconSpeaker } from './assets/svg-component';


export default {
  name: 'Detect',
  filters: {
    formatClassStatus: (key) => {
      const map = {
        0: i18next.t('待开始'),
        1: i18next.t('进行中'),
        2: i18next.t('已结束'),
      };
      return map[key];
    },
  },
  components: {
    ScreenCaptureDialog,
    IconMic,
    IconSpeaker,
  },
  extends: DeviceDetectBaseComponent,

  data() {
    return {
      modal: false,
      detectDevices: [],
      isElectron: false,
      suggestionMessage: null,
      classData: null,
      callback: null,
      isTeacher: false,
      isCollegeClass: false,
      isDestroyed: false,
      roomInfo: {},
      networkStatus: 1,
      isClassStarted: false,
      networkDetecting: false,
      networkTipsVisible: false,
      isRTCMode: false,
    };
  },

  computed: {
    isDetecting() {
      return !!this.detectDevices.find(item => this.detection[item].available === null);
    },
    translateTip() {
      return {
        netWorkContent: i18next.t('开始{{arg_0}}后才能进行网络检测', { arg_0: this.roomInfo.startRoom }),
      };
    },
    networkStatusText() {
      if (!this.isClassStarted) {
        return i18next.t('开始{{arg_0}}后才能进行网络检测', { arg_0: this.roomInfo.startRoom });
      }
        if (this.networkDetecting) {
          return i18next.t('网络检测中');
        }
          if (this.networkStatus < 3) {
            return i18next.t('正常');
          } if (this.networkStatus === 3) {
            return i18next.t('一般');
          } if (this.networkStatus === 4) {
            return i18next.t('较差');
          } if (this.networkStatus === 5) {
            return i18next.t('很差');
          }
            return i18next.t('当前无网络，请检查网络设置');
    },
    networkStatusTipsText() {
      if (this.networkDetecting) {
        return '';
      }
      if (this.networkStatus < 3) {
        return i18next.t('网络状况正常');
      } if (this.networkStatus === 3) {
        return i18next.t('网络状况一般');
      } if (this.networkStatus === 4) {
        return i18next.t('网络状况较差');
      } if (this.networkStatus === 5) {
        return i18next.t('网络状况很差');
      }
      return i18next.t('当前无网络，请检查网络设置');
    },
  },

  watch: {
    /*
      这些已经移到 DeviceDetectBase:
        mirror
        audioPlayStatus
    */
  },

  async mounted() {
    this.roomInfo = TCIC.SDK.instance.getRoleInfo().roomInfo;
    this.deviceDetectReportAction = 'device-detect';
    this.initDeviceData();
    // 加入课堂事件
    // this.addLifecycleTCICEventListener(TCIC.TMainEvent.After_Enter, this.onJoinClass);
    this.makeSureClassJoined(this.onJoinClass);
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start).then(() => {
      this.isClassStarted = true;
    });
    // 记录网络状态
    TCIC.SDK.instance.subscribeState(TCIC.TMainState.Network_Quality_Status, (status) => {
      this.networkStatus = status;
    }, { noEmitWhileSubscribe: true, noEmitWhileRegister: true });
  },

  methods: {
    async onJoinClass() {
      const userId = TCIC.SDK.instance.getUserId();
      const classData = TCIC.SDK.instance.getClassInfo();
      const teachersInfo = await TCIC.SDK.instance.getUserInfo(classData.teacherId);
      classData.teacherName = teachersInfo.nickname;
      this.isTeacher = userId === classData.teacherId;
      this.classData = classData;
      this.isElectron = TCIC.SDK.instance.isElectron();
      this.isCollegeClass = TCIC.SDK.instance.isCollegeClass();
      this.isRTCMode = TCIC.SDK.instance.getState(TCIC.TMainState.RTC_Mode, true);
      if (this.isRTCMode) {
        this.isAIDenoiseSupported = TCIC.SDK.instance.isAIDenoiseSupported();
        this.enableAIDenoise = this.isAIDenoiseSupported;
      }
      // 设备检测阶段静音，若已退出设备检测不再禁音
      if (!this.isDestroyed) {
        if (this.isElectron) {
          window.MTTrtc.muteAllRemoteAudio(true);
        } else {
          TCIC.SDK.instance.muteAllRemoteAudio(true);
        }
      }
    },
    async reset() {
      this.deviceDetectStatus = 'reset';
      await this.stopDetectCamera();
      await this.stopDetectMicrophone();
      await this.stopDetectMicrophoneVolume();
      await this.stopDetectSpeaker();
    },
    async stop() {
      this.callback = null;
      await this.reset();
      this.doHide();
      if (this.suggestionMessage) {
        this.suggestionMessage.close();
      }
    },
    start(action) {
      if (!action.devices || action.devices.length === 0) {
        console.error('params error', action);
        return;
      }
      if (action.callback) {
        this.callback = action.callback;
      }
      // 桌面端不进行浏览器检测 ， 浏览器端不进行屏幕分享检测
      if (TCIC.SDK.instance.isElectron()) {
        // Mac 才检测屏幕分享
        if (!TCIC.SDK.instance.isMac()) {
          action.devices = action.devices.filter(item => item !== 'screen-capture');
        }
        // Electron 不检测浏览器
        action.devices = action.devices.filter(item => item !== 'browser');
        console.debug('detect devices =>', action.devices);
      } else if (TCIC.SDK.instance.isWeb()) {
        action.devices = action.devices.filter(item => item !== 'screen-capture');
      }
      if ((TCIC.SDK.instance.isWeb() && DetectUtil.isSafari()) || TCIC.SDK.instance.isMobile() || TCIC.SDK.instance.isPad()) {
        action.devices = action.devices.filter(item => item !== 'speaker');
      }
      // 获取要检测的设备
      this.detectDevices = action.devices;
      // 初始化事件监听
      this.initDeviceListener();
      // 开始检测
      TCIC.SDK.instance.reportLog(
        'device-detect',
        `[startDetect] ${JSON.stringify(this.detectDevices)}`,
      );
      this.startDetect(action.devices);

      this.show();
      setTimeout(() => {
        this.suggestionMessage = this.$message({
          message: i18next.t('建议您佩戴好耳机，打开摄像头和麦克风，以便获得更好的{{arg_0}}体验', { arg_0: this.roomInfo.name }),
          duration: 3000,
          type: 'warning',
          showClose: false,
          offset: 48,
        });
      }, 500);
    },

    deviceStatusUpdateListener(result) {
      const { state, device } = result;
      let detection = null;
      switch (device) {
        case 'mic':
          detection = this.detection.microphone;
          break;
        case 'camera':
          detection = this.detection.camera;
          break;
          // case 'speaker':
          //   detection = this.detection.speaker;
          //   break;
        default:
          break;
      }
      if (!detection) {
        console.error('Status_Update => ', result);
        return;
      }
      switch (state) {
        case - 1001: // 打开失败
        case - 1002: // 找不到设备
        case - 1003: // 未授权
        case - 1004: // 设备被占用
        case 2: // 已关闭
          detection.available = false;
          break;
        case 1: // 已打开
          if (device === 'mic' && this.microphones.length > 0) {
            detection.available = true;
          } else if (device === 'camera' && this.cameras.length > 0) {
            detection.available = true;
          }
          break;
        default:
          break;
      }
      // 收到麦克风的通知，不处理重试逻辑。
      if (device === 'mic') {
        this.unbindRetryMicrophoneTest();
      }
    },

    show() {
      this.modal = true;
      // 隐藏导航条
      TCIC.SDK.instance.setState(Constant.TStateDeviceDetect, true);
      this.updateTbmTarget();
    },
    doHide() {
      this.modal = false;
      // 显示导航条
      TCIC.SDK.instance.setState(Constant.TStateDeviceDetect, false);
      this.hide();
      this.updateTbmTarget();
    },

    getHelp: DetectUtil.getHelp,

    startDetect(objects) {
      objects.forEach((item) => {
        setTimeout(() => {
          this.detect(item, 'start');
        }, 0);
      });
    },

    async detect(object, reason) {
      switch (object) {
        case 'screen-capture':
          this.detectScreenCapture();
          break;
        case 'browser':
          this.detectBrowser();
          break;
        case 'camera':
          await this.enumerateCamera(true, reason || 'call detect');
          break;
        case 'microphone':
          await this.enumerateMicrophone(true, reason || 'call detect');
          break;
        case 'speaker':
          await this.enumerateSpeaker(true, reason || 'call detect');
          break;
      }
    },

    /**
     * detectXXX 都移到 DeviceDetectBase 了
     */

    /**
     * 检测摄像头
     */
    async enumerateCamera(init = false, reason) {
      const { camera } = this.detection;
      TCIC.SDK.instance.reportLog(
        'device-detect',
        `[enumerateCamera] init: ${init}, reason: ${reason}, enumerating: ${camera.enumerating}`,
      );
      if (camera.enumerating) {
        return;
      }
      camera.enumerating = true;
      try {
        this.cameras = await DetectUtil.getCameras();
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[enumerateCamera] [succ] length: ${this.cameras?.length}, ${JSON.stringify(this.cameras)}, this.camera ${this.camera?.deviceId}`,
        );
      } catch (err) {
        // 获取默认设备不阻塞其他流程
        this.cameras = [];
        console.error(err);
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[enumerateCamera] [error] name: ${err.name}, message: ${err.message}`,
        );
      }
      camera.enumerating = false;
      if (Util.isArray(this.cameras) && this.cameras.length) {
        if (!this.camera) {
          try {
            const deviceId = await DetectUtil.getCameraDeviceId();
            // const lastDeviceId = localStorage.getItem('cameraId');
            const lastDeviceId = '';
            const lastDevice = this.cameras?.find(item => item.deviceId === lastDeviceId);
            if (lastDevice) {
              TCIC.SDK.instance.reportLog(
                'device-detect',
                `[enumerateCamera] use camera from localStorage, deviceId ${lastDevice.deviceId}, deviceName ${lastDevice.deviceName}`,
              );
              this.camera = lastDevice;
            } else {
              const device = this.cameras?.find(item => item.deviceId === deviceId);
              TCIC.SDK.instance.reportLog(
                'device-detect',
                `[enumerateCamera] use camera, deviceId ${device?.deviceId}, deviceName ${device?.deviceName}`,
              );
              this.camera = device;
            }
            if (init || this.camera.deviceId !== deviceId) {
              this.onCameraChange(this.camera, init, `enumerateCamera success and device change, ${deviceId} -> ${this.camera?.deviceId}`);
            } else if (!init && this.$refs['camera-select']) {
              this.$refs['camera-select'].$emit('change');
            }
          } catch (e) {
            const newDevice = this.cameras[0];
            console.log(`::device-detect, trtcCameraId error ${e.name} ${e.message}, use camera ${newDevice.deviceId} ${newDevice.deviceName}`);
            this.camera = newDevice;
            if (!init && this.$refs['camera-select']) {
              this.$refs['camera-select'].$emit('change');
            }
          }
        }
      } else {
        if (this.camera) {
          this.stopDetectCamera();
          this.camera = null;
        }
        camera.available = false;
      }
    },
    uniqueByDeviceName(arr) {
      const uniqueMap = new Map();
      arr.forEach((item) => {
          if (!uniqueMap.has(item.deviceName)) {
            const keys = Array.from(uniqueMap.keys());
            if (keys.length === 0) {
              uniqueMap.set(item.deviceName, item);
            } else {
              const likeItem = keys.find(k => k.indexOf(item.deviceName) > -1);
              const likeItemReverse = keys.find(k => item.deviceName.indexOf(k) > -1);
              if (!likeItem) {
                uniqueMap.set(item.deviceName, item);
              }
              if (likeItemReverse) {
                uniqueMap.delete(likeItemReverse.key);
                uniqueMap.set(item.deviceName, item);
              }
            }
          }
      });
      return Array.from(uniqueMap.values());
    },
    /**
     * 检测麦克风
     */
    async enumerateMicrophone(init = false, reason) {
      const { microphone } = this.detection;
      TCIC.SDK.instance.reportLog(
        'device-detect',
        `[enumerateMicrophone] init: ${init}, reason: ${reason}, enumerating: ${microphone.enumerating}`,
      );
      if (microphone.enumerating) {
        return;
      }
      microphone.enumerating = true;
      try {
        this.microphones = await DetectUtil.getMicrophones();
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[enumerateMicrophone] [succ] length: ${this.microphones?.length}, ${JSON.stringify(this.microphones)}, this.microphone ${this.microphone?.deviceId}`,
        );
      } catch (err) {
        // 获取默认设备不阻塞其他流程
        this.microphones = [];
        console.error(err);
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[enumerateMicrophone] [error] name: ${err.name}, message: ${err.message}`,
        );
      }
      microphone.enumerating = false;
      if (Util.isArray(this.microphones) && this.microphones.length) {
        if (!this.microphone) {
          try {
            const deviceId = await DetectUtil.getMicDeviceId();
            // const lastDeviceId = localStorage.getItem('micId');
            const lastDeviceId = '';
            const lastDevice = this.microphones?.find(item => item.deviceId === lastDeviceId);
            if (lastDevice) {
              TCIC.SDK.instance.reportLog(
                'device-detect',
                `[enumerateMicrophone] use microphone from localStorage, deviceId ${lastDevice.deviceId}, deviceName ${lastDevice.deviceName}`,
              );
              this.microphone = lastDevice;
            } else {
              const device = this.microphones?.find(item => item.deviceId === deviceId) || this.microphones?.[0];
              TCIC.SDK.instance.reportLog(
                'device-detect',
                `[enumerateMicrophone] use microphone, deviceId ${device?.deviceId}, deviceName ${device?.deviceName}getMicDeviceId: ${deviceId}`,
              );
              this.microphone = device;
              DetectUtil.switchMic(device.deviceId);
            }
            if (init || this.microphone.deviceId !== deviceId) {
              this.onMicrophoneChange(this.microphone, init, `enumerateMicrophone success and device change, ${deviceId} -> ${this.microphone?.deviceId}`);
            } else if (!init && this.$refs['microphone-select']) {
              this.$refs['microphone-select'].$emit('change');
            }
          } catch (e) {
            const newDevice = this.microphones[0];
            console.log(`::device-detect, trtcMicId error ${e.name} ${e.message}, use microphone ${newDevice.deviceId} ${newDevice.deviceName}`);
            this.microphone = newDevice;
            DetectUtil.switchMic(newDevice.deviceId);
            if (!init && this.$refs['microphone-select']) {
              this.$refs['microphone-select'].$emit('change');
            }
          }
        }
      } else {
        if (this.microphone) {
          this.stopDetectMicrophone();
          this.microphone = null;
        }
        microphone.available = false;
      }
    },

    /**
     * 检测扬声器
     */
    async enumerateSpeaker(init = false, reason) {
      const { speaker } = this.detection;
      TCIC.SDK.instance.reportLog(
        'device-detect',
        `[enumerateSpeaker] init: ${init}, reason: ${reason}, enumerating: ${speaker.enumerating}`,
      );
      if (speaker.enumerating) {
        return;
      }
      speaker.enumerating = true;
      speaker.available = null;
      let error;
      try {
        this.speakers = await DetectUtil.getSpeakers();
        speaker.enumerating = false;
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[enumerateSpeaker] [succ] length: ${this.speakers?.length}, ${JSON.stringify(this.speakers)}, this.speaker ${this.speaker?.deviceId}`,
        );
      } catch (err) {
        this.speakers = [];
        error = err;
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[enumerateSpeaker] [error] name: ${error.name}, message: ${error.message}`,
        );
      }
      speaker.enumerating = false;
      if (Util.isArray(this.speakers) && this.speakers.length) {
        if (!this.speaker) {
          DetectUtil.getSpeakerDeviceId()
            .then((deviceId) => {
              // const lastDeviceId = localStorage.getItem('speakerId');
              const lastDeviceId = '';
              const lastDevice = this.speakers.find(item => item.deviceId === lastDeviceId);
              if (lastDevice) {
                TCIC.SDK.instance.reportLog(
                  'device-detect',
                  `[enumerateSpeaker] use speaker from localStorage, deviceId ${lastDevice.deviceId}, deviceName ${lastDevice.deviceName}`,
                );
                this.speaker = lastDevice;
              } else {
                const device = this.speakers?.find(item => item.deviceId === deviceId) || this.speakers[0];
                TCIC.SDK.instance.reportLog(
                  'device-detect',
                  `[enumerateSpeaker] use speaker, deviceId ${device.deviceId}, deviceName ${device.deviceName} getSpeakerDeviceId ${deviceId}`,
                );
                this.speaker = device;
              }
              if (init || this.speaker.deviceId !== deviceId) {
                this.onSpeakerChange(this.speaker, `enumerateSpeaker success and device change, ${deviceId} -> ${this.speaker?.deviceId}`);
              } else if (!init && this.$refs['speaker-select']) {
                this.$refs['speaker-select'].$emit('change');
              }
            })
            .catch((e) => {
              const newDevice = this.speakers[0];
              console.log(`::device-detect, trtcSpeakerId error ${e.name} ${e.message}, use speaker ${newDevice.deviceId} ${newDevice.deviceName}`);
              this.speaker = newDevice;
              if (!init && this.$refs['speaker-select']) {
                this.$refs['speaker-select'].$emit('change');
              }
            });
        }
        // 怎么直接就 true 了？
        speaker.available = true;
        this.result.speaker = {
          available: true,
          reason: null,
        };
        this.detectSpeakerVolume();
      } else {
        if (this.speaker) {
          this.stopDetectSpeaker();
          this.speaker = null;
        }
        speaker.available = false;
        this.result.speaker = {
          available: false,
          reason: error ? (`${error.name || ''} : ${error.message || ''}` || error) : null,
        };
      }
    },

    /**
     * 进入课堂
     */
    enter() {
      if (this.isDetecting) {
        return;
      }
      if (this.callback) {
        this.callback(this.result);
      }
      // TCIC.SDK.instance.notify('device-detect-completed', {});    // 设备检测完成

      // 只判断要检查的设备
      const detectRes = {};
      let isDeviceUnAvailable = false;
      this.detectDevices.forEach((deviceType) => {
        const data = this.detection[deviceType];
        detectRes[deviceType] = { available: data.available };
        // console.log('device-detect', deviceType, data);
        if (!data.available) {
          console.warn('device-detect', deviceType, 'not available');
          isDeviceUnAvailable = true;
        }
        if (deviceType === 'microphone' || deviceType === 'speaker') {
          detectRes[deviceType].volume = data.volume;
          if (data.volume === 0 || (data.volume > 0 && data.volume <= 80)) {
            console.warn('device-detect', deviceType, 'volume 0');
            isDeviceUnAvailable = true;
          }
        }
      });
      TCIC.SDK.instance.reportLog('device-detect', `[enter] showWarning ${isDeviceUnAvailable}, ${JSON.stringify(detectRes)}`);
      if (isDeviceUnAvailable) {
        // 如果有任意一个设备不可用，弹窗提示
        TCIC.SDK.instance.showMessageBox(
          i18next.t('注意'),
          i18next.t('您的设备检测异常，是否仍然进入课堂'),
          [
            i18next.t('确认'),
            i18next.t('取消'),
          ],
          (index) => {
            if (index === 0) {
              // 如果选择确认，依旧执行进入课堂的后续动作
              this.doEnter();
            }
          },
        );
      } else {
        // 如果所有设备正常，直接进入课堂
        this.doEnter();
      }
    },

    async doEnter() {
      if (this.detectDevices.includes('camera')) {
        this.$EventBus.$emit('camera-available', { deviceId: this.camera?.deviceId, available: this.detection.camera.available });
      }
      if (this.detectDevices.includes('microphone')) {
        this.$EventBus.$emit('mic-available', { deviceId: this.microphone?.deviceId, available: this.detection.microphone.available });
        this.$EventBus.$emit('update:micVolume', this.detection.microphone.volume);
      }
      if (this.detectDevices.includes('speaker')) {
        this.$EventBus.$emit('speaker-available', { deviceId: this.speaker?.deviceId, available: this.detection.speaker.available });
        this.$EventBus.$emit('update:speakerVolume', this.detection.speaker.volume);
      }
      await this.stop();
      this.isDestroyed = true;
      // 设备检测完成后取消静音
      if (this.isElectron) {
        window.MTTrtc.muteAllRemoteAudio(false);
        setTimeout(() => {
          TCIC.SDK.instance.muteTicPusherUser();
        }, 300);
      } else {
        TCIC.SDK.instance.muteAllRemoteAudio(false);
      }
      this.$destroy();
      TCIC.SDK.instance.reportLog('device-detect', '[doEnter] handle_enter_class');
      const enterTime = new Date().getTime();
      TCIC.SDK.instance.reportEvent('handle_enter_class', {}, 0, enterTime);
      window.tbm.removeTarget('deviceDetect');
      this.$EventBus.$emit('do-enter-class', true);
    },

    /*
    * 设备新增
    * 如果当前没有此类型设备：
    *   1.设备枚举，设备检测
    * 如果不是当前设备：
    *   2.枚举设备
    * */
    async deviceAddHandler({ deviceId, type }) {
      setTimeout(async () => {
        const {
          Mic,
          Speaker,
          Camera,
        } = TCIC.TTrtcDeviceType;
        switch (type) {
          case Mic:
            console.log('::device-detect, deviceChange | deviceAddHandler | mic => ', deviceId);
            this.microphone = null;
            await this.enumerateMicrophone(false, `add device ${deviceId}`);
            break;
          case Camera:
            console.log('::device-detect, deviceChange | deviceAddHandler | camera => ', deviceId);
            this.camera = null;
            await this.enumerateCamera(false, `add device ${deviceId}`);
            break;
          case Speaker:
            console.log('::device-detect, deviceChange | deviceAddHandler | speaker => ', deviceId);
            this.speaker = null;
            // 如果当前有设备，只重新枚举设备
            await this.enumerateSpeaker(false, `add device ${deviceId}`);
            break;
        }
      }, 2000); // 2s之后，获取设别列表的接口的default设备才更新。先这么解决
    },
    /*
    * 设备移除
    * 如果是当前设备：
    *   1.先复位当前选中设备 2.重新设备枚举，设备检测
    * 如果不是当前设备：
    *   2.枚举设备
    * */
    async deviceRemoveHandler({ deviceId, type }) {
      // const { camera, microphone, speaker } = this.detection;
      const {
        Mic,
        Speaker,
        Camera,
      } = TCIC.TTrtcDeviceType;
      switch (type) {
        case Mic:
          console.log('::device-detect, deviceChange | deviceRemoveHandler | mic => ', deviceId);
          if (this.microphone?.deviceId === deviceId) {
            console.warn(`::device-detect, remove current microphone ${this.microphone.deviceId} ${this.microphone.deviceName}`);
            this.stopDetectMicrophone();
          }
          this.microphone = null;
          await this.enumerateMicrophone(false, `remove device ${deviceId}`);
          break;
        case Camera:
          console.log('::device-detect, deviceChange | deviceRemoveHandler | camera => ', deviceId);
          if (this.camera?.deviceId === deviceId) {
            console.warn(`::device-detect, remove current camera ${this.camera.deviceId} ${this.camera.deviceName}`);
            this.stopDetectCamera();
          }
          this.camera = null;
          await this.enumerateCamera(false, `remove device ${deviceId}`);
          break;
        case Speaker:
          console.log('::device-detect, deviceChange | deviceRemoveHandler | speaker => ', deviceId);
          if (this.speaker?.deviceId === deviceId) {
            console.warn(`::device-detect, remove current speaker ${this.speaker.deviceId} ${this.speaker.deviceName}`);
            this.stopDetectSpeaker();
          }
          if (this.audioPlayStatus) {
            this.toggleAudioPlay();
          }
          this.speaker = null;
          await this.enumerateSpeaker(false, `remove device ${deviceId}`);
          break;
      }
    },
    /*
    * 设备可用
    * 如果是当前设备：
    *   1.设备检测
    * 如果不是当前设备：
    *   do nothing
    * */
    async deviceActiveHandler({ deviceId, type }) {
      const {
        Mic,
        Speaker,
        Camera,
      } = TCIC.TTrtcDeviceType;
      switch (type) {
        case Mic:
          console.log('::device-detect, deviceChange | deviceActiveHandler | mic => ', deviceId);
          // 如果当前设备切换成可用，重新检测
          if (this.microphone.deviceId === deviceId) {
            await this.detect('microphone', `active device ${deviceId}`);
          }
          break;
        case Camera:
          console.log('::device-detect, deviceChange | deviceActiveHandler | camera => ', deviceId);
          if (this.camera.deviceId === deviceId) {
            await this.detect('camera', `active device ${deviceId}`);
          }
          break;
        case Speaker:
          console.log('::device-detect, deviceChange | deviceActiveHandler | speaker => ', deviceId);
          if (this.speaker.deviceId === deviceId) {
            await this.detect('speaker', `active device ${deviceId}`);
          }
          break;
      }
    },

    showScreenCaptureGuide() {
      this.$refs['screen-capture'].show();
    },
    screenCaptureStatusUpdate() {
      this.detect('screen-capture');
      this.$refs['screen-capture'].hide();
    },
    getSectionButtons(dom) {
      return Array.from(dom.querySelectorAll('i.base-icon'))
        .filter(item => this.checkValid(item));
    },
    checkValid(dom) {   // 检测按钮是否可用
      const validClassArr = ['arrows-left-abled', 'arrows-right-abled', 'icon-sub-abled', 'icon-add-abled'];
      if (dom) {
        if (dom.style.display === 'none') return false;     // 不可见时直接返回不可用
        const domClasses = Array.from(dom.classList);
        for (let i = 0; i < domClasses.length; i ++) {
          if (validClassArr.includes(domClasses[i])) return true;
        }
      }
      return false;
    },
    updateTbmTarget: Lodash.throttle(function () {
      // 注册按键事件
      this.$nextTick(() => {
        if (!this.modal) {     // 不显示时重置
          window.tbm.removeTarget('deviceDetect');
          return ;
        };
        const subTargets = ['camera', 'cameraSelect', 'cameraMirror', 'mic', 'micSelect', 'micVolume', 'speaker', 'speakerVolumes', 'test', 'join'];
        const sections = Array.from(this.$el.querySelectorAll('div.section-item'));
        const count = Math.min(subTargets.length, sections.length);
        window.tbm.pushTarget('deviceDetect', subTargets);
        for (let i = 0; i < count; i ++) {
          window.tbm.updateTarget(
            'deviceDetect',
            this.getSectionButtons(sections[i])
              .map(item => window.tbm.generateNode(item)),
            subTargets[i],
          );
        }
        window.tbm.updateTarget(
          'deviceDetect',
          Array.from(this.$el.querySelectorAll('button.audition-button'))
            .map(item => window.tbm.generateNode(item)),
          'button',
        );
        // 优先选中进入课堂
        window.tbm.updateTarget('deviceDetect', [
          window.tbm.generateNode(this.$el.querySelector('button.el-button.enter')),      // 进入课堂
        ], 'join');
        window.tbm.updateTarget('deviceDetect', [
          window.tbm.generateNode(this.$el.querySelector('button.el-button.plain')),      // 试听
        ], 'test');
        window.tbm.lockTarget('deviceDetect');
      });
    }, 500, {
      leading: false,
      trailing: true,
    }),
    // 重新检测网络
    reDetectNetwork() {
      this.networkStatusText = i18next.t('检测中');
      this.networkDetecting = true;
      setTimeout(() => {
        TCIC.SDK.instance.checkNetworkConnection();
        this.networkDetecting = false;
        // this.networkStatus = 6;
        // 出错时候强制显示下tips
        // if (this.networkStatus >= 4) {
        //   this.networkTipsVisible = true;
        // }
      }, 3000);
    },
  },
};
</script>

<style lang="less">
.el-popup-parent--hidden {
  padding-right: 0 !important;
}

.v-modal {
  outline: 0;
}

.el-message {
  -webkit-app-region: no-drag;
}

.network-error-tps.el-tooltip__popper.is-dark {
  background-color: #FFE1E1;
  color: #8F2B3D;
}

.detect-error-tps.el-tooltip__popper.is-dark {
  background-color: #FFE1E1;
  color: #8F2B3D;
}

.detect-error-tps .open-link {

}

.device-detect-wrapper {
  @primary-color: #006EFF;
  @main-text-color: var(--text-color, #fff);
  @sub-text-color: #999;
  @error-text-color: #D95A53;
  @warning-text-color: #FF7200;

  .el-divider {
    background: rgb(99, 99, 99);

    &.el-divider--horizontal {
      margin: 20px 0 0 0;
    }
  }

  .link-primary {
    color: @primary-color;

    &:hover {
      opacity: .8;
    }
    .question-icon {
      margin-left: 2px;
    }
  }

  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: @main-text-color;
    font-size: 16px;
    position: relative;
    top: 1px;
  }

  .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background: @primary-color;
    border-color: @primary-color;
  }

  .main-text {
    font-weight: 400;
    font-size: 14px;
    line-height: 38px;
    color: @main-text-color;
  }

  .sub-text {
    color: @sub-text-color;
  }

  .error-text {
    color: @error-text-color;
  }

  button.plain {
    background: transparent;
    color: @main-text-color;
    padding: 7px;

    &:hover {
      opacity: .8;
    }

    &:active, &:hover, &:focus {
      background: transparent;
      border-color: @main-text-color;
      color: @main-text-color;
    }
  }

  .detect-capture-guide {
    .el-dialog__body {
      padding: 10px 20px;
    }
  }

  .detect-enter {
    margin-top: 0;

    .setting {
      // display: flex;
      // align-items: center;
      width: 448px;
      position: relative;
      top: 8px;
      left: 48px;
      z-index: 2;
      // padding: 30px 15px 20px;
      padding: 16px 20px;
      transform-origin: left top;
      background: var(--ui-box-background, #1D2233);
      border-radius: 8px;
      .is-error {
        border: @error-text-color 1px solid;
      }
      .is-warning {
        border: @warning-text-color 1px solid;
      }
    }

    @media screen and (max-height: 800px) {
      .setting {
        transform: scale(0.9)
      }
    }
    @media screen and (max-height: 700px) {
      .setting {
        transform: scale(0.8)
      }
    }
    @media screen and (max-height: 600px) {
      .setting {
        transform: scale(0.7)
      }
    }
    @media screen and (max-height: 500px) {
      .setting {
        transform: scale(0.6)
      }
    }
    @media screen and (max-height: 400px) {
      .setting {
        transform: scale(0.5)
      }
    }

    .detect-preview__video {
      position: absolute;
      height: auto !important;
      width: 100%;
      min-height: 100%;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      z-index: 0;
      overflow: hidden;
      background: var(--video-bg-color, #000) no-repeat center center / 160px 160px url('./assets/camera.png');
    }

    .base-icon {
      min-width: 16px;
      height: 30px;
      display: inline-block;
      vertical-align: middle;
      position: relative;
    }

    .success-icon {
      background: no-repeat center center / 100% auto url('./assets/icon-success-new.svg');
    }

    .warning-icon {
      background: no-repeat center center / 100% auto url('./assets/icon-warning-new.svg');
    }

    .hint-icon {
      background: no-repeat center center / 100% auto url('./assets/icon-hint-new.svg');
    }

    .mute-icon {
      background: no-repeat center center / 100% auto url('./assets/mute.svg');
    }

    .question-icon {
      min-width: 14px;
      height: 14px;
      display: inline-block;
      vertical-align: top;
      background: no-repeat center center / 100% auto url('./assets/icon-question.png');
      vertical-align: middle;
    }

    .volume {
      display: flex;
      flex-wrap: nowrap;
      padding-top: 13px;
      height: 29px;

      &.disabled {
        opacity: .4;
      }

      .mic-icon, .speaker-icon {
        width: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .mic-volume-value {
        width: 35px;
        line-height: 16px;
        font-weight: bolder;
        color: @main-text-color;
        text-align: center;
      }

      .capacity {
        position: relative;
        flex-grow: 1;
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        padding: 0 4px 0 13px;
        margin-right: 37px;

        .slider-wrapper {
          display: block;
          width: 100%;
          height: 100%;
        }

        .item {
          width: 4px;
          height: 100%;
          border-radius: 2px;
          background: #D8D8D8;
          justify-content: space-between;

          &.active {
            background: @primary-color;
          }
        }
      }
    }

    .section {
      display: flex;
      padding-bottom: 24px;
      color: #515a6e;

      &.header {
        display: flex;
        align-items: center;
        padding-bottom: 16px;
        border-bottom: 1px solid rgba(238, 238, 238, 0.2);
        .main-text {
          line-height: 22px;
        }
      }

      &:nth-child(2) {
        padding: 16px 0;
      }

      .label {
        font-size: 14px;
        line-height: 38px;
        color: @main-text-color;
        width: 112px;
        flex-shrink: 0;
      }

      .section-item {
        display: flex;
        align-items: center;
        .el-select {
          background: rgba(255, 255, 255, 0.08);
          .el-input {
            border: none;
            line-height: 38px;
            width: 266px;
            border-radius: 4px;
          }
        }
        .el-tooltip {
          min-width: 16px;
          margin-left: 13px;
        }
      }

      .volume-test {
        .volume-scrollbar {
          width: 266px;
          align-items: center;
          .speaker-icon {
            height: 16px;
          }
          .capacity {
            margin-right: 12px;
          }
          .el-button {
            padding: 0;
            color: @primary-color;
            &:hover {
              border-color: transparent;
            }
          }
        }
      }

      .network-btn {
        color: #006eff !important;
        font-size: 14px;
      }
    }

    .enter-section {
      text-align: center;
      padding-top: 18px;
      padding-bottom: 16px;
      position: relative;
      z-index: 0;
      .enter {
        position: relative;
        width: 100%;
        height: 36px;
        font-size: 14px;
      }
    }
  }

  .el-loading-spinner {
    margin-top: -10px;
  }
}


@import "./theme/college-web.less";
@import "./theme/college-electron.less";

</style>
