<template>
  <div :class="clientPC ? 'introduction-discuss-component client-black' : 'introduction-discuss-component'">
    <NoticeLiveAppDialog />
    <div
      v-show="hasJoinedClass && !onlyDiscuss"
      class="curriculum-nav"
    >
      <el-tabs
        v-model="activeName"
        :stretch="true"
        @tab-click="handleClick"
      >
        <el-tab-pane
          name="introduction"
        >
          <div
            slot="label"
            class="tab-label"
          >
            <div
              v-if="isSmallScreen"
              :class="clientPC ? 'tab-introduction' : 'tab-introduction mobile'"
            />
            <el-tooltip
              v-else
              :hide-after="3000"
              :content="clientPC ? $t('直播简介') : ''"
            >
              <div :class="clientPC ? 'tab-introduction' : 'tab-introduction mobile'" />
            </el-tooltip>
          </div>
        </el-tab-pane>
        <el-tab-pane
          v-if="showCourse"
          name="courseware"
        >
          <div
            slot="label"
            class="tab-label"
          >
            <el-tooltip
              :hide-after="3000"
              :content="roomInfo.courseware"
            >
              <div class="tab-course mobile" />
            </el-tooltip>
          </div>
        </el-tab-pane>
        <el-tab-pane
          name="discuss"
        >
          <div
            slot="label"
            class="tab-label"
          >
            <div
              v-if="isSmallScreen"
              :class="clientPC? 'tab-discuss' : 'tab-discuss mobile'"
            />
            <el-tooltip
              v-else
              :hide-after="3000"
              :content="$t('讨论区')"
            >
              <div :class="clientPC? 'tab-discuss' : 'tab-discuss mobile'" />
            </el-tooltip>
          </div>
        </el-tab-pane>
        <el-tab-pane
          v-if="clientPC && (isTeacher || isAssistant || isSupervisor) && !isRecordMode"
          name="member"
        >
          <div
            slot="label"
            class="tab-label"
          >
            <el-tooltip
              :hide-after="3000"
              :content="totalMemberInfo"
            >
              <div :class="clientPC? 'tab-member-list' : 'tab-member-list mobile'" />
            </el-tooltip>
            <span>({{ onlineNumber }})</span>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <Introduction v-show="activeName === 'introduction'" />
    <NoticeCollapse
      v-show="activeName === 'discuss'"
      :is-expand-defult.sync="isNoticeExpand"
    />
    <Discuss v-show="activeName === 'discuss'" />
    <Member v-if="activeName === 'member'" />
    <div
      v-show="activeName === 'courseware'"
      ref="course"
      class="course"
    >
      <el-button
        class="course-button"
        circle
        @click="setDeviceOrientation"
      >
        <i class="icon" />
      </el-button>
    </div>
  </div>
</template>

<script>
import i18next from 'i18next';
import BaseComponent from '@/component/core/BaseComponent';
import Introduction from './Introduction.vue'; // 简介区
import Discuss from './Discuss.vue'; // 讨论区
import Member from './Member.vue'; // 成员
import Constant from '@/util/Constant';
import NoticeLiveAppDialog from '../notice-component/NoticeLiveAppDialog.vue';
import NoticeCollapse from '../notice-component/NoticeCollapse.vue';

export default {
  name: 'IntroductionDiscussComponent',
  components: {
    Introduction, // 简介区
    Discuss, // 讨论区
    Member, // 成员
    NoticeCollapse, // 公告栏
    NoticeLiveAppDialog, // 公告弹窗
  },
  extends: BaseComponent,
  data() {
    return {
      activeName: '',
      input: '',
      clientPC: true, // 是否为pc客户端
      isTeacher: false,
      isAssistant: false,
      totalMemberInfo: '',
      totalMemberCount: 0,
      offlineMemberCount: 0,
      bFirstLoad: true,
      isSupervisor: false,
      isRecordMode: false,
      isNoticeExpand: false,
      enableStage: false, // 是否允许上台
      classLayout: '', // 布局
      isPortrait: false, // 是否竖屏
      hasJoinedClass: false, // 是否进入课堂
      isClassStarted: false, // 是否开始上课
      showCourse: false, // 是否显示课件tab
      onlineNumber: 0, // 在线人数
      roomInfo: null,
      roleInfo: null,
    };
  },
  computed: {
    // 是否只有消息tab
    onlyDiscuss() {
      return this.classLayout === TCIC.TClassLayout.Three || this.classLayout === TCIC.TClassLayout.VideoIM;
    },
  },
  watch: {
    activeName(newVal) {
      if (newVal ===  'member') {
        TCIC.SDK.instance.setState(Constant.TStateHeaderMemberActive, true);
      }
    },
  },
  mounted() {
    const { roleInfo, roomInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    this.roomInfo = roomInfo;
    this.addLifecycleTCICStateListener(Constant.TStateRecordMode, (state) => {
      // 主要测试使用
      this.isRecordMode = state;
    });
    this.makeSureClassJoined(() => {
      this.hasJoinedClass = true;
      this.isSupervisor = TCIC.SDK.instance.isSupervisor();
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      this.isRecordMode = TCIC.SDK.instance.getState(Constant.TStateRecordMode, false);
      this.clientPC = !(TCIC.SDK.instance.isMobile() && !TCIC.SDK.instance.isPad());
      this.classLayout = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Layout);
      this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Layout, (layout) => {
        this.classLayout = layout;
	      this.updateShowCourse();
        if (this.onlyDiscuss) {
          this.activeName = 'discuss';
        }
      });

      this.isClassStarted = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status) === TCIC.TClassStatus.Already_Start;
      if (!this.isClassStarted) {
        TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start).then(() => {
          this.isClassStarted = true;
          this.updateShowCourse();
        });
      }

      this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
        this.isPortrait = (orientation === TCIC.TDeviceOrientation.Portrait);
        this.updateShowCourse();
        if (this.activeName === 'courseware') {
          this.updateCourseTab();
        }
      });

      if (this.totalMemberCount === 0 && (this.isTeacher || this.isSupervisor || this.isAssistant) && this.bFirstLoad) {
        this.bFirstLoad = false;
        this.getFirstMemberListFilter();
      }
      // 上台相关的状态监听
      this.addLifecycleTCICStateListener(TCIC.TMainState.Enable_Stage, (enable) => {
        console.log(`===>>> Introduction:Intro : Enable_Stage : ${enable}, ${this.enableStage}`);
        if (!this.isTeacher) {
          if (this.enableStage && !enable) {
            // 已开启情况下关闭了
            window.showToast(i18next.t('{{arg_0}}已关闭上台模式', { arg_0: this.roleInfo.teacher }));
          }
          if (!this.enableStage && enable) {
            if (TCIC.SDK.instance.isSupervisor()) {
              // 巡课不提示申请上台
              window.showToast(i18next.t('{{arg_0}}已开启上台模式', { arg_0: this.roleInfo.teacher }));
            } else if (TCIC.SDK.instance.getPermission(TCIC.SDK.instance.getUserId())?.stage) {
              // 已在台上则不需要提示
            } else {
              // 学生提示
              window.showToast(i18next.t('{{arg_0}}已开启上台模式，你可以申请上台', { arg_0: this.roleInfo.teacher }));
            }
          }
        }
        this.enableStage = enable;
        // if (!this.enableStage) {
        //   TCIC.SDK.instance.setCommandStatus(TCIC.TCommandID.Stage, TCIC.TMainState.Ask_Stage_Status, TCIC.TCommandStatus.None);
        // }
      });
      this.enableStage = TCIC.SDK.instance.getState(TCIC.TMainState.Enable_Stage, false);
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Member_Count, (onlineNumber) => {
      this.onlineNumber = onlineNumber;
      this.totalMemberInfo = i18next.t('成员({{arg_0}})', { arg_0: onlineNumber });
    });

    TCIC.SDK.instance.registerState(Constant.TStateHeaderMemberActive, '菜单栏的成员列表是否处于激活态', false);
  },
  methods: {
    onBoundingClientRectChange(rect) {
      if (!this.hasJoinedClass) {
        return;
      }
      if (this.activeName === '' || this.activeName === '0') {
        this.activeName = this.onlyDiscuss ? 'discuss' : 'introduction';
      }
    },
    getFirstMemberListFilter() {
      // 构建查询参数
      const filter = {
        page: 1,
        limit: 1,
        type: TCIC.TMemberType.All,
        keyword: '',
      };
      TCIC.SDK.instance.updateMemberListFilter(filter);
    },
    handleClick(tab, event) {
      if (this.activeName === 'courseware') {
        this.updateCourseTab();
      }
    },
    setDeviceOrientation() {
      const classLayout = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Layout, TCIC.TClassLayout.Right);
      if (classLayout === TCIC.TClassLayout.VideoDoc) {
        // 公开课布局为 手机文档+视频，横屏时最大化显示白板
        setTimeout(() => {
          TCIC.SDK.instance.setState(Constant.TStateBigVideoMode, false);
        }, 300);
        TCIC.SDK.instance.setDeviceOrientation(TCIC.TDeviceOrientation.Landscape);
      }
    },
    updateShowCourse() {
      const lastShowCourse = this.showCourse;
      const isUnitedLiveClass = TCIC.SDK.instance.isUnitedLiveClass();
      this.showCourse = this.classLayout === TCIC.TClassLayout.VideoDoc && this.isSmallScreen && this.isPortrait && this.isClassStarted && !isUnitedLiveClass;
      if (lastShowCourse !== this.showCourse) {
        this.updateCourseTab();
      }
    },
    updateCourseTab() {
      const boardDom = TCIC.SDK.instance.getComponent('board-component');
      const screenPlayerDom = TCIC.SDK.instance.getComponent('screen-player-component');
      if (!boardDom || !screenPlayerDom || this.classLayout !== TCIC.TClassLayout.VideoDoc) {
        return;
      }
      if (this.showCourse) {
        this.$nextTick(() => {
          if (!this.$refs.course) {
            return;
          }
          if (boardDom.parentNode !== this.$refs.course) {
            this.$refs.course.appendChild(boardDom);
          }
          const boardLayout = { top: 0, left: 0, width: '100%', height: '100%',  display: 'block', zIndex: 1, transform: 'scale(1)', transformOrigin: 'center' };
          TCIC.SDK.instance.updateComponent('board-component', boardLayout).then();

          if (screenPlayerDom.parentNode !== this.$refs.course) {
            this.$refs.course.appendChild(screenPlayerDom);
          }
          const screenPlayerLayout = { top: 0, left: 0, width: '100%', height: '100%', zIndex: 2 };
          TCIC.SDK.instance.updateComponent('screen-player-component', screenPlayerLayout).then();
        });
      } else {
        if (boardDom.parentNode) {
          boardDom.parentNode.removeChild(boardDom);
        }
        if (screenPlayerDom.parentNode && screenPlayerDom.parentNode.contains(boardDom)) {
          screenPlayerDom.parentNode.removeChild(boardDom);
        }
      }
    },
  },
};
</script>
<style lang="less">
@import 'IntroductionDiscussPC.less';
@import 'IntroductionDiscussMobile.less';

.introduction-discuss-component {
  .notice-panel {
    position: absolute;
    background-color: #0f0;
    width: 100%;
    top: 48px;
    height: 360px;
    z-index: 2;
    opacity: 0.7;
    max-height: 440px;
    overflow-y:auto;
  }

  .course {
    width: 100%;
    padding-bottom: 28.125%;
    padding-top: 28.125%;
    position: relative;
    .course-button {
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      bottom: 9px;
      right: 16px;
      width: 40px;
      height: 40px;
      background-color: rgba(0,0,0,.45);
      border: 0;
      border-radius: 20px;
      z-index: 5;
      .icon {
        width: 20px;
        height: 20px;
        display: block;
        background: url("assets/ic_course_full.svg") no-repeat center;
      }
    }
  }

  .el-tabs__item.is-active {
    .tab-introduction {
      background: url("assets/ic_introduction_active.svg") no-repeat center;
      &.mobile {
        background: url("assets/ic_mobile_introduction_active.svg") no-repeat center;
      }
    }

    .tab-discuss {
      background: url("assets/ic_discuss_active.svg") no-repeat center;
      &.mobile {
        background: url("assets/ic_mobile_discuss_active.svg") no-repeat center;
      }
    }

    .tab-member-list {
      background: url("./assets/ic_member_list_active.svg") no-repeat center;
    }
    .tab-course {
      background: url("./assets/ic_course_active.svg") no-repeat center;
      &.mobile {
        background: url("./assets/ic_mobile_course_active.svg") no-repeat center;
      }
    }
  }

  .tab-label {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .tab-introduction {
      width: 24px;
      height: 24px;
      background: url("assets/ic_introduction.svg") no-repeat center;
      &.mobile {
        background: url("assets/ic_mobile_introduction.svg") no-repeat center;
      }
    }

    .tab-discuss {
      width: 24px;
      height: 24px;
      background: url("assets/ic_discuss.svg") no-repeat center;
      &.mobile {
        background: url("assets/ic_mobile_discuss.svg") no-repeat center;
      }
    }

    .tab-member-list {
      width: 24px;
      height: 24px;
      min-width: 20px;
      background: url("./assets/ic_member_list.svg") no-repeat center;
    }
    .tab-course {
      width: 24px;
      height: 24px;
      background: url("./assets/ic_mobile_course.svg") no-repeat center;
      &.mobile {
        background: url("assets/ic_mobile_course.svg") no-repeat center;
      }
    }
  }
}

</style>

