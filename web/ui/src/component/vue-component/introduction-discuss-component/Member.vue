<template>
  <div class="curriculum-member">
    <div
      v-show="isTeacher || isAssistant || isSupervisor"
      class="cu-stage"
    >
      <label>{{ $t('上台') }}</label>
      <el-tooltip
        :manual="isClassStarted"
        class="item"
        :content="translateTip.content"
        placement="bottom"
        :hide-after="2000"
        :disabled="isClassStarted"
      >
        <el-switch
          v-model="enableStage"
          class="video-model-switch"
          active-color="#006EFF"
          :disabled="!isClassStarted"
          :inactive-text="enableStage ? $t('已开启') : $t('已关闭')"
          @change="onStageSwitchChanged"
        />
      </el-tooltip>
    </div>
    <div
      v-show="enableStage && stageMemberList.length > 0"
      class="cu-stage-manage"
    >
      <el-popover
        v-model="expandStageList"
        popper-class="stage-popper"
        placement="bottom"
        trigger="click"
        @show="onIconShow"
        @hide="onIconHide"
      >
        <el-scrollbar class="cu-member-list">
          <div
            v-for="(item, index) in stageMemberList"
            :key="index"
            class="cu-member-item"
          >
            <div class="left">
              <i
                class="ic-me"
                :class="[getPlatformStringFromCode(item.param.device).icon]"
              />
              <label :title="item.param.nickname">{{ item.param.nickname }}</label>
              <span class="stage-apply">{{ $t('申请上台') }}&nbsp;({{ item.remain }}s)&nbsp;</span>
            </div>
            <el-dropdown
              trigger="click"
              size="mini"
              placement="bottom-start"
              split-button
              type="primary"
              @click="approve(item)"
              @command="refuse(item)"
            >
              {{ $t('上台') }}&nbsp;1
              <el-dropdown-menu
                slot="dropdown"
                class="stage-dropdown"
              >
                <el-dropdown-item command="refuse">
                  <el-button
                    type="primary"
                    size="mini"
                  >
                    {{ $t('拒绝') }}
                  </el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </el-scrollbar>
        <div
          slot="reference"
          class="stage-ref"
        >
          <label>{{ $t('上台管理') }}</label>
          <i :class="expandStageList ? 'ic-arrow-change' : 'ic-arrow-change up'" />
        </div>
      </el-popover>
      <i class="i-badge">{{ stageMemberList.length > 99 ? '99+' : stageMemberList.length }}</i>
    </div>
    <el-input
      v-model.trim="searchKey"
      class="cu-member-input"
      :placeholder="$t('输入名字搜索')"
      prefix-icon="el-icon-search"
      maxlength="10"
      clearable
      @input="searchName"
    />
    <div class="member-list__table-wrap">
      <table class="member-list__table">
        <thead class="member-list__thead">
          <tr>
            <th
              class="member-list__th member-list__user-name-wrap"
              style="text-align: left"
            >
              {{ $t('姓名') }}
            </th>
            <th class="member-list__th">
              {{ $t('上台') }}
            </th>
            <th class="member-list__th">
              {{ $t('禁言') }}
            </th>
            <th class="member-list__th">
              {{ $t('移出') }}
            </th>
          </tr>
        </thead>
        <tbody
          ref="memberlist-tbody"
          class="member-list__tbody"
        >
          <div
            v-if="renderMemberList.length === 0"
            class="empty"
          >
            {{ $t('无搜索结果') }}
          </div>
          <tr
            v-for="(item, index) in renderMemberList"
            v-else
            :key="index"
            :class="['member-list__tr', item.currentStatus === TStatuesOnline ? 'member-list__row-online' : 'member-list__row-offline']"
          >
            <td
              class="member-list__td member-list__user-name-wrap"
              :title="item.userName"
            >
              <span
                :class="['device-icon', { offline: item.currentStatus !== TStatuesOnline }, getPlatformStringFromCode(item.device).icon]"
                :title="getPlatformStringFromCode(item.device).label"
              />
              <i :class="[item.role === 'teacher' ? 'teacher-icon' : item.role === 'assistant' ? 'assistant-icon' : 'student-icon', 'role-icon']">
                <span>
                  {{ roleInfo[item.role] }}
                </span>
              </i>
              <span
                id="teacher-name"
                :class="['member-list__user-name', { offline: item.currentStatus !== TStatuesOnline }]"
              >
                <HighlightText :text="item.colorName" />
              </span>
            </td>
            <td class="member-list__td">
              <div class="item-wrapper">
                <div
                  v-if="item.currentStatus === TStatuesOnline && isClassStarted"
                  :class="['member-list__icon', item.stage ? 'member-list__stage-off' : 'member-list__stage-on']"
                  @click="onMemberAction(item, item.stage ? TTypeStageDown : TTypeStageUp)"
                />
              </div>
            </td>
            <td class="member-list__td">
              <div class="item-wrapper">
                <div
                  v-if="item.currentStatus === TStatuesOnline"
                  :class="[
                    'member-list__icon',
                    !item.silence ? 'member-list__chat-on' : 'member-list__chat-off',
                  ]"
                  @click="onMemberAction(item, item.silence ? TTypeSilenceCancel : TTypeSilence)"
                />
              </div>
            </td>
            <td class="member-list__td">
              <div class="item-wrapper">
                <div
                  v-if="item.currentStatus === TStatuesOnline"
                  :class="['member-list__icon', item.currentStatus === TStatuesOnline ? 'member-list__kick-off' : 'member-list__kick-no']"
                  @click="onKick(item)"
                />
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="cu-member-pagination">
      <el-pagination
        background
        layout="prev, pager, next"
        :current-page.sync="pageIndex"
        :page-size="1"
        :pager-count="5"
        :total="pageCount"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import i18next from 'i18next';
import BaseComponent from '@/component/core/BaseComponent';
import HighlightText from '@/component/ui-component/highlight-text-component/HighlightText';
import Constant from '@/util/Constant';
import Util from '@/util/Util';

export default {
  name: 'CurriculumMember',
  components: {
    HighlightText,
  },
  extends: BaseComponent,
  data() {
    return {
      input: '',
      // TCIC常量重定义，保证在HTML可以直接绑定
      TTypeCameraOpen: TCIC.TMemberActionType.Camera_Open,
      TTypeCameraClose: TCIC.TMemberActionType.Camera_Close,
      TTypeMicOpen: TCIC.TMemberActionType.Mic_Open,
      TTypeMicClose: TCIC.TMemberActionType.Mic_Close,
      TTypeHandUp: TCIC.TMemberActionType.Hand_Up,
      TTypeHandUpCancel: TCIC.TMemberActionType.Hand_Up_Cancel,
      TTypeKickOut: TCIC.TMemberActionType.Kick_Out,
      TTypeBoardEnable: TCIC.TMemberActionType.Board_Enable,
      TTypeBoardDisable: TCIC.TMemberActionType.Board_Disable,
      TTypeSilence: TCIC.TMemberActionType.Silence,
      TTypeSilenceCancel: TCIC.TMemberActionType.Silence_Cancel,
      TTypeStageUp: TCIC.TMemberActionType.Stage_Up,
      TTypeStageDown: TCIC.TMemberActionType.Stage_Down,
      TTypeKickOutForever: TCIC.TMemberActionType.Kick_Out_Forever,
      TStatuesOnline: TCIC.TMemberStatus.Online,
      TDeviceStatusUnknown: TCIC.TDeviceStatus.Unknown,
      TDeviceStatusOpen: TCIC.TDeviceStatus.Open,
      // 其他变量定义
      arrowOffset: 0,
      memberList: [],
      pageIndex: 1,
      pageCount: 1,
      // 是否是直播课
      isLive: false,
      // 是否可见
      isVisible: false,
      // 奖杯TaskId
      trophyTaskId: 'trophy',
      enableStage: false, // 连麦开关
      expandStageList: false, // 连麦管理小图标切换
      stageMemberList: [], // 连麦人员, TStageCommand列表
      stageTimer: 0, // 刷新连麦相关的UI
      trophyList: [],
      isSendingTrophy: false,
      totalMemberCount: 0, // 成员总人数
      offlineMemberCount: 0, // 离线人数
      showAllMember: true, // 是否展示所有成员
      searchKey: '', // 搜索关键字
      pageSize: 0, // 每一页显示人数
      popoverSTO: null,
      isSupervisor: false,
      isTeacher: false,
      isAssistant: false,
      isClassStarted: false, // 是否已开始上课
      inviteStageMap: new Map(), // 邀请上麦学生列表
      inviteTimer: null, // 邀请上麦计时器，本地超时取消
      roleInfo: null,
      roomInfo: null,
    };
  },

  computed: {
    renderMemberList() {
      const { memberList } = this;
      const classInfo = TCIC.SDK.instance.getClassInfo();
      memberList.forEach((member) => {
        if (this.searchKey.length > 0) {
          member.colorName = `${member.userName.replace(this.searchKey, `<span class="em">${this.searchKey}</span>`)}`;
        } else {
          member.colorName = `${member.userName}`;
        }
        switch (true) {
          case classInfo.assistants.includes(member.userId): {
            member.role = 'assistant';
            break;
          }
          case member.userId === classInfo.teacherId: {
            member.role = 'teacher';
            break;
          }
          default: {
            member.role = 'student';
          }
        }
      });
      return memberList;
    },
  },
  computed: {
    translateTip() {
      return {
        content: i18next.t('开始{{arg_0}}后才可开启上台', { arg_0: this.roomInfo?.startRoom }),
      };
    },
  },

  mounted() {
    this.roleInfo = TCIC.SDK.instance.getRoleInfo().roleInfo;
    this.roomInfo = TCIC.SDK.instance.getRoleInfo().roomInfo;
    this.addLifecycleTCICStateListener(TCIC.TMainState.Member_Count, (onlineNumber) => {
      this.$nextTick(() => {
        if (this.isVisible) {
          this.updateMemberList();
        }
      });
    });
    this.addLifecycleTCICStateListener(
      TCIC.TMainState.Member_List,
      (memberList) => {
        this.memberList = memberList;
        this.pageCount = TCIC.SDK.instance.getState(TCIC.TMainState.Member_List_Page_Count); // 避免因未变更而不通知
        this.updatePopper(200);
      },
      { noEmitWhileSubscribe: true },
    );
    this.addLifecycleTCICStateListener(
      TCIC.TMainState.Member_List_Page_Count,
      (pageCount) => {
        this.pageCount = pageCount;
      },
      { noEmitWhileSubscribe: true },
    );
    this.addLifecycleTCICStateListener(Constant.TStateHeaderMemberActive, (isVisible) => {
      this.isVisible = isVisible;
      if (isVisible) {
        // 重新显示后重置内容
        this.pageIndex = 1;
        this.showAllMember = true;
        this.searchKey = '';
        this.updateMemberList();
      }
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Member_List_Total_Member_Count, (count) => {
      this.totalMemberCount = count;
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Member_List_Offline_Member_Count, (count) => {
      this.offlineMemberCount = count;
    });

    this.addLifecycleTCICStateListener(TCIC.TMainState.Enable_Stage, (enable) => {
      this.enableStage = enable;
      window.clearInterval(this.stageTimer);
      if (this.enableStage) {
        // cpu高时限频
        const interval = Util.getProperIntervalByCPUUsage(500, 2000);
        // TODO : 主动去拉取下getAllCommand列表
        this.stageTimer = window.setInterval(this.refreshStage, interval);
        TCIC.SDK.instance.getAllCommand(TCIC.TCommandID.Stage);
      } else {
        this.stageTimer = 0;
        this.stageMemberList = [];
      }
    });

    this.isClassStarted = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status) === TCIC.TClassStatus.Already_Start;
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start).then(() => {
      this.isClassStarted = true;
    });

    this.addLifecycleTCICStateListener(TCIC.TMainState.Ask_Stage_List, (list) => {
      this.mergeStageList(list);
    });
    this.makeSureClassJoined(this.onJoinClass);

    // 监听窗口尺寸变更
    // window.addEventListener('resize', this.onWindowResize);
    const resizeObserver = new ResizeObserver((entries) => {
      this.onBoundingClientRectChange(entries[0].contentRect);
    });
    resizeObserver.observe(this.$el);
  },
  methods: {
    onTaskUpdate(taskInfo) {
      if (taskInfo.taskId.indexOf(Constant.TConstantLiveInviteTaskId) === -1) {
        // 不属于连麦任务
        return;
      }
      if (taskInfo.status === 0) {
        this.inviteStageMap.delete(taskInfo.taskId);
        return;
      }

      const item = JSON.parse(taskInfo.content);
      if (!this.inviteStageMap.has(taskInfo.taskId)) {
        this.inviteStageMap.set(taskInfo.taskId, item);
      }
      console.log(`===>>> : onTaskUpdate : ${item.userName} : ${item.status}`);
      this.onRefreshTaskItem(taskInfo.taskId, item, false);

      // 如清理过，则清空计时器
      const allTaskIds = Array.from(this.inviteStageMap.keys());
      if (allTaskIds.length === 0) {
        window.clearInterval(this.inviteTimer);
        this.inviteTimer = null;
        console.log('===>>> : onTaskUpdate : clearInterval');
      }
    },
    onBoundingClientRectChange(rect) {
      console.info(`VideoComponent::onBoundingClientRectChange=>${JSON.stringify(rect)}, ${this.needRender}`);
      if (rect && rect.width > 0 && rect.height > 0) {
        const height = rect.height;
        const itemHeight = 69;
        const otherHeight = 120 + 60;
        let size = Math.floor((height - otherHeight - 48) / itemHeight);
        if (size < 1) {
          // 最少显示一个人
          size = 1;
        }
        let listHeight = height - otherHeight;
        // if (this.isClassStarted) {
        listHeight = listHeight - 50;
        // }

        if (listHeight - size * 69 > 69) {
          size += 1;
        }
        this.$refs['memberlist-tbody'].style.height = `${listHeight}px`;
        if (size !== this.pageSize) {
          this.pageSize = size;
          if (this.$refs['memberlist-tbody']) {
            this.$refs['memberlist-tbody'].style.minHeight = `${this.pageSize * 69}px`;
            this.updateMemberList();
            this.updatePopper();
          }
          setTimeout(() => {
            this.updateMemberList();
            this.updatePopper();
          }, 1000);
        }
      }
    },
    onJoinClass() {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      this.isLive = TCIC.SDK.instance.isLiveClass();
      this.updateMemberList();
      // this.onWindowResize();    // 计算页数
      this.isSupervisor = TCIC.SDK.instance.isSupervisor();
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      if (this.isLive) {
        this.addLifecycleTCICEventListener(TCIC.TMainEvent.Task_Updated, this.onTaskUpdate);
        // 更新最初的状态
        TCIC.SDK.instance.getTasks(0).then((result) => {
          result.tasks.forEach((taskInfo) => {
            this.onTaskUpdate(taskInfo);
          });
        });
      }
    },

    onWindowResize() {
      const height = document.body.clientHeight;
      const itemHeight = 69;
      const otherHeight = 455;
      let size = Math.floor((height - otherHeight - 48) / itemHeight);
      if (size < 1) {
        // 最少显示一个人
        size = 1;
      }
      const listHeight = height - otherHeight - 48;

      if (listHeight - size * 69 > 69) {
        size += 1;
      }
      this.$refs['memberlist-tbody'].style.height = `${listHeight}px`;
      if (size !== this.pageSize) {
        this.pageSize = size;
        if (this.$refs['memberlist-tbody']) {
          this.$refs['memberlist-tbody'].style.minHeight = `${this.pageSize * 69}px`;
          this.updateMemberList();
          this.updatePopper();
        }
        setTimeout(() => {
          this.updateMemberList();
          this.updatePopper();
        }, 1000);
      }
    },
    getPlatformStringFromCode(device) {
      // 将平台码转换成可阅读的文本
      switch (device) {
        case TCIC.TDevice.Windows:
        case TCIC.TDevice.Mac:
          return {
            icon: 'pc',
            label: i18next.t('电脑'),
          };
        case TCIC.TDevice.Pad:
          return {
            icon: 'pad',
            label: i18next.t('平板'),
          };
        case TCIC.TDevice.Phone:
          return {
            icon: 'phone',
            label: i18next.t('手机'),
          };
        case TCIC.TDevice.TV:
          return {
            icon: 'pc',
            label: 'TV',
          };
        case TCIC.TDevice.Miniprogram:
          return {
            icon: 'mini',
            label: i18next.t('小程序'),
          };
        default:
          return {
            icon: 'unknow',
            label: i18next.t('未知'),
          };
      }
    },
    onRefreshTaskItem(taskId, item, isFromTimer = true) {
      const nowTime = TCIC.SDK.instance.getServerTimestamp();
      switch (item.status) {
        case Constant.TConstantLiveInviteCreate: {
          if (isFromTimer) {
            if (item.timeStamp + item.timeout * 1000 + 5 * 1000 < nowTime) {
              // 说明已过期
              item.status = Constant.TConstantLiveInviteCancel;
              TCIC.SDK.instance
                .updateTask(taskId, JSON.stringify(item))
                .then((task) => {
                  // 更新连麦状态并隐藏
                  console.log('===>>> : 刷新 : 连麦邀请 成功 : ');
                })
                .catch((error) => {
                  // 更新定时器失败
                  console.log('===>>> : 刷新 : 发起连麦邀请 失败 : ');
                });
            }
          }
          break;
        }
        case Constant.TConstantLiveInviteApprove: {
          console.log(`===>>> : onRefreshTaskItem : approve : ${item.userName}`);
          // 让其上麦
          window.showToast(i18next.t('{{arg_0}} 同意了你的上台邀请', { arg_0: item.userName }));
          const classInfo = TCIC.SDK.instance.getClassInfo();
          const param = {
            classId: classInfo.classId,
            classType: classInfo.classType,
            userId: item.userId,
            actionType: this.TTypeStageUp,
          };
          TCIC.SDK.instance.memberAction(param).catch((err) => {
            window.showToast(err.errorMsg, 'error');
          });
          TCIC.SDK.instance.stopTask(taskId);
          this.inviteStageMap.delete(taskId);
          break;
        }
        case Constant.TConstantLiveInviteReject: {
          console.log(`===>>> : onRefreshTaskItem : reject : ${item.userName}`);
          window.showToast(i18next.t('{{arg_0}} 拒绝了你的上台邀请', { arg_0: item.userName }));
          TCIC.SDK.instance.stopTask(taskId);
          this.inviteStageMap.delete(taskId);
          break;
        }
        case Constant.TConstantLiveInviteCancel: {
          console.log(`===>>> : onRefreshTaskItem : cancel : ${item.userName}`);
          TCIC.SDK.instance.stopTask(taskId);
          this.inviteStageMap.delete(taskId);
          break;
        }
        default:
          break;
      }
    },
    onRefreshInviteMap() {
      const nowTime = TCIC.SDK.instance.getServerTimestamp();
      let allTaskIds = Array.from(this.inviteStageMap.keys());
      console.log(`===>>> : onRefreshInviteMap : ${nowTime} : ${allTaskIds}`);
      allTaskIds.forEach((taskId) => {
        const item = this.inviteStageMap.get(taskId);
        this.onRefreshTaskItem(taskId, item, true);
      });
      allTaskIds = Array.from(this.inviteStageMap.keys());
      console.log(`===>>> : onRefreshInviteMap : end : ${nowTime} : ${allTaskIds}`);
      if (allTaskIds.length === 0) {
        window.clearInterval(this.inviteTimer);
        this.inviteTimer = null;
        console.log('===>>> : onTaskUpdate : clearInterval');
      }
    },
    onMemberAction(user, actionType) {
      // if (this.isSupervisor) {
      //   window.showToast('不可操作');
      //   return;
      // }
      const classInfo = TCIC.SDK.instance.getClassInfo();
      if (user.currentStatus !== TCIC.TMemberStatus.Online) {
        // 不允许操作离线学员
        window.showToast(i18next.t('该操作只对在线{{arg_0}}有效', { arg_0: this.roleInfo.student }));
        return;
      }
      const stageActions = [
        // 上台相关操作
        TCIC.TMemberActionType.Stage_Up,
        TCIC.TMemberActionType.Stage_Down,
        TCIC.TMemberActionType.Camera_Open,
        TCIC.TMemberActionType.Camera_Close,
        TCIC.TMemberActionType.Mic_Open,
        TCIC.TMemberActionType.Mic_Close,
      ];
      if (stageActions.includes(actionType)) {
        // 如果是上台相关功能
        const classInfo = TCIC.SDK.instance.getClassInfo();
        if (this.isLive && user.userId !== classInfo.teacherId && !classInfo.assistants.includes(user.userId)) {
          // 直播课不允许上台
          // 直播课老师和助教可以直接上台
          if (actionType === TCIC.TMemberActionType.Stage_Up) {
            // 下台情况下，发邀请
            // 1. 如果已邀请，提示
            // 2. 未邀请，且未超过连麦限制时，则发起邀请任务
            const inviteTaskId = `${Constant.TConstantLiveInviteTaskId}-${user.userId}`;
            const hadInvited = this.inviteStageMap.has(inviteTaskId);
            if (hadInvited) {
              window.showToast(i18next.t('正在邀请该学员上台'));
              return;
            }
            const stageCount = TCIC.SDK.instance.getState(TCIC.TMainState.Stage_Count, 0);
            if (stageCount >= 4) {
              // 上台人数已满后不允许邀请
              window.showToast(i18next.t('上台人数已达上限'));
              return;
            }
            const allTaskIds = Array.from(this.inviteStageMap.keys());
            if (allTaskIds.length + stageCount >= 4) {
              // 上台人数已满后不允许上台
              window.showToast(i18next.t('邀请上台人数已达上限，请稍后再试'));
              return;
            }

            if (!this.inviteTimer) {
              this.inviteTimer = window.setInterval(this.onRefreshInviteMap, 5000);
            }
            // 0. 先清理过期
            const inviteTask = {
              userId: user.userId,
              userName: user.userName,
              status: Constant.TConstantLiveInviteCreate,
              timeStamp: TCIC.SDK.instance.getServerTimestamp(),
              timeout: 15, // 10秒内对放没收到，本地也更新下
            };
            this.inviteStageMap.set(inviteTaskId, inviteTask);
            TCIC.SDK.instance
              .updateTask(inviteTaskId, JSON.stringify(inviteTask))
              .then((task) => {
                // 更新连麦状态并隐藏
                console.log('===>>> : 发起连麦邀请 成功 : ', user.userId);
              })
              .catch((error) => {
                // 更新定时器失败
                console.log('===>>> : 发起连麦邀请 失败 : ', user.userId);
              });

            return;
          }

          if (actionType === TCIC.TMemberActionType.Stage_Down) {
            // 已上台，则按正常逻辑下台
            console.log('===>>> : 下麦 : ', user.userId);
          }
        }

        if (classInfo.maxRtcMember === 0) {
          // 上台人数已满后不允许上台
          window.showToast(i18next.t('当前{{arg_0}}设定上台人数为0，成员无法上台', { arg_0: this.roomInfo?.name }));
          return;
        }
      }
      const boardActions = [
        // 涂鸦相关操作
        TCIC.TMemberActionType.Board_Enable,
        TCIC.TMemberActionType.Board_Disable,
      ];
      if (!this.isClassStarted) {
        // 未开始上课，禁用上台及涂鸦相关操作
        if (stageActions.includes(actionType) || boardActions.includes(actionType)) {
          // window.showToast('上课前禁止该操作');
          return;
        }
      }
      const param = {
        classId: classInfo.classId,
        classType: classInfo.classType,
        userId: user.userId,
        actionType,
      };
      TCIC.SDK.instance
        .memberAction(param)
        .then((res) => {
          if (actionType === TCIC.TMemberActionType.Stage_Down) {
            window.showToast(i18next.t('{{nickname}}已下台', { nickname: this.nickname }));
          }
        })
        .catch((err) => {
          window.showToast(err.errorMsg, 'error');
        });
    },
    handleCurrentChange(val) {
      this.pageIndex = val;
      this.updateMemberList();
    },
    onKick(user) {
      // if (this.isSupervisor) {
      //   window.showToast('不可操作');
      //   return;
      // }
      if (user.currentStatus !== TCIC.TMemberStatus.Online) {
        window.showToast(i18next.t('该操作只对在线{{arg_0}}有效', { arg_0: this.roleInfo.student }));
      } else {
        TCIC.SDK.instance.showMessageBox(
          '',
          i18next.t('是否确认将{{arg_0}}移出{{arg_1}}？', { arg_0: user.userName, arg_1: this.roomInfo?.name }),
          [i18next.t('确定'), i18next.t('取消')],
          (index, options) => {
            if (index === 0) {
              if (options[0]) {
                this.onMemberAction(user, TCIC.TMemberActionType.Kick_Out_Forever);
              } else {
                this.onMemberAction(user, TCIC.TMemberActionType.Kick_Out);
              }
            }
          },
          [{ text: i18next.t('不允许该{{arg_0}}再次加入该{{arg_1}}', { arg_0: this.roleInfo?.student, arg_1: this.roomInfo?.name }), value: false }],
        );
      }
    },
    updatePopper(delay = 800) {
      // 通知父组件刷新位置
      // 延时刷新popper(延时500ms仍能复现不更新的情况)
      clearTimeout(this.popoverSTO);
      this.popoverSTO = setTimeout(() => {
        const parentPopper = this.$parent;
        if (parentPopper && parentPopper.updatePopper && this.isVisible) {
          parentPopper.updatePopper();
        }
      }, delay);
    },
    updateMemberList() {
      TCIC.SDK.instance.promiseState(TCIC.TMainState.Joined_Class, true).then(() => {
        if ((!this.isTeacher && !this.isAssistant && !this.isSupervisor) || !this.isVisible) {
          // 如果不是老师或者不可见，不需要拉取成员列表
          return;
        }
        let filterType = TCIC.TMemberType.All;
        if (!this.showAllMember && this.searchKey.length === 0) {
          // 不搜索且选择离线tab才会查询离线用户
          filterType = TCIC.TMemberType.Offline;
        }
        // 构建查询参数
        const filter = {
          page: this.pageIndex,
          limit: this.pageSize,
          type: filterType,
          keyword: this.searchKey,
        };
        TCIC.SDK.instance.updateMemberListFilter(filter);
      });
    },
    mergeStageList(newStageList) {
      window.clearInterval(this.stageTimer);
      {
        // console.log(`====>>>> : mergeStageList : ${JSON.stringify(newStageList)}`);
        // 转成UI需要的信息
        const array = [];
        {
          if (newStageList.cmds instanceof Array) {
            const svrTime = TCIC.SDK.instance.getServerTimestamp();
            newStageList.cmds.forEach((item) => {
              const obj = item.serialize();
              const remainTime = Math.ceil((obj.timeout * 1000 - svrTime) / 1000);
              obj.remain = remainTime <= 10 ? remainTime : 10;
              array.push(obj);
            });
          }
        }
        // 开始合并
        this.stageMemberList = array;
      }
      this.refreshStage();
      if (this.stageMemberList.length > 0) {
        window.clearInterval(this.stageTimer);
        const interval = Util.getProperIntervalByCPUUsage(500, 2000);
        this.stageTimer = window.setInterval(this.refreshStage, interval);
      }
    },
    searchName(val) {
      this.searchKey = val.trim();
      this.pageIndex = 1;
      this.updateMemberList();
    },
    refreshStage() {
      const svrTime = TCIC.SDK.instance.getServerTimestamp();
      for (let index = 0; index < this.stageMemberList.length; index++) {
        const item = this.stageMemberList[index];
        const remainTime = Math.ceil((item.timeout * 1000 - svrTime) / 1000);
        item.remain = remainTime <= 10 ? remainTime : 10;
        if (item.remain < 0) {
          // 本地主动删掉
          this.stageMemberList.splice(index, 1);
          index -= 1;
          // this.refuse(item);
        }
      }

      if (this.stageMemberList.length === 0) {
        this.expandStageList = false;
        // window.setTimeout(() => {
        //   console.log(`===>>> : expandStageList : ${this.expandStageList}`);
        // }, 10000);
      }
    },
    onStageSwitchChanged() {
      console.log(`===>>> onStageSwitchChanged : ${this.enableStage}`);
      TCIC.SDK.instance.setEnableStage(this.enableStage).catch((error) => {
        window.showToast(
          i18next.t('{{arg_0}}失败: {{arg_1}} {{arg_2}}', {
            arg_0: this.enableStage ? i18next.t('开启上台') : i18next.t('关闭上台'),
            arg_1: error.errorCode,
            arg_2: error.errorMsg,
          }),
          'error',
        );
      });
    },
    onIconShow() {
      if (this.stageMemberList.length > 0) {
        this.expandStageList = true;
      }
    },
    onIconHide() {
      this.expandStageList = false;
    },
    // 拒绝连麦
    refuse(item) {
      console.log(`====>>>> : refuse : ${JSON.stringify(item)}`);
      const req = new TCIC.TCommandReq();
      req.cmd = TCIC.TCommandID.Stage;
      req.userId = item.userId;
      req.classId = TCIC.SDK.instance.getClassInfo().classId;
      req.type = TCIC.TCommandStatus.Reject;
      TCIC.SDK.instance.sendCommand(req).catch((error) => {
        // TODO: 从本地删除，并刷新
        window.showToast(error.errorMsg, 'error');
        console.log(`===>>> : refuse : ${item.userId}`);
      });
    },
    // 同意连麦
    approve(item) {
      const stageCount = TCIC.SDK.instance.getState(TCIC.TMainState.Stage_Count, 0);
      if (stageCount >= 4) {
        // 上台人数已满后不允许上台
        window.showToast(i18next.t('上台人数已达上限'));
        return;
      }
      console.log(`====>>>> : approve : ${JSON.stringify(item)}`);
      const req = new TCIC.TCommandReq();
      req.cmd = TCIC.TCommandID.Stage;
      req.userId = item.userId;
      const classInfo = TCIC.SDK.instance.getClassInfo();
      req.classId = classInfo.classId;
      req.type = TCIC.TCommandStatus.Approve;
      TCIC.SDK.instance
        .sendCommand(req)
        .then(() => {
          // 延时一秒等学生那边进TRTC房间
          setTimeout(() => {
            const param = {
              classId: classInfo.classId,
              classType: classInfo.classType,
              userId: item.userId,
              actionType: TCIC.TMemberActionType.Stage_Up,
            };
            TCIC.SDK.instance
              .memberAction(param)
              .then(() => {
                TCIC.SDK.instance.promiseState(TCIC.TMainState.Wait_Stage_UserId, item.userId).then(() => {
                  window.showToast(i18next.t('上台成功'));
                });
              })
              .catch((err) => {
                window.showToast(err.errorMsg, 'error');
              });
          }, 1000);
        })
        .catch((error) => {
          // TODO: 从本地删除，并刷新
          window.showToast(error.errorMsg, 'error');
          console.log(`===>>> : approve : ${item.userId}`);
        });
    },
  },
};
</script>

<style lang="less" scoped>
#teacher-name {
  width: calc(100% - 40px);
}
.role-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  padding: 0 4px;
  border-radius: 2px;
  height: 20px;
  &.assistant-icon {
    background: linear-gradient(152deg, #23be82 0%, #08ae6e 94%);
  }
  &.teacher-icon {
    background: linear-gradient(152deg, #00a6ff 0%, #006dff 94%);
  }
  &.student-icon{
    border: 1px solid rgba(255,255,255,0.40);
    border-radius: 2px;
  }
  span {
    font-size: 12px;
    color: #fff;
  }
}
</style>
