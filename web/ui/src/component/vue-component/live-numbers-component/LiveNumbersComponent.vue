<template>
  <div
    v-if="isJoinClass"
    class="live-numbers"
  >
    <div v-if="classStatus == 1">
      <div
        v-if="deviceOrientation === 0"
        class="live-numbers__number landscape"
      >
        <button
          class="live-numbers__portrait-btn"
          @click="setDeviceOrientation()"
        >
          <i class="live-numbers__portrait" />
        </button>
        <div class="live-numbers__sep" />
        {{ getUserNumberText(onlineNumber) }}
      </div>
      <div
        v-else
        class="live-numbers__number portrait"
      >
        {{ getUserNumberText(onlineNumber) }}
      </div>
    </div>
  </div>
</template>

<script>
import i18next from 'i18next';
import BaseComponent from '@/component/core/BaseComponent';

export default {
  extends: BaseComponent,
  data() {
    return {
      classStatus: TCIC.TClassStatus.Not_Start,
      onlineNumber: 0,
      deviceOrientation: TCIC.TDeviceOrientation.Portrait,
      isJoinClass: false,
    };
  },
  mounted() {
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Joined_Class, true).then(() => {
      this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, () => {
        this.updateStatus();
      });
      this.isJoinClass = true;
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.deviceOrientation = orientation;
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Member_Count, (count) => {
      this.onlineNumber = count;
    });
  },
  methods: {
    setDeviceOrientation() {
      if (this.deviceOrientation === TCIC.TDeviceOrientation.Landscape) {
        TCIC.SDK.instance.setDeviceOrientation(TCIC.TDeviceOrientation.Portrait);
      } else {
        TCIC.SDK.instance.setDeviceOrientation(TCIC.TDeviceOrientation.Landscape);
      }
    },
    updateStatus() {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      this.classStatus = classInfo.status;
    },
    getUserNumberText(num) {
      return i18next.t('{{arg_0}}人在线', { arg_0: num });
    },
  },
};
</script>

<style lang="less">

.live-numbers {
  width: fit-content;
  height: fit-content;
  background: olivedrab;
  .live-numbers__number {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: auto;
    left: 18px;
    top: 18px;
    color: #FFFFFF;
    line-height: 20px;
    background: rgba(0, 0, 0, 0.45);
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    button {
      width: 24px;
      height: 24px;
    }
    &.landscape {
      height: 48px;
      border-radius: 24px;
      font-size: 14px;
      padding: 0 12px;
    }
    &.portrait {
      height: 24px;
      border-radius: 12px;
      font-size: 12px;
      padding: 0 10px;
    }
    .live-numbers__sep {
      background: white;
      width: 1px;
      height: 15px;
      margin: 0px 12px;
    }
  }
  .live-numbers__status {
    width: 100%;
    height: 100%;
    .live-numbers__content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      .live-numbers__tips {
        font-size: 18px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
        line-height: 18px;
        text-align: center;
      }
      .live-numbers__time {
        margin-top: 8px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 13px;
        text-align: center;
      }
    }
  }
  .live-numbers__landscape-btn {
    position: absolute;
    right: 18px;
    bottom: 9px;
    background: rgba(0, 0, 0, 0.45);
  }
  button {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    border-radius: 20px;
    padding: 0;
    outline: none;
    width: 40px;
    height: 40px;
    i {
      width: 24px;
      height: 24px;
    }
    .live-numbers__landscape {
      background: url('./assets/ic_landscape.svg') no-repeat center;
    }
    .live-numbers__portrait {
      background: url('./assets/ic_portrait.svg') no-repeat center;
    }
  }
}
</style>
