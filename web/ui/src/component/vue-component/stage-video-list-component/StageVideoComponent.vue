<template>
  <div :class="['stage-video-component']">
    <VideoComponent
      ref="videoControl"
      class="stage-video-container"
      :user-id="user.userId"
      :user-role="showAssistantIcon ? roleInfo.assistant : ''"
      :is-disable="isDisable"
      :network-quality="netQuality"
      :enable-show-mic-icon="false"
      :enable-show-nickname="false"
      :enable-show-ctrl-component="false"
    />
    <div class="stage-swiper-title">
      <label>{{ user.userName }}</label>
      <div
        v-show="user.userId === selfUserId"
        class="stage-swiper-ic"
      >
        <i
          :id="'stage-mic-' + user.userId"
          :class="user.micState === deviceStateOpen ? 'ic-record' : 'ic-record closed'"
          @show="user.userId === selfUserId"
          @click="user.userId === selfUserId ? onClickAudio(user) : ''"
        />
        <i
          :id="'stage-camera-' + user.userId"
          :class="user.cameraState === deviceStateOpen ? 'ic-video' : 'ic-video closed'"
          @show="user.userId === selfUserId"
          @click="user.userId === selfUserId ? onClickVideo(user) : ''"
        />
      </div>
    </div>
  </div>
</template>


<script>
import i18next from 'i18next';
import BaseComponent from '@core/BaseComponent';
import Util from '@util/Util';
import VideoComponent from '../video-component/VideoComponent.vue';

export default {
  components: {
    VideoComponent,
  },
  extends: BaseComponent,
  props: {
    user: {
      required: true,
      type: TCIC.TPermission,
      default() {
        return new TCIC.TPermission();
      },
    },
  },
  data() {
    return {
      selfUserId: '', // 当前用户Id
      isTeacher: false,
      isAssistant: false,
      isDisable: false,
      isSmallMode: false, // 是否缩小模式(缩小模式下仅展示视频，不显示奖杯)
      netQuality: 0, // 网络质量
      deviceStateOpen: TCIC.TDeviceStatus.Open, // 设备开启状态
      isCollegeClass: false, // 是否大教学模式(没有奖杯)
      isLiveClass: false, // 是否直播课(没有奖杯)
      showAssistantIcon: false,
      roleInfo: {},
    };
  },

  mounted() {
    this.roleInfo = TCIC.SDK.instance.getRoleInfo().roleInfo;
    this.selfUserId = TCIC.SDK.instance.getUserId();
    this.isDisable = Util.getBooleanParams('disableInnerStudent');

    // 监听加入课堂事件
    this.makeSureClassJoined(this.onJoinClass);

    this.addLifecycleTCICEventListener(TCIC.TStatisticsEvent.Network_Statistics, this.networkStatisticsHandler);
    // this.translateQualityToStatus(5);
  },
  beforeDestroy() {},
  methods: {
    onJoinClass() {
      this.showAssistantIcon = TCIC.SDK.instance.getClassInfo().assistants.includes(this.userId);
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      this.isCollegeClass = TCIC.SDK.instance.isCollegeClass();
      this.isLiveClass = TCIC.SDK.instance.isLiveClass();
    },
    setControlDirect(direct) {
      this.$refs.videoControl.updateControl(direct);
    },
    // 是否在脱离视频栏
    onLeaveVideoWrap(flag) {
      if (this.isTeacher || this.isAssistant) {
        // 是老师则展示复位按钮
        this.$refs.videoControl.updateShowReset(flag);
      }
      // 设置用户视频清晰度
      TCIC.SDK.instance.setVideoEncodeQuality(this.userId, flag);
    },
    // 视频暂停/恢复
    onPauseRender() {
      return this.$refs.videoControl.onPauseRender();
    },
    onResumeRender() {
      return this.$refs.videoControl.onResumeRender();
    },
    onResetRender() {
      return this.$refs.videoControl.onResetRender();
    },
    // 设置缩小模式
    enableSmallMode(flag) {
      this.isSmallMode = flag;
      this.$refs.videoControl.enableSmallMode(flag);
    },
    // 操作麦克风
    enableMic(flag) {
      return this.$refs.videoControl.enableMic(flag);
    },
    // 操作摄像头
    enableCamera(flag) {
      return this.$refs.videoControl.enableCamera(flag);
    },
    // 打开屏幕分享
    enableScreenShare() {
      return this.$refs.videoControl.enableScreenShare();
    },
    // 操作摄像头
    disableScreenShare() {
      this.$refs.videoControl.disableScreenShare();
    },
    networkStatisticsHandler(netStatus) {
      // 公开课不需要显示网络状态
      if (TCIC.SDK.instance.isLiveClass()) {
        return;
      }
      // 本人
      if (this.userId === TCIC.SDK.instance.getUserId()) {
        this.translateQualityToStatus(netStatus.networkQuality);
      } else {
        // 其他用户
        netStatus.remoteStatisticsArray.forEach((item) => {
          if (this.userId === item.userId) {
            this.translateQualityToStatus(item.networkQuality);
            return;
          }
        });
      }
    },
    onClickVideo(user) {
      console.log(`===>>> : onClickVideo : ${user.userId}, ${user.camera}, ${user.cameraState}`);
      if (user.camera) {
        const isOpen = user.cameraState === TCIC.TDeviceStatus.Open;
        this.enableCamera(!isOpen);
      } else {
        window.showToast(i18next.t('摄像头权限已被{{arg_0}}禁用', { arg_0: this.roleInfo.teacher }), 'error');
      }
    },
    onClickAudio(user) {
      console.log(`===>>> : onClickVideo : ${user.userId}, ${user.mic}, ${user.micState}`);
      if (user.mic) {
        const isOpen = user.micState === TCIC.TDeviceStatus.Open;
        this.enableMic(!isOpen);
      } else {
        window.showToast(i18next.t('麦克风权限已被{{arg_0}}禁用', { arg_0: this.roleInfo.teacher }), 'error');
      }
    },
    translateQualityToStatus(networkQuality) {
      if (networkQuality >= 3) {
        this.netQuality = networkQuality;
      } else {
        this.netQuality = 0;
      }
    },
  },
};
</script>

<style lang="less">
.stage-video-component {
  .stage-video-container {
      width: 128px;
      height: 72px;
      margin: 4px 4px 0 4px;
      background: #ddd;
      border-radius: 8px;
      overflow: hidden;
      transform: rotate(0deg);
      background-image: url('./assets/video_empty.png');
      background-size: contain;
      background-repeat: no-repeat;
      .stage-swiper-video {
        width: 100%;
        height: 100%;
        &.no-video {
          width: 1px;
          height: 1px;
          overflow: hidden;
        }
      }
    }
    .stage-swiper-title {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 32px;
      margin: 0 4px;
      color: #fff;
      label,
      .stage-swiper-ic {
        flex: 1;
        text-align: left;
      }
      .stage-swiper-ic {
        display: flex;
        align-items: center;
        justify-content: space-around;
        .ic-video {
          position: relative;
          display: flex;
          width: 24px;
          height: 24px;
          background: url("./assets/ic_video.svg") no-repeat center;
          &.closed {
            background: url("./assets/ic_video_closed.svg") no-repeat center;
          }
        }
        .ic-record {
          position: relative;
          display: flex;
          width: 24px;
          height: 24px;
          background: url("./assets/ic_record.svg") no-repeat center;
          &.closed {
            background: url("./assets/ic_record_closed.svg") no-repeat center;
          }
        }
      }
    }
}
</style>
