<template>
  <div class="vod-player__wrap">
    <!-- 视频播放 - 老师端 -->
    <div
      v-if="isMC"
      class="vod-player center vod-player__blur"
    >
      <button
        v-if="video.playing"
        @click="pauseVideo"
      >
        <i class="ic_video_pause" />
      </button>
      <button
        v-else
        @click="playVideo"
      >
        <i class="ic_video_play" />
      </button>

      <div
        class="vod-player-progress"
        @mousedown="isMouseUp = false"
        @mouseup="isMouseUp = true"
      >
        <label class="video-time">{{ video.process || timeFormat }}</label>
        <el-slider
          v-model="video.process"
          :max="video.duration"
          class="video-slider"
          :show-tooltip="false"
          @change="seekVideo"
        />
        <label class="video-time">{{ video.duration || timeFormat }}</label>
      </div>

      <el-popover
        v-if="false"
        popper-class="board-back-bg volume-popover"
        placement="bottom"
        trigger="click"
      >
        <button
          slot="reference"
          :class="isVolumeActive? 'active':''"
          @click="toggleVolume"
        >
          <i class="ic_video_volume" />
        </button>
        <el-slider
          v-model="video.volume"
          :max="volumeUint"
          :min="0"
          class="video-slider-vertical"
          :show-tooltip="false"
          vertical
          height="80px"
          @change="setVideoVolume"
        />
      </el-popover>

      <button>
        <i
          :class="!isFullscreen ? 'ppt-tool__board-full' : 'ppt-tool__board-shrink'"
          @click="toggleFullscreen"
        />
      </button>
    </div>
    <div
      v-if="isMC"
      class="vod-player vod-close vod-player__blur"
    >
      <button @click="stopVideo">
        <i class="ic_video_close" />
      </button>
    </div>
    <!-- 视频播放 - 学生端 -->
    <div
      v-if="false"
      class="vod-player center vod-player__blur vod-player-student"
    >
      <div class="vod-player-progress">
        <label class="video-time">{{ video.process || timeFormat }}</label>
        <el-slider
          v-model="video.process"
          :max="video.duration"
          class="video-slider"
          :show-tooltip="false"
          disabled
        />
        <label class="video-time">{{ video.duration || timeFormat }}</label>
      </div>
    </div>
  </div>
</template>

<script>
import i18next from 'i18next';
import BaseComponent from '@core/BaseComponent';
import Constant from '@/util/Constant';

export default {
  name: 'WebVodToolComponent',
  filters: {
    timeFormat(ms) {
      const duration = parseInt(ms, 10);
      let minute = parseInt(duration / 60, 10);
      let sec = `${(duration % 60)}`;
      const isM0 = ':';
      if (minute === 0) {
        minute = '00';
      } else if (minute < 10) {
        minute = `0${minute}`;
      }
      if (sec.length === 1) {
        sec = `0${sec}`;
      }
      return minute + isM0 + sec;
    },
  },
  extends: BaseComponent,
  props: {},
  data() {
    return {
      isFullscreen: false,
      isVolumeActive: false,
      timer: 0,
      isTeacher: false,
      isAssistant: null,
      isMC: false, // 是否主持人
      taskId: 'webVideoPlayer',
      videoEleId: 'web-video-player',
      videoElement: null,
      sequence: '', // 播流成功后的ID
      volumeUint: 200,
      video: {
        duration: 0,
        process: 0,
        volume: 100,
        playing: false,
        status: 0, // stop 0; play 1; pause 2; loaded 3;
      },
      isMouseUp: true,
      liveEventArr: [],
    };
  },
  computed: {
    roleType() {
      let tempType = '';
      if (this.isTeacher) tempType = 'teacher';
      if (this.isAssistant) tempType = 'assistant';
      return tempType;
    },
  },
  mounted() {
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Joined_Class, true).then(() => {
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
    });
    // 全屏回调
    this.addLifecycleTCICStateListener(Constant.TStateFullScreen, (isFullscreen) => {
      this.isFullscreen = isFullscreen;
    });
    // 更新最初的状态
    TCIC.SDK.instance.getTasks(0).then((result) => {
      result.tasks.forEach((taskInfo) => {
        if (taskInfo.taskId === this.taskId) {
          this.onTaskUpdate(taskInfo);
        }
      });
    });
    // 任务更新
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Task_Updated, this.onTaskUpdate);
    // 添加触屏样式
    if (TCIC.SDK.instance.isMobile()) {
      // HTMLCollection 没有 forEach 方法
      const domList = document.getElementsByClassName('vod-player__blur');
      for (const dom of domList) {
        this.addLiveEventListener(dom, 'touchstart', () => {
          dom.classList.add('active');
        });
        this.addLiveEventListener(dom, 'touchend', () => {
          dom.classList.remove('active');
        });
      }
    }
    // 防止拖动到slider外部区域进度不动的问题
    document.addEventListener('mouseup', () => {
      this.isMouseUp = true;
    });
  },
  beforeDestroy() {
    this.liveEventArr.forEach((info) => {
      info.dom.removeEventListener(info.event, info.func);
    });
  },
  methods: {
    async loadVideo(url) {
      // 有播放中的音视频文件直接拦截提醒（视频切视频相对容易，音频切视频跨文件考虑的要多些）
      try {
        this.isMC = true;
        TCIC.SDK.instance.setState(TCIC.TMainState.Is_Web_Audio_Vod_MC, true);
        TCIC.SDK.instance.setState(Constant.TStateImmerseMode, true);

        await this.stopVideo();
        setTimeout(() => {
          TCIC.SDK.instance.setState(Constant.TStateWebVodPlayerVisible, true); // 显示当前组件
          this.createVideoElement(url);
        }, 500);
      } catch (err) {
        TCIC.SDK.instance.setState(Constant.TStateWebVodPlayerVisible, false);
        console.error('音视频课件-加载视频资源失败', err);
        window.showToast(i18next.t('加载视频资源失败'));
      }
    },
    getPlayerElement() {
      const playerComponent = TCIC.SDK.instance.getComponent('vod-player-component');
      return playerComponent.getVueInstance().getPlayerElement();
    },
    createVideoElement(url) {
      const playerElement = this.getPlayerElement();
      this.videoElement = document.createElement('video');
      this.videoElement.setAttribute('src', url);
      this.videoElement.setAttribute('id', this.videoEleId);
      this.videoElement.setAttribute('width', '100%');
      this.videoElement.setAttribute('height', '100%');
      this.videoElement.setAttribute('crossOrigin', 'anonymous');
      this.videoElement.setAttribute('autoplay', true);
      this.videoElement.setAttribute('loop', true);
      this.videoElement.addEventListener('loadedmetadata', () => {
        this.startVideo();
      });
      this.videoElement.addEventListener('error', (err) => {
        playerElement.removeChild(playerElement.firstElementChild);
        this.videoElement = null;
        TCIC.SDK.instance.setState(Constant.TStateWebVodPlayerVisible, false);
        console.error('音视频课件-加载视频资源失败', err);
        window.showToast(i18next.t('加载视频资源失败'));
      });

      playerElement.appendChild(this.videoElement);
    },
    // 开始播放
    async startVideo() {
      try {
        await TCIC.SDK.instance.startCaptureStream(this.videoEleId);
        this.getVideoPropsByLoad();
        this.getCurrentTimeByPlay();
        this.updateTask();
        TCIC.SDK.instance.setState(TCIC.TMainState.Web_Vod_Play, 0);
        console.info('音视频课件-vod-startVideo');
      } catch (err) {
        console.error('音视频课件-播放视频资源失败', err);
        window.showToast(i18next.t('播放视频资源失败'));
      }
    },
    // 停止播放
    async stopVideo() {
      if (!this.video.status) return;
      if (!this.isMC) return;

      try {
        const playerElement = this.getPlayerElement();
        clearInterval(this.timer);
        this.resetVideoData();
        await this.updateTask();
        await TCIC.SDK.instance.stopCaptureStream();
        playerElement.removeChild(playerElement.firstElementChild);
        this.videoElement = null;
        TCIC.SDK.instance.setState(Constant.TStateWebVodPlayerVisible, false);
        TCIC.SDK.instance.setState(TCIC.TMainState.Web_Vod_Play, 2);
        console.info('音视频课件-vod-stopVideo');
      } catch (err) {
        console.error('音视频课件-停止播放视频资源失败', err);
        window.showToast(i18next.t('停止播放视频资源失败'));
      }
    },
    // 暂停播放
    pauseVideo() {
      clearInterval(this.timer);
      this.video.playing = false;
      this.video.status = 2;
      this.videoElement.pause();
      this.updateTask();
    },
    // 恢复播放
    playVideo() {
      this.videoElement.play();
      this.getCurrentTimeByPlay();
      this.updateTask();
    },
    getVideoPropsByLoad() {
      this.video.duration = this.videoElement.duration;
      this.video.volume = this.videoElement.volume * this.volumeUint;
    },
    getCurrentTimeByPlay() {
      const { video } = this;
      video.playing = true;
      video.status = 1;
      clearInterval(this.timer);
      this.timer = setInterval(() => {
        this.video.process = this.videoElement.currentTime;
        // this.updateTask(); 只有主持人显示controls
      }, 1000);
    },
    resetVideoData() {
      this.video = this.$options.data().video;
      this.isMC = false;
      TCIC.SDK.instance.setState(TCIC.TMainState.Is_Web_Audio_Vod_MC, false);
    },
    // 播放进度
    seekVideo(value) {
      this.videoElement.currentTime = value;
      // this.updateTask();
    },
    // 音量控制
    toggleVolume() {
      this.isVolumeActive = !this.isVolumeActive;
      if (this.isVolumeActive) {
        this.addHideVolumeEvent();
      } else {
        this.removeHideVolumeEvent();
      }
    },
    addHideVolumeEvent() {
      // 点击空白区域，取消声音按钮的高亮状态
      document.addEventListener('mousedown', this.toggleVolume, true);
      document.addEventListener('touchstart', this.toggleVolume, true);
    },
    removeHideVolumeEvent() {
      document.removeEventListener('mousedown', this.toggleVolume, true);
      document.removeEventListener('touchstart', this.toggleVolume, true);
    },
    setVideoVolume(volume) {
      console.info('音视频课件-setVolume', volume / this.volumeUint);
      this.videoElement.volume = volume / this.volumeUint;
    },
    // 任务同步
    async updateTask() {
      if (!TCIC.SDK.instance.isTeacherOrAssistant()) return;

      const userId = TCIC.SDK.instance.getUserId();
      const content = JSON.stringify({
        ...this.video,
        roleType: this.roleType,
      });
      return TCIC.SDK.instance.updateTask(this.taskId, content, -1, false, userId);
    },
    onTaskUpdate(task) {
      if (task.taskId === this.taskId) console.info('音视频课件-onTaskUpdate webVideo1', task, this.sequence, !!this.sequence);
      TCIC.SDK.instance.promiseState(TCIC.TMainState.Joined_Class, true).then(() => {
        if (task.taskId !== this.taskId) return;

        if (task.status === 0) {
          console.info('音视频课件-onTaskUpdate-webVideo-刷新页面等场景，用户生命周期结束');
          TCIC.SDK.instance.setState(Constant.TStateWebVodPlayerVisible, false);
          this.sequence = '';
          TCIC.SDK.instance.setState(TCIC.TMainState.Web_Vod_Play, 2);
          return;
        }
        if (task.content) {
          this.video = JSON.parse(task.content);
          if (this.isTeacher && this.video.roleType === 'teacher') return;
          if (this.isAssistant && this.video.roleType === 'assistant') return;

          if (this.video) {
            console.info('音视频课件-onTaskUpdate webVideo2', task.content);
            if (this.video.status === 0) {
              TCIC.SDK.instance.setState(Constant.TStateWebVodPlayerVisible, false);
              this.sequence = '';
              TCIC.SDK.instance.setState(TCIC.TMainState.Web_Vod_Play, 2);
              return;
            }
            TCIC.SDK.instance.setState(Constant.TStateWebVodPlayerVisible, true);
            TCIC.SDK.instance.setState(Constant.TStateImmerseMode, true);
            if (!this.sequence) {
              this.sequence = TCIC.SDK.instance.bindRemoteVideoDom(
                task.bindingUser,
                TCIC.TTrtcVideoStreamType.Vod,
                this.getPlayerElement(),
              );
              TCIC.SDK.instance.setState(TCIC.TMainState.Web_Vod_Play, 0);
            }
          }
        } else {
          TCIC.SDK.instance.setState(Constant.TStateWebVodPlayerVisible, false);
        }
      });
    },

    addLiveEventListener(dom, event, func) {
      dom.addEventListener(event, func);
      this.liveEventArr.push({
        dom,
        event,
        func,
      });
    },
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen;
      TCIC.SDK.instance.setState(Constant.TStateFullScreen, this.isFullscreen);
    },
  },
};

</script>

<style lang="less">

@--color-primary: #006EFF;
@--color-public: #14181D;
@--color-disable: #b9bbbc;
@--color-back: #1C2131;

.ic_video_pause {
  background: url('./assets/ic_video_pause.svg') no-repeat center;

  &:hover {
    background: url('./assets/ic_video_pause_hover.svg') no-repeat center;
  }
}
.ic_video_play {
  background: url('./assets/ic_video_play.svg') no-repeat center;

  &:hover {
    background: url('./assets/ic_video_play_hover.svg') no-repeat center;
  }
}
.ic_video_volume {
  background: url('./assets/ic_video_volume.svg') no-repeat center;

  &:hover {
    background: url('./assets/ic_video_volume_hover.svg') no-repeat center;
  }
}
.ic_video_close {
  background: url('./assets/ic_video_close.svg') no-repeat center;

  &:hover {
    background: url('./assets/ic_video_close_hover.svg') no-repeat center;
  }
}

/*工具内容选择面板*/
.el-popover.vod-player__back-popper {
  padding: 0;
  background: transparent;
  border: none;
  box-shadow: none;
}

.vod-player__back-popper {
  .vod-player__board-drop {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    background: @--color-back;
    color: #fff;
  }

  .vod-player__list-popper {
    margin-bottom: 10px;
    overflow: hidden;

    .el-scrollbar__wrap {
      max-height: 255px;
      margin-bottom: 0px !important;
    }

    ul {
      li {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: 56px;
        padding-left: 16px;
        padding-right: 16px;
        border-bottom: 1px solid rgba(#eee, .1);
        font-size: 16px;
        color: #fff;

        &:last-child {
          border-bottom: 0
        }

        &:hover {
          label {
            color: @--color-primary;
          }
        }

        &.vod-player__li-active {
          background: @--color-primary;

          &:hover {
            label {
              color: #fff;
            }
          }
        }

        label {
          flex: 1;
          max-width: 176px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        i {
          margin-right: 10px;
          width: 24px;
          height: 24px;
        }

        i.vod-player__btn-close {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 20px;
          height: 20px;
          margin-left: 10px;
          border-radius: 4px;
          opacity: 0.3;
          background: rgba(0, 0, 0, .05);

          &:hover {
            opacity: 1
          }
        }
      }
    }
  }
}

.vod-player__wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  pointer-events: none;

  .vod-player {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 6px 10px;
    border-radius: 10px;
    //background: rgba(0,0,0,0.3); // V1.3.3更改之前
    //filter: blur(2px); // 模糊效果，预期是模糊图层下面的块，暂时用.filter-blur-bg
    opacity: 0.5; // 后期应该是会去掉渐隐这个效果
    background: linear-gradient(141deg, rgba(21, 27, 48, 0.7) 0%, rgba(28, 33, 49, 0.9) 100%);
    box-shadow: 0 0 3px 0 rgba(32, 32, 32, 0.4);
    overflow: hidden;
    pointer-events: auto;
    margin-right: 8px;

    &.vod-close {
      width: 80px;
    }

    &.vod-player__blur {
      transition: opacity 1s;
      transition-delay: 3s;

      &.active {
        opacity: 1;
        transition: all 0s;
      }

      &:hover {
        opacity: 1;
        transition: all 0s;
      }
    }

    &.close {
      width: 72px;
      height: 72px;
      top: 24px;
      right: 24px;
      padding: 6px;
    }

    &.right {
      left: 80%;
      width: 140px;
    }

    &.center {
      &.vod-player-student {
        left: 50%;
        right: auto;
        transform: translateX(-50%);
      }

      button.vod-player__btn-board-list {
        position: relative;
        display: flex;
        align-items: center;
        margin-left: 20px;

        &:before {
          left: -8px;
          width: 1px;
          height: 24px;
          opacity: 0.2;
          border-left: 1px solid #fff;
          background: #fff;
          content: "";
        }
      }
    }

    &.vod-player-student {
      display: flex;
      align-items: center;
      width: 304px;
      padding: 22px 0;
      .vod-player-progress {
        position: relative;
        z-index: 9;
        background: none;
        .el-slider {
          &__button-wrapper {
            display: none;
          }
        }
        .el-slider__runway {
          &:before {
            cursor: default;
          }
        }
        .el-slider__runway.disabled .el-slider__bar {
          background: @--color-primary;
        }
      }
    }

    button {
      position: relative;
      z-index: 9;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      width: 60px;
      height: 60px;
      border-radius: 4px;
      padding: 0;
      background: none;
      outline: none;

      &.active {
        transition: all .4s;
        background: @--color-primary;
        &:hover {
          .ic_video_volume {
            background: url('./assets/ic_video_volume.svg') no-repeat center;
          }
        }
      }

      i {
        display: flex;
        width: 40px;
        height: 40px;
      }
    }

    // 视频工具栏
    .vod-player-progress {
      display: flex;
      align-items: center;
      width: 303px;
      height: 40px;
      padding: 0 16px;
      margin: 0 6px;
      border-radius: 4px;
      background: rgba(0, 0, 0, 0.2);
      .video-slider {
        flex: 1;
        margin: 0 10px;
        .el-slider__runway .el-slider__bar,
        .el-slider__runway:before {
          height: 6px;
        }
      }
      .video-time {
        font-size: 20px;
        color: #fff;
        flex-shrink: 0;
      }
    }
  }
}

// 视频工具栏 - 音量
.volume-popover {
  min-width: inherit !important;
  width: 60px !important;
  height: 112px;
  .video-slider-vertical {
    .el-slider__runway {
      background: #dfdfdf;
      margin: 0;
      &:before {
        top: inherit;
      }
      .el-slider__bar {
        top: inherit;
      }
    }
  }
}
</style>
