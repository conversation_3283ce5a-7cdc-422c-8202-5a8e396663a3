<template>
  <div
    :class="['shortcut-toolbar-component', { 'mobile-layout': isMobile, 'expand': showShortcutContent }]"
    :style="'width:' + comWidth"
    @mouseenter.capture="onMouseOver"
    @mousemove.capture="onMouseOver"
    @mouseleave.capture.self="onMouseLeave"
  >
    <div
      class="toolbar-header__wrap"
    >
      <div class="toolbar-header__float-left">
        <div
          v-show="showShortcutContent"
          class="toolbar-float__class-info"
        >
          <i
            class="icon"
          />
        </div>
        <div class="toolbar-float__network">
          <div
            v-for="n in 5"
            :key="n"
            class="network-quality"
            :class="[ n <= 6 - uplinkNetworkQuality ? 'active' : 'inactive']"
            :style="{height: `${n * 20}%` }"
          />
        </div>
      </div>
      <!-- 上层的花名册和消息按钮结束 -->
      <div
        v-if="!showShortcutContent"
        class="toolbar-header__title"
      >
        <span class="class-title">{{ screenShareStatus === 0 ? $t('您正在屏幕共享') : $t('屏幕共享已暂停') }}</span>
      </div>
      <div
        v-show="showShortcutContent"
        class="toolbar-header__float-right"
      >
        <div class="toolbar-header__float-right--status">
          <span class="icon" />
          <span class="text">{{ $t('roomStatusDesc.hasStartedWithExtraInfo', roomInfo) }}</span>
        </div>
        <div class="toolbar-header__float-right--time">
          <span> {{ classDuration | durationFilter }} </span>
          <span class="count">({{ classOnlineMemberText }})</span>
        </div>
      </div>
    </div>
    <span
      v-show="showShortcutContent"
      class="divider"
    />
    <div
      class="toolbar-content__wrap"
      :class="[{ collapse: !showShortcutContent }]"
    >
      <div
        v-for="menu in menuList"
        :key="menu.key"
        :class="[
          {
            'toolbar-content__item-media': menu.type !== 'divider',
            'toolbar-content__item-divider': menu.type === 'divider',
            'hide': !menu.availbale
          }
        ]"
        @click="handleMenuClick(menu)"
        @mouseenter="handleMenuMouseEnter(menu)"
        @mouseleave="handleMenuMouseLeave(menu)"
      >
        <div
          v-if="menu.type !== 'divider'"
          :ref="menu.key"
          :class="[
            'content-item__block',
            menu.key,
            {open: menu.open,
             close: !menu.open
            },
          ]"
        >
          <div
            v-show="(menu.key === 'message' || menu.key === 'handsup') && getUnreadCount(menu.key) > 99"
            class="circle-total__tips"
            :class="{ active: !!getUnreadCount(menu.key) }"
          >
            {{ getUnreadCount(menu.key) > 99 ? '99+' : getUnreadCount(menu.key) }}
          </div>
          <div
            v-show="(menu.key === 'message' || menu.key === 'handsup') && getUnreadCount(menu.key) <= 99"
            class="circle-total__tip"
            :class="{ active: !!getUnreadCount(menu.key) }"
          >
            {{ getUnreadCount(menu.key) > 99 ? '99+' : getUnreadCount(menu.key) }}
          </div>
        </div>
        <span class="toolbar-content__item-sub-title">{{ menu.label }}</span>
      </div>
      <!--已经上课才显示屏幕分享的控制 -->
      <!-- <el-tooltip
        v-if="classStatusText === ''"
        class="item"
        :content="$t('暂停/继续')"
        :disabled="enableTooltip"
        placement="bottom"
      >
        <div
          class="toolbar-content__item share-status-item"
          @click="pauseOrResumeShare"
        >
          <div
            v-if="screenShareStatus === 0"
            class="content-item__block share-status-item__icon"
          />
          <div
            v-if="screenShareStatus === 1"
            class="content-item__block share-status-item__icons"
          />
          <span class="toolbar-content__item-sub-title">{{ screenShareStatus === 1 ? $t('继续共享') : $t('暂停共享') }}</span>
        </div>
      </el-tooltip>
      <el-tooltip
        v-if="isTeacher && !isCoTeachingClass"
        ref="roster-tooltip-ref"
        class="item"
        :content="roomInfo.memberList"
        :disabled="enableTooltip"
        placement="bottom"
      >
        <div
          ref="memberListBtn"
          class="toolbar-content__item roster-item"
          @click="showRosterDialog"
        >
          <div
            class="content-item__block roster-item__icon"
          />
          <span class="toolbar-content__item-sub-title">{{ $t('花名册') }}</span>
        </div>
      </el-tooltip>
      <el-tooltip
        v-if="!isCoTeachingClass"
        ref="tim-tooltip-ref"
        class="item"
        :content="$t('消息')"
        :disabled="enableTooltip"
        placement="bottom"
      >
        <div
          ref="messageBtn"
          class="toolbar-content__item message-items"
          @click="showMessageDialog"
        >
          <div
            class="content-item__block message-item__icon"
            :class="{ checked: !!unReadCount }"
          >
            <div
              v-show="unReadCount > 99"
              class="circle-total__tips"
              :class="{ active: !!unReadCount }"
            >
              {{ unReadCount > 99 ? '99+' : unReadCount }}
            </div>
            <div
              v-show="unReadCount <= 99"
              class="circle-total__tip"
              :class="{ active: !!unReadCount }"
            >
              {{ unReadCount > 99 ? '99+' : unReadCount }}
            </div>
          </div>
          <span class="toolbar-content__item-sub-title">{{ $t('消息') }}</span>
        </div>
      </el-tooltip>
      <div
        class="toolbar-content__item more-item"
        @mouseenter="onMoreMouseOver()"
        @mouseleave="onMoreMouseLeave()"
      >
        <div class="content-item__block more-item__icon" />
        <span class="toolbar-content__item-sub-title">{{ $t('更多操作') }}</span>
      </div> -->
      <div class="end-share-container">
        <el-button
          size="small"
          type="danger"
          class="close-share-btn"
          @click="stopShare"
        >
          {{ $t('结束共享') }}
        </el-button>
      </div>
    </div>

    <el-popover
      ref="popover"
      trigger="manual"
      :width="800"
      @show="onPopOverShow"
      @hide="onPopOverHide"
    >
      <ShareSources
        ref="screen-sources"
        @hide="onHide"
      />
    </el-popover>
    <div
      id="shortcut-panel__wrap"
      class="shortcut-panel__wrap"
    >
      <div
        class="shortcut-panel more-shortcut-panel"
        :class="[{ 'show-panel': isShowMorePanel }, { 'coteaching-fix': isCoTeachingClass }]"
        @mouseenter="onMoreMouseOver()"
        @mouseleave="onMoreMouseLeave()"
      >
        <div class="shortcut-panel__arrow" />
        <ul class="shortcut-panel__more-list">
          <li
            v-if="!isLive && isTeacher"
            ref="muteall"
            class="more-item share-device-audio__toggle"
            style="margin-bottom: 10px"
            @click="openClockTool"
          >
            <div class="device-audio-toggle__label">
              <i class="clock_tool" />{{ $t('计时器') }}
            </div>
          </li>
          <li
            v-if="!isLive && isTeacher"
            ref="muteall"
            class="more-item share-device-audio__toggle"
            style="margin-bottom: 10px"
            @click="openTimeTool"
          >
            <div class="device-audio-toggle__label">
              <i class="time_tool" />{{ $t('定时器') }}
            </div>
          </li>
          <li
            v-if="!isLive && isTeacher"
            ref="muteall"
            class="more-item share-device-audio__toggle"
            @click="openQuiz"
          >
            <div class="device-audio-toggle__label">
              <i class="quiz_tool" />{{ $t('答题器') }}
            </div>
          </li>
        </ul>
      </div>
      <div
        v-show="showShortcutToolbarTip"
        class="shortcut-panel tip-shortcut-panel"
      >
        <div class="shortcut-panel__arrow" />
        <ul class="shortcut-panel__tip-list">
          <li class="tip-item">
            {{ $t('顶部工具栏都在这里，鼠标移入时，会自动展开') }}
          </li>
        </ul>
      </div>
      <HandUpIcon
        ref="handupIcon"
        :show-hands-up-icon="false"
        @onShow="onPopOverShow"
        @onHide="onPopOverHide"
      />
    </div>
  </div>
</template>

<script>
import i18next from 'i18next';
import Util from '@/util/Util';
import Constant from '@/util/Constant';
import BaseComponent from '@/component/core/BaseComponent';
import VideoCtrlBaseComponent from '../video-component/VideoCtrlBase';
import DeviceUtil from '../../../util/DeviceUtil';
import ShareSources from '@vueComponent/share-sources-component/ShareSources.vue';
import HandUpIcon from '@vueComponent/hand-up-member-component/HandUpIcon.vue';

export default {
  filters: {
    // classDuration过滤器
    durationFilter(duration) {
      return duration && duration > 0
        ? Util.formatDuration(duration, 'HH:mm:ss')
        : '';
    },
  },
  components: {
    ShareSources,
    HandUpIcon,
  },
  extends: BaseComponent,
  data() {
    return {
      micStatus: 'close',
      cameraStatus: 'close',
      enableTooltip: false, // 禁用tooltip
      classDuration: 0, // 开始上课时长
      classStatus: 0, // 0为未开始，1为正在上课，2为已结束
      screenShareStatus: 0, // 0  分享中 1 暂停分享 2 分享结束
      permissionMembers: [], // 当前具有权限的人数
      uplinkNetworkQuality: 0, // 网络质量 0：未知（连接未建立） 1 极佳 2 较好 3 一般 4 较差 5 极差
      showShortcutToolbarTip: false, // 显示工具栏的提示
      showShortcutContent: false, // 显示工具栏内容
      isMobile: TCIC.SDK.instance.isMobile(), // 是否是手机
      classInfo: null, // 课堂信息
      classTopic: '您正在共享屏幕', // 课堂标题
      isShowMorePanel: false, // 显示更多的面板
      isShareSystemAudio: false, // 是否分享系统声音
      toggleMorePanelTask: null, // 切换更多面板的任务
      muteAll: false, // 全员静音状态  false 全员非静音  true 全员静音
      comWidth: '150px', // 组件宽度
      unReadCount: 99, // 消息未读数
      isLive: false,
      isTeacher: false, // 是否是老师
      isCoTeachingClass: false, // 是否是双师课
      classOnlineMemberText: '',
      isShowScreenSharePanel: false,
      selfUserId: 0,
      handUpCount: 0, // 举手人数
      roleInfo: {},
      roomInfo: {},
      menuList: [
        {
          key: 'mic',
          open: true,
          label: i18next.t('关闭麦克风'),
          availbale: true,
        },
        {
          key: 'camera',
          open: true,
          label: i18next.t('关闭摄像头'),
          availbale: true,
        },
        {
          key: 'divider-1',
          type: 'divider',
          availbale: true,
        },
        {
          key: 'message',
          open: true,
          label: i18next.t('消息'),
          availbale: true,
        },
        {
          key: 'roaster',
          open: true,
          label: i18next.t('花名册'),
          availbale: true,
        },
        {
          key: 'handsup',
          open: true,
          label: i18next.t('举手'),
          availbale: true,
        },
        {
          key: 'tools',
          open: true,
          label: i18next.t('工具箱'),
          availbale: true,
        },
        {
          key: 'divider-2',
          type: 'divider',
          availbale: true,

        },
        {
          key: 'newShare',
          open: true,
          label: i18next.t('新的共享'),
          availbale: true,
        },
        {
          key: 'pauseOrResumShare',
          open: true,
          label: i18next.t('暂停共享'),
          availbale: true,
        },
      ],
    };
  },

  computed: {
    // 课堂状态
    classStatusText() {
      // 课堂状态；0为未开始，1为正在上课，2为已结束
      let text = '';
      switch (this.classStatus) {
        case 0:
          text = i18next.t('未开始');
          break;
        case 2:
          text = i18next.t('已结束');
          break;
        case 1: // 上课中需要显示为上课的时间，则直接返回‘’
        default:
          text = '';
          break;
      }
      return text;
    },
  },
  watch: {
    isShowMorePanel(show) {
      this.$nextTick(() => {
        if (show) {
          // 添加子按钮
          window.tbm.updateTarget('screentool', [window.tbm.generateNode(this.$refs.muteall)], 'muteall');
          window.tbm.updateTarget('screentool', [window.tbm.generateNode(this.$refs.systemaudio)], 'systemaudio');
        } else {
          window.tbm.updateTarget('screentool', [], 'muteall');
          window.tbm.updateTarget('screentool', [], 'systemaudio');
        }
      });
    },
    showShortcutContent(val) {
      if (!val) {
        this.comWidth = '150px';
      } else {
        this.comWidth = this.isTeacher ? '700px' : '546px';
      }
    },
  },

  mounted() {
    const { roleInfo, roomInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    this.roomInfo = roomInfo;
    this.selfUserId = TCIC.SDK.instance.getUserId();
    console.log('[ShareToolbar] mounted]');
    // 监听 Electron 主动发来的屏幕暂停、恢复通知
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Electron_Screen_Share_Pause, () => {
      this.pauseScreenShare(i18next.t('屏幕共享已暂停，请将您共享的窗口置于最上方'));
    });
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Electron_Screen_Share_Resume, this.resumeScreenShare);

    // 监听屏幕共享状态
    this.addLifecycleTCICStateListener(TCIC.TMainState.Screen_Share, this.toggleScreenShareModeHanlder);
    // 监听网络质量
    this.addLifecycleTCICEventListener(TCIC.TTrtcEvent.Network_Quality, this.networkQualityHandler);
    // 监听课堂信息
    this.addLifecycleTCICStateListener(TCIC.TMainState.Message_Unread_Count, this.messageUnreadCountChangeHandler);

    // 监听全员静音状态
    this.addLifecycleTCICStateListener(TCIC.TMainState.Mute_All, (value) => {
      this.muteAll = value;
    });
    this.addLifecycleTCICStateListener(
      TCIC.TMainState.Hand_Up_Member_List,
      (members) => {
        // 只有老师关心举手列表
        if (this.isTeacher || this.isAssistant || this.isSupervisor) {
          this.handUpCount = members.length;
        }
      },
    );

    // 课堂状态变更
    // TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start).then(() => {
    //   this.classStatus = TCIC.TClassStatus.Already_Start;
    //   const permissionList = TCIC.SDK.instance.getPermissionList();
    //   if (!this.isScreenShareOpen) {
    //     TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Visible, true).then(() => {
    //       this.processScreenShare(permissionList);
    //     });
    //   }
    //   console.log('[::this.classState] ', this.classState, permissionList);
    // });
    // 监听课堂状态变更
    this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, (status) => {
      console.log('[ShareToolbar] mounted] TCIC.TMainState.Class_Status', status);
      this.classStatus = status;
    });

    // 监听课堂时长
    this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Duration, (duration) => {
      this.classDuration = duration;
    });

    // 监听是否采集系统声音
    this.addLifecycleTCICStateListener(Constant.TStateEnableSystemAudioLoopback, (status) => {
      this.isShareSystemAudio = status;
    });

    // 初始化数据
    this.initData();
    const isAdvanceMode = TCIC.SDK.instance.isFeatureAvailable('ScreenShareAdvanceMode');
    // console.log('share ToolBar ------------------> ', isAdvanceMode);
    if (TCIC.SDK.instance.isCoTeachingClass()) {    // 双师课始终显示
      this.showShortcutContent = true;
    } else if (isAdvanceMode) {
      // 显示快捷面板的提示
      this.showShortcutToolbarTipTask();
    } else {
      // 简单模式下不收起工具条
      this.showShortcutContent = !isAdvanceMode;
    }

    // 屏幕共享顶部工具栏可拖动
    if (TCIC.SDK.instance.isElectron()) {
      this.toggleComponentDrag(true, '.shortcut-toolbar-component', null, false);
    }

    // 监听在线人数
    this.addLifecycleTCICStateListener(TCIC.TMainState.Member_Count, (onlineNumber) => {
      const num =  onlineNumber ?? 0;
      this.classOnlineMemberText = i18next.t('{{arg_0}}人在线', { arg_0: num });
    });

    this.addLifecycleTCICEventListener(Constant.TStateLoadingMicState, async ({ userId, type, value }) => {
      if (userId !== this.selfUserId) {
        return;
      }
      if (type === 'loadingOwnMic') {
        if (typeof value.open === 'boolean') {
          const deviceStatus = TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Device_Status);
          // eslint-disable-next-line vue/no-mutating-props, no-nested-ternary
          const micCtrlStatus = value.open ? 'open' : (
              TCIC.SDK.instance.isDeviceAbnormal(deviceStatus) ? 'error' : 'close'
          );
          this.micStatus = micCtrlStatus;
          this.menuList = this.menuList.map((i) => {
            if (i.key === 'mic') {
              i.label = value.open ? i18next.t('关闭麦克风') : i18next.t('开启麦克风');
              i.open = value.open;
            }
            return i;
          });
        }
      }
    });

      this.addLifecycleTCICEventListener(Constant.TStateLoadingCameraState, ({ userId, type, value }) => {
        if (userId !== this.selfUserId) {
          return;
        }
        if (type === 'loadingOwnCamera') {
          if (typeof value.open === 'boolean') {
            const deviceStatus = TCIC.SDK.instance.getState(TCIC.TMainState.Video_Device_Status);
            // eslint-disable-next-line vue/no-mutating-props, no-nested-ternary
            const cameraCtrlStatus = value.open ? 'open' : (
              TCIC.SDK.instance.isDeviceAbnormal(deviceStatus) ? 'error' : 'close'
            );
            this.cameraStatus = cameraCtrlStatus;
            this.menuList = this.menuList.map((i) => {
              if (i.key === 'camera') {
                i.label =  value.open ? i18next.t('关闭摄像头') : i18next.t('开启摄像头');
                i.open = value.open;
              }
              return i;
            });
          }
        }
      });
  },
  beforeDestroy() {
    if (this.mouseInOutTask) {
      clearTimeout(this.mouseInOutTask);
    }
  },

  methods: {
    getUnreadCount(key) {
      if (key === 'message') {
        return this.unReadCount;
      }
      if (key === 'handsup') {
        return this.handUpCount;
      }
    },
    handleMenuClick(menu) {
      const { key, open } = menu;
      const menuHandlList = {
        camera: this.handleCamera,
        mic: this.handleMic,
        message: this.showMessageDialog,
        muteAll: this.controlAllMicStatus,
        roaster: this.showRosterDialog,
        systemAudio: this.onShareSystemAudio,
        pauseOrResumShare: this.pauseOrResumeShare,
        handsup: this.handleHandsUp,
        newShare: this.newShare,
      };
      if (menuHandlList[key]) {
        menuHandlList[key](open);
      }
    },
    async openQuiz() {
      const quizComponent = TCIC.SDK.instance.getComponent('quiz-component');
      quizComponent.getVueInstance().render();
    },
    async openClockTool() {
      if (!TCIC.SDK.instance.getComponent('clock-tool-component')) {
        await TCIC.SDK.instance.loadComponent('clock-tool-component');
      }
      TCIC.SDK.instance.updateComponent('clock-tool-component', {
        display: 'block',
      });
    },
    async openTimeTool() {
      if (!TCIC.SDK.instance.getComponent('timer-tool-component')) {
        await TCIC.SDK.instance.loadComponent('timer-tool-component');
      }
      TCIC.SDK.instance.updateComponent('timer-tool-component', {
        display: 'block',
        zIndex: 1000,
      });
    },
    handleMenuMouseEnter(menu) {
      if (menu.key === 'tools') {
        this.onMoreMouseOver();
      }
    },
    handleMenuMouseLeave(menu) {
       if (menu.key === 'tools') {
        this.onMoreMouseLeave();
      }
    },
    newShare() {
      this.$refs.popover.doShow();
    },
    handleMic() {
      const isMicOpen = this.micStatus === 'open';
      TCIC.SDK.instance.muteLocalAudio(isMicOpen);
    },
    handleHandsUp() {
      console.log('[ShareToolbar] handleHandsUp]', this.$refs.handupIcon);
      this.$refs.handupIcon.handupIconHandler();
    },
    handleCamera()  {
      const isCameraOpen = this.cameraStatus === 'open';
      if (isCameraOpen) {
         DeviceUtil.toggleLocalDeviceWithLoadingEvent(
            'camera',
            false,
            () => TCIC.SDK.instance.stopLocalVideo(),
            {
              caller: 'ShareToolBar',
              reason: 'enableCamera-false',
              reportAction: 'stopLocalVideo',
            },
          );
      } else {
        DeviceUtil.toggleLocalDeviceWithLoadingEvent(
          'camera',
          true,
          () => TCIC.SDK.instance.startLocalVideo(this.$refs.video),
          {
            caller: 'VideoComponent',
            reason: 'enableCamera-true',
            reportAction: 'startLocalVideo',
          },
        );
      }
    },

    onPopOverShow() {
      TCIC.SDK.instance.updateComponent('share-toolbar-component', {
        height: '100%',
      });
      this.enableDrag(false);
      this.isShowScreenSharePanel = true;
      this.$refs['screen-sources'].notifyVisibilityChange(true);
    },
    onPopOverHide() {
      TCIC.SDK.instance.updateComponent('share-toolbar-component', {
        height: '300px',
      });
      this.enableDrag(true);
      this.isShowScreenSharePanel = false;
      this.$refs['screen-sources'].notifyVisibilityChange(false);
    },
    onHide() {
      this.$refs.popover.doClose();
      setTimeout(() => {
        TCIC.SDK.instance.setState(TCIC.TMainState.Screen_Share, 0);
      }, 500);
    },
    enableDrag(val) {
      this.toggleComponentDrag(val, '.shortcut-toolbar-component', null, false);
    },
    onComponentVisibilityChange(visible) {
      const isAdvanceMode = TCIC.SDK.instance.isFeatureAvailable('ScreenShareAdvanceMode');
      if (visible) {
        // 加载屏幕分享下的花名册窗口
        TCIC.SDK.instance.loadComponent('member-list-component', {
          width: '702px',
          height: 'auto',
          zIndex: 350,
        });
        // 加载屏幕分享下的IM聊天窗口
        TCIC.SDK.instance.loadComponent('float-im-component', {
          width: '400px',
          height: '680px',
          zIndex: 350,
        });

        if (!isAdvanceMode) {
          TCIC.SDK.instance.updateComponent('screen-preview-component', {
            left: 'calc(100vw - 176px - 10px)',
            top: 'calc(100vh - 132px - 10px)',
            width: '176px',
            height: '132px',
            display: 'block',
            zIndex: '301',
          });
        }
        this.$nextTick(() => this.updateTbmButtons());
      } else {
        // 销毁IM聊天窗口
        // 此处移除后，否则恢复到正常非分享页面时，无法加加IM组件。改为隐藏
        // TCIC.SDK.instance.removeComponent('float-im-component');
        TCIC.SDK.instance.updateComponent('float-im-component', {
          display: 'none',
        });

        // 销毁花名册窗口
        TCIC.SDK.instance.removeComponent('member-list-component');

        if (!isAdvanceMode) {
          TCIC.SDK.instance.updateComponent('screen-preview-component', {
            display: 'none',
          });
        }
        window.tbm.clearTarget('screentool');
      }
    },

    // --------------------------------------   methods start -------------------------------------
    /**
     * 初始化数据
     */
    initData() {
      this.makeSureClassJoined(() => {
        // 当前课堂信息
        this.classInfo = TCIC.SDK.instance.getClassInfo();
        this.isLive = TCIC.SDK.instance.isLiveClass();
        this.isCoTeachingClass = TCIC.SDK.instance.isCoTeachingClass();
        if (this.isCoTeachingClass) {
          this.comWidth = '250px';
        }

        this.isTeacher = TCIC.SDK.instance.isTeacher();

        // 当前课堂状态
        this.classStatus = this.classInfo.status;

        // 获取当前具有权限的成员列表
        this.permissionMembers = TCIC.SDK.instance.getPermissionList();

        this.menuList = this.menuList.map((i) => {
          if (i.key === 'roaster' || i.key === 'handsup' || i.key === 'tools') {
            i.availbale = this.isTeacher && !this.isCoTeachingClass;
          }
          if (i.key === 'message') {
            i.availbale = !this.isCoTeachingClass;
          }
          return i;
        });
      });
    },

    /**
     * 消息未读数变更回调
     */
    messageUnreadCountChangeHandler(count) {
      this.unReadCount = count;
      if (this.mouseInOutTask) {
        clearTimeout(this.mouseInOutTask);
      }
      this.showShortcutContent = true;
      this.mouseInOutTask = setTimeout(() => {
        this.showShortcutContent = false;
      }, 3000);
    },

    // 监听到屏幕分享后
    toggleScreenShareModeHanlder(value) {
      // 分享状态 0 分享中 1 暂停分享 2 未开始分享/分享结束
      const shareFromPPt = TCIC.SDK.instance.getState(Constant.TStatePPtSharingState, false);
      if (value === 2 && shareFromPPt) return;

      this.screenShareStatus = value;
      this.menuList = this.menuList.map((i) => {
        if (i.key === 'pauseOrResumShare') {
          i.label = value === 1 ? i18next.t('继续共享') : i18next.t('暂停共享') ;
          i.open = value !== 1;
        }
        return i;
      });
    },

    /**
     * 网络质量变更事件处理
     */
    networkQualityHandler({ uplinkNetworkQuality }) {
      this.uplinkNetworkQuality = uplinkNetworkQuality;
    },

    // 显示快捷工具栏的提示任务
    showShortcutToolbarTipTask() {
      // 展示顶部工具栏初次进入的提示语
      this.showShortcutToolbarTip = true;
      // 展示快捷面板的详细内容
      this.showShortcutContent = true;
      setTimeout(() => {
        // 隐藏顶部工具栏初次进入的提示语
        this.showShortcutContent = false;
        // 隐藏快捷面板的详细内容
        this.showShortcutToolbarTip = false;
      }, 3000);
    },

    // 如果鼠标移入，则清空鼠标移出隐藏提示框的任务
    onMouseOver() {
      clearTimeout(this.mouseInOutTask);
      this.showShortcutContent = true;
    },

    // 如果鼠标移出，则启动鼠标移除隐藏任务
    onMouseLeave() {
      if (this.isShowScreenSharePanel) {
        return;
      }
      const isAdvanceMode = TCIC.SDK.instance.isFeatureAvailable('ScreenShareAdvanceMode');
      if (isAdvanceMode) {
        clearTimeout(this.mouseInOutTask);
        this.mouseInOutTask = setTimeout(() => {
          this.showShortcutContent = false;
        }, 200);
      }
    },

    /**
     * 显示更多的详细面板
     */
    onMoreMouseOver() {
      this.isShowMorePanel = true;
      clearTimeout(this.toggleMorePanelTask);
    },

    /**
     * 隐藏更多的详细面板
     */
    onMoreMouseLeave() {
      clearTimeout(this.toggleMorePanelTask);
      this.toggleMorePanelTask = setTimeout(() => {
        this.isShowMorePanel = false;
      }, 200);
    },

    // 屏幕共享混合系统声音
    onShareSystemAudio() {
      this.isShareSystemAudio = !this.isShareSystemAudio;
      TCIC.SDK.instance.enableSystemAudioLoopback(this.isShareSystemAudio);
      TCIC.SDK.instance.setState(
        Constant.TStateEnableSystemAudioLoopback,
        this.isShareSystemAudio,
      );
    },

    // 显示花名册窗口
    showRosterDialog() {
      // 修复屏幕分享下，打开花名册窗口，点击花名册区域外，再点击花名册区域内，tooltip莫名其妙获得焦点，重新出现
      // this.$refs['roster-tooltip-ref'].$el.blur();
      if (TCIC.SDK.instance.getState(TCIC.TMainState.Network_Broken)) return;  // 网络不可用时禁止操作
      const layout = TCIC.SDK.instance.getComponentLayout('member-list-component');
      const visible = layout.display === 'block';
      let newLayout = {
        display: visible ? 'none' : 'block',
      };
      if (!layout.left || !layout.top) {
        const isAdvanceMode = TCIC.SDK.instance.isFeatureAvailable('ScreenShareAdvanceMode');
        if (isAdvanceMode) {
          const memberListBtnRect = this.$refs.roaster[0].getBoundingClientRect();
          newLayout = Object.assign(newLayout, {
            left: `calc(${memberListBtnRect.right}px - 473px)`,
            top: `calc(${memberListBtnRect.bottom}px + 50px)`,
          });
        } else {  // 简单模式下特殊布局
          newLayout = Object.assign(newLayout, {
            left: '3px',
            top: '153px',
          });
        }
      }
      TCIC.SDK.instance.updateComponent('member-list-component', newLayout);
      TCIC.SDK.instance.getComponent('member-list-component')?.getVueInstance()
        .notifyVisibilityChange(true);
      TCIC.SDK.instance.getComponent('member-list-component')?.getVueInstance()
        .updateMemberList();
    },

    // 显示消息窗口
    showMessageDialog() {
      // 修复屏幕分享下，打开消息窗口，点击消息区域外，再点击消息区域内，tooltip莫名其妙获得焦点，重新出现
      // this.$refs['tim-tooltip-ref'].$el.blur();
      if (TCIC.SDK.instance.getState(TCIC.TMainState.Network_Broken)) return;  // 网络不可用时禁止操作
      const layout = TCIC.SDK.instance.getComponentLayout('float-im-component');
      const visible = layout.display === 'block';
      let newLayout = {
        display: visible ? 'none' : 'block',
      };
      if (!layout.left || !layout.top) {
        const isAdvanceMode = TCIC.SDK.instance.isFeatureAvailable('ScreenShareAdvanceMode');
        if (isAdvanceMode) {
          const messageBtnRect = this.$refs.message[0].getBoundingClientRect();
          const top = Math.ceil(messageBtnRect.bottom + 50);
          newLayout = Object.assign(newLayout, {
            left: `calc(${messageBtnRect.right}px - 334px)`,
            top: `calc(${top}px)`,
            height: `min(100vh - ${top}px, 680px)`,
          });
        } else {
          newLayout = Object.assign(newLayout, {
            left: '593px',
            top: '153px',
          });
        }
      }
      TCIC.SDK.instance.updateComponent('float-im-component', newLayout);
      setTimeout(() => {
        // 50ms后再开启拖拽，防止抖动
        TCIC.SDK.instance.getComponent('float-im-component').getVueInstance()
          .toggleDraggable(true);
      }, 50);
      TCIC.SDK.instance.setState(Constant.TStateShowChatBox, !visible);
    },

    // 暂停或者恢复共享
    pauseOrResumeShare() {
      // screenShareStatus 0  分享中 1 暂停分享 2 未开始分享/分享结束
      if (this.screenShareStatus === 0) {
        this.pauseScreenShare();
      } else if (this.screenShareStatus === 1) {
        this.resumeScreenShare();
      }
    },

    // 暂停屏幕分享
    pauseScreenShare(tips) {
      TCIC.SDK.instance.pauseScreenShare();

      // console.log('pauseScreenShare', 1);
      // 这里不要主动设置，因为 toggleScreenShareModeHanlder 也会设置，避免快速点击时，状态不一致
      // this.screenShareStatus = 1;

      if (!tips) return window.showToast(i18next.t('屏幕共享已暂停'));

      return window.showToast(tips);
    },

    // 恢复屏幕分享
    resumeScreenShare(tips) {
      TCIC.SDK.instance.resumeScreenShare();

      // 这里不要主动设置，因为 toggleScreenShareModeHanlder 也会设置，避免快速点击时，状态不一致
      // this.screenShareStatus = 0;
      // console.log('resumeScreenShare', 0);

      if (!tips) return window.showToast(i18next.t('屏幕共享已恢复'));

      return window.showToast(tips);
    },

    // 全员静音
    controlAllMicStatus() {
      if (TCIC.SDK.instance.getState(TCIC.TMainState.Network_Broken)) return;  // 网络不可用时禁止操作
      if (TCIC.SDK.instance.isCoTeachingClass()) {
        const classInfo = TCIC.SDK.instance.getClassInfo();
        TCIC.SDK.instance.updateTask(Constant.TConstantCoTeachingMuteAll, JSON.stringify({ timeStamp: new Date().getTime() }), -1, false, classInfo.teacherId)
          .then(() => window.showToast(i18next.t('{{arg_0}}已开启全员静音', { arg_0: this.roleInfo.teacher })))
          .catch((error) => {
            window.showToast(i18next.t('{{arg_0}}失败: {{arg_1}} {{arg_2}}', {
              arg_0: i18next.t('全员静音'),
              arg_1: error.errorCode, arg_2: error.errorMsg,
            }), 'error');
          });
      } else {
        TCIC.SDK.instance.muteAll(!this.muteAll)
          .catch((error) => {
            window.showToast(error.errorMsg, 'error');
          });
      }
    },

    // 停止屏幕分享
    stopShare() {
      if (TCIC.SDK.instance.isElectron()) {
        TCIC.SDK.instance.setFeatureAvailable('ScreenShareAdvanceMode', true);
        const screenWrap = TCIC.SDK.instance.getComponent('screen-videowrap-component');
        const shareToolWrap = TCIC.SDK.instance.getComponent('share-toolbar-component');
        screenWrap.getVueInstance().hasDraged = false;
        shareToolWrap.getVueInstance().hasDraged = false;
        // 结束共享后恢复背景色
        document.body.style.backgroundColor = '';
      }
      TCIC.SDK.instance.setState(Constant.TStatePPtSharingState, false);
      TCIC.SDK.instance.setState(Constant.TEventShutDownScreenShare, true);
      // !先默认一个时间差来同步TStatePPtSharingState，可能会有同步的隐患
      setTimeout(() => {
        TCIC.SDK.instance.reportLog('stopScreenShare', '[ShareToolbar] stopShare');
        TCIC.SDK.instance.stopScreenShare();
      }, 500);
    },

    // 更新按钮
    updateTbmButtons() {
      // 添加按钮
      const nodes = [];
      // 添加暂停按钮
      nodes.push(window.tbm.generateNode(
        this.$el.querySelector('.toolbar-content__item.share-status-item'),
        () => this.pauseOrResumeShare(),
      ));
      // 添加更多按钮
      nodes.push(window.tbm.generateNode(
        this.$el.querySelector('.toolbar-content__item.more-item'),
        () => this.isShowMorePanel = !this.isShowMorePanel,
      ));
      // 添加结束按钮
      nodes.push(window.tbm.generateNode(this.$el.querySelector('.toolbar-content__item.stop-share-item__btn button')));
      window.tbm.updateTarget('screentool', nodes, 'menu');
    },

    onComponentDragStart() {
      const shareToolWrap = TCIC.SDK.instance.getComponent('share-toolbar-component');
      if (shareToolWrap) {
        shareToolWrap.getVueInstance().hasDraged = true; // 被拖动过
      }
    },
    // --------------------------------------   methods end -------------------------------------
  },
};
</script>
<style lang="less">
@keyframes dot-flash {
  0% {
    opacity: 1;
  }

  30% {
    opacity: 1;
  }

  70% {
    opacity: 0.2;
  }
  100% {
    opacity: 0.2;
  }
}

.shortcut-toolbar-component {
  position: relative;
  background: rgba(30, 33, 42, 0.7);
  left: 50%;
  top: 0;
  transform: translateX(-50%);
  border-radius: 8px;
  z-index: 4;
  user-select: none;
  pointer-events: auto;
  z-index: 1000;

  &.expand {
    background-color: rgba(30, 33, 42, 1);
  }

  .roster-shortcut-panel {
    left: 50px;
    bottom: -40px;
    margin: 0;
    z-index: 1000;
    transform: none;
  }

  .circle-total__tip {
    position: relative;
    display: none;
    top: -7px;
    right: -10px;
    width: 18px;
    height: 18px;
    line-height: 18px;
    font-size: 12px;
    background: #fa3b3b;
    color: #fff;
    border-radius: 50%;
    text-align: center;

    &.active {
      display: block;
    }
  }

  .circle-total__tips {
    position: relative;
    display: none;
    top: -10px;
    right: -10px;
    width: 25px;
    height: 18px;
    line-height: 18px;
    font-size: 12px;
    background: #fa3b3b;
    color: #fff;
    border-radius: 8px;
    text-align: center;

    &.active {
      display: block;
    }
  }

  .divider {
    height: 1px;
    width: calc(100% - 24px);
    display: block;
    margin: 0 12px;
    background-color: rgba(43, 44, 48, 1);
  }

  .toolbar-header__wrap {
    display: flex;
    padding: 8px 12px;
    align-items: center;
    // justify-content: space-around;
    // height: 28px;
    // line-height: 28px;
    // box-sizing: content-box;

    // &.expand {
    //   justify-content: center;
    // }

    .toolbar-header__float-left {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      height: 20px;

      .toolbar-float__network {
        height: 12px;
        display: flex;
        align-items: flex-end;

        & > .network-quality {
          width: 2px;
          margin-right: 2px;
          display: inline-block;
        }

        .active {
          background: #1AFFC9;
        }

        .inactive {
          background: #CFD4E6
        }
      }

      .toolbar-float__class-info {
        .icon {
          display: block;
          width: 16px;
          height: 16px;
          margin-right: 8px;
          background-image: url('./assets/class-info.svg')
        }
      }

      .toolbar-float__network ~ * {
        margin-left: 8px;
      }

      .toolbar-float__dot {
        width: 6px;
        height: 6px;
        background: #12b121;
        border-radius: 50%;
        display: none;

        &.active {
          display: block;
          animation: dot-flash 1s infinite;
        }
      }

      .toolbar-float__time {
        font-size: 14px;
        color: #ffffff;

      }

      .toolbar-float__handup-icon,
      .toolbar-float__messages-icon {
        position: relative;
        margin-left: 16px;
        width: 20px;
        height: 20px;
        text-align: center;
        background: no-repeat center center;
        background-size: contain;
      }

      .toolbar-float__handup-icon {
        background-image: url('./assets/handup-icon.png');

        &.active {
          background-image: url('./assets/handup-icon.png');
        }
      }

      .toolbar-float__messages-icon {
        background-image: url('./assets/message-icon.png');

        &.active {
          background-image: url('./assets/message-icon.png');
        }
      }
    }

    .toolbar-header__title {
      text-align: center;
      margin-left: 4px;
      display: flex;
      overflow: hidden;
      align-items: center;

      .class-title {
        font-size: 14px;
        color: #ffffff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
      }
    }

    .toolbar-header__float-right {
      display: flex;
      flex: 1;
      justify-content: flex-end;

      &--status {
        .icon {
          display: inline-block;
          width: 8px;
          height: 8px;
          background: #38A673;
          margin-right: 4px;
          border-radius: 2.67px;
        }

        .text {
          font-size: 14px;
          color: #38A673;
          font-weight: 500;
        }
      }

      &--time {
        margin-left: 12px;
        color: rgba(255, 255, 255, 0.55);
        font-size: 14px;
        font-weight: 400;
      }

      // flex-shrink: 0;
      // text-align: right;
      // // width: 120px;
      // height: 28px;
      // line-height: 28px;
      // font-size: 14px;
      // color: #ffffff;

      // .share-play-sign,
      // .share-pause-sign {
      //   display: inline-block;
      //   vertical-align: middle;
      //   margin-right: 5px;
      //   width: 6px;
      //   height: 6px;
      //   border-radius: 50%;
      // }

      // .share-play-sign {
      //   background: #28c83e;
      // }

      // .share-pause-sign {
      //   background: #f9c114;
      // }
    }

    .toolbar-header_handup_im {
      flex-shrink: 0;
      display: flex;
      justify-content: center;

      .toolbar-header__item {
        width: 30px;
        cursor: pointer;
        position: relative;

        &.message-items {
          margin-left: 10px;
        }
      }

      .header-item__block {
        background: #fff;
        width: 28px;
        height: 28px;
        line-height: 28px;
        border-radius: 4px;
        background: no-repeat center center;
        background-size: contain;

        &.roster-item__icon {
          background-image: url('./assets/roster-icon.png');
        }

        &.message-item__icon {
          background-image: url('./assets/message-icon.png');
        }
      }
    }
  }

  .close-share-btn {
    border-color: #E6594C!important;
    background: transparent!important;
    color: #E6594C!important;
    font-size: 14px;
    border-radius: 4px;
    font-weight: 600;
  }

  .toolbar-content__wrap {
    .toolbar-header__item {
      width: 60px;
      text-align: center;
      cursor: pointer;
      border-radius: 6px;
      box-sizing: content-box;
      position: absolute;
      bottom: -3px;
    }

    .hide {
      display: none;
    }

    .toolbar-content__item-media {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 4px 4px 4px 0;
      margin-right: 12px;
      min-width: 52px;
      cursor: pointer;
    }

    .toolbar-content__item-divider {
      height: 20px;
      width: 1px;
      margin-right: 12px;
      background-color: rgba(43, 44, 48, 1)
    }

    .header-item__block {
      margin: 0 auto;
      position: relative;
      background: #fff;
      width: 28px;
      height: 28px;
      line-height: 28px;
      border-radius: 4px;
      background: no-repeat center center;
      background-size: contain;

      &.roster-item__icon {
        background-image: url('./assets/roster-icon.png');
      }

      &.message-item__icon {
        background-image: url('./assets/message-icon.png');
      }
    }

    .message-items {
      left: 142px;
    }
  }

  .end-share-container {
    flex: 1;
    text-align: right;
  }

  .toolbar-content__wrap {
    padding: 12px;
    display: flex;
    max-height: 500px;
    overflow: hidden;
    align-items: center;
    margin-bottom: 14px;

    &.collapse {
      max-height: 0;
      padding: 0;
    }

    .toolbar-content__item {
      text-align: center;
      cursor: pointer;
      padding: 10px;
      border-radius: 6px;
      box-sizing: content-box;

      &:hover {
      }

      &.roster-item {
        position: relative;
      }
    }
    .toolbar-content__item-sub-title {
        font-size: 12px;
        font-weight: 400;
        line-height: 16.8px;
        color: rgba(255, 255, 255, 0.55);
        white-space: nowrap;
      }

    .content-item__block {
      width: 24px;
      height: 24px;
      line-height: 24px;
      border-radius: 4px;
      margin-bottom: 4px;
      background: no-repeat center center;
      background-size: contain;

      &.roster-item__icon {
        background-image: url('./assets/roster-icon.png');
      }

      &.message-item__icon {
        background-image: url('./assets/message-icon.png');
      }

      &.share-status-item__icon {
        background-image: url('./assets/share-status-1.png');
      }

      &.share-status-item__icons {
        background-image: url('./assets/share-status-2.png');
      }

      &.more-item__icon {
        background-image: url('./assets/more-icon.png');
      }
      &.mic.open {
        background-image: url('./assets/mic-open.svg');
      }
      &.mic.close {
        background-image: url('./assets/mic-close.svg');
      }
      &.camera.open {
        background-image: url('./assets/camera-open.svg');
      }
      &.camera.close {
        background-image: url('./assets/camera-close.svg');
      }
      &.message.open {
        background-image: url('./assets/message.svg');
      }
      &.roaster.open {
        background-image: url('./assets/huamingce.svg');
      }
      &.handsup.open {
        background-image: url('./assets/handsup.svg');
      }
      &.tools.open {
        background-image: url('./assets/tools.svg');
      }
      &.muteAll.open {
        background-image: url('./assets/huamingce.svg');
      }
      &.systemAudio.open {
        background-image: url('./assets/huamingce.svg');
      }
      &.newShare.open {
        background-image: url('./assets/new-share.svg');
      }
      &.pauseOrResumShare.open {
        background-image: url('./assets/screen-pause.svg');
      }
      &.pauseOrResumShare.close {
        background-image: url('./assets/screen-containue.svg');
      }
    }

    .content-item__text {
      margin-top: 6px;
      font-size: 12px;
      color: #fff;
    }
  }

  /** 快捷面板 */

  .shortcut-panel__wrap {
    position: relative;

    .shortcut-panel {
      position: absolute;
      top: 30px;
      background: rgba(30, 33, 42);
      padding: 10px 16px;
      border-radius: 4px;

      &.show-panel {
        display: block !important;
      }

      &.roster-shortcut-panel {
        min-width: 280px;
        display: none;
        left: 90px;

        &.show-content_wrap {
          left: 0;
        }
      }

      &.message-shortcut-panel {
        display: none;
        left: 130px;

        &.show-content_wrap {
          left: 90px;
        }
      }

      &.more-shortcut-panel {
        display: none;
        left: 370px;
        top: 0;
        &.coteaching-fix {
          left: 74px;
        }
      }

      &.tip-shortcut-panel {
        font-size: 12px;
        color: #ffffff;
        left: 50%;
        transform: translateX(-50%);
        white-space: nowrap;

        .shortcut-panel__arrow {
          left: 48%;
          transform: translateX(-50%);
        }
      }

      .shortcut-panel__arrow {
        position: absolute;
        top: -16px;
        left: 20px;
        width: 0px;
        height: 0px;
        border-left: 8px solid transparent;
        border-bottom: 8px solid rgb(30, 33, 42);
        border-right: 8px solid transparent;
        border-top: 8px solid transparent;
      }

      // 更多
      .shortcut-panel__more-list {
        padding: 0;

        .more-item {
          display: flex;

          &.share-device-audio__toggle {
            align-items: center;
            cursor: pointer;

            .device-audio-toggle__checkbox {
              position: relative;
              margin-right: 8px;
              width: 16px;
              height: 16px;
              background: transparent;
              border: 1px solid #ffffff;
              border-radius: 2px;

              .device-audio__checked {
                position: absolute;
                transform: rotate(-45deg);
                top: 3px;
                left: 2px;
                border-left: 1px solid #fff;
                border-bottom: 1px solid #fff;
                width: 10px;
                height: 6px;
                display: none;
              }

              &.checked {
                background: #2971ff;
                border-color: #2971ff;

                .device-audio__checked {
                  display: block;
                }
              }
            }

            .device-audio-toggle__label {
              font-size: 12px;
              height: 18px;
              line-height: 18px;
              color: #fff;

              .allpeoqx {
                background: no-repeat center center;
                background-size: contain;
                width: 18px;
                height: 18px;
                float: left;
                margin-right: 10px;
                vertical-align: middle;
                background-image: url('./assets/muted-mic.png');
              }

              .quiz_tool {
                background: no-repeat center center;
                background-size: contain;
                width: 18px;
                height: 18px;
                float: left;
                margin-right: 10px;
                vertical-align: middle;
                background-image: url('./assets/ic_tool_answer.svg');
              }

              .time_tool {
                background: no-repeat center center;
                background-size: contain;
                width: 18px;
                height: 18px;
                float: left;
                margin-right: 10px;
                vertical-align: middle;
                background-image: url('./assets/ic_tool_timer.svg');
              }

              .clock_tool {
                background: no-repeat center center;
                background-size: contain;
                width: 18px;
                height: 18px;
                float: left;
                margin-right: 10px;
                vertical-align: middle;
                background-image: url('./assets/ic_tool_clock.svg');
              }
            }
          }
        }
      }
    }
  }
}
</style>
