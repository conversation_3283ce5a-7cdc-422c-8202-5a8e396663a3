<template>
  <div
    v-show="isTeacher && isWorking"
    ref="wrap"
    class="teacher-videowrap-component"
  >
    <div
      v-show="isShowVideoList"
      class="video-content"
    >
      <div
        class="title"
      >
        {{ studentsNum }}{{ translateTip.studentNum }}
      </div>
      <div
        ref="videoList"
        class="video-list"
      >
        <div
          id="collclass-teacher-wrap"
          ref="collclassTeacherWrap"
          class="video-wrap"
        >
          <div
            v-if="teacherIsFullScreen"
            style="position: relative;height: 112px;width: 100%;"
          />
        </div>
        <div
          id="collclass-student-list"
          ref="studentList"
          class="student-list"
        />
      </div>
      <div
        v-if="showFooter"
        class="video-footer"
      >
        <div
          class="up-button"
          @click="upBtnClick"
        >
          <i
            class="ic_up"
            :style="{'opacity': upBtnEnable ? 1 : 0.35}"
          />
        </div>
        <span class="split-line" />
        <div
          class="down-button"
          @click="downBtnClick"
        >
          <i
            class="ic_down"
            :style="{'opacity': downBtnEnable ? 1 : 0.35}"
          />
        </div>
      </div>
      <div :style="{'height': showFooter ? '0px' : '10px'}" />
    </div>
    <div
      v-if="!isShowVideoList"
      class="vide-hide"
      @click="toggleShow"
    >
      <div
        class="num-text"
      >
        {{ studentsNum }}
      </div>
      <div class="text">
        {{ translateTip.studentNum }}
      </div>
    </div>
    <div
      class="right-switch"
      @click="toggleShow"
    >
      <i
        :class="['icon', isShowVideoList ? 'ic_right' : 'ic_left']"
      />
    </div>
  </div>
</template>

<script>
import BaseComponent from '@core/BaseComponent';
import Constant from '@/util/Constant';
// import TeacherComponent from '../video-component/TeacherComponent.vue';
import Lodash from 'lodash';
import i18next from 'i18next';

export default {
  name: 'TeacherVideoWrapComponent',
  extends: BaseComponent,
  props: {},
  data() {
    return {
      videoWidth: 200,
      videoHeight: 112,
      isShowVideoList: true,
      isWorking: false,   // 当前布局是否生效
      teacherDom: null,
      studentDoms: [],
      studentWrapDoms: [],
      noSortWrapDoms: [], // 学生全屏/退出全屏后5s内不参与排序
      fullScreenVideoCom: null,
      isTeacher: false, // 当前是否老师
      teacherId: '',
      delayLoadMap: new Map(), // 延时加载学生
      permissionMap: new Map(),
      volumeMap: new Map(),
      upBtnEnable: false,
      downBtnEnable: false,
      teacherIsFullScreen: false,
      showVideoCtrlUserId: '',
      roleInfo: {},
    };
  },
  computed: {
    showFooter() {
      return this.studentWrapDoms.length > 3;
    },
    studentsNum() {
      return this.studentWrapDoms.length + this.delayLoadMap.size;
    },
    translateTip() {
      return {
        studentNum: i18next.t('名台上{{arg_0}}', { arg_0: this.roleInfo.student }),
      };
    },
  },
  mounted() {
    this.roleInfo = TCIC.SDK.instance.getRoleInfo().roleInfo;
    TCIC.SDK.instance.registerState(Constant.TStateCollClassVodFullScreen, '大教学视频全屏状态', '');

    // 监听音量回调
    this.addLifecycleTCICEventListener(TCIC.TTrtcEvent.Volume_Update, this.onVolume);
    this.addLifecycleTCICEventListener(TCIC.TLiveEvent.Volume_Update, this.onVolume);

    // 监听视频组件加载事件
    this.addLifecycleTCICEventListener(Constant.TEventAddVideoComponent, this.onVideoAdd);
    this.addLifecycleTCICEventListener(Constant.TEventRemoveVideoComponent, this.onVideoRemove);
    this.addLifecycleTCICStateListener(Constant.TStateCollegeVideoLayout, this.onLayoutUpdate);

    // 监听加入课堂事件
    this.makeSureClassJoined(this.onJoinClass);
    // 监听权限变更事件
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Permission_Update, this.permissionListHandler);

    // 上课后加载缓存组件
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start)
      .then(() => {
        // 将上台的学生加入到delayLoadMap中
        const permissions = TCIC.SDK.instance.getPermissionList();
        this.permissionListHandler(permissions);
        const classInfo = TCIC.SDK.instance.getClassInfo();
        this.teacherId = classInfo.teacherId;
        // permissions.forEach((permission) => {
        //   if (permission.stage && !this.delayLoadMap.has(permission.userId) && permission.userId !== classInfo.teacherId) {
        //     this.delayLoadMap.set(permission.userId, permission);
        //   }
        // });
        const that = this;
        // setTimeout(() => {
        //   Array.from(this.delayLoadMap.keys()).forEach((userId) => {
        //     that.loadUserComponent(that.delayLoadMap.get(userId));
        //   });
        //   // 清空delayLoadMap
        //   that.delayLoadMap.clear();
        // }, 100);
        this.addLifecycleTCICEventListener(TCIC.TMainEvent.Recv_Member_Action, ({ action }) => {
          that.permissionListHandler(TCIC.SDK.instance.getPermissionList());
        });
      });
    TCIC.SDK.instance.subscribeState(Constant.TStateVideoCtrlUserId, (userId) => {
      if (userId === this.teacherId) {
        return;
      }
      if (this.showVideoCtrlUserId.length > 0 && this.showVideoCtrlUserId !== userId) {
        if (!this.fullScreenVideoCom || this.fullScreenVideoCom.getAttribute('label') !== this.showVideoCtrlUserId) {
          // showVideoCtrlUserId全屏时不移除，其他的情况移除
          this.removeNoSortVideo(this.showVideoCtrlUserId);
          this.sortStudents();
        }
      }
      this.showVideoCtrlUserId = userId || '';
      if (this.showVideoCtrlUserId.length > 0) {
        this.addNoSortVideo(this.showVideoCtrlUserId);
      }
    });
  },
  methods: {
    onLayoutUpdate(layout) {
      const oldStatus = this.isWorking;
      this.isWorking = layout === Constant.TConstantCollegeLayoutNormal;
      if (this.isWorking && !oldStatus) {
        this.$nextTick(() => {
          this.loadVideos(this);
        });
      } else if (!this.isWorking && oldStatus) {
        this.unloadVideos();
      }
    },
    onVideoAdd(info) {
      if (!this.isWorking) return ;
      if (info.isTeacher) {
        this.teacherDom = TCIC.SDK.instance.getComponent('teacher-component');
        this.teacherDom.classList.add('video');
        this.$refs.collclassTeacherWrap.appendChild(this.teacherDom);
        TCIC.SDK.instance.updateComponent('teacher-component', {
          position: 'relative',
        });
      } else {
        const wrap = document.getElementById('collclass-student-list');
        const ele = TCIC.SDK.instance.getComponent('student-component', info.userId);
        const studentWrapId = `student-video-bg-${info.userId}`;
        const studentWrapDiv = document.createElement('div');
        studentWrapDiv.id = studentWrapId;
        studentWrapDiv.classList.add('video-wrap');
        studentWrapDiv.setAttribute('label', info.userId);
        studentWrapDiv.appendChild(ele);
        wrap.appendChild(studentWrapDiv);
        this.studentWrapDoms.push(studentWrapDiv);
        ele.classList.add('video');
        TCIC.SDK.instance.updateComponent('student-component', {
          position: 'relative',
        }, info.userId);
        this.studentDoms.push(ele);
        ele.getVueInstance().setControlDirect('left');
        this.updateLayout();
        this.layoutStudents();
      }
    },
    onVideoRemove(info) {
      if (info.isTeacher || !this.isWorking) return ;
      // 如果学生正全屏展示，先退出全屏
      if (this.fullScreenVideoCom && this.fullScreenVideoCom.getAttribute('label') === info.userId) {
        this.exitFullScreen();
      }
      const newStudents = [];
      this.studentDoms.forEach((ele) => {
        if (ele.getAttribute('label') === info.userId) {
          ele.setAttribute('delete', 'true'); // 标记为移除
        } else {
          newStudents.push(ele);
        }
      });
      this.studentDoms = newStudents;
      const studentWrapId = `student-video-bg-${info.userId}`;
      const studentWrapDiv = document.getElementById(studentWrapId);
      const index = this.studentWrapDoms.indexOf(studentWrapDiv);
      if (index >= 0 && index < this.studentWrapDoms.length) {
        this.studentWrapDoms.splice(index, 1);
      }
      this.removeNoSortVideo(info.userId);
      studentWrapDiv.parentNode.removeChild(studentWrapDiv);
      this.updateLayout();
      this.layoutStudents();
    },

    onJoinClass() {
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      const classInfo = TCIC.SDK.instance.getClassInfo();
      this.teacherId = classInfo.teacherId;
      this.$nextTick(() => {
        this.teacherDom && this.teacherDom.getVueInstance().setControlDirect('left');
      });
    },
    onVolume(info) {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      if (info.userId === classInfo.teacherId || info.volume <= 0) {
        return;
      }
      let list = this.volumeMap.get(info.userId);
      const item = { time: new Date().getTime(), volume: info.volume };
      if (!list) {
        list = [item];
        this.volumeMap.set(info.userId, list);
      } else {
        list.push(item);
      }
      this.sortStudents();
    },
    // 获取某个userid，最近seconds内的声音总量
    getVolumeByUser(userId, seconds) {
      const permission = this.permissionMap.get(userId);
      if (!permission || !permission.mic || permission.micState !== 1) {
        // 当麦克风状态非打开时，直接返回0
        return 0;
      }
      const list = this.volumeMap.get(userId);
      let result = 0;
      if (!list || list.length <= 0) {
        return result;
      }
      const expireTime = new Date().getTime() - seconds * 1000;
      for (let index = list.length - 1; index >= 0; index--) {
        const element = list[index];
        if (element.time < expireTime) {
          // 删除过期数据，减少运算量
          list.splice(index, 1);
        } else {
          result += element.volume;
        }
      }
      return result;
    },
    permissionListHandler(permissionList) {
      permissionList.forEach((permission) => {
        this.permissionMap.set(permission.userId, permission);
      });
      this.sortStudents();
    },
    sortStudents: Lodash.throttle(function () {
      if (this.studentWrapDoms.length <= 1 || !this.isShowVideoList) {
        return;
      }
      const that = this;
      this.studentWrapDoms.sort((item1, item2) => {
        const userid1 = item1.getAttribute('label');
        const userid2 = item2.getAttribute('label');
        const permission1 = that.permissionMap.get(userid1);
        const permission2 = that.permissionMap.get(userid2);
        const volume1 = that.getVolumeByUser(userid1, 3);
        const volume2 = that.getVolumeByUser(userid2, 3);
        const shareScreen1 = permission1.screen > 0 && (permission1.screenState === TCIC.TScreenState.Sharing || permission1.screenState === TCIC.TScreenState.Paused);
        const shareScreen2 = permission2.screen > 0 && (permission2.screenState === TCIC.TScreenState.Sharing || permission2.screenState === TCIC.TScreenState.Paused);
        // 1、屏幕分享 （同时屏幕分享，进房顺序）
        if (shareScreen1 && !shareScreen2) {
          return -1;
        }
        if (shareScreen2 && !shareScreen1) {
          return 1;
        }
        if (shareScreen1 && shareScreen2) {
          return permission1.lastEnterTime  - permission2.lastEnterTime;
        }
        // 2、是否在说话（同时说话，音量大小）
        if (volume1 > 0 && volume2 === 0) {
          return -1;
        }
        if (volume2 > 0 && volume1 === 0) {
          return 1;
        }
        if (volume1 !== volume2) {
          return volume2  - volume1;
        }
        // 3、视频是否打开
        // cameraState表示摄像头状态，0是未知，1是打开，2是关闭
        if (permission1.cameraState === 1 && permission2.cameraState !== 1) {
          return -1;
        }
        if (permission1.cameraState !== 1 && permission2.cameraState === 1) {
          return 1;
        }

        // 4、进入课堂顺序
        if (permission1.lastEnterTime && permission2.lastEnterTime) {
          return permission1.lastEnterTime  - permission2.lastEnterTime;
        }
        return 0;
      });
      for (let i = 0; i < this.noSortWrapDoms.length; i++) {
        const wrapDom = this.noSortWrapDoms[i];
        const index = wrapDom.getAttribute('index') / 1;
        if (!isNaN(index) && index >= 0 && index < this.studentWrapDoms.length) {
          const nowIndex = this.studentWrapDoms.indexOf(wrapDom);
          // 先从当前位置删除wrapDom
          this.studentWrapDoms.splice(nowIndex, 1);
          // 再插入到固定位置
          this.studentWrapDoms.splice(index, 0, wrapDom);
        }
      }
      // 更新UI布局
      this.layoutStudents();
    }, 200, {
      leading: true,
      trailing: false,
    }),
    layoutStudents() {
      const itemH = 112;
      const itemW = 200;
      const gap = 10;
      for (let index = 0; index < this.studentWrapDoms.length; index++) {
        const element = this.studentWrapDoms[index];
        const top = itemH * index + gap * index;
        const stlyeText = `position: absolute;left: 0;top: ${top}px;width: ${itemW}px;height: ${itemH}px;zIndex: auto;display: block;`;
        element.style = stlyeText;
      }
      const h = this.studentWrapDoms.length > 0 ? this.studentWrapDoms.length * itemH + (this.studentWrapDoms.length - 1) * gap : 0;
      this.$refs.studentList.style = `height:${h}px;`;
      setTimeout(() => {
        this.updateUpDownBtnState();
      }, 200);
    },
    upBtnClick: Lodash.throttle(
      function () {
        if (this.$refs.videoList.scrollTop <= 0) {
          return;
        }
        const itemH = 4 * (this.videoHeight + 10);
        const scrollTop = this.adjustScrollTop(this.$refs.videoList.scrollTop - itemH);
        this.$refs.videoList.scrollTop = scrollTop;
        this.upBtnEnable = scrollTop > 0;
        this.downBtnEnable = scrollTop + this.$refs.videoList.clientHeight < this.$refs.videoList.scrollHeight;
      },
      500,
      {
        leading: true,
        trailing: false,
      },
    ),
    downBtnClick: Lodash.throttle(
      function () {
        if (this.$refs.videoList.scrollTop + this.$refs.videoList.clientHeight >= this.$refs.videoList.scrollHeight) {
          return;
        }
        const itemH = 4 * (this.videoHeight + 10);
        const scrollTop = this.adjustScrollTop(this.$refs.videoList.scrollTop + itemH);
        this.$refs.videoList.scrollTop = scrollTop;
        this.upBtnEnable = scrollTop > 0;
        this.downBtnEnable = scrollTop + this.$refs.videoList.clientHeight < this.$refs.videoList.scrollHeight;
      },
      500,
      {
        leading: true,
        trailing: false,
      },
    ),
    updateUpDownBtnState() {
      this.upBtnEnable = this.$refs.videoList.scrollTop > 0;
      this.downBtnEnable = this.$refs.videoList.scrollTop + this.$refs.videoList.clientHeight < this.$refs.videoList.scrollHeight;
    },
    toggleShow() {
      this.isShowVideoList = !this.isShowVideoList;
      this.updateLayout();
    },
    adjustScrollTop(top) {
      // 确保scrollTop是整数个videoHeight + gap，保证UI对齐
      let scrollTop = top;
      const count = Math.floor(scrollTop / (this.videoHeight + 10));
      scrollTop = (this.videoHeight + 10) * count;
      scrollTop = Math.min(scrollTop, this.$refs.videoList.scrollHeight - this.$refs.videoList.clientHeight);
      scrollTop = Math.max(scrollTop, 0);
      return scrollTop;
    },
    updateLayout() {
      const videoH = 112;
      const videoCount = Math.min(this.studentWrapDoms.length + 1, 4);
      let height = 36.0 + videoCount * videoH + (videoCount - 1) * 10;
      let width = 220 + 40;
      if (this.showFooter) {
        height += 36;
      } else {
        height += 10;
      }
      if (!this.isShowVideoList) {
        width = 36.0 + 40;
        height = 120.0;
      }
      TCIC.SDK.instance.updateComponent('teacher-videowrap-component', {
        top: `calc(50% - ${height / 2}px)`,
        left: `calc(100% - ${width}px)`,
        width: `${width}px`,
        height: `${height}px`,
        display: 'block',
        style: 'overflow: visible;',
      });
      this.$nextTick(() => {
        if (this.$refs.videoList) {
          const scrollTop = this.adjustScrollTop(this.$refs.videoList.scrollTop);
          if (scrollTop !== this.$refs.videoList.scrollTop) {
            this.$refs.videoList.scrollTop = scrollTop;
            this.upBtnEnable = scrollTop > 0;
            this.downBtnEnable = scrollTop + this.$refs.videoList.clientHeight < this.$refs.videoList.scrollHeight;
          }
        }
      });
    },
    toggleFullScreen(userId) {
      if (this.fullScreenVideoCom && this.fullScreenVideoCom.getAttribute('label') === userId) {
        // 退出全屏
        this.exitFullScreen();
      } else {
        this.enterFullScreen(userId);
      }
    },
    startLocalVideo() {
      const videoInstance = this.teacherDom.getVueInstance().videoVueInstance();
      if (videoInstance) {
        videoInstance.enableCamera(true);
      }
    },
    stopLocalVideo() {
      const videoInstance = this.teacherDom.getVueInstance().videoVueInstance();
      if (videoInstance) {
        videoInstance.enableCamera(false);
      }
    },
    startLocalAudio() {
      const videoInstance = this.teacherDom.getVueInstance().videoVueInstance();
      if (videoInstance) {
        videoInstance.enableMic(true);
      }
    },
    stopLocalAudio() {
      const videoInstance = this.teacherDom.getVueInstance().videoVueInstance();
      if (videoInstance) {
        videoInstance.enableMic(false);
      }
    },
    memberAction(param) {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      // 操作学员权限，给出提示
      if (param.userId !== classInfo.teacherId) {
        switch (param.actionType) {
          case TCIC.TMemberActionType.Camera_Close:
            window.showToast(i18next.t('正在关闭学员摄像头, 请稍候'));
            break;
          case TCIC.TMemberActionType.Camera_Open:
            window.showToast(i18next.t('正在打开学员摄像头, 请稍候'));
            break;
          case TCIC.TMemberActionType.Mic_Close:
            window.showToast(i18next.t('正在关闭学员麦克风, 请稍候'));
            break;
          case TCIC.TMemberActionType.Mic_Open:
            window.showToast(i18next.t('正在打开学员麦克风, 请稍候'));
            break;
        }
      }
      TCIC.SDK.instance.memberAction(param)
        .catch((err) => {
          window.showToast(err.errorMsg, 'error');
        });
    },
    isFullScreen() {
      if (this.fullScreenVideoCom) {
        return true;
      }
      return false;
    },
    exitFullScreen() {
      const userId = this.fullScreenVideoCom.getAttribute('label');
      const classInfo = TCIC.SDK.instance.getClassInfo();
      if (classInfo.teacherId === userId) {
        const wrap = document.getElementById('collclass-teacher-wrap');
        wrap.appendChild(this.fullScreenVideoCom);
        this.teacherIsFullScreen = false;
      } else {
        const studentWrapId = `student-video-bg-${userId}`;
        const wrap = document.getElementById(studentWrapId);
        wrap.appendChild(this.fullScreenVideoCom);
        // 5s后重新排序
        setTimeout(() => {
          this.removeNoSortVideo(userId);
          this.sortStudents();
        }, 5000);
      }
      this.fullScreenVideoCom.style = '';
      this.fullScreenVideoCom = null;
      TCIC.SDK.instance.setState(Constant.TStateCollClassVodFullScreen, '');
    },
    enterFullScreen(userId) {
      // userId已经全屏，直接返回
      if (this.fullScreenVideoCom && this.fullScreenVideoCom.getAttribute('label') === userId) {
        return;
      }
      // 先退出全屏，再进入全屏
      if (this.fullScreenVideoCom) {
        this.exitFullScreen();
      }
      const classInfo = TCIC.SDK.instance.getClassInfo();
      let dom = null;
      if (classInfo.teacherId === userId) {
        dom = this.teacherDom;
        dom.setAttribute('label', classInfo.teacherId);
        this.teacherIsFullScreen = true;
      } else {
        dom = this.studentDoms.find(ele => ele.getAttribute('label') === userId);
        this.addNoSortVideo(userId);
      }
      dom.style = 'position: fixed;width: 100%;height: 100%;left: 0;top: 0;';
      this.fullScreenVideoCom = dom;
      document.body.appendChild(dom);
      TCIC.SDK.instance.setState(Constant.TStateCollClassVodFullScreen, userId);
    },
    getVideoElement(userId) {
      const video = document.getElementById(`video-${userId}`);
      return video;
    },
    getAudioElement(userId) {
      const audio = document.getElementById(`audio-${userId}`);
      return audio;
    },
    addNoSortVideo(userId) {
      const found = this.noSortWrapDoms.find(ele => ele.getAttribute('label') === userId);
      if (found || userId === this.teacherId) {
        return;
      }
      const wrapDom = this.studentWrapDoms.find(ele => ele.getAttribute('label') === userId);
      if (!wrapDom) {
        console.warn('show VideoCtrl not found dom or wrapDom!');
        return;
      }
      const index = this.studentWrapDoms.indexOf(wrapDom);
      wrapDom.setAttribute('index', index);
      this.noSortWrapDoms.push(wrapDom);
    },
    removeNoSortVideo(userId) {
      const wrapDom = this.noSortWrapDoms.find(ele => ele.getAttribute('label') === userId);
      if (wrapDom) {
        wrapDom.removeAttribute('index');
        const index = this.noSortWrapDoms.indexOf(wrapDom);
        if (index >= 0 && index < this.noSortWrapDoms.length) {
          this.noSortWrapDoms.splice(index, 1);
        }
      }
    },
    // 初始化当前布局
    loadVideos() {
      const loaderCom = TCIC.SDK.instance.getComponent('video-loader-component');
      if (loaderCom) {
        const loaderVue = loaderCom.getVueInstance();
        const videoInfos = loaderVue.getCurLoadedVideoInfos();
        videoInfos.forEach((info) => {
          this.onVideoAdd(info);
        });
      }
      this.updateLayout();
      this.layoutStudents();
    },
    unloadVideos() {
      const wrap = document.getElementById('collclass-student-list');
      this.studentDoms = [];
      this.studentWrapDoms = [];
      this.noSortWrapDoms = [];
      wrap.innerHTML = '';
      // 清除全屏状态
      if (this.fullScreenVideoCom) {
        this.fullScreenVideoCom.style = '';
        this.fullScreenVideoCom = null;
      }
      this.teacherIsFullScreen = false;
      TCIC.SDK.instance.setState(Constant.TStateCollClassVodFullScreen, '');
    },
  },
};
</script>

<style lang="less">
.teacher-videowrap-component {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  .right-switch {
    display: flex;
    align-items: center;
    width: 40px;
    height: 84px;
    background-color: transparent;
    .icon {
      width: 40px;
      height: 40px;
      display: block;
    }
    .ic_right {
      margin-right: 5px;
      background: url('./assets/right.svg') no-repeat center;
    }
    .ic_left {
      margin-left: 2px;
      background: url('./assets/left.svg') no-repeat center;
    }
  }
  .vide-hide {
    width: 36px;
    background: rgba(0, 0, 0, 0.4);
    border-radius: 18px;
    .num-text {
      width: 100%;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 16px;
      text-align: center;
      margin-top: 12px;
    }
    .text {
      width: 20px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 16px;
      margin-left: 8px;
      margin-bottom: 12px;
      text-align: center;
    }
  }
  .video-content {
    .title {
      margin-top: 10px;
      text-align: center;
      font-size: 12px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #000000;
      line-height: 16px;
      margin-bottom: 10px;
    }
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.25);
    box-shadow: 0px 0px 24px 0px rgba(0, 0, 0, 0.1);
    border: 0;
    border-image: linear-gradient(
        180deg,
        rgba(216, 216, 216, 0.25),
        rgba(216, 216, 216, 0)
      )
      1 1;
    .video-list {
      display: flex;
      width: 100%;
      flex-direction: column;
      justify-content: center;
      padding: 0 10px;
      pointer-events: auto;
      touch-action: none;
      position: relative;
      max-height: 478px;
      overflow-y: hidden;
      scroll-behavior: smooth;
      .video-wrap {
        width: 200px;
        height: 112px;
        background-color: #141414;
        background-image: url('./assets/holder.png');
        background-repeat: no-repeat;
        background-position:center;
        background-size: 34px 34px;
        border-radius: 9px;
      }
      .video {
        top: 0 !important;
        left: 0 !important;
        position: relative;
        width: 100% !important;
        height: 112px !important;
        display: block;
        overflow: hidden;
        border-radius: 6px;
      }
      .student-list {
        position: relative;
        margin-top: 10px;
      }
    }

    .video-footer {
      // background-color: #f00;
      display: flex;
      height: 20px;
      margin: 8px 10px;

      .up-button {
        background-color: transparent;
        width: 100px;
        height: 20px;
        .ic_up {
          background: url('./assets/up.svg') no-repeat center;
          width: 100%;
          height: 20px;
          display: block;
        }
      }
      .split-line {
        width: 1px;
        height: 20px;
        background: #141414;
        opacity: 0.3;
      }
      .down-button {
        background-color: transparent;
        width: 99px;
        height: 20px;
        .ic_down {
          background: url('./assets/down.svg') no-repeat center;
          width: 100%;
          height: 20px;
          display: block;
        }
      }
    }
  }
}
</style>
