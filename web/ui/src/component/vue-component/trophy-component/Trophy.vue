<template>
  <div
    ref="trophyRef"
    class="trophy-component"
  >
    <div
      v-for="(trophy, index) in animationList"
      :id="`trophy_${trophy.uid}`"
      :key="index"
      class="wrapper"
      :class="{ 'mobile-device': isMobile, 'pad-device': isPad }"
    >
      <div class="circle">
        <div class="trophy-wrapper">
          <div class="trophy" />
        </div>
      </div>
      <div class="stars" />
      <div class="ribbon" />
      <div class="paper" />
      <div class="wording">
        <span
          v-if="trophy.userId === 'everyone'"
          class="content"
        >{{ $t('大家都很棒！') }}</span>
        <span
          v-else
          class="content"
        >{{ $t('恭喜') }}<span class="nickname">{{ trophy.nickname }}</span>{{ $t('获得了奖杯') }}</span>
      </div>
    </div>
    <audio
      ref="audio"
      :src="trophyAudio"
    />
  </div>
</template>
<script>
import BaseComponent from '@/component/core/BaseComponent';
import Lodash from 'lodash';

export default {
  name: 'TrophyComponent',
  components: {},
  extends: BaseComponent,
  data() {
    return {
      taskId: 'trophy',
      trophyAudio: 'https://res.qcloudclass.com/assets/trophy.mp3',
      animationList: [],
      trophyList: null,
      dataSource: null,
      isTeacher: false,
      isStudent: false,
      everyoneTrophyCount: 0,
      userMap: {},
      pausePlayerTask: null,
      isMobile: false,
      isPad: false,
    };
  },
  watch: {
    trophyList(val) {
      this.dataSource = Lodash.clone(val);
    },
  },
  mounted() {
    this.makeSureClassJoined(this.onJoinClass);

    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Task_Updated, this.onTaskUpdate);
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start)
      .then(() => {
        if (!this.trophyList) {
          this.trophyList = [];
        }
      });
  },
  methods: {

    onJoinClass() {
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isStudent = TCIC.SDK.instance.isStudent();
      this.isMobile = TCIC.SDK.instance.isMobile();
      this.isPad = TCIC.SDK.instance.isPad();
      const { status } = TCIC.SDK.instance.getClassInfo();
      if (this.isTeacher && TCIC.TClassStatus.Not_Start === status) {
        // 初始化数据关联上课行为，避免二次初始化
        TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start).then(() => {
          TCIC.SDK.instance.updateTask(this.taskId, JSON.stringify([]), - 1, 0, null);
        });
      }
      document.addEventListener('touchstart', this.initAudio);
    },


    triggerAnimation(userId) {
      if (userId === 'everyone') {
        this.renderAnimation(userId);
      } else {
        TCIC.SDK.instance.getUserInfo(userId).then((result) => {
          this.renderAnimation(userId, result.nickname);
        });
      }
    },

    initAudio() {
      this.playAudio(true);
      document.removeEventListener('touchstart', this.initAudio);
    },

    playAudio(muted = false) {
      clearTimeout(this.pausePlayerTask);
      const audio = this.$refs.audio;
      audio.currentTime = 0;
      audio.muted = muted;
      audio.volume = 0.4;
      audio.play();
      TCIC.SDK.instance.reportLog('trophy-play-audio', `muted ${muted}`);
      this.pausePlayerTask = setTimeout(() => {
        audio.pause();
      }, 2900);
    },

    renderAnimation(userId, nickname = '') {
      const userComponent = TCIC.SDK.instance.getComponent('student-component', userId);
      const toElement = userComponent ? userComponent.getVueInstance().$el : document.body;
      const toElementClientRect = toElement.getBoundingClientRect();
      const studentWrap = document.querySelector('#student-wrap');
      let zoom = studentWrap ? window.getComputedStyle(studentWrap).zoom : 1;
      if (userId === 'everyone' || (userComponent && userComponent.getAttribute('drag') === 'true')) {   // 拖动状态也不缩放
        zoom = 1;
      }
      const user = {
        uid: `${userId}_${parseInt(Math.random() * 100000000, 10)}`,
        userId,
        nickname,
      };
      this.animationList.push(user);
      this.playAudio();
      const screenH = document.body.clientHeight;
      // 超过屏幕高度时，重新计算宽高，保证显示完整
      if (screenH < 90 * 4) {
        this.$nextTick(() => {
          const trophy = this.$refs.trophyRef.querySelector(`#trophy_${user.uid}`);
          if (trophy) {
            const itemH = screenH / 4 - 10;
            trophy.style.width = `${itemH}px`;
            trophy.style.height = `${itemH}px`;
          }
        });
      }
      setTimeout(() => {
        const trophy = this.$refs.trophyRef.querySelector(`#trophy_${user.uid}`);
        this.animateTo({
          trophy,
          to: toElementClientRect,
          uid: user.uid,
          zoom,
        });
      }, 2000);
    },

    // 内存保存一个对象，记录最近一次给这个用户发奖杯的时间
    // 在这一时间范围内只允许触发一次发奖杯操作，考虑到页面重新加载的耗时大于等待时长，不做本地持久化。
    distributeWithThrottle(userId, animation = true, wait = 2000) {
      const { userMap } = this;
      if (!userMap[userId]) {
        userMap[userId] = {
          lastSendTime: 0,
        };
      }
      const now = + new Date();
      if (userMap[userId] && now - userMap[userId].lastSendTime > wait) {
        userMap[userId].lastSendTime = now;
        this.distribute(userId, animation);
        return true;
      }
      console.debug('distributeWithThrottle reach the throttle condition');
      return false;
    },

    /*
     * 发放奖杯
     *
     */
    distribute(userId, animation = true) {
      if (this.isStudent) {
        // 学生不能发奖杯
        return;
      }
      this.dataSource = this.dataSource || [];
      const toUser = this.dataSource.find(item => item.userId === userId);
      if (toUser) {
        toUser.count += 1;
      } else {
        this.dataSource.push({
          userId,
          count: 1,
        });
      }
      if (userId === 'everyone') {
        this.everyoneTrophyCount += 1;
      }
      TCIC.SDK.instance.updateTask(this.taskId, JSON.stringify(this.dataSource), - 1, 0, null).then((task) => {
        // 发放奖杯成功，渲染动画
        if (animation) {
          this.triggerAnimation(userId);
        }
      })
        .catch((error) => {
          window.showToast(error.errorMsg);
        });
    },


    /*
     * 全员发放奖杯
     * 1.选择【全员奖励】后，维持现有动画不变。文案更改为“大家都很棒！”；
     * 2.若课堂中没有学生，toast提示：“课堂中无学生，不可全员奖励”
     */
    distributeToEveryone() {
      this.distribute('everyone', true);
    },

    onTaskUpdate(taskInfo) {
      if (taskInfo.taskId !== this.taskId) {
        // 不属于奖杯
        return;
      }
      const isInit = !!this.trophyList;
      if (!isInit) {
        // 只做数据初始化
        this.trophyList = JSON.parse(taskInfo.content);
      } else if (JSON.stringify(this.trophyList) !== taskInfo.content) {
        const content = JSON.parse(taskInfo.content);
        const differ = Lodash.differenceWith(content, this.trophyList, Lodash.isEqual);
        differ.forEach((user) => {
          this.triggerAnimation(user.userId);
        });
        this.trophyList = content;
      }
      const everyone = this.trophyList.find(item => item.userId === 'everyone');
      this.everyoneTrophyCount = everyone ? everyone.count : 0;
    },

    animateTo(opt) {
      const { trophy, to, zoom } = opt;
      // let toElement = to.getBoundingClientRect();
      const toElement = to;
      const trophyElement = trophy.getBoundingClientRect();
      const x = toElement.left * zoom - trophyElement.left - trophyElement.width / 2 + toElement.width * zoom / 2;
      const y = toElement.top * zoom - trophyElement.top - trophyElement.height / 2 + toElement.height * zoom / 2;
      const animateDuration = 1;
      trophy.style.transform = `translateX(${x}px) translateY(${y}px) scale(${zoom})`;
      trophy.style.transition = `all ${animateDuration}s;`;
      setTimeout(() => {
        trophy.classList.add('fade-in');
        // 回收
        // setTimeout(() => {
        //   this.animationList = this.animationList.filter( item => item.uid !== uid);
        // }, 3000)
      }, animateDuration * 1000);
    },

    getCountByUserId(userId) {
      const user = (this.trophyList || []).find(item => item.userId === userId);
      return (user ? user.count : 0) + this.everyoneTrophyCount;
    },

    getList() {
      return this.trophyList || [];
    },
  },
};
</script>
<style lang="less">
.trophy-component {
  * {
    pointer-events: none !important;
  }
  .wrapper {
    width: 80px;
    height: 80px;
    position: absolute;
    margin: auto;
    text-align: center;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 1;
    transform: scale(4);
    transition: all 1s;
    &.mobile-device {
      transform: scale(1.5);
    }
    &.pad-device {
      transform: scale(3);
    }
    &.fade-in {
      opacity: 0;
      pointer-events: none;
    }
  }

  .circle {
    width: 100%;
    height: 100%;
    position: relative;
    border-radius: 100%;
    overflow: hidden;
    animation: circle 0.5s ease-in-out 0s 1 normal forwards;
    animation-duration: 0.8s;
    animation-delay: 0s;
    transform: scale(0.01) translateZ(0);

    &:after {
      content: ' ';
      width: 100%;
      height: 100%;
      background: #fff;
      position: absolute;
      top: 0;
      left: 0;
      animation: circle-after 0.5s cubic-bezier(0.1, -0.6, 0.2, 0) 0s 1 normal forwards;
      animation-duration: 0.6s;
      animation-delay: 0.2s;
      opacity: 1;
    }

    &.finished:after {
      opacity: 0;
      transition: all 0.5s;
    }

    .trophy-wrapper {
      border: 2px solid #fff;
      width: 100%;
      height: 100%;
      border-radius: 100%;
      overflow: hidden;
      padding: 1px;
      position: absolute;
      z-index: 1;

      .trophy {
        width: 100%;
        height: 100%;
        background: url('./assets/trophy.png') center no-repeat;
        background-size: 100%;
        transform: scale(0.7) translateY(150px) translateZ(0);
        animation: trophy-show 0.5s cubic-bezier(0.1, -0.6, 0.2, 0) 0.5s 1 normal forwards;
        animation-duration: 0.4s;
        animation-delay: 0.2s;
        opacity: 0;
      }
    }

  }

  @keyframes circle {
    0% {
      transform: scale(0.01) translateZ(0)
    }
    80% {
      transform: scale(1) translateZ(0)
    }
    100% {
      transform: scale(0.95) translateZ(0)
    }
  }


  @keyframes circle-after {
    33% {
      background: #999;
    }
    50% {
      opacity: 1;
    }
    100% {
      background: #fff;
      opacity: 0;
    }
  }


  @keyframes trophy-show {
    0% {
      opacity: 1;
      transform: scale(0.7) translateY(250px) translateZ(0);
    }
    10% {
      transform: scale(0.7) translateY(250px) translateZ(0);
    }
    100% {
      opacity: 1;
      transform: scale(1) translateY(0) translateZ(0);
    }
  }

  .ribbon {
    width: 200%;
    height: 144%;
    background: url('./assets/ribbon.png') center no-repeat;
    background-size: 100% auto;
    top: -300px;
    left: -300px;
    bottom: -300px;
    right: -300px;
    margin: auto;
    position: absolute;
    animation: ribbon 0.5s cubic-bezier(0.1, -0.6, 0.2, 0) 0.5s 1 normal forwards;
    animation-duration: 1s;
    animation-delay: 0.5s;
    transform: scale(0.01) translateZ(0);
    opacity: 0;
    pointer-events: none;
  }

  @keyframes ribbon {
    0% {
      opacity: 0;
      transform: scale(0.01) translateZ(0)
    }
    50% {
      opacity: 1;
      transform: scale(1) translateZ(0)
    }
    80% {
      opacity: 1;
      transform: scale(1) translateZ(0)
    }
    100% {
      opacity: 0;
      transform: scale(1) translateZ(0)
    }
  }

  .paper {
    width: 180%;
    height: 124%;
    background: url('./assets/paper.png') center no-repeat;
    background-size: 100% auto;
    top: -300px;
    left: -300px;
    bottom: -300px;
    right: -300px;
    margin: auto;
    position: absolute;
    animation: paper 0.5s cubic-bezier(0.1, -0.6, 0.2, 0) 0.5s 1 normal forwards;
    animation-duration: 1.2s;
    animation-delay: 0.6s;
    transform: scale(0.01) translateZ(0);
    opacity: 0;
    pointer-events: none;
  }


  @keyframes paper {
    0% {
      opacity: 0;
      transform: scale(0.01) translateZ(0)
    }
    50% {
      opacity: 1;
      transform: scale(1) translateZ(0)
    }
    80% {
      opacity: 1;
      transform: scale(1) translateZ(0)
    }
    100% {
      opacity: 0;
      transform: scale(1) translateZ(0)
    }
  }

  .stars {
    width: 100%;
    height: 100%;
    background: url('./assets/stars.png') center no-repeat;
    background-size: 100% auto;
    top: -300px;
    left: -300px;
    bottom: -300px;
    right: -300px;
    margin: auto;
    position: absolute;
    animation: stars 0.5s cubic-bezier(0.1, -0.6, 0.2, 0) 0.5s 1 normal forwards;
    animation-duration: 1.2s;
    animation-delay: 0.3s;
    transform: scale(0.01);
    opacity: 0;
    pointer-events: none;
  }

  @keyframes stars {
    0% {
      opacity: 0;
      transform: scale(0.01) translateZ(0)
    }
    50% {
      opacity: 1;
      transform: scale(1) translateZ(0)
    }
    75% {
      opacity: 1;
      transform: scale(1) translateZ(0)
    }
    100% {
      opacity: 0;
      transform: scale(1) translateZ(0)
    }
  }

  .wording {
    text-align: center;
    height: 20px;
    line-height: 20px;
    display: inline-block;
    margin-left: -180px;
    margin-right: -180px;
    position: absolute;
    bottom: -10px;
    animation: wording 0.5s ease-in-out 0.5s 1 normal forwards;
    animation-duration: 2s;
    animation-delay: 0.2s;
    transform: scale(0.01);
    opacity: 0;
    left: 0;
    right: 0;

    .content {
      padding: 0 20px;
      font-size: 12px;
      border-radius: 20px;
      color: rgb(249, 233, 27);
      background: rgba(0, 0, 0, .4);

      .nickname {
        display: inline-block;
        max-width: 100px;
        white-space: nowrap;
        word-break: keep-all;
        overflow: hidden;
        font-size: 12px;
        vertical-align: top;
        text-overflow: ellipsis;
      }
    }
  }

  @keyframes wording {
    0% {
      opacity: 0;
      transform: scale(0.01) translateZ(0);
    }
    50% {
      opacity: 1;
      transform: scale(0.5) translateZ(0);
    }
    70% {
      opacity: 1;
      transform: scale(0.5) translateZ(0);
    }
    100% {
      opacity: 0;
      transform: cale(0.4) translateZ(0);
    }
  }
}
</style>
