<template>
  <div
    class="timer-tool"
    :class="{ 'timer-tool__student': (!isTeacher && !isAssistant) }"
  >
    <div class="timer-tool__content">
      <div class="timer-tool__time-content-box">
        <div
          class="timer-tool__time-content"
          :class="['timer-status__' + currentStatus]"
        >
          <div
            class="timer-tool__time-number"
            :class="{ 'number-warning': remainSeconds === 0 }"
          >
            <button
              v-if="isTeacher || isAssistant"
              class="add"
              :class="{'touch-device': supportTouch}"
              @click="onChangeMinute(1, 0)"
            />
            <i>{{ minuteHigh }}</i>
            <button
              v-if="isTeacher || isAssistant"
              class="cut"
              :class="{'touch-device': supportTouch}"
              @click="onChangeMinute(-1, 0)"
            />
          </div>
          <div
            class="timer-tool__time-number"
            :class="{ 'number-warning': remainSeconds === 0 }"
          >
            <button
              v-if="isTeacher || isAssistant"
              class="add"
              :class="{'touch-device': supportTouch}"
              @click="onChangeMinute(0, 1)"
            />
            <i>{{ minuteLow }}</i>
            <button
              v-if="isTeacher || isAssistant"
              class="cut"
              :class="{'touch-device': supportTouch}"
              @click="onChangeMinute(0, -1)"
            />
          </div>
          <div
            class="timer-tool__time-number timer-tool__time-unit timer-tool__mr-10"
          >
            {{ $t('分') }}
          </div>
          <div
            class="timer-tool__time-number"
            :class="{ 'number-warning': remainSeconds <= 10 }"
          >
            <button
              v-if="isTeacher || isAssistant"
              class="add"
              :class="{'touch-device': supportTouch}"
              @click="onChangeSecond(1, 0)"
            />
            <i>{{ secondHigh }}</i>
            <button
              v-if="isTeacher || isAssistant"
              class="cut"
              :class="{'touch-device': supportTouch}"
              @click="onChangeSecond(-1, 0)"
            />
          </div>
          <div
            class="timer-tool__time-number"
            :class="{ 'number-warning': remainSeconds <= 10 }"
          >
            <button
              v-if="isTeacher || isAssistant"
              class="add"
              :class="{'touch-device': supportTouch}"
              @click="onChangeSecond(0, 1)"
            />
            <i>{{ secondLow }}</i>
            <button
              v-if="isTeacher || isAssistant"
              class="cut"
              :class="{'touch-device': supportTouch}"
              @click="onChangeSecond(0, -1)"
            />
          </div>
          <div class="timer-tool__time-number timer-tool__time-unit">
            {{ $t('秒') }}
          </div>
        </div>
        <svg
          v-show="currentStatus === ClockStatus.Start
            || currentStatus === ClockStatus.Pause
            || currentStatus === ClockStatus.Ended"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          class="timer-tool_progress-svg"
          :style="{
            '--duration': animationDuration + 's',
            '--progress': animationProgress,
            '--stroke-width': timerProgressBorderWidth + 'px',
            '--stroke-color': remainSeconds < 11 ? '#EE5F22' : '#006EFF',
          }"
        >
          <rect
            :style="{
              'animation-name':
                currentStatus === ClockStatus.Start
                  ? 'timer-animation-spin'
                  : '',
            }"
            :x="timerProgressBorderWidth / 2"
            :y="timerProgressBorderWidth / 2"
            rx="30"
            ry="30"
          />
        </svg>
      </div>

      <div
        v-if="!isTeacher && !isAssistant && currentStatus === ClockStatus.Pause"
        class="timer-tool__teacher-tip"
      >
        {{ $t('倒计时已暂停') }}
      </div>

      <div
        v-if="!isTeacher && !isAssistant && currentStatus === ClockStatus.Ended"
        class="timer-tool__teacher-tip"
      >
        {{ $t('倒计时已结束') }}
      </div>

      <!-- 学生端定时器,计时器通用 -->
      <div
        v-if="isTeacher || isAssistant"
        class="timer-tool__op-btn"
      >
        <!-- 计时器 -->
        <button
          v-if="currentStatus === ClockStatus.Ready"
          class="timer-tool__answer-btn"
          :class="{disabled: totalSeconds === 0}"
          @click="onStart"
        >
          {{ $t('开始倒计时') }}
        </button>
        <button
          v-if="currentStatus === ClockStatus.Start"
          class="timer-tool__answer-btn"
          @click="onPause"
        >
          {{ $t('暂停') }}
        </button>
        <button
          v-if="currentStatus === ClockStatus.Pause || currentStatus === ClockStatus.Ended"
          :class="[remainSeconds ? 'timer-tool__answer-btn-border' : 'timer-tool__answer-btn']"
          @click="onRestart"
        >
          {{ $t('重新开始') }}
        </button>
        <button
          v-if="currentStatus === ClockStatus.Pause && remainSeconds"
          class="timer-tool__answer-btn continue-btn"
          @click="onContinue"
        >
          {{ $t('继续') }}
        </button>
      </div>
    </div>
    <div
      v-if="isTeacher || isAssistant"
      class="timer-tool__btn-close"
    >
      <i
        class="el-icon-close"
        @click="onClose"
      />
    </div>
    <div class="timer-tool__reckon-time-bg" />

    <div class="ring-content">
      <audio
        ref="ring-ref"
        loop
        class="ring-audio"
        :src="ringMp3Src"
      />
    </div>
  </div>
</template>

<script>
import BaseComponent from '../../core/BaseComponent';

const getTaskString = (task) => {
  if (!task) {
    return '';
  }
  return `${task.status}-${task.remainSeconds.toFixed(2)}/${task.totalSeconds}@${task.time}`;
};

export default {
  extends: BaseComponent,
  data() {
    return {
      activeTimer: false, // 是否有激活过计时器
      ringMp3Src: 'https://res.qcloudclass.com/assets/ring.mp3',
      ringDuration: 5000, // 铃声时长
      MAX_SECONDS: 3599, // 59：59
      // 时钟状态
      ClockStatus: {
        Ready: 0,
        Start: 1,
        Pause: 2,
        Ended: 3,
      },
      currentStatus: 0, // 当前状态
      minuteHigh: 0, // 分钟（十位）
      minuteLow: 0, // 分钟（个位）
      secondHigh: 0, // 秒（十位）
      secondLow: 0, // 分钟（个位）
      totalSeconds: 60, // 总秒数(初始为60s)
      remainSeconds: 0, // 剩余的秒数
      timer: null, // 定时器
      timerLocalParams: {
        startSeconds: 0,
        startTime: 0,
        updateTime: 0,
      },
      taskId: 'class_timer', // 任务 ID
      // 任务协议
      taskContent: {
        status: 0, // 当前状态
        totalSeconds: 0, // 总的秒数
        remainSeconds: 0, // 剩余的秒数
        time: 0, // 记录当前秒数对应的时间戳
      },
      hasJoinClass: false, // 组件是否收到进房通知
      isTeacher: false, // 如果是老师，展示计时器的控制按钮
      isAssistant: false, // 如果是助教，展示计时器的控制按钮
      isStudent: false,
      timerProgressBorderWidth: 4,
      animationProgress: 0, // 初始剩余进度
      animationDuration: 0, // animation duration ( 动画剩余多少时间走完)
      supportTouch: TCIC.SDK.instance.supportTouch(), // 是否支持移动设备
    };
  },
  computed: {
    currentStatusDetail() {
      return `${this.currentStatus}-${this.remainSeconds}/${this.totalSeconds}`;
    },
  },
  mounted() {
    this.toggleComponentDrag(true, '.timer-tool');
    this.resetData();
    this.updateClock();
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Task_Updated, this.onTaskUpdate);
    this.makeSureClassJoined(this.onJoinClass);
    // 更新最初的状态
    TCIC.SDK.instance.getTasks(0).then((result) => {
      result.tasks.forEach((taskInfo) => {
        this.onTaskUpdate(taskInfo);
      });
    });
    // 监听课堂结束
    this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, (status) => {
      if (status === TCIC.TClassStatus.Has_Ended) {
        if (this.isTeacher) {
          // 如果是老师就关闭
          this.onClose();
        } else { // 如果是学生
          this.onEnded();
          this.hide();
        }
      }
    });
  },
  beforeDestroy() {
    this.pauseRing();
    clearTimeout(this.pauseRingTask);
    clearInterval(this.timer);
  },
  methods: {
    onJoinClass() {
      this.hasJoinClass = true;
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      this.isStudent = TCIC.SDK.instance.isStudent();
    },
    /*
     * 开始
     */
    onStart() {
      if (!this.totalSeconds) {
        return;
      }
      this.currentStatus = this.ClockStatus.Start;
      this.remainSeconds = this.totalSeconds;
      this.startTimer();
      this.updateTask();
    },

    /*
     * 暂停
     */
    onPause() {
      this.currentStatus = this.ClockStatus.Pause;
      if (this.timerLocalParams?.startTime) {
        const timerDuration = TCIC.SDK.instance.getServerTimestamp() - this.timerLocalParams.startTime;
        this.remainSeconds = Math.max(0, this.timerLocalParams.startSeconds - timerDuration / 1000);
        this.updateClock();
      }
      this.stopTimer();
      this.updateTask();
      this.updateClock();
    },
    /*
     * 继续
     */
    onContinue() {
      this.currentStatus = this.ClockStatus.Start;
      this.startTimer();
      this.updateTask();
    },
    /*
     * 重新开始
     */
    onRestart() {
      this.resetData();
      this.updateClock();
      // this.currentStatus = this.ClockStatus.Start;
      // this.startTimer();
      // this.updateTask();
    },

    /**
     * 任务结束
     */
    onEnded() {
      this.currentStatus = this.ClockStatus.Ended;
      this.stopTimer();
      this.updateTask();
    },

    setRing(ringMp3Src) {
      TCIC.SDK.instance.reportLog('set-ring', ringMp3Src);
      if (ringMp3Src) {
        this.ringMp3Src = ringMp3Src;
      }
    },

    resetData() {
      this.activeTimer = false;
      this.currentStatus = this.ClockStatus.Ready;
      this.remainSeconds = this.totalSeconds; // 耗时重置为总时长
      this.timeCounts = [];
      // 修改过 taskContent，reset 时也要一起重置
      if (this.taskContent.time) {
        this.taskContent = {
          status: this.currentStatus,
          totalSeconds: this.totalSeconds,
          remainSeconds: this.remainSeconds,
          time: TCIC.SDK.instance.getServerTimestamp(),
        };
      }
    },
    /*
     * 开始计时
     */
    startTimer() {
      this.activeTimer = true; // 启用过定时器
      this.stopTimer();
      this.pauseRing();
      this.updateClock();
      this.timerLocalParams = {
        startSeconds: this.remainSeconds,
        startTime: TCIC.SDK.instance.getServerTimestamp(),
        updateTime: TCIC.SDK.instance.getServerTimestamp(),
      };
      this.timer = setInterval(() => {
        const serverTime = TCIC.SDK.instance.getServerTimestamp();
        this.timerLocalParams.updateTime = serverTime;
        const timeFromStartMS = serverTime - this.timerLocalParams.startTime;
        this.remainSeconds = Math.max(0, this.timerLocalParams.startSeconds - timeFromStartMS / 1000);
        this.updateClock();
      }, 1000);

      // 初始剩余动画耗时
      this.animationDuration = this.remainSeconds + 1; // + 1 是因为动画是直接启动运行的，而定时器的数字是延迟1s
      // 初始化进度（剩余进度） = （总时长 - 剩余的时长） / 总时长
      // eslint-disable-next-line max-len
      this.animationProgress = Math.max(0, Math.min(1, ((this.totalSeconds + 1) - (this.remainSeconds + 1)) / (this.totalSeconds + 1)));
    },
    /*
     * 停止计时
     */
    stopTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
        this.timerLocalParams = {
          startSeconds: 0,
          startTime: 0,
          updateTime: 0,
        };
      }

      // 如果倒计时为0，则显示剩余动画时长是0，进度为1
      if (this.remainSeconds === 0) {
        this.animationDuration = 0;
        this.animationProgress = 1;
      } else {
        // 初始剩余动画耗时
        this.animationDuration = this.remainSeconds + 1; // + 1 是因为动画是直接启动运行的，而定时器的数字是延迟1s
        // 初始化进度（剩余进度） = （总时长 - 剩余的时长） / 总时长
        // eslint-disable-next-line max-len
        this.animationProgress = Math.max(0, Math.min(1, ((this.totalSeconds + 1) - (this.remainSeconds + 1)) / (this.totalSeconds + 1)));
      }
    },

    onChangeMinute(high, low) {
      this.minuteHigh = (this.minuteHigh + high + 6) % 6; ;
      this.minuteLow = (this.minuteLow + low + 10) % 10;
      this.totalSeconds =        (this.minuteHigh * 10 + this.minuteLow) * 60
        + (this.secondHigh * 10 + this.secondLow);
      this.totalSeconds = Math.min(this.MAX_SECONDS, this.totalSeconds);
      this.remainSeconds = this.totalSeconds;
      this.updateClock();
    },

    onChangeSecond(high, low) {
      this.secondHigh = (this.secondHigh + high + 6) % 6;
      this.secondLow = (this.secondLow + low + 10) % 10;
      this.totalSeconds =        (this.minuteHigh * 10 + this.minuteLow) * 60
        + (this.secondHigh * 10 + this.secondLow);
      this.totalSeconds = Math.min(this.MAX_SECONDS, this.totalSeconds);
      this.remainSeconds = this.totalSeconds;
      this.updateClock();
    },

    /*
     * 关闭
     */
    onClose() {
      this.stopTask().then(() => {
        this.stopTimer();
        this.resetData();
        this.updateClock();
        this.hide();
        this.pauseRing();
        clearTimeout(this.pauseRingTask);
        clearInterval(this.timer);
      });
    },
    /* @param taskInfo
       @param taskInfo.seq = 0
       @param taskInfo.taskId = '';
       @param taskInfo.status = 0;
       @param taskInfo.content = '';
       @param taskInfo.createTime = 0;
       @param taskInfo.updateTime = 0;
       @param taskInfo.expireTime = 0;
       @param taskInfo.bindingUser = '';
     */
    onTaskUpdate(taskInfo) {
      if (taskInfo.taskId !== this.taskId) {
        // 不属于定时器任务
        return;
      }

      const taskLogPrefix = `[TimerTool][${taskInfo.taskId}_${taskInfo.seq}]`;
      if (taskInfo.status === 0) {
        // 定时器任务停止，关闭定时器，关闭组件
        console.log(`${taskLogPrefix} onTaskComplete begin, localTask ${getTaskString(this.taskContent)}, currentStatus ${this.currentStatusDetail}`);
        this.resetData();
        this.stopTimer();
        this.updateClock();
        this.hide();
        console.log(`${taskLogPrefix} onTaskComplete end, localTask ${getTaskString(this.taskContent)}, currentStatus ${this.currentStatusDetail}`);
        return;
      }
      // 展示组件
      this.show();

      if (JSON.stringify(this.taskContent) !== taskInfo.content) {
        const contentJson = JSON.parse(taskInfo.content);
        const serverTime = TCIC.SDK.instance.getServerTimestamp();
        const offsetMS = serverTime - contentJson.time;
        console.log(
          `${taskLogPrefix}\n ==== onTaskContent ${contentJson.status} begin, offsetMS ${offsetMS}, newTask ${getTaskString(contentJson)},`,
          `localTask ${getTaskString(this.taskContent)}, currentStatus ${this.currentStatusDetail}`,
        );
        // 快速点击 时序问题
        if (contentJson.time < this.taskContent.time) {
          console.warn(`${taskLogPrefix} newTask before localTask, newTask ${getTaskString(contentJson)}, localTask ${getTaskString(this.taskContent)}`);
          return;
        }
        let taskTime = contentJson.time;
        this.totalSeconds = contentJson.totalSeconds;
        // 定时器总时长
        this.totalSeconds = Math.min(this.MAX_SECONDS, this.totalSeconds);
        switch (contentJson.status) {
          case this.ClockStatus.Start:
            this.currentStatus = this.ClockStatus.Start;
            this.remainSeconds = contentJson.remainSeconds;
            if (offsetMS >= 0) {
              const acceptedMaxRttMS = 2000;
              if (contentJson.time < TCIC.SDK.instance.getServerTimestampAfterJoin()) {
                // 进房前start需要调整
                // 可能还没收到 onJoinClass，不能用 this.isTeacher/this.isAssistant
                if (TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isAssistant()) {
                  // 有权限的，用自己的时间 updateTask
                  let adjustSec = offsetMS / 1000;
                  adjustSec = Math.min(adjustSec, contentJson.remainSeconds);
                  this.remainSeconds = contentJson.remainSeconds - adjustSec;
                  taskTime = serverTime;
                  console.log(`${taskLogPrefix} ClockStatus.Start before join, offsetMS ${offsetMS}, isTeacherOrAssistant, remainSeconds -${adjustSec}, updateTask`);
                  this.updateTask();
                } else {
                  // 没有权限的，留点合理延迟
                  const acceptedRttMS = Math.min(TCIC.SDK.instance.getJoinRtt(), acceptedMaxRttMS);
                  let adjustSec = Math.floor((offsetMS - acceptedRttMS) / 1000); // 注意是 floor，学生端只能延后不能提前
                  adjustSec = Math.min(adjustSec, contentJson.remainSeconds);
                  this.remainSeconds = contentJson.remainSeconds - adjustSec;
                  taskTime = contentJson.time + adjustSec * 1000;
                  console.log(`${taskLogPrefix} ClockStatus.Start before join, offsetMS ${offsetMS}, acceptedRttMS ${acceptedRttMS}, remainSeconds -${adjustSec}`);
                }
              } else if (offsetMS > acceptedMaxRttMS) {
                // 进房后start，相差太多还是兜个底
                let adjustSec = Math.floor((offsetMS - acceptedMaxRttMS) / 1000);
                adjustSec = Math.min(adjustSec, contentJson.remainSeconds);
                this.remainSeconds = contentJson.remainSeconds - adjustSec;
                taskTime = contentJson.time + adjustSec * 1000;
                console.warn(`${taskLogPrefix} ClockStatus.Start after join but offsetMS ${offsetMS} > ${acceptedMaxRttMS}, remainSeconds -${adjustSec}`);
              }
            }
            this.startTimer();
            break;
          case this.ClockStatus.Pause:
            this.currentStatus = this.ClockStatus.Pause;
            //  本次剩余时长
            this.remainSeconds = contentJson.remainSeconds;
            this.stopTimer();
            break;
          case this.ClockStatus.Ended:
            this.currentStatus = this.ClockStatus.Ended;
            //  本次剩余时长
            this.remainSeconds = contentJson.remainSeconds;
            this.stopTimer();
            break;
          default:
            break;
        }
        this.taskContent = {
          status: this.currentStatus,
          totalSeconds: this.totalSeconds,
          remainSeconds: this.remainSeconds,
          time: taskTime,
        };
        this.updateClock();
        console.log(
          `${taskLogPrefix}\n onTaskContent ${contentJson.status} end, offsetMS ${offsetMS}, newTask ${getTaskString(contentJson)},`,
          `localTask ${getTaskString(this.taskContent)}, currentStatus ${this.currentStatusDetail}\n`,
        );
      }
    },
    /*
     * 更新任务
     */
    updateTask() {
      if (!this.isTeacher && !this.isAssistant) {
        // 学生没有权限更新计时器任务
        return;
      }
      this.taskContent = {
        status: this.currentStatus,
        totalSeconds: this.totalSeconds,
        remainSeconds: Number(Math.max(0, this.remainSeconds).toFixed(2)),
        time: TCIC.SDK.instance.getServerTimestamp(),
      };
      console.log(`[TimerTool] updateTask, localTask ${getTaskString(this.taskContent)}`);

      TCIC.SDK.instance
        .updateTask(this.taskId, JSON.stringify(this.taskContent))
        .then((task) => {
          // 更新定时器成功
        })
        .catch((error) => {
          // 更新定时器失败
          window.showToast(error.errorMsg);
          TCIC.SDK.instance.reportEvent('update_timer_tool', error, -1);
        });
    },

    // 停止任务
    stopTask() {
      return new Promise((resolve, reject) => {
        if (!this.isTeacher && !this.isAssistant) {
          // 学生没有权限更新计时器任务
          reject();
        }
        TCIC.SDK.instance.stopTask(this.taskId).then(() => {
          resolve();
        })
          .catch((error) => {
            // 这些当作成功，关闭窗口
            const ignoreErrorCodes = [
              10301, // 课堂已结束
              10308, // 任务不存在
              10309, // 任务已结束
            ];
            if (ignoreErrorCodes.indexOf(error.errorCode) >= 0) {
              resolve();
            } else {
              // 先只上报，不提示，和其他组件保持一致
              // window.showToast(error.errorMsg);
              TCIC.SDK.instance.reportEvent('stop_timer_tool', error, -1);
              reject();
            }
          });
      });
    },

    // 更新时间
    updateClock() {
      // 计时器
      if (this.remainSeconds <= 0) {
        this.remainSeconds = 0;
        if (this.currentStatus === this.ClockStatus.Start) {
          this.onEnded();
        }
        // 如果有激活过定时器，且定时器计数为0，则需要响铃
        if (this.activeTimer) {
          this.playRing();
          this.activeTimer = false;
        }
      } else {
        this.remainSeconds = Math.min(3599, this.remainSeconds);
      }
      const remainSeconds = Math.ceil(this.remainSeconds);
      const minutes = parseInt(remainSeconds / 60, 10);
      const seconds = remainSeconds % 60;
      this.minuteHigh = parseInt(minutes / 10, 10);
      this.minuteLow = minutes % 10;
      this.secondHigh = parseInt(seconds / 10, 10);
      this.secondLow = seconds % 10;
    },

    // 播放铃声
    playRing() {
      clearTimeout(this.pauseRingTask);
      const ringRef = this.$refs['ring-ref'];
      ringRef.currentTime = 0;
      ringRef.play();
      TCIC.SDK.instance.reportLog('timer-play-ring', '');
      // 响铃任务
      this.pauseRingTask = setTimeout(() => {
        this.pauseRing();
        if (this.isStudent) {
          this.hide();
        }
      }, this.ringDuration);
    },

    // 暂停响铃
    pauseRing() {
      console.log('====================  pauseRing');
      const ringRef = this.$refs['ring-ref'];
      ringRef.pause();
      TCIC.SDK.instance.reportLog('timer-pause-ring', '');
    },
  },
};
</script>
<style lang="less">
@--color-primary: #006eff;
@--color-public: #14181d;
@--color-disable: #b9bbbc;

@timer-board-radius: 30px;
@timer-content-width: 300px;
@timer-content-height: 152px;

.timer-tool {
  width: 100%;
  height: 100%;
  line-height: 1;
  color: #393939;
  &:hover {
    cursor: move;
  }
  &.timer-tool__student {
    position: relative;
    height: 351px;
    .timer-tool__content {
      height: 292px;
    }
    .timer-tool__teacher-tip {
      font-size: 24px;
      text-align: center;
      font-weight: bold;
      margin-top: 30px;
      color: #393939;
      z-index: 3;
    }
  }
  /*背景*/
  .timer-tool__reckon-time-bg {
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 2;
    width: 424px;
    height: 151px;
    background: url('./assets/timer-tool__clock.svg') no-repeat bottom;
  }
  /*关闭按钮*/
  .timer-tool__btn-close {
    position: absolute;
    right: 0;
    top: 30px;
    z-index: 9;
    display: flex;
    align-items: center;
    width: 35px;
    height: 40px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 0 4px 4px 0;
    color: rgba(#979797, 0.5);
    i {
      font-size: 30px;
    }
    &:hover {
      color: rgba(#fff, 0.9);
      background: rgba(0, 0, 0, 0.2);
      transition: all 0.5s;
      cursor: pointer;
    }
    &:active {
      color: rgba(#fff, 0.9);
      background: rgba(0, 0, 0, 0.2);
      transition: all 0.5s;
      cursor: pointer;
    }
  }
  .timer-tool__content {
    width: 390px;
    height: 332px;
    border-radius: 40px;
    padding: 5px;
    background-image: linear-gradient(-43deg, #c5c5c5 0%, #dadada 100%),
      linear-gradient(148deg, #f1f1f1 0%, #ffead8 100%);
    background-clip: content-box, padding-box;

    .timer-tool__time-content-box {
      position: relative;
      width: @timer-content-width;
      height: @timer-content-height;
      margin: 40px 40px 0 40px;
      background: #e1e1e1;
      border-radius: @timer-board-radius;
    }

    .timer-tool__time-content {
      padding: 10px 18px;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &:hover {
        .timer-tool__time-number > button {
          opacity: 1;
          transition: all 0.8s;
        }
      }

      &.timer-status__0 {
        // 准备中
        .timer-tool__time-number.number-warning {
          color: #393939;
        }
      }

      &.timer-status__1, &.timer-status__2 {
        // 开始了 和 暂停状态都不能调整倒计时
        .timer-tool__time-number {
          button.add,
          button.cut {
            display: none;
          }
        }
      }

      .timer-tool__time-number {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 46px;
        position: relative;
        flex-shrink: 0;
        &.number-warning {
          color: #ee5f22;
        }
        button {
          width: 35px;
          height: 16px;
          opacity: 0;
          &.cut {
            position: absolute;
            bottom: 0;
            background: url('./assets/timer-tool__down.svg') no-repeat;
            &:hover, &:active {
              background: url('./assets/timer-tool__down_a.svg') no-repeat;
            }
          }
          &.add {
            position: absolute;
            top: 0;
            background: url('./assets/timer-tool__up.svg') no-repeat;
            &:hover, &:active{
              background: url('./assets/timer-tool__up_a.svg') no-repeat;
            }
          }

          &.touch-device {
            opacity: 1;
          }
        }
        i {
          font-size: 110px;
          font-family: Number;
          margin: 14px 0 6px 0;
          cursor: default;
        }
        &.timer-tool__time-unit {
          font-size: 24px;
          width: auto;
          align-self: flex-end;
          font-weight: 600;
          margin-bottom: 26px;
        }
        &.timer-tool__mr-10 {
          margin-right: 10px;
        }
        &.countdown {
          color: #ee5f22;
        }
      }
    }

    .timer-tool_progress-svg {
      // 周长
      @perimeter: calc(
        ~'@{timer-content-width} * 2 - 120px + @{timer-content-height} * 2 - 120px + 2 * 3.14 * 30px'
      );
      @perimeter-offset: calc(~'@{perimeter} * -1');
      @progress: calc(~'@{perimeter-offset} * var(--progress)');
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      rect {
        fill: none;
        stroke: var(--stroke-color);
        stroke-width: var(--stroke-width);
        stroke-dasharray: @perimeter @perimeter;
        stroke-dashoffset: @progress;
        animation-timing-function: linear;
        animation-duration: var(--duration);
        animation-fill-mode: forwards;
        width: calc(100% - var(--stroke-width));
        height: calc(100% - var(--stroke-width));
      }

      @keyframes timer-animation-spin {
        to {
          stroke-dashoffset: @perimeter-offset;
        }
      }
    }

    .timer-tool__op-btn {
      position: relative;
      z-index: 3;
      margin-top: 40px;
      text-align: center;
    }
  }

  .timer-tool__reckon-time {
    position: absolute;
    left: -45px;
    top: 40px;
    width: 76px;
    .el-scrollbar__wrap {
      max-height: 260px;
      padding-right: 12px;
    }
    .timer-tool__time-note {
      display: flex;
      flex-direction: column;
      i {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 40px;
        margin-bottom: 16px;
        font-family: Number;
        font-size: 22px;
        background: url('./assets/timer-tool__note.svg') no-repeat;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

/* 渐变色边框按钮 */
button {
  border: none;
  background: none;
  outline: none;
  box-sizing: border-box;
  font-weight: normal;
  &.timer-tool__answer-btn {
    min-width: 144px;
    height: 58px;
    border-radius: 50px;
    padding: 4px 20px;
    font-size: 24px;
    color: #fff;
    background-image: linear-gradient(@--color-public, @--color-public),
      linear-gradient(148deg, #e7e7e7 0%, #a5a5a5 100%);
    background-clip: padding-box, padding-box;
    // transition: all 2s;
    &:hover:not(.disabled) {
      background-image: linear-gradient(@--color-primary, @--color-primary),
      linear-gradient(148deg, #e7e7e7 0%, #a5a5a5 100%);
      // transition: all 2s;
    }
    &:focus {
      background-image: linear-gradient(@--color-primary, @--color-primary),
      linear-gradient(148deg, #e7e7e7 0%, #a5a5a5 100%);
      // transition: all 2s;
    }
    &:active:not(.disabled) {
      background-image: linear-gradient(@--color-primary, @--color-primary),
      linear-gradient(148deg, #e7e7e7 0%, #a5a5a5 100%);
      // transition: all 2s;
    }
    &.disabled {
      opacity: 0.3;
    }

    &.continue-btn {
      margin-left: 16px;
    }
  }
  &.timer-tool__answer-btn-border {
    min-width: 140px;
    height: 52px;
    border-radius: 50px;
    font-size: 24px;
    color: @--color-public;
    border: 2px solid @--color-public;
    &:hover {
      border-color: @--color-primary;
      color: @--color-primary;
    }
    &:active {
      border-color: @--color-primary;
      color: @--color-primary;
    }

    &.timer-ended {

    }
  }
}
</style>
