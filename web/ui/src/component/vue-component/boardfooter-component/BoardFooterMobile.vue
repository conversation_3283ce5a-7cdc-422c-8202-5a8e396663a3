<!-- eslint-disable vue/no-mutating-props -->
<template>
  <div class="board-footer-component board-footer-mobile">
    <div
      id="board-footer-mobile__wrap"
      :class="!isPageFile ? 'center' : ''"
    >
      <!--
        课件控制工具栏（上一页、下一页）
        [新增] 未开课时如果有默认课件也支持其他角色预览
      -->
      <div
        v-if="isPageFile && (isTeacher || !isClassStarted)"
        class="board-footer-mobile__page"
      >
        <a
          :class="[currentPage === 1 ? 'disabled' : '', 'board-footer-mobile__btn']"
          @click="onPrevPage"
        >
          <i class="board-footer-mobile__prev" />
        </a>
        <div class="board-footer-mobile__page-text">
          <span>{{ currentPage }}</span>
          <span> / {{ totalPage }}</span>
        </div>
        <a
          :class="[currentPage === totalPage ? 'disabled' : '', 'board-footer-mobile__btn']"
          @click="onNextPage"
        >
          <i class="board-footer-mobile__next" />
        </a>
      </div>
      <!--白板工具栏，右侧-和ppt同时出现-->
      <div
        v-if="isTeacher"
        class="board-tool right board-tool__blur"
      >
        <button class="board-list">
          <IcBoardToolAdd
            class="board-tool__add"
            @click.stop="$parent.onAddBoard"
          />
        </button>
        <el-popover
          ref="boardListPopper"
          v-model="listActive"
          popper-class="board-tool__back-popper"
          placement="top-end"
          trigger="manual"
          :visible-arrow="false"
          class="board-list"
        >
          <div
            ref="board-tool__list-popper"
            class="board-tool__board-drop board-tool__list-popper"
          >
            <el-scrollbar>
              <ul>
                <li
                  v-for="item in fileInfoList"
                  :key="item.fileId"
                  :class="{ 'board-tool__li-active' : item.fileId === currentBoardFileId }"
                  @click="switchBoard(item.fileId, item.boardId)"
                >
                  <i :class="item.type" />
                  <label>{{ item.title }}</label>
                  <i
                    v-if="item.canDel"
                    class="board-tool__btn-close el-icon-close"
                    @click.stop="$parent.onDeleteBoard(item.fileId, item.boardId)"
                  />
                </li>
              </ul>
            </el-scrollbar>
          </div>
          <button
            slot="reference"
            ref="board-tool__list"
            class="board-tool__btn-board-list"
            :class="{'active' : listActive}"
            @click="$parent.onBoardList"
          >
            <IcBoardToolList
              class="board-tool__list"
              :class="{'active' : listActive}"
            />
          </button>
        </el-popover>
      </div>
      <!-- 涂鸦工具栏 未开课且不是老师的情况下不展示涂鸦工具 -->
      <div
        v-if="!(!isClassStarted && !isTeacher) && false"
        v-show="!hideBrush"
        :class="['brush-tool-wrap', {'student-brush-wrap': isStudent && isPortrait, 'is-ios': isIOSNative, 'is-andriod': isAndroidNative}]"
      >
        <div class="board-brush">
          <template v-for="(tool, index) in toolTypes">
            <a
              v-if="tool.key === currentToolType"
              :key="index"
              :class="['board-footer-mobile__' + tool.key, 'board-footer-mobile__btn']"
              @click="toggleToolBox()"
            >
              <i
                v-if="tool.key !== 'tool-text'"
                :ref="'tool-button_' + tool.key"
                :title="tool.value"
              />
              <i
                v-else
                :ref="'tool-button_' + tool.key"
                :title="tool.value"
                :style="tooTextIcon"
              />
              <span
                :ref="'tool-button_' + tool.key"
                :title="tool.value"
              />
              <span
                v-if="tool.key === 'tool-pen'"
                class="brush-tool__corner"
                :class="['tool-type-' + currentBrushType, 'tool-box-icon-pos']"
                :style="{ '--border-color': currentBrushColor }"
              />
            </a>
            <template v-if="tool.key === 'tool-pen'">
              <div
                :key="tool.key"
                ref="tool-box"
                class="board-footer-mobile__tool-box"
              >
                <div
                  class="board-footer-mobile__tool-box-wrap"
                  :class="[
                    isShowToolBox ? 'show-tool-box' : 'hide-tool-box',
                    isPen ? 'board-footer-mobile__tool-box-pen' : '',
                    isText ? 'board-footer-mobile__tool-box-text' : '',
                    isEraser || isSelect ? 'board-footer-mobile__tool-box-short' : '',
                  ]"
                >
                  <div
                    v-if="isPen"
                    class="board-footer-mobile__tool-box-section"
                  >
                    <ul>
                      <li
                        v-for="(thin, sub_index) in brushThins"
                        :key="sub_index"
                        :class="{ actived: thin.key === currentThin }"
                        @click="setBrushThin(thin)"
                      >
                        <span :class="['board-footer-mobile__tool-box-thin', thin.key]" />
                      </li>
                    </ul>
                  </div>
                  <div
                    v-if="isPen"
                    class="board-footer-mobile__tool-box-section"
                  >
                    <ul>
                      <li
                        v-for="(type, sub_index) in brushTypes"
                        :key="sub_index"
                        :class="{ actived: type.key === currentBrushType }"
                        @click="setBrushType(type)"
                      >
                        <span
                          :class="['tool-pen', type.key,
                                   currentBrushType === 'graphical-wave' && type.key === 'graphical-wave'? 'graphical-wave-sel' : '']"
                        />
                      </li>
                    </ul>
                  </div>
                  <div
                    v-if="isText"
                    class="board-footer-mobile__tool-box-section"
                  >
                    <ul>
                      <li
                        v-for="(value, sub_index) in textSize.values"
                        :key="sub_index"
                        class="board-footer-mobile__tool-box-text-li"
                        :class="{ selected: textSize.selectedIndex === sub_index }"
                        @click="setTextSize(sub_index)"
                      >
                        <span :class="['text-' + value]">{{ value }}</span>
                      </li>
                    </ul>
                  </div>
                  <div
                    v-if="isText"
                    class="board-footer-mobile__tool-box-section color-pick__wrap"
                  >
                    <ul>
                      <li
                        v-for="(color, sub_index) in brushColors"
                        :key="sub_index"
                        class="board-footer-mobile__tool-box-color-margin"
                        @click="setTextColor(color)"
                      >
                        <div class="board-footer-mobile__tool-box-color-wrap">
                          <span
                            class="board-footer-mobile__tool-box-color"
                            :style="'background-color:' + color"
                          />
                          <span
                            v-if="color === currentTextColor"
                            class="board-footer-mobile__tool-box-sel-color"
                          />
                        </div>
                      </li>
                    </ul>
                  </div>
                  <div
                    v-if="isPen"
                    class="board-footer-mobile__tool-box-section color-pick__wrap"
                  >
                    <ul>
                      <li
                        v-for="(color, sub_index) in brushColors"
                        :key="sub_index"
                        class="board-footer-mobile__tool-box-color-margin"

                        @click="setBrushColor(color)"
                      >
                        <div class="board-footer-mobile__tool-box-color-wrap">
                          <span
                            class="board-footer-mobile__tool-box-color"
                            :style="'background-color:' + color"
                          />
                          <span
                            v-if="color === currentBrushColor"
                            class="board-footer-mobile__tool-box-sel-color"
                          />
                        </div>
                      </li>
                    </ul>
                  </div>
                  <div class="board-footer-mobile__tool-box-section">
                    <ul class="board-footer-mobile__tool-operator-ul">
                      <li
                        v-for="(sub_tool, sub_index) in toolTypes"
                        :key="sub_index"
                        class="board-footer-mobile__tool-operator-li"
                        :class="[
                          sub_tool.key === currentToolType ? 'active' : '',
                          'board-footer-mobile__' + sub_tool.key,
                          'board-footer-mobile__btn',
                        ]"
                        :style="sub_tool.key === 'tool-text' ? tooTextIcon : ''"
                        @click="setToolType(sub_tool)"
                      >
                        <span
                          :ref="'tool-button_' + sub_tool.key"
                          :title="sub_tool.value"
                        />
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </template>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BaseComponent from '../../core/BaseComponent';
import { getStartedStatus } from '@/util/class';
import ToolBarIcon from '@/component/vue-component/boardtool-component/ToolIcon';
import Constant from '@/util/Constant';
import DocumentUtil from '@/util/Document';
import IcBoardToolAdd from './assets/svg-component/board-tool__add.svg';
import IcBoardToolList from './assets/svg-component/board-tool__list.svg';

const TEduBoard = window.TEduBoard;
export default {
  components: {
    IcBoardToolAdd,
    IcBoardToolList,
  },
  extends: BaseComponent,
  mixins: [ToolBarIcon],
  props: {
    fileInfoList: {
      type: Array,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: [],
    },
    currentPage: {
      type: Number,
      required: true,
    },
    // 当前所在白板ID或文件ID
    currentBoardFileId: {
      type: String,
      required: true,
    },
    // 总页数
    totalPage: {
      type: Number,
      required: true,
    },
    isPdfOrDocFile: {
      type: Boolean,
      required: true,
    },
    isPageFile: {
      type: Boolean,
      required: true,
    },
    listActive: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      teduBoard: null,
      // 当前白板是否在播放视频
      hideBrush: false,
      currentToolType: 'tool-pen', // 默认涂鸦
      toolTypes: [
        { key: 'tool-pen', value: TEduBoard.TOOL_TYPE.TEDU_BOARD_TOOL_TYPE_PEN },
        { key: 'tool-select', value: TEduBoard.TOOL_TYPE.TEDU_BOARD_TOOL_TYPE_RECT_SELECT },
        { key: 'tool-eraser', value: TEduBoard.TOOL_TYPE.TEDU_BOARD_TOOL_TYPE_ERASER },
        { key: 'tool-text', value: TEduBoard.TOOL_TYPE.TEDU_BOARD_TOOL_TYPE_TEXT },
      ],
      brushColors: [
        '#FFFFFF',
        '#9C9B9B',
        '#2E3039',
        '#000000',
        '#FF0000',
        '#00FF01',
        '#FFD300',
        '#FF6300',
        '#006EFF',
        '#00FFFA',
        '#6A00FF',
        '#FF959B',
      ],
      currentBrushColor: '#FF0000',
      currentTextColor: '#FF0000',
      brushTypes: [
        { key: 'graphical-wave', value: TEduBoard.TOOL_TYPE.TEDU_BOARD_TOOL_TYPE_PEN },
        { key: 'graphical-rect', value: TEduBoard.TOOL_TYPE.TEDU_BOARD_TOOL_TYPE_RECT },
        { key: 'graphical-line', value: TEduBoard.TOOL_TYPE.TEDU_BOARD_TOOL_TYPE_LINE },
        { key: 'graphical-line-dotted', value: TEduBoard.TOOL_TYPE.TEDU_BOARD_TOOL_TYPE_LINE, lineType: TEduBoard.TEduBoardLineType.TEDU_BOARD_LINE_TYPE_DOTTED },
        { key: 'graphical-arrow', value: TEduBoard.TOOL_TYPE.TEDU_BOARD_TOOL_TYPE_LINE, endArrowType: TEduBoard.TEduBoardArrowType.TEDU_BOARD_ARROW_TYPE_SOLID },
        { key: 'graphical-round', value: TEduBoard.TOOL_TYPE.TEDU_BOARD_TOOL_TYPE_OVAL },
      ],
      currentBrushType: 'graphical-wave',
      brushThins: [
        { key: 'size-4', value: 25 },
        { key: 'size-8', value: 50 },
        { key: 'size-12', value: 75 },
        { key: 'size-16', value: 100 },
      ],
      currentThin: 'size-8',
      isShowToolBox: false,
      textSize: {
        selectedIndex: 1,
        values: [14, 16, 18, 20],
        sizes: [280, 320, 360, 400],
      },
      // 是否是老师
      isTeacher: false,
      isStudent: false,
      classId: null,
      isPortrait: false,
      isIOSNative: false,
      isClassStarted: false,
      isAndroidNative: false,
    };
  },
  computed: {
    brushColor() {
      return {
        textToolSelectedColor: this.currentTextColor,
      };
    },
    isText() {
      return this.currentToolType === 'tool-text';
    },
    isEraser() {
      return this.currentToolType === 'tool-eraser';
    },
    isSelect() {
      return this.currentToolType === 'tool-select';
    },
    isPen() {
      return this.currentToolType === 'tool-pen';
    },
  },
  mounted() {
    this.isStudent = TCIC.SDK.instance.isStudent();
    this.isClassStarted = getStartedStatus();
    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.isPortrait = orientation === TCIC.TDeviceOrientation.Portrait;
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, (status) => {
      this.isClassStarted = status === TCIC.TClassStatus.Already_Start;
    });
    this.isIOSNative = TCIC.SDK.instance.isIOS() && TCIC.SDK.instance.isMobileNative();
    this.isAndroidNative = TCIC.SDK.instance.isAndroid() && TCIC.SDK.instance.isMobileNative();
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Joined_Class, true).then(() => {
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.classId = TCIC.SDK.instance.getClassInfo().classId;
      TCIC.SDK.instance.promiseState(TCIC.TMainState.Board_Ready, true)
        .then(() => {
          this.teduBoard = TCIC.SDK.instance.getBoard();

          // 视频课件不显示白板的笔刷工具
          const fileId = this.teduBoard.getCurrentFile();
          const fileInfo = this.teduBoard.getFileInfo(fileId);
          if (fileInfo.fileType === 4) {
            this.hideBrush = true;
          }
          this.teduBoard.on(TEduBoard.EVENT.TEB_SWITCHFILE, (fileId) => {
            if (fileId !== '#DEFAULT') {
              const fileInfo = this.teduBoard.getFileInfo(fileId);
              // 视频课件
              if (fileInfo.fileType === 4) {
                this.hideBrush = true;
              } else {
                this.hideBrush = false;
              }
            } else {
              this.hideBrush = false;
            }
          });
          this.$nextTick(() => {
            // 获取最近使用的画刷颜色
            this.getHistoryBrushColor();
          });
        });
    });
    this.addLifecycleTCICStateListener(Constant.TStateBoardToolType, (toolTypeValue) => {
      const brushType = this.brushTypes.find(brushType => brushType.value === toolTypeValue);
      const toolType = this.toolTypes.find(toolType => toolType.value === toolTypeValue);
      if (brushType) {
        this.currentToolType = 'tool-pen';
        this.currentBrushType = brushType.key;
        if (brushType.key === 'graphical-line') {
          if (this.teduBoard.getGraphStyle().lineType === TEduBoard.TEduBoardLineType.TEDU_BOARD_LINE_TYPE_DOTTED) {
            this.currentBrushType = 'graphical-line-dotted';
          } else if (this.teduBoard.getGraphStyle().endArrowType === TEduBoard.TEduBoardArrowType.TEDU_BOARD_ARROW_TYPE_SOLID) {
            this.currentBrushType = 'graphical-arrow';
          }
        }
      } else if (toolType) {
        this.currentToolType = toolType.key;
      }
    });
  },
  beforeDestroy() {
  },
  methods: {
    switchBoard(fileId, boardId) {
      this.$parent.onGotoBoardFile(fileId, boardId);
      this.setToolType(this.toolTypes.find(tool => tool.key === this.currentToolType));
    },
    async onPrevPage() {
      await DocumentUtil.addSnapshotMark('onPrevPage');
      this.teduBoard.prevBoard();
    },
    async onNextPage() {
      await DocumentUtil.addSnapshotMark('nextPage');
      this.teduBoard.nextBoard();
    },

    setToolType(tool) {
      if (!this.teduBoard) {
        return;
      }
      this.currentToolType = tool.key;

      const teduBoardToolType = this.currentToolType === 'tool-pen'
        ? this.brushTypes.find(brush => brush.key === this.currentBrushType).value
        : tool.value;
      this.teduBoard.setToolType(teduBoardToolType);


      TCIC.SDK.instance.setState(Constant.TStateBoardToolType, teduBoardToolType);

      if (tool.key === 'tool-pen' || tool.key === 'tool-text') {
        return;
      }

      if (tool.key === 'tool-pen') {
        this.toggleToolBox();
      } else {
        this.toggleToolBox(false);
      }
    },

    // 显示涂鸦工具面板
    toggleToolBox(show) {
      if (typeof show === 'boolean') {
        this.isShowToolBox = show;
      } else {
        this.isShowToolBox = !this.isShowToolBox;
      }
      // 点击其他地方
      if (this.isShowToolBox) {
        document.addEventListener('touchstart', this.hideToolBox, true);
      } else {
        document.removeEventListener('touchstart', this.hideToolBox, true);
      }
    },

    hideToolBox(e) {
      if (this.$refs['tool-button_tool-pen'][0].contains(e.target) || this.$refs['tool-box'][0].contains(e.target)) {
        // 如果点击在画笔图标，跳过操作
        // 如果在选择画板点击，跳过操作
        return;
      }
      this.toggleToolBox(false);
    },

    // 设置笔刷类型
    setBrushType(type) {
      this.currentBrushType = type.key;
      this.teduBoard.setToolType(type.value);
      TCIC.SDK.instance.setState(Constant.TStateBoardToolType, type.value);
      this.setHistoryBrushType(type);

      let lineType = TEduBoard.TEduBoardLineType.TEDU_BOARD_LINE_TYPE_SOLID;
      let endArrowType = TEduBoard.TEduBoardArrowType.TEDU_BOARD_ARROW_TYPE_NONE;
      console.log({ lineType, endArrowType });
      if (type.lineType !== undefined) {
        lineType = type.lineType;
      }
      if (type.endArrowType !== undefined) {
        endArrowType = type.endArrowType;
      }
      this.teduBoard.setGraphStyle({ lineType, endArrowType });
    },

    // 设置笔刷粗细
    setBrushThin(thin) {
      this.currentThin = thin.key;
      this.teduBoard.setBrushThin(thin.value);
    },

    // 设置笔刷颜色
    setBrushColor(color) {
      this.currentBrushColor = color;
      this.teduBoard.setBrushColor(color);
      this.setHistoryBrushColor(color);
    },

    setTextColor(color) {
      this.currentTextColor = color;
      this.teduBoard.setTextColor(color);
    },

    setTextSize(index) {
      if (!this.teduBoard) return;
      this.textSize.selectedIndex = index;
      const size = this.textSize.sizes[index];
      this.teduBoard.setTextSize(size);
    },

    onComponentVisibilityChange(visible) {
      if (visible) {
        // 重置为画笔
        // this.setToolType(this.currentToolType);
      }
      // 可见性发生变化，关闭工具面板
      this.toggleToolBox(false);
    },
    /**
     * 获取历史笔刷数据
     */
    getHistoryBrushColor() {
      const key = `TIW_BRUSH_HISTORY_COLOR-${this.classId}`;
      const historyBrushColor = localStorage.getItem(key);
      if (historyBrushColor) {
        this.setToolType(this.toolTypes[0]);
        this.setBrushColor(historyBrushColor);
      }
    },

    /**
     * 设置历史的笔刷数据
     */
    setHistoryBrushColor(color) {
      if (this.classId) {
        const key = `TIW_BRUSH_HISTORY_COLOR-${this.classId}`;
        localStorage.setItem(
          key,
          color,
        );
      }
    },
    /**
     * 获取历史笔刷类型
     */
    getHistoryBrushType() {
      const key = `TIW_BRUSH_HISTORY_TYPE-${this.classId}`;
      const historyBrushType = localStorage.getItem(key);
      if (historyBrushType) {
        this.setBrushType(JSON.parse(historyBrushType));
        this.currentBrushType = JSON.parse(historyBrushType).key;
      }
    },

    /**
     * 设置历史的笔刷类型
     */
    setHistoryBrushType(bruchType) {
      if (this.classId) {
        const key = `TIW_BRUSH_HISTORY_TYPE-${this.classId}`;
        localStorage.setItem(
          key,
          JSON.stringify(bruchType),
        );
      }
    },
  },
};
</script>

<style lang="less">

.foot-tool, .foot-tool .board-footer-mobile {
  pointer-events: none!important;
}
.tedu-component-board-content {
  height: calc(100%);
  background-color: #222224;
}

.xl-course-control-com-default {
  position: absolute;
  top: calc(100% - 58px);
  left: 88px;
  z-index: 8;
}

.board-footer-mobile {
  font-size: 16px;
  text-align: center;
}

#board-footer-mobile__wrap {
  pointer-events: none;
  width: 100%;
  display: flex;
  margin-left: 4px;
  justify-content: flex-end;
  flex-shrink: 0;
}
.center {
  justify-content: center;
}

#board-footer-mobile__wrap > div {
  right: 60px;
  bottom: 0px;
  display: inline-block;
  padding-top: 6px;
  padding-bottom: 8px;
  background-color: rgba(120,120,122,.5);
  border-radius: 8px;
  color: #fff;
  margin-left: 10px;
  height: 40px;
  width: auto;
  box-sizing: border-box;
}
#board-footer-mobile__wrap > .board-tool {
  width: 30%;
  min-width: 110px;
  display: flex;
  opacity: 1;
  padding: 0;
  .board-list{
    width: 50%;
  }
}
.board-brush {
  position: absolute;
}

#board-footer-mobile__wrap > div.brush-tool-wrap {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin-left: 0;
  background-color: rgba(120,120,122,.5);
}

#board-footer-mobile__wrap > div.board-footer-mobile__page {
  background-color: transparent;
  padding: 0;
  min-width: 150px;
}

#board-footer-mobile__wrap > div:first-child {
  margin-left: 0;
}

.board-footer-mobile__page-text {
  display: inline-block;
  vertical-align: middle;
}

.board-footer-mobile__page-text > span {
  display: inline-block;
  height: 100%;
  line-height: 40px;
  vertical-align: middle;
  margin: 0 6px;
}

.board-footer-mobile__page-text > span:last-child {
  margin-left: 0;
}

.board-footer-mobile__btn {
  vertical-align: middle;
  border-radius: 3px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: space-evenly;
  width: 40px;
  height: 20px;
  // margin-left: 10px;
}

.board-footer-mobile__btn:first-child {
  margin-left: 0;
}

.board-footer-mobile__page-text .board-footer-mobile__btn {
  margin-left: 2px;
  margin-right: 6px;
  margin-top: 1px;
}

.board-footer-mobile__btn:not(.disabled):hover {
  // background-color: ;
  border-color: #006eff;
}

.board-footer-mobile__btn:not(.disabled):active {
  // background-color: ;
  border-color: #006eff;
}

.board-footer-mobile__btn:not(.disabled):active,
.board-footer-mobile__page > a.board-footer-mobile__btn:not(.disabled):active,
.board-footer-mobile__btn:not(.disabled).active,
.board-footer-mobile__btn > a.board-footer-mobile__btn:not(.disabled).active {
  // background-color: ;
  background-color: #006eff;
}

.board-footer-mobile__btn > i {
  display: inline-block;
  width: 24px;
  height: 24px;
  background-repeat: no-repeat;
  background-size: 110%;
  background-position: center;
}

.board-footer-mobile__btn.disabled {
  cursor: default;
}

.board-footer-mobile__btn.disabled > i {
  opacity: 0.5;
}

.board-footer-mobile__page-text .board-footer-mobile__btn > i {
  width: 18px;
  height: 18px;
  background-size: 100%;
}

.board-footer-mobile__page .board-footer-mobile__btn > i {
  background-size: 80%;
}

.board-footer-mobile__prev {
  background-image: url('./assets/board-footer-mobile__prev.png');
}

.board-footer-mobile__next {
  background-image: url('./assets/board-footer-mobile__next.png');
}

.board-footer-mobile__page > a.board-footer-mobile__btn,
.board-footer-mobile__page-text {
  height: 100%;
  // padding: 6px 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(120,120,122,.5);
  box-sizing: border-box;
  margin: 0;
}


.board-footer-mobile__page > a.board-footer-mobile__btn:first-child {
  border-radius: 30px 0 0 30px;
}

.board-footer-mobile__page > a.board-footer-mobile__btn:last-child {
  border-radius: 0 30px 30px 0;
}

.hide {
  display: none !important;
}
.board-footer-mobile__tool-pen{
  width: 40px;
  height: 20px;
}
.board-footer-mobile__page {
  width: 60%;
  min-width: 120px;
}

.board-footer-mobile__tool-pen > i {
  background: url('@assets/images/tools/ic_tool_pen.svg') no-repeat center;
  background-size: 24px;
}

.board-footer-mobile__tool-select > i {
  background: url('@assets/images/tools/ic_tool_choice.svg') no-repeat center;
  background-size: 24px;
}

.board-footer-mobile__tool-eraser > i {
  background: url('@assets/images/tools/ic_tool_eraser.svg') no-repeat center;
  background-size: 24px;
}

.board-footer-mobile__tool-text > i {
  background: url('@assets/images/tools/ic_tool_text.svg') no-repeat center;
  background-size: 24px;
}

.board-footer-mobile__tool-box {
  position: absolute;
  z-index: 1000;

  .show-tool-box {
    display: block;
  }

  .hide-tool-box {
    display: none;
  }
}

.board-footer-mobile__tool-box-wrap {
  position: relative;
  right: 180px;
  bottom: 167px;
  background: rgba(0, 0, 0, 1);
  border-radius: 4px;
  padding: 0px 8px;
  width: 178px;
  height: 172px !important;
  transition: all 0.15s ease;
  padding-top: 10px;
}

.board-footer-mobile__tool-box-pen {
  height: 172px !important;
}

.board-footer-mobile__tool-box-pen-extend {
  bottom: -7px !important;
}

.board-footer-mobile__tool-box-pen-extend2 {
  bottom: -35px !important;
}

.board-footer-mobile__tool-box-text {
  height: 131px !important;
  bottom: 98px;
}

.board-footer-mobile__tool-box-short {
  height: 43px !important;
  padding: 5px;
  bottom: 10px;
}

.board-footer-mobile__tool-box-wrap ul {
  font-size: 0;
  list-style: none;
  padding: 0;
  display: flex;
  justify-content: space-between;
  overflow-x: auto;
  &::-webkit-scrollbar {
    display: none;
  }
}

.board-footer-mobile__tool-box-wrap li {
  list-style: none;
  font-size: 0;
  overflow: hidden;
  vertical-align: middle;
  width: 20px;
  height: 20px;
  cursor: pointer;
  text-align: center;
  position: relative;
  border-radius: 6px;
  flex-shrink: 0;
}

.board-footer-mobile__tool-box-wrap li:before {
  content: '';
  display: inline-block;
  vertical-align: middle;
  width: 0;
  height: 100%;
}

.board-footer-mobile__tool-box-thin {
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  vertical-align: middle;
  cursor: pointer;
  background: #737882;
}

.board-footer-mobile__tool-box-wrap li:hover .board-footer-mobile__tool-box-thin {
  background-color: #dcebeb;
}

.board-footer-mobile__tool-box-wrap li.actived .board-footer-mobile__tool-box-thin {
  background-color: #dcebeb;
}

.board-footer-mobile__tool-box-wrap li:active .board-footer-mobile__tool-box-thin,
.board-footer-mobile__tool-box-wrap li.actived .board-footer-mobile__tool-box-thin {
  background-color: #dcebeb;
}

.board-footer-mobile__tool-box-thin.size-8 {
  width: 8px;
  height: 8px;
}

.board-footer-mobile__tool-box-thin.size-12 {
  width: 12px;
  height: 12px;
}

.board-footer-mobile__tool-box-thin.size-16 {
  width: 16px;
  height: 16px;
}

.board-footer-mobile__tool-box-wrap li:hover {
  background-color: #006eff;
}

.board-footer-mobile__tool-box-wrap li.actived {
  background-color: #006eff;
}

.board-footer-mobile__tool-box-wrap li.selected {
  color: #006eff;
}

.board-footer-mobile__tool-box-wrap li:active,
.board-footer-mobile__tool-box-wrap li.actived {
  background-color: #006eff;
}

.board-footer-mobile__tool-box-wrap .board-footer-mobile__tool-box-section + .board-footer-mobile__tool-box-section {
  padding-top: 10px;
  margin-top: 10px;
  border-top: 1px solid rgba(52, 54, 59, 0.8);
}

.board-footer-mobile__tool-box-wrap .tool-pen {
  width: 18px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}

.tool-pen.graphical-wave {
  border: none;
  width: 20px;
  height: 20px;
  background-size: 20px 20px;
  background-image: url('./assets/ic-curve-gray.svg');
}

.tool-pen.graphical-wave-sel {
  border: none;
  width: 20px;
  height: 20px;
  background-size: 20px 20px;
  background-image: url('./assets/ic-curve.svg') !important;
}
.tool-pen.graphical-arrow {
  border: none;
  width: 20px;
  height: 20px;
  background-size: 20px 20px;
  background-image: url('@/component/vue-component/boardtool-component/assets/ic-arrow-gray.svg');
}

li.actived .tool-pen.graphical-arrow {
  border: none;
  width: 20px;
  height: 20px;
  background-size: 20px 20px;
  background-image: url('@/component/vue-component/boardtool-component/assets/ic-arrow.svg')!important;
}

.tool-pen {
  display: block;
  width: 20px;
  height: 14px;
  margin: 5px auto 1px;
  border: 2px solid #737882;
}

.tool-pen.graphical-line {
  border: 0;
  height: 4px;
  border-top: 2px solid #737882;
}

.tool-pen.graphical-line-dotted {
  border: 0;
  height: 4px;
  border-top: 2px dotted #737882;
}

.tool-pen.graphical-round {
  border-radius: 50%;
  width: 20px;
  height: 20px;
}

.board-footer-mobile__tool-box-wrap li:hover .tool-pen {
  border-color: #dcebeb;
}

.board-footer-mobile__tool-box-wrap li.actived .tool-pen {
  border-color: #dcebeb;
}

.board-footer-mobile__tool-box-wrap li:active .tool-pen,
.board-footer-mobile__tool-box-wrap li.actived .tool-pen {
  border-color: #dcebeb;
}

.board-footer-mobile__tool-box-wrap li .board-footer-mobile__tool-box-color-wrap {
  display: block;
  width: 18px;
  height: 18px;
  border: 1px solid #737882;
  position: absolute;
  margin: auto;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: 50%;
}

.board-footer-mobile__tool-box-wrap li.board-footer-mobile__tool-box-color-margin {
  // margin-right: 10px;
}

.board-footer-mobile__tool-box-sel-color {

    position: absolute;
    right: -2px;
    top: -2px;
    width: 20px;
    height: 20px;
    background-image: url('./assets/color-select.svg');
    background-size: 20px;
}

.board-footer-mobile__tool-box-wrap li .board-footer-mobile__tool-box-color {
  display: inline-block;
  margin: 0;
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 50%;
}

.board-footer-mobile__tool-box-wrap ul.board-footer-mobile__tool-operator-ul {
  font-size: 0;
  list-style: none;
  padding: 0;
  margin-top: 2px;
  display: flex;
  justify-content: space-between;
  overflow-x: auto;
}

.board-footer-mobile__tool-box-wrap li.board-footer-mobile__tool-operator-li {
  list-style: none;
  font-size: 0;
  overflow: hidden;
  vertical-align: middle;
  width: 28px;
  height: 28px;
  cursor: pointer;
  text-align: center;
  position: relative;
  border-radius: 6px;
  flex-shrink: 0;
  background-size: 100%;
}

.board-footer-mobile__tool-box-wrap li.board-footer-mobile__tool-pen {
  background: url('@assets/images/tools/ic_tool_pen.svg') no-repeat center;
  background-size: 24px;
}

.board-footer-mobile__tool-box-wrap li.board-footer-mobile__tool-select {
  background: url('@assets/images/tools/ic_tool_choice.svg') no-repeat center;
  background-size: 24px;
}

.board-footer-mobile__tool-box-wrap li.board-footer-mobile__tool-eraser {
  background: url('@assets/images/tools/ic_tool_eraser.svg') no-repeat center;
  background-size: 24px;
}

.board-footer-mobile__tool-box-wrap li.board-footer-mobile__tool-text {
  background: url('@assets/images/tools/ic_tool_text.svg') no-repeat center;
  background-size: 24px;
}

.board-footer-mobile__tool-box-wrap li.board-footer-mobile__tool-box-text-li {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
}

.text-20 {
  font-size: 18px;
}

.text-14 {
  font-size: 12px;
}

.text-16 {
  font-size: 14px;
}

.text-18 {
  font-size: 16px;
}
.brush-tool-wrap {
  width: 40px;
  height: 40px;
  padding: 0;
  border-radius: 8px;
  background-color: rgba(120,120,122,.5);
}
#board-footer-mobile__wrap {
  .student-brush-wrap {
    position: fixed;
    top: 28%;
    right: 8px;

    .layout-side.portrait-screen:not(.video-left) & {
      right: 138px;
    }

    height: 30px;
    width: 30px;
    z-index: 100;
    &.is-ios {
      top: calc(30% + env(safe-area-inset-top));
    }
    &.is-andriod {
      top: calc(30% + 40px)
    }
    .board-footer-mobile__tool-pen {
      height: 30px;
      width: 30px;
    }
  }
}

.brush-tool__corner {
  position: absolute;
  &.tool-type-graphical-wave {
    // 画笔工具
    position: absolute;
    right: 4px;
    top: 17px;
    bottom: 3px;
    width: 6px;
    height: 6px;
    border-top: 2px solid var(--border-color);
    border-left: 2px solid var(--border-color);
    border-top-left-radius: 100%;
    transform: rotate(50deg) scale(1.2);
  }

  &.tool-type-graphical-line {
    // 直线
    position: absolute;
    right: 2px;
    top: 18px;
    width: 10px;
    height: 1px;
    border-bottom: 3px solid var(--border-color);
  }

  &.tool-type-graphical-line-dotted {
    // 直线
    position: absolute;
    right: 2px;
    top: 18px;
    width: 10px;
    height: 1px;
    border-bottom: 3px dotted var(--border-color);
  }

  &.tool-type-graphical-arrow {
    // 直线
    position: absolute;
    width: 8px;
    height: 8px;
    top: 14px;
    border-right: 2px solid var(--border-color);
    border-top: 2px solid var(--border-color);
    &:before {
      content: '';
      position: absolute;
      width: 11px;
      display: block;
      transform-origin: right;
      transform: rotate(-45deg);
      height: 2px;
      top: -1px;
      right: 0;
      background-color: var(--border-color);
    }
  }

  &.tool-type-graphical-round {
    // 圆形
    position: absolute;
    right: 1px;
    top: 12px;
    width: 11px;
    height: 11px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
  }

  &.tool-type-graphical-rect {
    // 矩形
    position: absolute;
    right: 2px;
    top: 14px;
    width: 8px;
    height: 8px;
    border: 2px solid var(--border-color);
  }

  &.tool-type-text-line {
    // 直线
    position: absolute;
    right: 6px;
    top: 18px;
    width: 12px;
    height: 1px;
    border-bottom: 3px solid var(--border-color);
  }

  &.tool-box-icon-pos {
    right: 8px !important;
  }

  &.tool-box-icon-text-pos {
    right: 14px !important;
    top: 26px !important;
  }

}
</style>
