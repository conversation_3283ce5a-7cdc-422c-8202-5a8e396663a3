<!-- eslint-disable vue/no-mutating-props -->
<template>
  <!-- 未开课下如果存在默认课件，所有角色都可预览 -->
  <div
    v-if="hasPermission && rbacPermission && !isVideoOnlyClass || !isClassStarted"
    class="board-footer-component"
  >
    <!--ppt工具栏-->
    <div
      v-if="isPageFile"
      class="ppt-tool ppt-tool__blur"
      :class="[{'ppt-tool__pad' : isPad && !isPdfOrDocFile}, {'ppt-tool__pdfordoc' : !isPad && isPdfOrDocFile}, {'ppt-tool__pad-pdfordoc' : isPad && isPdfOrDocFile}]"
    >
      <div class="ppt-tool__tag">
        <CustomFeature feature="WhiteBoardPPT">
          <i
            class="ppt-tool__prev"
            :class="{ 'disabled' : currentPage === 1 }"
            @click="onPrePage"
          />
        </CustomFeature>
        <!-- 当前所在页 -->
        <div class="ppt-tool__page">
          <label>{{ currentPage }}/{{ totalPage }}</label>
          <CustomFeature feature="WhiteBoardPPT">
            <i
              class="ppt-tool__thumbnail"
              :class="{ 'active' : thumbnailActive }"
              @click="$parent.toggleThumbnail(!thumbnailActive)"
            >
              <IcToolThumbnail />
            </i>
          </CustomFeature>
        </div>
        <CustomFeature feature="WhiteBoardPPT">
          <i
            class="ppt-tool__next"
            :class="{ 'disabled' : currentPage === totalPage }"
            @click="onNextPage"
          />
        </CustomFeature>
      </div>
      <button v-if="!isPad">
        <i
          class="ppt-tool__shrink"
          :class="{ 'disabled' : currentScale <= minScale }"
          @click="$parent.onScale(-scaleStep)"
        >
          <IcToolShrink />
        </i>
      </button>
      <button v-if="!isPad">
        <i
          class="ppt-tool__magnify"
          :class="{ 'disabled' : currentScale >= maxScale }"
          @click="$parent.onScale(scaleStep)"
        >
          <IcToolMagnify />
        </i>
      </button>
      <button
        class="ppt-tool__btn-drag"
        :class="{ 'active' : dragActive && currentScale !== minScale, 'disabled' : currentScale === minScale}"
        @click="$parent.onDrag(!dragActive)"
      >
        <i
          class="ppt-tool__drag"
          :class="{ 'active' : dragActive && currentScale !== minScale, 'disabled' : currentScale === minScale }"
        >
          <IcToolDrag />
        </i>
      </button>
      <button v-if="isPdfOrDocFile">
        <i
          :class=" minScale === currentScale ? 'ppt-tool__pdf-full' : 'ppt-tool__pdf-shrink'"
          @click="$parent.togglePdfFillBoard(minScale === currentScale)"
        >
          <IcToolPdfFull v-if="minScale === currentScale" />
          <IcToolPdfShrink v-else />
        </i>
      </button>
      <button>
        <Component
          :is="!isBoardFullscreen ? 'IcPPTToolFull' : 'IcPPTToolShrink'"
          :class="!isBoardFullscreen ? 'ppt-tool__board-full' : 'ppt-tool__board-shrink'"
          @click="toggleFullscreen"
        />
      </button>
    </div>

    <!-- PPT的白板工具栏 -->
    <CustomFeature feature="WhiteBoardList">
      <div
        v-if="isPageFile"
        class="board-tool right board-tool__blur"
      >
        <button class="board-list">
          <!-- <i
            class="board-tool__add"
            @click.stop="$parent.onAddBoard"
          /> -->
          <IcBoardToolAdd
            class="board-tool__add"
            @click.stop="$parent.onAddBoard"
          />
        </button>
        <el-popover
          ref="boardListPopper"
          v-model="listActive"
          popper-class="board-tool__back-popper"
          placement="top-end"
          trigger="manual"
          :visible-arrow="false"
          class="board-list"
        >
          <div
            ref="board-tool__list-popper"
            class="board-tool__board-drop board-tool__list-popper"
          >
            <el-scrollbar>
              <ul>
                <li
                  v-for="item in fileInfoList"
                  :key="item.fileId"
                  :class="{ 'board-tool__li-active' : item.fileId === currentBoardFileId }"
                  @click="$parent.onGotoBoardFile(item.fileId, item.boardId)"
                >
                  <IcListBoard
                    v-if="item.type === 'board-tool__list-board'"
                  />
                  <i
                    v-else
                    :class="item.type"
                  />
                  <label>{{ item.title }}</label>
                  <i
                    v-if="item.canDel"
                    class="board-tool__btn-close el-icon-close"
                    @click.stop="$parent.onDeleteBoard(item.fileId, item.boardId)"
                  />
                </li>
              </ul>
            </el-scrollbar>
          </div>

          <button
            slot="reference"
            ref="board-tool__list"
            class="board-tool__btn-board-list"
            :class="{'active' : listActive}"
            @click="$parent.onBoardList"
          >
            <!-- <i
              class="board-tool__list"
              :class="{'active' : listActive}"
            /> -->
            <IcBoardToolList
              class="board-tool__list"
              :class="{'active' : listActive}"
            />
          </button>
        </el-popover>
      </div>
    </CustomFeature>

    <!-- 涂鸦的白板工具栏 -->
    <div
      v-if="!isPageFile"
      class="board-tool right board-tool__blur board-tool-full"
    >
      <CustomFeature
        feature="WhiteBoardList"
        style="height: 40px;"
      >
        <button>
          <!-- <i
            class="board-tool__add"
            @click.stop="$parent.onAddBoard"
          /> -->
          <IcBoardToolAdd
            class="board-tool__add"
            @click.stop="$parent.onAddBoard"
          />
        </button>
      </CustomFeature>
      <button>
        <Component
          :is="!isBoardFullscreen ? 'IcPPTToolFull' : 'IcPPTToolShrink'"
          :class="!isBoardFullscreen ? 'ppt-tool__board-full' : 'ppt-tool__board-shrink'"
          @click="toggleFullscreen"
        />
      </button>
      <CustomFeature
        feature="WhiteBoardList"
        style="height: 40px;"
      >
        <el-popover
          ref="boardListPopper"
          v-model="listActive"
          popper-class="board-tool__back-popper"
          placement="top-start"
          trigger="manual"
          :visible-arrow="false"
          class="board-list"
          :style="{width: '100%', height: '100%'}"
        >
          <div
            ref="board-tool__list-popper"
            class="board-tool__board-drop board-tool__list-popper"
          >
            <el-scrollbar>
              <ul>
                <li
                  v-for="item in fileInfoList"
                  :key="item.fileId"
                  :class="{ 'board-tool__li-active' : item.fileId === currentBoardFileId }"
                  @click="$parent.onGotoBoardFile(item.fileId, item.boardId)"
                >
                  <IcListBoard
                    v-if="item.type === 'board-tool__list-board'"
                  />
                  <i
                    v-else
                    :class="item.type"
                  />
                  <label>{{ item.title }}</label>
                  <i
                    v-if="item.canDel"
                    class="board-tool__btn-close el-icon-close"
                    @click.stop="$parent.onDeleteBoard(item.fileId, item.boardId)"
                  />
                </li>
              </ul>
            </el-scrollbar>
          </div>
          <button
            slot="reference"
            ref="board-tool__list"
            class="board-tool__btn-board-list"
            :class="{'active' : listActive}"
            @click="$parent.onBoardList"
          >
            <!-- <i
              class="board-tool__list"
              :class="{'active' : listActive}"
            /> -->
            <IcBoardToolList
              class="board-tool__list"
              :class="{'active' : listActive}"
            />
          </button>
        </el-popover>
      </CustomFeature>
    </div>
  </div>
  <div
    v-else-if="(!hasPermission || !rbacPermission) && !isVideoOnlyClass"
    class="board-footer-component"
  >
    <div
      v-if="isPageFile"
      class="ppt-tool ppt-tool__blur"
      :class="[{'ppt-tool__pad' : isPad && !isPdfOrDocFile}, {'ppt-tool__pdfordoc' : !isPad && isPdfOrDocFile}, {'ppt-tool__pad-pdfordoc' : isPad && isPdfOrDocFile}]"
    >
      <button v-if="!isPad">
        <i
          class="ppt-tool__shrink"
          :class="{ 'disabled' : currentScale <= minScale }"
          @click="$parent.onScale(-scaleStep)"
        >
          <IcToolShrink />
        </i>
      </button>
      <button v-if="!isPad">
        <i
          class="ppt-tool__magnify"
          :class="{ 'disabled' : currentScale >= maxScale }"
          @click="$parent.onScale(scaleStep)"
        >
          <IcToolMagnify />
        </i>
      </button>
      <button v-if="isPdfOrDocFile">
        <i
          :class=" minScale === currentScale ? 'ppt-tool__pdf-full' : 'ppt-tool__pdf-shrink'"
          @click="$parent.togglePdfFillBoard(minScale === currentScale)"
        >
          <IcToolPdfFull v-if="minScale === currentScale" />
          <IcToolPdfShrink v-else />
        </i>
      </button>
    </div>
  </div>
</template>

<script>
import i18next from 'i18next';
import Constant from '@/util/Constant';
import BaseComponent from '../../core/BaseComponent';
import { getStartedStatus } from '@/util/class';
import DocumentUtil from '@/util/Document';
import IcBoardToolAdd from './assets/svg-component/board-tool__add.svg';
import IcBoardToolList from './assets/svg-component/board-tool__list.svg';
import IcPPTToolFull from './assets/svg-component/ppt-tool__board-full.svg';
import IcPPTToolShrink from './assets/svg-component/ppt-tool__board-shrink.svg';
import CustomFeature from '../../ui-component/feature/CustomFeature.vue';
import IcToolDrag from './assets/svg-component/ppt-tool__drag.svg';
import IcToolShrink from './assets/svg-component/ppt-tool__shrink.svg';
import IcToolMagnify from './assets/svg-component/ppt-tool__magnify.svg';
import IcToolPdfFull from './assets/svg-component/ppt-tool__pdf_full.svg';
import IcToolPdfShrink from './assets/svg-component/ppt-tool__pdf_shrink.svg';
import IcToolThumbnail from './assets/svg-component/ppt-tool__thumbnail.svg';
import IcListBoard from './assets/svg-component/board-tool__list-board.svg';


export default {
  components: {
    CustomFeature,
    IcBoardToolAdd,
    IcListBoard,
    IcBoardToolList,
    IcPPTToolFull,
    IcPPTToolShrink,
    IcToolThumbnail,
    IcToolDrag,
    IcToolShrink,
    IcToolMagnify,
    IcToolPdfFull,
    IcToolPdfShrink,
  },
  extends: BaseComponent,
  props: {
    fileInfoList: {
      type: Array,
      required: true,
    },
    thumbnailActive: {
      type: Boolean,
      required: true,
    },
    dragActive: {
      type: Boolean,
      required: true,
    },
    // 当前所在白板ID或文件ID
    currentBoardFileId: {
      type: String,
      required: true,
    },
    minScale: {
      type: Number,
      required: true,
    },
    // 最大缩放比例
    maxScale: {
      type: Number,
      required: true,
    },
    // 缩放单步系数
    scaleStep: {
      type: Number,
      required: true,
    },
    // 当权缩放比例
    currentScale: {
      type: Number,
      required: true,
    },
    // 当前所在页
    currentPage: {
      type: Number,
      required: true,
    },
    // 总页数
    totalPage: {
      type: Number,
      required: true,
    },
    isPdfOrDocFile: {
      type: Boolean,
      required: true,
    },
    isPageFile: {
      type: Boolean,
      required: true,
    },
    listActive: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      teduBoard: null,
      // 切换白板全屏和正常大小按钮
      isBoardFullscreen: false,
      // 是否是老师
      isTeacher: false,
      // 是否是助教
      isAssistant: false,
      isStudent: false,
      // 是否是pad
      isPad: false,
      // 视频时长
      valueSlider: 20,
      // 音量
      btnActive: false,
      // // 设备上scroll防抖
      // scrollTimer : null,
      // // 滑动时起始的放大系数
      // scrollingScale: 0,
      // 是否有白板操作权限
      hasPermission: false,
      rbacPermission: false,
      isVideoOnlyClass: false,  // 是否纯视频课
      isClassStarted: false,
    };
  },
  computed: {
  },
  mounted() {
    this.isClassStarted = getStartedStatus();
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Joined_Class, true).then(() => {
      this.rbacPermission = TCIC.SDK.instance.checkPermission(TCIC.TResourcePath.Board_Operate, TCIC.TPermissionFlag.Read);
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isStudent = TCIC.SDK.instance.isStudent();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      this.isPad = TCIC.SDK.instance.isPad();
      this.isVideoOnlyClass = TCIC.SDK.instance.isVideoOnlyClass();
      TCIC.SDK.instance.promiseState(TCIC.TMainState.Board_Ready, true)
        .then(() => {
          this.teduBoard = TCIC.SDK.instance.getBoard();
        });
    });

    // 监听白板授权状态
    this.addLifecycleTCICStateListener(TCIC.TMainState.Board_Permission, (value, oldValue, reason) => {
      this.hasPermission = value;
    });
    // 监听全屏状态变更
    this.addLifecycleTCICStateListener(Constant.TStateFullScreen, this.fullScreenStateChangeHandler);
    // 监听是否开始上课事件
    this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, (status) => {
      this.isClassStarted = status === TCIC.TClassStatus.Already_Start;
    });
  },
  beforeDestroy() {
  },
  methods: {
    fullScreenStateChangeHandler(flag) {
      this.isBoardFullscreen = flag;
    },
    toggleFullscreen(isFull, isSync = false) {
      if (typeof isFull !== 'boolean') {
        isFull = !this.isBoardFullscreen;
      }
      if (this.isBoardFullscreen !== isFull) {
        this.isBoardFullscreen = isFull;
        if (!isSync) {
          TCIC.SDK.instance.setState(Constant.TStateFullScreen, this.isBoardFullscreen);
        }
      }
    },
    async onPrePage() {
      await DocumentUtil.addSnapshotMark('onPrePage');
      DocumentUtil.intervalChangePage(this.teduBoard, 'prev');
    },
    async onNextPage() {
      await DocumentUtil.addSnapshotMark('onNextPage');
      DocumentUtil.intervalChangePage(this.teduBoard, 'next');
    },
  },
};
</script>
<style lang="less">
@--color-primary: #006EFF;
@--color-public: #14181D;
@--color-disable: #b9bbbc;
@--color-back: #1C2131;

@board-tool-button-size: var(--board-tool-button-size, 40px);
@board-tool-sub-button-size: var(--board-tool-sub-button-size, 26px);

@board-tool-font-size: var(--board-tool-font-size, 14px);
.light{
  --text-color: #2B2C30;
  --bg-color: #fff;
  .board-tool__li-active {
    --text-color: #fff;
    --icon-color: #fff;
  }
  .board-footer-component {
    --active-color: #006EFF;
    --icon-color: #1D2029;
  }
}

.ic_video_pause {
  background-image: url('./assets/ic_video_pause.svg');

  &:hover {
    background-image: url('./assets/ic_video_pause_hover.svg');
  }
}
.ic_video_play {
  background-image: url('./assets/ic_video_play.svg');

  &:hover {
    background-image: url('./assets/ic_video_play_hover.svg');
  }
}
.ic_video_volume {
  background-image: url('./assets/ic_video_volume.svg');

  &:hover {
    background-image: url('./assets/ic_video_volume_hover.svg');
  }
}
.ic_video_close {
  background-image: url('./assets/ic_video_close.svg');

  &:hover {
    background-image: url('./assets/ic_video_close_hover.svg');
  }
}
.ppt-tool__shrink, .ppt-tool__magnify,
.ppt-tool__drag, .ppt-tool__pdf-full, .ppt-tool__pdf-shrink, .ppt-tool__thumbnail {
    &:hover {
      --icon-color: var(--active-color, #006EFF);
    }

    &.disabled {
      cursor: not-allowed;
      opacity: 0.4;
    }
}


.ppt-tool__drag {
  &.active {
    --icon-color: var(--active-color, #006EFF);
  }

}

.ppt-tool__board-full {
  width: 26px;
  height: 26px;
  &:hover {
    --icon-color: var(--active-color, #006EFF);
  }
}

.ppt-tool__board-shrink {
  width: 26px;
  height: 26px;
  &:hover {
    --icon-color: var(--active-color, #006EFF);
  }
}

.ppt-tool__pdf-full {

}

.ppt-tool__pdf-shrink {

}

.ppt-tool__thumbnail {
  cursor: pointer;
  &.active {
    --icon-color: var(--active-color, #006EFF);
  }
}

.board-tool__add {
  width: 26px;
  height: 26px;
  &:hover {
    --icon-color: var(--active-color, #006EFF);
  }
}

.board-tool__list {
  width: 26px;
  height: 26px;
  &:hover {
    --icon-color: var(--active-color, #006EFF);
  }

  &.active {
    --icon-color: var(--active-color, #006EFF);
  }
}

.board-tool__list-ppt {
  background-image: url('./assets/board-tool__list-ppt.svg');
}

.board-tool__list-pdf {
  background-image: url('./assets/board-tool__list-pdf.svg');
}


.board-tool__list-word {
  background-image: url('./assets/board-tool__list-word.svg');
}

.board-tool__list-picture {
  background-image: url('./assets/board-tool__list-picture.svg');
}

/*工具内容选择面板*/
.el-popover.board-tool__back-popper {
  padding: 0;
  background: transparent;
  border: none;
  box-shadow: none;
  .board-tool__board-drop {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    background: var(--bg-color, @--color-back);
    color: var(--text-color, #fff);
  }

  .board-tool__list-popper {
    margin-bottom: 10px;
    overflow: hidden;

    .el-scrollbar__wrap {
      max-height: 255px;
      margin-bottom: 0px !important;
    }

    ul {
      li {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: @board-tool-button-size;
        padding-left: 16px;
        padding-right: 16px;
        border-bottom: 1px solid rgba(#eee, .1);
        font-size: @board-tool-font-size;
        color: var(--text-color, #fff);

        &:last-child {
          border-bottom: 0
        }

        &:hover {
          label {
            color: var(--active-color, #006EFF);
          }
        }

        &.board-tool__li-active {
          background: @--color-primary;

          &:hover {
            label {
              color: #fff;
            }
          }
        }

        label {
          flex: 1;
          max-width: 176px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        i {
          margin-right: 10px;
          width: 24px;
          height: 24px;
        }

        i.board-tool__btn-close {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 20px;
          height: 20px;
          margin-left: 10px;
          border-radius: 4px;
          opacity: 0.3;
          background: rgba(0, 0, 0, .05);

          &:hover {
            opacity: 1
          }
        }
      }
    }
  }
}

.ppt-tool {
  display: flex;
  width: 75%;
  height: 100%;
  padding: 0 10px;
  border-radius: 10px;
  pointer-events: auto;
  overflow: hidden;
  opacity: 0.5;
  filter: initial;
  align-items: center;

  &.ppt-tool__pad {
    width: 380px;
  }

  &.ppt-tool__blur {
    transition: opacity 1s;
    transition-delay: 3s;

    &:hover {
      opacity: 1;
      transition: all 0s;
    }
  }

  .ppt-tool__tag {
    position: relative;
    display: flex;
    align-items: center;
    margin-right: 14px;
    padding-right: 14px;
    z-index: 9;
    width: 60%;
    height: 100%;
    max-width: 160px;

    &:after {
      position: absolute;
      right: 0;
      width: 1px;
      height: 24px;
      content: "";
      background: rgba(#fff, .15);
    }
    .custom-feature {
      width: 40px;
      height: 100%;
      display: flex;
      align-items: center;
      i, i.ppt-tool__thumbnail{
        width: 100%;
      }
    }

    i {
      display: flex;
      width: 20%;
      height: 100%;
      max-width: @board-tool-sub-button-size;
      max-height: @board-tool-sub-button-size;
      background-size: 100%;
      background-repeat: no-repeat;
      background-position: center;
      &.ppt-tool__prev, &.ppt-tool__next {
        display: flex;
        align-items: center;
        cursor: pointer;
        justify-content: center;
        // background-image: url("./assets/ppt-tool__pack.svg");
        &:before{
          content: '\e6de';
          font-family: 'element-icons';
          font-weight: bolder;
          font-size: 26px;
          color: var(--text-color, #fff);
        }

        &:hover {
          transition: all .4s;
          color: #006EFF;
        }

        &.disabled {
          opacity: 0.4;
        }
      }

      &.ppt-tool__next {
        transform: rotate(180deg);
      }
    }

    .ppt-tool__page {
      display: flex;
      align-items: center;
      justify-content: center;
      max-height: 40px;
      max-width: 200px;
      width: 75%;
      height: 100%;
      border-radius: 4px;

      label {
        font-size: 20px;
        color: var(--text-color, #fff);
        letter-spacing: 1px;
        white-space: nowrap;
        width: 60%;
        min-width: 20px;
        text-align: center;
        display: inline-table;
        margin-right: 5px;
      }

      .ppt-tool__thumbnail {
        width: 50%;
      }
    }
  }

  button {
    position: relative;
    z-index: 9;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    width: 10%;
    height: 100%;
    max-width: @board-tool-button-size;
    max-height: @board-tool-button-size;
    border-radius: 4px;
    padding: 0;
    background: none;
    outline: none;

    &.active {
      transition: all .4s;
      //background: @--color-primary;
    }

    i {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      max-width: @board-tool-sub-button-size;
      max-height: @board-tool-sub-button-size;
      background-size: 100%;
      background-repeat: no-repeat;
      background-position: center;
    }
  }
}


.board-tool {
  top: 0%;
  display: flex;
  height: 100%;
  width: 25%;
  padding: 0 10px;
  border-radius: 10px;
  opacity: 0.5;
  overflow: hidden;
  pointer-events: auto;
  align-items: center;
  justify-content: flex-end;
  max-width: 110px;
  &.board-tool-full {
    width: 100%;
  }
  .custom-feature {
    height: 40px;
    button{
      width: 100%;
    }
  }
  .board-list {
    width: 50%;
    height: 100%;
    display: flex;
    align-items: center;
    span {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
    }
    button {
      width: 100%;
    }

  }

  &.board-tool__blur {
    transition: opacity 1s;
    transition-delay: 3s;

    &:hover {
      opacity: 1;
      transition: all 0s;
    }
  }

  &.right {
    right: 10%;
    //width: 140px;
  }

  &.center {
    left: 50%;
    transform: translateX(-50%);
    width: 220px;

    button.board-tool__btn-board-list {
      position: relative;
      display: flex;
      align-items: center;
      margin-left: 20px;
      width: 100%;
      height: 100%;

      &:before {
        position: absolute;
        left: -8px;
        width: 1px;
        height: 24px;
        opacity: 0.2;
        border-left: 1px solid #fff;
        background: #fff;
        content: "";
      }
    }
  }

  &.video-tool {
    display: flex;
    align-items: center;
    width: 496px;
    padding: 6px 0;
  }

  &.video-tool-student {
    display: flex;
    align-items: center;
    width: 304px;
    padding: 22px 0;
    .video-tool-progress {
      position: relative;
      z-index: 9;
      background: none;
      .el-slider {
        &__button-wrapper {
          display: none;
        }
      }
      .el-slider__runway {
        &:before {
          cursor: default;
        }
      }
      .el-slider__runway.disabled .el-slider__bar {
        background: @--color-primary;
      }
    }
  }

  button {
    position: relative;
    z-index: 9;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    width: 33.3%;
    height: 100%;
    max-width: @board-tool-button-size;
    max-height: @board-tool-button-size;
    border-radius: 4px;
    padding: 0;
    background: none;
    outline: none;

    &.active {
      transition: all .4s;
      //background: @--color-primary;
      &:hover {
        .ic_video_volume {
          background: url('./assets/ic_video_volume.svg') no-repeat center;
        }
      }
    }

    i {
      display: flex;
      width: 100%;
      height: 100%;
      max-width: @board-tool-sub-button-size;
      max-height: @board-tool-sub-button-size;
      background-size: 100%;
      background-repeat: no-repeat;
      background-position: center;
    }
  }

  // 视频工具栏
  .video-tool-progress {
    display: flex;
    align-items: center;
    width: 303px;
    height: 40px;
    padding: 0 16px;
    margin: 0 6px;
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.2);
    .video-slider {
      flex: 1;
      margin: 0 8px;
      .el-slider__runway .el-slider__bar,
      .el-slider__runway:before {
        height: 6px;
      }
    }
    .video-time {
      font-size: 20px;
      color: #fff;
      flex-shrink: 0;
    }
  }
}
// 视频工具栏 - 音量
.volume-popover {
  min-width: inherit !important;
  width: 60px !important;
  height: 112px;
  .video-slider-vertical {
    .el-slider__runway {
      background: #dfdfdf;
      margin: 0;
      &:before {
        top: inherit;
      }
      .el-slider__bar {
        top: inherit;
      }
    }
  }
}
@media (max-width: 500px) {
  .ppt-tool .ppt-tool__tag .ppt-tool__page {
    label {
      font-size: 12px;
    }
  }
}
</style>
