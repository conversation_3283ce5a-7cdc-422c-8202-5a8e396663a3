<template>
  <div
    ref="screen-videowrap-ref"
    class="screen-videowrap-component share-screen__mode"
  >
    <div class="drag-module-header__wrap">
      <div class="drag-module-btn">
        <el-tooltip
          effect="dark"
          :content="$t('隐藏所有上台的人')"
          placement="top-start"
          :visible-arrow="false"
          :manual="interactiveIsTouchEvent()"
        >
          <button
            class="minimum-btn"
            :class="{ checked: videoShowStatus === 2 }"
            @click="onMinimumTap"
          />
        </el-tooltip>
        <el-tooltip
          effect="dark"
          :content="translateTip.onlyTeacher"
          placement="top-start"
          :visible-arrow="false"
          :manual="interactiveIsTouchEvent()"
        >
          <button
            class="user-btn"
            :class="{ checked: videoShowStatus === 1 }"
            @click="onUserTap"
          />
        </el-tooltip>
        <el-tooltip
          effect="dark"
          :content="$t('显示所有上台的人')"
          placement="top-start"
          :visible-arrow="false"
          :manual="interactiveIsTouchEvent()"
        >
          <button
            class="maximum-btn"
            :class="{ checked: videoShowStatus === 0 }"
            @click="onMaximumTap"
          />
        </el-tooltip>
      </div>
      <div class="drag-module-title">
        {{ getUserNumberText(callCount) }}
      </div>
    </div>
    <div
      v-show="videoShowStatus!==2"
      ref="video-wrap-ref"
      :class="['video__wrap', {'one-on-one' : isOneOnOneClass}, {'teacher-only' : videoShowStatus==1}]"
    />
  </div>
</template>

<script>
import i18next from 'i18next';
import BaseComponent from '@core/BaseComponent';

export default {
  components: {},

  extends: BaseComponent,

  data() {
    return {
      callCount: 0, // 通话人数
      videoShowStatus: 0, // 0 显示全部  1 显示自己 2 都不显示
      isVideoLoaded: false,  // 视频组件加载状态，用于防重入
      isOneOnOneClass: false,   // 是否是1v1课堂
      roleInfo: {},
      videos: {},
    };
  },
  computed: {
    translateTip() {
      return {
        onlyTeacher: i18next.t('只显示{{arg_0}}', { arg_0: this.roleInfo.teacher }),
      };
    },
  },
  watch: {
    videoShowStatus() {
      this.$nextTick(() => {
        this.updateClientRect();
      });
    },
  },
  mounted() {
    const { roleInfo, roomInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    // 开启拖动
    this.updateClientRect = this.toggleComponentDrag(true);
    this.callCount = TCIC.SDK.instance.getPermissionList().filter(permission => permission.stage).length;
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Permission_Update, (list) => {
      this.callCount = list.filter(permission => permission.stage).length;
    });
    this.makeSureClassJoined(() => {
      this.isOneOnOneClass = TCIC.SDK.instance.isOneOnOneClass();
    });
  },
  methods: {
    getUserNumberText(num) {
      return i18next.t('{{arg_0}}人正在视频', { arg_0: num });
    },
    onMinimumTap() {
      this.videoShowStatus = 2;
    },

    onMaximumTap() {
      this.videoShowStatus = 0;
      const wrap = this.$refs['video-wrap-ref'].getElementsByClassName('videowrap-component-default')[0];
      if (wrap) {
        wrap.getVueInstance().setWrapMode('full');
      }
    },

    onUserTap() {
      this.videoShowStatus = 1;
      const wrap = this.$refs['video-wrap-ref'].getElementsByClassName('videowrap-component-default')[0];
      if (wrap) {
        wrap.getVueInstance().setWrapMode('teacher');
      }
    },

    /**
     * 加载视频区
     */
    loadVideoWraper() {
      if (this.isOneOnOneClass) {
        this.loadOneOnOneVideoWraper();
        return;
      }
      if (!this.isVideoLoaded) {
        const wrap = TCIC.SDK.instance.getComponent('videowrap-component');
        if (wrap) {
          if (TCIC.SDK.instance.isLiveClass()
          || (TCIC.SDK.instance.isInteractClass() && TCIC.SDK.instance.getClassLayout() === TCIC.TClassLayout.Three)) {
            // 1、公开课文档+视频（纯视频没有屏幕共享入口）
            // 2、互动班课三分屏布局
            // 老师视频组件会独立显示，所以需要将老师视频添加到videowrap组件中
            const teacherCom = TCIC.SDK.instance.getComponent('teacher-component');
            const teacherWrap = document.getElementById('teacher-wrap');
            if (teacherWrap) {
              teacherWrap.appendChild(teacherCom);
            } else {
              console.error('teacher wrap not found when load video');
            }
          }
          this.$refs['video-wrap-ref'].appendChild(wrap);
          wrap.getVueInstance().setWrapLayout('screen');
          // 默认展示全部视频
          this.onMaximumTap();
          this.isVideoLoaded = true;
        } else {
          console.warn('ScreenVideoWrapComponent::loadVideoWraper=>wrap not found');
        }
      }
    },

    loadVideos(videos) {
      const { teacherVideo, studentVideos } = videos;
      console.warn('loadVideos', teacherVideo, studentVideos);
      if (teacherVideo) {
        const teacherCom = TCIC.SDK.instance.getComponent('teacher-component');
        if (teacherCom) {
          this.$refs['video-wrap-ref'].appendChild(teacherCom);
        } else {
          console.error('teacher-component not found when load video');
        }
      }
      studentVideos?.forEach((info) => {
        const studentCom = TCIC.SDK.instance.getComponent('student-component', info.userId);
        if (studentCom) this.$refs['video-wrap-ref'].appendChild(studentCom);
      });
    },

    /**
     * 卸载视频区
     * layout: 当前视频布局
     */
    unloadVideoWraper(layout) {
      if (this.isOneOnOneClass) {
        this.unloadOneOnOneVideoWraper(layout);
        return;
      }
      if (this.isVideoLoaded) {
        const wrap = TCIC.SDK.instance.getComponent('videowrap-component');
        const root = document.getElementById('app');
        if (wrap && root) {
          if (TCIC.SDK.instance.isLiveClass()
          || (TCIC.SDK.instance.isInteractClass() && TCIC.SDK.instance.getClassLayout() === TCIC.TClassLayout.Three)) {
            // 1、公开课文档+视频（纯视频没有屏幕共享入口）
            // 2、互动班课三分屏布局
            // 老师视频组件独立显示，所以需要将老师视频还原回teacher-component组件中
            const teacherCom = TCIC.SDK.instance.getComponent('teacher-component');
            if (teacherCom) {
              root.appendChild(teacherCom);
            } else {
              console.error('teacher-component not found when unload video');
            }
          }
          root.appendChild(wrap);
          wrap.getVueInstance().setWrapLayout(layout);
          this.isVideoLoaded = false;
        } else {
          console.warn(`ScreenVideoWrapComponent::loadVideoWraper=>wrap: ${wrap}, root: ${root}`);
        }
      }
    },

    /**
     * 加载1v1视频区
     */
    loadOneOnOneVideoWraper() {
      if (!this.isVideoLoaded) {
        const wrap = TCIC.SDK.instance.getComponent('ooo-video-wrap-component');
        if (wrap) {
          const teacherCom = TCIC.SDK.instance.getComponent('teacher-component');
          const studentCom = TCIC.SDK.instance.getComponent('student-component', wrap.getVueInstance().studentId);
          if (teacherCom) this.$refs['video-wrap-ref'].appendChild(teacherCom);
          if (studentCom) this.$refs['video-wrap-ref'].appendChild(studentCom);
          wrap.getVueInstance().setWrapLayout('screen');
          // 默认展示全部视频
          this.onMaximumTap();
          this.isVideoLoaded = true;
        } else {
          console.warn('ScreenVideoWrapComponent::loadOneOnOneVideoWraper=>wrap not found');
          const teacherCom = TCIC.SDK.instance.getComponent('teacher-component');
          const studentCom = TCIC.SDK.instance.getComponent('student-component', wrap.getVueInstance().studentId);
          if (teacherCom) this.$refs['video-wrap-ref'].appendChild(teacherCom);
          if (studentCom) this.$refs['video-wrap-ref'].appendChild(studentCom);
          // 默认展示全部视频
          this.onMaximumTap();
          this.isVideoLoaded = true;
        }
      }
    },

    /**
     * 卸载视频区
     * layout: 当前视频布局
     */
    unloadOneOnOneVideoWraper(layout) {
      if (this.isVideoLoaded) {
        const wrap = TCIC.SDK.instance.getComponent('ooo-video-wrap-component');
        const root = document.getElementById('app');
        if (wrap && root) {
          const teacherCom = TCIC.SDK.instance.getComponent('teacher-component');
          const studentCom = TCIC.SDK.instance.getComponent('student-component', wrap.getVueInstance().studentId);
          if (teacherCom) root.appendChild(teacherCom);
          if (studentCom) root.appendChild(studentCom);
          wrap.getVueInstance().setWrapLayout(layout);
          this.isVideoLoaded = false;
        } else {
          console.warn(`ScreenVideoWrapComponent::unloadOneOnOneVideoWraper=>wrap: ${wrap}, root: ${root}`);
        }
      }
    },

    /** 开始拖动 */
    onComponentDragStart() {
      const wrap = TCIC.SDK.instance.getComponent('videowrap-component');
      const screenWrap = TCIC.SDK.instance.getComponent('screen-videowrap-component');
      if (wrap) {   // 隐藏所有控制栏
        wrap.getVueInstance().updateAllControlDirect(true);
        screenWrap.getVueInstance().hasDraged = true; // 被拖动过
      }
    },
    /** 停止拖动 */
    onComponentDragEnd() {
      const wrap = TCIC.SDK.instance.getComponent('videowrap-component');
      if (wrap) { // 恢复所有控制栏
        wrap.getVueInstance().updateAllControlDirect(false);
      }
    },
  },
};

</script>


<style lang="less">
.screen-videowrap-component {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-height: calc(100vh - 80px);
  border-radius: 6px 6px 0 0;

  .drag-module-header__wrap {
    display: flex;
    width: 100%;
    line-height: 48px;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background: rgba(42, 44, 47, 0.8);
    border-radius: 6px 6px 0 0;

    .drag-module-title {
      width: 100%;
      line-height: 40px;
      font-size: 13px;
      color: #ffffff;
      padding-left: 20px;
      text-align: left;
      border-top-style: solid;
      border-top-color: #aaa;
      border-width: 1px;
    }

    .drag-module-btn {
      height: 48px;
      white-space: nowrap;
    }

    .maximum-btn,
    .minimum-btn,
    .user-btn {
      width: 27px;
      height: 27px;
      background: no-repeat center center;
      background-size: contain;
      outline: none;
      vertical-align: middle;
      margin: 0 6px;
    }

    .maximum-btn {
      background-image: url("./assets/maximum.png");
      &:hover,
      &.checked {
        border-radius: 2px;
        background-color: #006eff;
      }
    }

    .minimum-btn {
      background-image: url("./assets/minimum.png");
      &:hover,
      &.checked {
        border-radius: 2px;
        background-color: #006eff;
      }
    }

    .user-btn {
      background-image: url("./assets/user.png");
      &:hover,
      &.checked {
        border-radius: 2px;
        background-color: #006eff;
      }
    }
  }

  .video__wrap {
    position: relative;
    max-height: calc(100% - 89px);
    background: rgba(44, 42, 47, .5);
    --video-height: 90px;
    videowrap-component {
      position: relative !important;
      top: 0px !important;
      left: 0px !important;
      height: 100% !important;
      width: 100% !important;
    }

    &.one-on-one {
      teacher-component, student-component {
        position: relative !important;
        top: 0px !important;
        left: 8px !important;
        height: 90px !important;
        width: 160px !important;
      }
    }
    &.one-on-one.teacher-only {
      student-component {
        display: none !important;
      }
    }
  }
}
</style>
