<template>
  <div
    v-loading="loading"
    class="quiz-sub-student-component"
  >
    <!--学生端答题面板-->
    <div
      v-if="activeView === 'questionForm' && questionInfo"
      class="an-content student-an-panel"
    >
      <div class="an-panel-title">
        <h3> {{ questionInfo.questionContent || $t('测试题') }}</h3>
        <div>
          <span v-if="questionInfo.correctAnswer">{{ questionInfo.type === 0 ? $t('单选题') : $t('多选题') }}</span>
          <span class="time">{{ }}</span>
          <!-- 限时答题 -->
          <template v-if="questionInfo.duration > 0">
            <span v-if="questionInfo.state === 0">( {{ currentAnswer ? $t('已提交') : $t('剩余时间') }} <CountDown
              :time.sync="questionInfo.ttl"
            />)</span>
            <span v-else>({{ $t('已结束，用时') }} {{ questionInfo.endTime - questionInfo.startTime | timeFormat }})</span>
          </template>
          <!-- 不限时答题 -->
          <template v-else>
            <span v-if="questionInfo.state === 0">({{ currentAnswer ? $t('已提交') : $t('已用时') }} <CountUp
              :time.sync="questionInfo.ttl"
            />)</span>
            <span v-else>({{ $t('已结束，用时') }} {{ questionInfo.endTime - questionInfo.startTime | timeFormat }})</span>
          </template>
        </div>
      </div>
      <div class="option-btn">
        <ul :class="{'flex-start': optionList.length > 4 }">
          <!--by开发：选中加class="active"-->
          <template v-for="(item, index) in optionList">
            <li
              :key="item.value"
              :class="{ active: item.checked}"
              @click="toggleAnswer(item, index)"
            >
              {{
                item.value
              }}
            </li>
          </template>
        </ul>
      </div>
      <!--  未提交答案  -->
      <div
        v-if="currentAnswer"
        class="option-tip"
      >
        {{ translateTip.tip }}
      </div>
      <div class="active-btn">
        <button
          v-if="!currentAnswer"
          v-role="['student']"
          class="answer-btn"
          :disabled="!submitAvailable"
          @click="answerQuestion"
        >
          <span>{{ $t('提交答案') }}</span>
        </button>
        <button
          v-else
          v-role="['student']"
          class="answer-btn"
          :disabled="!submitAvailable"
          @click="answerQuestion"
        >
          <span>{{ $t('修改答案') }}</span>
        </button>
      </div>
    </div>
    <!--学生端答题结果-->
    <div
      v-else-if="questionInfo"
      class="an-content student-an-result"
    >
      <div
        v-if="autoHideQuiz"
        class="bg-close"
        @click="handleConfirmClick"
      >
        {{ $t('关闭') }}({{ closingRemaining }}s)
      </div>
      <template v-if="!currentAnswer || (!questionInfo.correctAnswer && questionInfo.state === 1)">
        <div class="result-header">
          <img
            src="./assets/pic_answer_stop.svg"
            alt=""
          >
          <!-- by开发：【答错了】显示内容 -->
          <div class="result-tip">
            {{ $t('已停止答题') }}
          </div>
        </div>
        <div
          class="result-confirm"
          @click="handleConfirmClick()"
        >
          {{ $t('确认') }}
        </div>
        <div
          v-if="currentAnswer"
          class="right-key mt120"
        >
          {{ $t('你的答案') }}<label>{{
            convertDecimalToAnswer(currentAnswer).join('')
          }}</label>
        </div>
      </template>
      <template v-else-if="questionInfo.correctAnswer">
        <div
          v-if="questionInfo.correctAnswer === currentAnswer"
          class="result-header"
        >
          <img
            src="./assets/pic_answer_right.svg"
            alt=""
          >
          <div class="result-tip">
            {{ $t('回答正确') }}
          </div>
        </div>
        <div
          v-if="questionInfo.correctAnswer !== currentAnswer"
          class="result-header"
        >
          <img
            src="./assets/pic_answer_wrong.svg"
            alt=""
          >
          <div
            v-if="questionInfo.correctAnswer && questionInfo.correctAnswer !== currentAnswer"
            class="result-tip"
          >
            {{ $t('回答错误') }}
          </div>
        </div>
        <div class="optioned">
          <i
            v-for="(item) in convertDecimalToAnswer(currentAnswer)"
            :key="item"
          >
            {{ item }}
          </i>
        </div>
        <div
          class="result-confirm"
          @click="handleConfirmClick()"
        >
          {{ $t('确认') }}
        </div>
        <div
          v-if="questionInfo.correctAnswer !== currentAnswer"
          class="right-key"
        >
          {{ $t('正确答案') }}<label>{{ convertDecimalToAnswer(questionInfo.correctAnswer).join('') }}</label>
        </div>  <!-- by开发：【答错了】显示内容 -->
      </template>
    </div>
  </div>
</template>

<script>
import i18next from 'i18next';
import QuizBase from './QuizBase';
import CountDown from './CountDown';
import CountUp from './CountUp';

export default {
  name: 'QuizForStudent',
  components: {
    CountDown,
    CountUp,
  },
  extends: QuizBase,
  data() {
    return {
      loading: false,
      optionList: [],
      activeView: '',       // questionForm 答题窗口 | questionResult 答题结果
      questionInfo: null,
      currentAnswer: null,
      roleInfo: null,
      closingRemaining: 5,
      countdownTimer: null,
      hasStartedCountdown: false,
      autoHideQuiz: true,
    };
  },
  computed: {
    submitAvailable() {
      // 选择了正确答案：
      //      1. 单选 ，必须选择1个答案
      //      2. 多选 ， 必须选择2+个答案
      // 没有选择正确答案：
      //      1. 至少选择1个答案
      if (this.questionInfo.type === 0 || !this.questionInfo.correctAnswer) {
        return this.answers.length > 0;
      } if (this.questionInfo.correctAnswer) {
        return this.answers.length > 1;
      }
      return false;
    },
    answers() {
      return this.optionList.filter(item => item.checked);
    },
    translateTip() {
      return {
        tip: i18next.t('耐心等待{{arg_0}}揭晓正确答案', { arg_0: this.roleInfo.teacher }),
      };
    },
  },
  mounted() {
    this.roleInfo = TCIC.SDK.instance.getRoleInfo().roleInfo;
    this.makeSureClassJoined(this.onClassJoin);
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Task_Updated, this.onTaskUpdate);
  },

  methods: {
    handleCountdownClose() {
      this.autoHideQuiz = TCIC.SDK.instance.isFeatureAvailable('AutoHideQuiz.Student');
      if (!this.autoHideQuiz) return;
      if (this.hasStartedCountdown) return;

      this.hasStartedCountdown = true;
      this.closingRemaining = 5;

      this.countdownTimer = setInterval(() => {
        if (this.closingRemaining > 1) {
          // eslint-disable-next-line no-plusplus
          this.closingRemaining--;
        } else {
          clearInterval(this.countdownTimer);
          this.closingRemaining = 5;
          this.hasStartedCountdown = false;
          this.closeQuestion();
        }
      }, 1000);
    },
    handleConfirmClick() {
      this.closeQuestion();
    },
    onClassJoin() {
      this.initEvent();
      this.autoHideQuiz = TCIC.SDK.instance.isFeatureAvailable('AutoHideQuiz.Student');
    },

    initEvent() {
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Question_End, (res) => {
        if (this.questionInfo) {
          this.questionInfo.state = 1;
          this.renderView('questionResult');
          this.handleCountdownClose();
        }
      });

      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Question_Begin, (question) => {
        this.reset();
        // 收到消息先处理，再获取丰富内容, 这里最好由后台一次下发
        this.questionInfo = question;
        this.renderView('questionForm');
        // this.getQuestionInfo(question)
      });

      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Question_Valid, this.updateValidQuestion);

      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Question_Abandon, (question) => {
        if (this.questionInfo && question.questionId === this.questionInfo.questionId) {
          window.showToast(i18next.t('{{arg_0}}已取消答题', { arg_0: this.roleInfo.teacher }), 'error');
          this.closeQuestion();
        }
      });

      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Question_Close, (question) => {
        if (this.questionInfo && question.questionId === this.questionInfo.questionId) {
          window.showToast(i18next.t('{{arg_0}}已关闭答题', { arg_0: this.roleInfo.teacher }), 'error');
          this.closeQuestion();
        }
      });
      // 更新最初的答题状态
      TCIC.SDK.instance.getMyQuestions()
        .then((ret) => {
          ret.questionInfos.forEach((question) => {
            if (question.state === 0 || question.state === 1) {
              this.updateValidQuestion({
                ...question,
                answer: ret.answerInfos.find(item => item.questionId === question.questionId),
              });
            }
          });
        })
        .catch((err) => {
          console.warn('updateQuestion', `check question fail: ${JSON.stringify(err)}`);
        });
    },

    updateValidQuestion(question) {
      this.questionInfo = question;
      this.currentAnswer = question.answer?.answer;
      if (this.questionInfo.state === 0) {
        this.renderView('questionForm');
      } else {
        this.renderView('questionResult');
        this.handleCountdownClose();
      }
    },

    initOptions() {
      // 初始化答案选项
      const answers = this.defaultOptions.substr(0, this.questionInfo.optionNumber).split('');
      const currentAnswerArray = this.currentAnswer ? this.convertDecimalToAnswer(this.currentAnswer) : [];

      this.optionList = answers.map(item => ({
        value: item,
        checked: currentAnswerArray.includes(item),
      }));
    },

    reset() {
      this.currentAnswer = null;
      this.questionInfo = null;
    },

    closeQuestion() {
      this.reset();
      this.$emit('hide');
    },

    // 恢复所有选项的选择态
    resetAnswer() {
      this.optionList.forEach((item) => {
        item.checked = false;
      });
    },

    toggleAnswer(answer) {
      // 单选题必须选择答案
      if (this.questionInfo.type === 0) {
        this.resetAnswer();
      }
      answer.checked = !answer.checked;
    },

    renderView(type) {
      this.activeView = type;
      if (type === 'questionForm') {
        this.initOptions();
      }
      this.$emit('show');
    },

    getQuestionInfo(question) {
      TCIC.SDK.instance.getQuestionInfo(question.questionId)
        .then((res) => {
          this.questionInfo = res;
          this.renderView('questionForm');
        });
    },

    answerQuestion() {
      const answers = this.answers.map(item => item.value);
      const currentAnswer = this.convertAnswerToDecimal(answers);

      this.loading = true;
      TCIC.SDK.instance.answerQuestion({
        questionId: this.questionInfo.questionId,
        answer: currentAnswer,
      })
        .then(() => {
          setTimeout(() => {
            this.loading = false;
          }, 500);
          this.currentAnswer = currentAnswer;
        })
        .catch((err) => {
          setTimeout(() => {
            this.loading = false;
          }, 500);
          TCIC.SDK.instance.showMessageBox(i18next.t('提示'), i18next.t('提交答题结果失败: {{arg_0}}', { arg_0: err.errorMsg }), [i18next.t('确定')], (index) => {
          });
        });
    },

    // 兜底逻辑，IM消息丢失的情况下，能通过心跳补拉，避免答题器无法关闭的情况。
    onTaskUpdate(taskInfo) {
      if (taskInfo.taskId !== this.taskId || !this.questionInfo || !this.isVisible) {
        return;
      }
      const info = JSON.parse(taskInfo.content);
      // 当前答题器ID与当前TaskId不符，关闭当前答题器
      // 当前答题器的state === 0 ，关闭答题答题器
      if (info.quizId !== this.questionInfo.questionId || info.state === 0) {
        this.closeQuestion();
      }
    },
  },
};
</script>
<style lang="less">
@--color-primary: #006EFF;
@--color-public: #14181D;
@--color-disable: #b9bbbc;
/* 答题器 */
.quiz-sub-student-component {
  /*提问问题*/

  .an-content {
    /* 学生端样式 === begin */

    &.student-an-panel {
      min-height: 316px;

      .an-panel-title {
        text-align: center;
        margin-bottom: 40px;

        h3 {
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 16px;
          line-height: 24px;
        }

        span + span {
          margin-left: 10px
        }
      }

      .option-btn {
        height: 140px;

        &.optioned {
          flex-direction: column;
          justify-content: flex-start;

          ul {
            display: flex;
            justify-content: center;

            li {
              margin-bottom: 12px;

              &:hover {
                cursor: default;
                color: #333;
                border-color: #CFCFCF;
              }
            }
          }
        }

        ul {
          width: 100%;
          padding: 0 0 0 26px;
          align-items: self-start;

          &.flex-start {
            justify-content: flex-start;
          }

          li {
            width: 50px;
            height: 50px;
            margin: 0 20px 22px 0;
            font-size: 32px;
          }
        }
      }

      .option-tip {
        color: #b8b8b8;
        text-align: center
      }
    }

    &.student-an-result {
      min-height: 316px;
      text-align: center;
      display: flex;
      flex-direction: column;

      .bg-close {
        position: absolute;
        top: 6px;
        right: 10px;
        padding: 4px 10px;
        background-color: #F9F9F9;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 14px;
        font-size: 15px;
        color: #666;
        line-height: 1;
        white-space: nowrap;
        cursor: pointer;
      }

      .result-header{
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        margin: 30px 0;
      }

      .result-tip {
        font-size: 20px;
        font-weight: bold;
        margin-left: 16px;
      }

      .result-confirm {
        display: inline-block;
        margin: 24px auto 0;
        padding: 16px 40px;
        background-color: #2B2B2B;
        color: #FFFFFF;
        font-size: 16px;
        font-weight: 500;
        border-radius: 999px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        text-align: center;
        cursor: pointer;
        user-select: none;
        transition: background-color 0.4s ease, box-shadow 0.4s ease;
      }
      .result-confirm:hover {
        background-color: #535353;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.34);
      }

      .optioned {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        padding-left: 20px;

        i {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          width: 50px;
          height: 50px;
          background: #FFFFFF;
          border: 1px solid #CFCFCF;
          border-radius: 3px;
          margin: 0 20px 22px 0;
          font-size: 32px;

          &.un-active {
            display: none;
          }

          // &.active {
          //   color: #fff;
          //   border-color: #13A449;
          //   background: #13A449
          // }
        }
      }

      .right-key {
        margin: 30px 0 2px 0;

        &.mt120 {
          margin-top: 70px;
        }
      }
    }

    /* 学生端样式 === end */
  }

  /*正确答案*/

  .right-key {
    color: #b8b8b8;
    margin: 24px 0 15px 0;

    label {
      margin-left: 10px;
      font-family: Number;
      font-size: 30px;
      color: #13A449;
      letter-spacing: 6px;
      line-height: 38px;
    }
  }
}

</style>
