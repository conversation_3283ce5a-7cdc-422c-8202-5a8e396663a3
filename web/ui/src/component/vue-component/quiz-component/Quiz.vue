<template>
  <div
    class="quiz-component"
  >
    <template v-if="isTeacher || isAssistant">
      <el-popconfirm
        :title="translateTip.answersTitle"
        :confirm-button-text="$t('结束答题')"
        :cancel-button-text="$t('取消')"
        :disabled="closable"
        popper-class="light"
        @confirm="confirmClose"
      >
        <div
          slot="reference"
          class="btn-close"
          @click="onClose"
        >
          <i class="el-icon-close" />
        </div>
      </el-popconfirm>
    </template>
    <div class="bg-top ">
      <div class="drag-zone drag-module-header__wrap" />
    </div>
    <div class="bg-box">
      <div class="bg-br">
        <div
          v-loading="loading"
          class="bg-con"
        >
          <!-- 老师端 -->
          <QuizForTeacher
            v-if="isTeacher || isAssistant"
            ref="quizRef"
            @hide="onHide"
            @show="onShow"
            @on-loading="onLoading"
          />
          <!-- 学生端 -->
          <QuizForStudent
            v-else
            ref="quizRef"
            @hide="onHide"
            @show="onShow"
            @on-loading="onLoading"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import BaseComponent from '@/component/core/BaseComponent';
import QuizForTeacher from './QuizForTeacher';
import QuizForStudent from './QuizForStudent';
import Constant from '@/util/Constant';
import i18next from 'i18next';

export default {
  name: 'QuizComponent',
  components: {
    QuizForTeacher,
    QuizForStudent,
  },
  extends: BaseComponent,
  data() {
    return {
      isTeacher: false,
      isAssistant: false,
      closable: true,
      loading: false,
      roleInfo: {},
    };
  },
  computed: {
    translateTip() {
      return {
        answersTitle: i18next.t('{{arg_0}}正在答题，确定要结束答题吗？', { arg_0: this.roleInfo.student }),
      };
    },
  },
  watch: {
    isVisible(val) {
      if (val && TCIC.SDK.instance.isRecordMode()) {
        // 录制模式下，3s 后自定隐藏
        setTimeout(() => {
          this.hide();
        }, 3000);
      }
    },
  },
  mounted() {
    this.roleInfo = TCIC.SDK.instance.getRoleInfo().roleInfo;
    this.makeSureClassJoined(this.onJoinClass);
    // this.toggleComponentDrag(true);
    this.toggleComponentDrag(true);
  },
  beforeDestroy() {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
    }
  },
  methods: {
    onJoinClass() {
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      // 定义X是否可以直接关闭
      TCIC.SDK.instance.registerState(Constant.TStateQuizClosable, '随堂测是否可以直接关闭', this.closable);
      this.addLifecycleTCICStateListener(Constant.TStateQuizClosable, (val) => {
        this.closable = val;
      });
    },

    onClose() {
      if (this.closable) {
        this.$refs.quizRef.closeQuestion();
      }
    },
    confirmClose() {
      this.closable = true;
      this.$refs.quizRef.abandonQuestion();
    },

    render() {
      if (!this.isVisible) {
        this.$refs.quizRef.renderView();
      }
    },
    convertAnswerToDecimal(answers) {
      const plainText = [];
      if (answers.length === 0) {
        return 0;
      }
      this.defaultOptions.split('').reverse()
        .forEach((item) => {
          plainText.push(answers.includes(item) ? 1 : 0);
        });
      const decimal = parseInt(plainText.join(''), 2);
      console.debug(`convertAnswerToDecimal => source => [${answers.join()}], to => [${plainText}] , decimal => ${decimal}`);
      return decimal;
    },

    convertDecimalToAnswer(int) {
      if (int === 0) {
        return null;
      }
      const bitArray = parseInt(int, 10).toString(2)
        .split('')
        .reverse();
      const answers = [];
      bitArray.forEach((item, index) => {
        if (item === '1') {
          answers.push(this.defaultOptions[index]);
        }
      });
      console.debug(`convertDecimalToAnswer => bitArray => [${bitArray}], to => [${answers.join()}] , decimal => ${int}`);
      return answers;
    },

    onShow() {
      this.show();
    },

    onHide() {
      this.hide();
    },

    onLoading(status) {
      this.loading = status;
    },

    onComponentVisibilityChange(val) {
    },
  },
};
</script>
<style lang="less">
@--color-primary: #006EFF;
@--color-public: #14181D;
@--color-disable: #b9bbbc;

@font-face {
  font-family: 'Number';
  src: url("@assets/font/Akrobat-Bold.otf");
}

/* 答题器 */
.quiz-component {
  * {
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "\5FAE\8F6F\96C5\9ED1", Arial, sans-serif;
    line-height: 1;
    box-sizing: border-box;
  }

  /*操作按钮*/

  .active-btn {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-top: 25px;
    opacity: .9;
    transition: all 0.2s;

    &:hover {
      opacity: 1;
    }
  }

  position: absolute;
  width: 100%;
  height: 100%;
  font-size: 16px;
  /*关闭按钮*/

  .btn-close {
    position: absolute;
    right: 0px;
    top: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    color: rgba(#979797, .5);

    i {
      font-size: 30px;
    }

    &:hover {
      color: rgba(#fff, .9);
      background: rgba(0, 0, 0, 0.2);
      transition: all .5s;
    }
  }

  .bg-top {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    width: calc(100% - 40px);
    height: 82px;
    background: center url('./assets/pic_clamp.svg') no-repeat;
    background-size: auto 100%;

    .drag-zone {
      width: 70%;
      height: 100%;
      margin: 0 auto;
      cursor: move;
    }
  }

  .bg-box {
    width: 360px;
    margin-top: 56px;
    background-image: linear-gradient(148deg, #E2BB69 0%, #CE9B70 100%);
    border-radius: 4px;
    padding: 9px 9px 22px 7px;
  }

  .bg-con {
    width: 344px;
    margin-top: -3px;
    margin-left: -3px;
    margin-bottom: 3px;
    padding: 26px 16px 20px 16px;
    background: #F9F9F9;
    box-shadow: 2px 2px 3px 0px rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    min-height: 300px;
  }

  .bg-br {
    display: inline-block;
    margin-top: 3px;
    margin-left: 3px;
    width: 344px;
    height: 100%;
    background: #F9F9F9;
    box-shadow: 2px 2px 3px 0px rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    position: relative;
  }


  .inp-answer {
    font-size: 16px;

    .el-input__inner {
      line-height: 38px;
      height: 38px;
      padding: 0 8px;
      word-break: keep-all;
    }
  }

  .option-btn {
    display: flex;
    justify-content: space-between;

    ul {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      width: 240px;
      justify-content: left;

      li {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        margin: 0 16px 16px 0;
        border: 1px solid #CFCFCF;
        border-radius: 3px;
        font-size: 26px;

        &:hover {
          color: #13A449;
          border-color: #13A449;
          cursor: pointer
        }

        &.active {
          color: #fff;
          border-color: #13A449;
          background: #13A449
        }
      }
    }
  }

  .num-btn {
    height: 40px;
    display: flex;
    align-items: center;

    button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border-radius: 4px;
      border: 1px solid @--color-public;
      color: @--color-public;

      i {
        &.cut {
          width: 10px;
          height: 2.86px;
          background: @--color-public;
        }

        &.add {
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          width: 10px;
          height: 2.86px;
          background: @--color-public;

          &:after {
            transform: rotate(180deg);
            content: "";
            width: 2px;
            height: 10px;
            background: @--color-public;
          }
        }
      }

      &:first-child {
        margin-right: 12px;
      }

      &:hover {
        border-color: @--color-primary;

        i {
          background: @--color-primary;

          &.add:after {
            background: @--color-primary;
          }
        }
      }

      &:focus {
        border-color: @--color-primary;
        background: @--color-primary;

        i {
          background: #fff;

          &.add:after {
            background: #fff;
          }
        }
      }

      &:disabled {
        border-color: @--color-disable;

        i {
          background: @--color-disable;

          &.add:after {
            background: @--color-disable;
          }
        }
      }
    }
  }

  .time-answer {
    margin-top: 40px;

    .el-checkbox {
      &__label {
        font-size: 16px;
        color: #333;
        padding-left: 6px;
      }

      &__inner {
        transform: scale(1.15)
      }

      &__input {
        margin-top: -1px;
      }

      &__input.is-checked .el-checkbox__inner, &__input.is-indeterminate .el-checkbox__inner {
        background-color: @--color-primary;
        border-color: @--color-primary;
      }

      &__input.is-focus .el-checkbox__inner {
        border-color: @--color-primary;
      }

      &__input:hover .el-checkbox__inner {
        border-color: @--color-primary;
      }
    }

    .el-select {
      margin-left: 4px;

      .el-input__inner {
        line-height: 30px;
        height: 30px;
        width: 90px;
        padding: 0 25px 0 8px;
        font-size: 16px;
        color: #333
      }

      .el-input__icon {
        line-height: 1
      }

      .el-input__suffix {
        right: 2px
      }

      .el-select__caret {
        color: @--color-public;
        font-weight: bold;

        &.is-reverse {
          color: @--color-primary;
        }
      }

      .el-input.is-focus .el-input__inner {
        color: @--color-primary;
        border-color: @--color-primary
      }
    }
  }
}


/* 答题器渐变色边框按钮 */
button {
  &.answer-btn {
    min-width: 144px;
    height: 58px;
    border-radius: 50px;
    padding: 4px;
    font-size: 24px;
    color: #FFF;
    background-image: linear-gradient(@--color-public, @--color-public), linear-gradient(148deg, #E7E7E7 0%, #A5A5A5 100%);
    background-clip: content-box, padding-box;

    &:hover {
      background-image: linear-gradient(@--color-primary, @--color-primary), linear-gradient(148deg, #E7E7E7 0%, #A5A5A5 100%);
    }

    &:disabled {
      opacity: .3;
    }

    span {
      padding: 0 15px;
    }
  }

  &.answer-btn-border {
    min-width: 140px;
    height: 52px;
    border-radius: 50px;
    font-size: 24px;
    color: @--color-public;
    border: 2px solid @--color-public;
    //&:focus{
    //  border-color: @--color-primary;
    //  color: @--color-primary;
    //}

    span {
      padding: 0 15px;
    }
  }
}

</style>
