<template>
  <div class="quiz-sub-teacher-component">
    <!-- 提问问题 -->
    <div
      v-if="activeView === 'questionForm'"
      class="an-content"
    >
      <el-input
        v-model="questionContent"
        :placeholder="$t('请输入问题')"
        class="inp-answer"
        @focus="onTitleFocus"
        @blur="onTitleBlur"
      />
      <div class="text-length">
        {{ inputLength }}/{{ questionContentMaxLength }}
      </div>
      <div class="option-btn">
        <ul>
          <!--by开发：选中加class="active"-->
          <template v-for="(item, index) in optionList">
            <li
              v-if="index < availableOptionsCount"
              :key="index"
              :class="{ active: item.checked}"
              @click="toggleAnswer(item, index)"
            >
              {{ item.value }}
            </li>
          </template>
        </ul>
        <div class="num-btn">
          <button
            class="border"
            :disabled="availableOptionsCount === 2"
            @click="availableOptionsCount--"
          >
            <i
              class="cut"
            />
          </button>
          <button
            class="border"
            :disabled="availableOptionsCount === defaultOptions.length"
            @click="availableOptionsCount += 1"
          >
            <i class="add" />
          </button>
        </div>
      </div>
      <div class="tip">
        {{ $t('如需预设正确答案，可点击如上选项') }}
      </div>
      <div class="time-answer">
        <el-checkbox
          v-model="timeLimitEnable"
          :label="$t('限时答题')"
        />
        <el-select
          v-if="timeLimitEnable"
          v-model="timeLimit"
          value-key="value"
          :placeholder="$t('请选择')"
          popper-class="light"
        >
          <el-option
            v-for="item in timeLimitList"
            :key="item.value"
            :label="item.label"
            :value="item"
          />
        </el-select>
      </div>
      <div class="active-btn">
        <button
          class="answer-btn"
          :disabled="!inputLength"
          @click="applyQuiz"
        >
          <span>{{ $t('开始答题') }}</span>
        </button>
      </div>
    </div>


    <!--提问结果-->
    <div
      v-else-if="activeView === 'questionStats'"
      v-loading="loading"
      class="an-result"
    >
      <template v-if="questionStats">
        <div class="title">
          <label>{{ questionStats.questionContent || $t('答题进行中') }}</label>
          <!-- 限时答题 -->
          <template v-if="questionStats.duration > 0">
            <span v-if="questionStats.state === 0">({{ $t('剩余时间') }} <CountDown
              :time.sync="questionStats.ttl"
              @stop-question="stopQuestion"
            />)</span>
            <span v-else>({{ $t('已结束，用时') }} {{ questionStats.endTime - questionStats.startTime | timeFormat }})</span>
          </template>
          <!-- 不限时答题 -->
          <template v-else>
            <span v-if="questionStats.state === 0">({{ $t('已用时') }} <CountUp :time.sync="questionStats.ttl" />)</span>
            <span v-else>({{ $t('已结束，用时') }} {{ questionStats.endTime - questionStats.startTime | timeFormat }})</span>
          </template>
        </div>
        <div class="many">
          <ul>
            <li>
              <label>{{ questionStats.answerCount }}<i class="num">/{{ questionStats.total }}</i><i>{{ $t('人') }}</i></label>
              <span>{{ $t('已回答') }}</span>
            </li>
            <li v-if="questionStats.correctAnswer">
              <label>{{ questionStats.correctCount }}<i>{{ $t('人') }}</i></label>
              <span>{{ $t('正确人数') }}</span>
            </li>
            <li v-if="questionStats.correctAnswer">
              <label>{{ questionStats.correctRate }}<i>%</i></label>
              <span>{{ $t('正确率') }}</span>
            </li>
          </ul>
        </div>
        <el-tabs
          v-model="activeName"
          class="an-tabs"
        >
          <el-tab-pane
            :label="$t('统计')"
            name="statistics"
          />
          <el-tab-pane
            :label="$t('明细')"
            name="detail"
          />
        </el-tabs>
        <div
          v-if="activeName === 'statistics'"
          class="tab-statistics"
        >
          <el-scrollbar class="an-result-scrollbar">
            <!--单选选项-->
            <div
              v-if="questionStats.stats.length === 0"
              class="el-scrollbar__wrap empty-tips"
            >
              <span>{{ $t('暂无数据') }}</span>
            </div>
            <table v-else>
              <tr
                v-for="(item, index) in questionStats.stats"
                :key="index"
                class="sta-item"
              >
                <td class="fit-content">
                  <label>{{ convertDecimalToAnswer(item.answer).join('') }}</label>
                </td>
                <td class="max-content sta-progress-td">
                  <el-progress
                    :percentage="parseInt( item.count / questionStats.answerCount * 100, 10)"
                    :show-text="false"
                    :stroke-width="5"
                    class="sta-progress"
                  />
                </td>
                <td class="fit-content">
                  <el-popover
                    popper-class="light sta-popover"
                    placement="bottom"
                    width="180"
                    trigger="click"
                  >
                    <ul>
                      <li class="title">
                        <label>{{ $t('姓名') }}</label>
                        <span>{{ $t('用时') }}</span>
                      </li>
                      <el-scrollbar>
                        <li
                          v-for="(sub, sub_index) in getCurrentUserList( item.answer )"
                          :key="sub_index"
                        >
                          <label>{{ sub.nickname || sub.userId }}</label>
                          <span>{{ sub.costTime | timeFormat }}</span>
                        </li>
                      </el-scrollbar>
                    </ul>
                    <el-button
                      slot="reference"
                      type="text"
                      class="sta-btn"
                    >
                      {{ item.count }}{{ $t('人') }}<i
                        class="el-icon-arrow-right"
                      />
                    </el-button>
                  </el-popover>
                </td>
              </tr>
            </table>
          </el-scrollbar>
        </div>
        <div
          v-if="activeName === 'detail'"
          class="tab-detail"
        >
          <ul>
            <li class="li-title">
              <label class="l-name">{{ $t('姓名') }}</label>
              <label class="l-answer">{{ $t('所选答案') }}</label>
              <label class="l-time">{{ $t('用时') }}</label>
            </li>
            <el-scrollbar
              v-if="questionStats.answers.length === 0"
              class="detail-scrollbar"
            >
              <div class="el-scrollbar__wrap empty-tips">
                <span>{{ $t('暂无数据') }}</span>
              </div>
            </el-scrollbar>
            <el-scrollbar
              v-else
              class="detail-scrollbar"
            >
              <!--by开发: 正确答案添加样式："txt-right"-->
              <li
                v-for="(item, index) in questionStats.answers"
                :key="index"
                :class="{ 'txt-right' : questionStats.correctAnswer && item.answer === questionStats.correctAnswer }"
              >
                <label class="l-name">{{ item.nickname || item.userId }}</label>
                <label class="l-answer">{{ convertDecimalToAnswer(item.answer).join('') }}</label>
                <label class="l-time">{{ item.costTime | timeFormat }}</label>
              </li>
            </el-scrollbar>
          </ul>
        </div>
        <div
          v-if="questionStats.correctAnswer"
          class="right-key"
        >
          {{ $t('正确答案') }}<label>{{ convertDecimalToAnswer(questionStats.correctAnswer).join('') }}</label>
        </div>
        <div
          v-if="questionStats.state === 0"
          class="active-btn"
        >
          <button
            class="answer-btn"
            @click="stopQuestion"
          >
            <span>{{ $t('结束答题') }}</span>
          </button>
        </div>
        <div
          v-else-if="questionStats.state === 1"
          class="active-btn"
        >
          <button
            class="answer-btn-border"
            @click="closeQuestion"
          >
            <span>{{ $t('关闭') }}</span>
          </button>
          <button
            class="answer-btn"
            @click="createQuestion"
          >
            <span>{{ $t('新建答题') }}</span>
          </button>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import i18next from 'i18next';
import QuizBase from './QuizBase';
import CountDown from './CountDown';
import CountUp from './CountUp';
import Lodash from 'lodash';
import Constant from '@/util/Constant';

export default {
  name: 'QuizForTeacher',
  components: {
    CountDown,
    CountUp,
  },
  extends: QuizBase,
  data() {
    return {
      loading: false,                  // 加载loading动画
      userCache: new Map(),            // 用户昵称缓存
      activeView: '',                  // questionForm 提问窗口 | questionStats 统计窗口
      questionContent: '',             // 题目
      defaultQuestionContent: i18next.t('测试题'), // 默认题目
      questionContentMaxLength: 20,    // 最大长度
      timeLimitList: [
        {
          value: 5,
          label: i18next.t('5秒'),
        }, {
          value: 10,
          label: i18next.t('10秒'),
        }, {
          value: 20,
          label: i18next.t('20秒'),
        }, {
          value: 30,
          label: i18next.t('30秒'),
        }, {
          value: 60,
          label: i18next.t('1分钟'),
        }, {
          value: 120,
          label: i18next.t('2分钟'),
        }, {
          value: 300,
          label: i18next.t('5分钟'),
        }, {
          value: 600,
          label: i18next.t('10分钟'),
        },
      ],                                // [限时答题] 的可选项
      timeLimitDefaultValue: 60,        // [限时答题] 的默认时长
      timeLimitEnable: false,           // [限时答题] 的勾选状态
      timeLimit: null,                  // [限时答题] 的时间选项
      activeName: 'statistics',         // [限时答题] 当前激活的统计详情
      inputLength: 0,                   // [限时答题] 长度提示
      questionId: null,                 // 当前测验ID
      questionStats: null,              // 统计数据
      retryQuestionStatInterval: null,  // 统计接口定时器
      retryCount: 0,                    // 重试计数器
      maxRetryCount: 3,                 // 最大重试计数
    };
  },
  watch: {
    questionContent(val) {
      const { questionContentMaxLength } = this;
      if (val.length > questionContentMaxLength) {
        this.questionContent = val.substring(0, questionContentMaxLength);
      }
      this.inputLength = this.questionContent.length;
    },
    questionStats(val) {
      // 如果当前的提问状态为进行中。
      if (val) {
        const state = val.state !== 0;
        if (TCIC.SDK.instance.getState(Constant.TStateQuizClosable) !== state) {
          TCIC.SDK.instance.setState(Constant.TStateQuizClosable, state);
        }
      } else {
        TCIC.SDK.instance.setState(Constant.TStateQuizClosable, true);
      }
    },
    availableOptionsCount(val, oldVal) {
      if (val < oldVal && this.optionList[val].checked) {
        this.optionList[val].checked = false;
      }
    },
  },
  mounted() {
    this.makeSureClassJoined(this.onClassJoin);
  },
  beforeDestroy() {
    this.reset();
  },

  methods: {
    onClassJoin() {
      this.initData();
      this.initEvent();
    },

    initData() {
      this.questionContent = this.defaultQuestionContent;
      // 初始化答案选项
      const answers = this.defaultOptions.split('');
      this.optionList = answers.map(item => ({
        value: item,
        checked: false,
      }));
      this.timeLimit = this.timeLimit || this.timeLimitList.find(item => item.value === this.timeLimitDefaultValue);
    },

    initEvent() {
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Question_Valid, (question) => {
        this.renderView(question);
      });
      // 收到通知才去拉取答题数据
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Question_Been_Answered, Lodash.debounce((question) => {
        if (question && question.questionId === this.questionId) {
          this.getQuestionStats();
        }
      }, 200, {
        maxWait: 5000,
      }));

      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Member_Join, Lodash.debounce(() => {
        if (this.activeView === 'questionStats') {
          this.getQuestionStats();
        }
      }, 2000, {
        maxWait: 5000,
      }));

      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Question_End, (res) => {
        this.getQuestionStats();
      });

      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Question_Begin, (question) => {
        this.renderView(question);
      });

      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Question_Abandon, (question) => {
        this.handleQuestionClose();
      });

      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Question_Close, (question) => {
        if (this.questionId === question.questionId) {
          this.handleQuestionClose();
        }
      });
    },
    reset() {
      if (this.questionId) {
        this.updateTask(this.questionId, 0);
      }
      this.questionId = null;
      this.questionStats = null;
      this.timeLimitEnable = false;
      this.availableOptionsCount = 4;
      this.initData();
      clearTimeout(this.retryQuestionStatInterval);
    },

    onTitleFocus() {
      if (this.questionContent === this.defaultQuestionContent) {
        this.questionContent = '';
      }
    },
    onTitleBlur() {
      if (!this.questionContent.trim()) {
        this.questionContent = this.defaultQuestionContent;
      }
    },

    renderView(question) {
      if (question) {
        this.activeView = 'questionStats';
        this.questionId = question.questionId;
        // 老师端展示数据统计
        this.getQuestionStats(true);
      } else {
        this.activeView = 'questionForm';
      }
      this.$parent.show();
    },

    getAnswers() {
      const answers = this.optionList.filter(item => item.checked);
      return answers.map(item => item.value);
    },

    toggleAnswer(answer) {
      answer.checked = !answer.checked;
    },

    applyQuiz() {
      const answers = this.getAnswers();
      const classInfo = TCIC.SDK.instance.getClassInfo();

      this.$emit('on-loading', true);
      const params = {
        classId: classInfo.classId,
        type: answers.length === 1 ? 0 : 1,       // 0为单选，1为多选 , 若教师选择了正确答案，则将单选或多选信息传给学生
        // 若教师选择了正确答案，则将单选或多选信息传给学生；
        // 若教师未选择正确答案，则该题目不限制学生作答选项的数目，不明确展示单选或多选，文案：请答题（剩余时间xx）
        questionContent: this.questionContent,
        optionNumber: this.availableOptionsCount,
        correctAnswer: this.convertAnswerToDecimal(answers),
        duration: this.timeLimitEnable ? this.timeLimit.value : 0,
      };
      // 发请求给后台
      TCIC.SDK.instance.createQuestion(params)
        .then((res) => {
          this.$emit('on-loading', false);
          this.questionId = res.questionId;
          this.renderView({ questionId: this.questionId });
          this.updateTask(this.questionId, 1);
          TCIC.SDK.instance.setState(Constant.TStateQuizClosable, false);
        })
        .catch((err) => {
          this.$emit('on-loading', false);
          TCIC.SDK.instance.showMessageBox(i18next.t('提示'), i18next.t('创建答题失败: {{arg_0}}', { arg_0: err.errorMsg }), [i18next.t('确定')], (index) => {
          });
        });
    },

    getQuestionStats(init = false) {
      clearTimeout(this.retryQuestionStatInterval);
      if (init) {
        this.$emit('on-loading', true);
      }
      if (!this.questionId) {
        this.$emit('on-loading', false);
        return;
      }
      TCIC.SDK.instance.getQuestionStats(this.questionId)
        .then((res) => {
          this.$emit('on-loading', false);
          this.retryCount = 0;
          this.enrichStatsData(res);
        })
        .catch((err) => {
          this.$emit('on-loading', false);
          if (this.retryCount > this.maxRetryCount) {
            TCIC.SDK.instance.showMessageBox(i18next.t('提示'), i18next.t('查询答题统计结果失败: {{arg_0}}', { arg_0: err.errorMsg }), [i18next.t('确定')], (index) => {
            });
            this.closeQuestion();
          } else {
            this.retryCount += 1;
            this.retryQuestionStatInterval = setTimeout(() => {
              this.getQuestionStats();
            }, 1000);
          }
        });
    },

    getCurrentUserList(answer) {
      return Lodash.sortBy(this.questionStats.answers.filter(item => item.answer === answer), 'costTime');
    },

    updateResultNick(answers, callback) {
      const { userCache } = this;
      let userIds = [];
      answers.forEach((item) => {
        if (!userCache.has(item.userId)) {
          userIds.push(item.userId);
        } else { // 如果cache里有则给answer赋值
          item.nickname = item.nickname || userCache.get(item.userId);
        }
      });
      userIds = Lodash.uniq(userIds);
      if (!userIds || userIds.length === 0) {
        callback();
        return;
      }
      TCIC.SDK.instance.getUserList(userIds)
        .then((infoArr) => {
          infoArr.users.forEach((item) => {
            userCache.set(item.userId, item.nickname);
          });
          answers.forEach((info) => {
            info.nickname = userCache.get(info.userId) || info.userId;
          });
          callback();
        })
        .catch((err) => {
          console.error('getUserList', err);
          callback();
        });
    },

    // 补充数据
    enrichStatsData(data) {
      // 正确答案个数
      const correctCount = data.stats.find(item => item.answer === data.correctAnswer)?.count || 0;
      // 正确率
      const correctRate = data.answerCount === 0 ? 0 : parseInt(correctCount / data.answerCount * 100, 10);
      const stats = Lodash.sortBy(data.stats, ['count', 'answer']).reverse();
      const answers = data.answers;
      this.updateResultNick(answers, () => {
        this.questionStats = Object.assign(data, {
          correctCount,
          correctRate,
          stats,
          answers,
        });
      });
    },

    createQuestion() {
      if (!this.questionId) {
        this.reset();
        this.renderView();
      } else {
        TCIC.SDK.instance.closeQuestion(this.questionId)
          .then(() => {
            this.loading = false;
            this.reset();
            this.renderView();
          })
          .catch((err) => {
            this.loading = false;
            TCIC.SDK.instance.showMessageBox(i18next.t('提示'), i18next.t('关闭答题失败: {{arg_0}}', { arg_0: err.errorMsg }), [i18next.t('确定')], (index) => {
            });
          });
      }
    },

    stopQuestion() {
      this.loading = true;
      TCIC.SDK.instance.stopQuestion(this.questionId)
        .then(() => {
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
          if (err.errorCode === 10700) {
            this.getQuestionStats();
          } else {
            TCIC.SDK.instance.showMessageBox(i18next.t('提示'), i18next.t('停止答题失败: {{arg_0}}', { arg_0: err.errorMsg }), [i18next.t('确定')], (index) => {
            });
          }
        });
    },

    closeQuestion() {
      if (!this.questionId) {
        this.handleQuestionClose();
        return;
      }
      this.loading = true;
      TCIC.SDK.instance.closeQuestion(this.questionId)
        .then(() => {
          this.loading = false;
          this.handleQuestionClose();
        })
        .catch((err) => {
          this.loading = false;
          TCIC.SDK.instance.showMessageBox(i18next.t('提示'), i18next.t('关闭答题失败: {{arg_0}}', { arg_0: err.errorMsg }), [i18next.t('确定')], (index) => {
          });
        });
    },

    handleQuestionClose() {
      this.reset();
      this.$emit('hide');
    },

    abandonQuestion() {
      this.loading = true;
      TCIC.SDK.instance.abandonQuestion(this.questionId)
        .then(() => {
          this.loading = false;
          this.handleQuestionClose();
        })
        .catch((err) => {
          this.loading = false;
          TCIC.SDK.instance.showMessageBox(i18next.t('提示'), i18next.t('取消答题失败: {{arg_0}}', { arg_0: err.errorMsg }), [i18next.t('确定')], (index) => {
          });
        });
    },
  },
};
</script>
<style lang="less">
@--color-primary: #006EFF;
@--color-public: #14181D;
@--color-disable: #b9bbbc;
.quiz-sub-teacher-component {
  /*提问问题*/

  .an-content {
    padding-top: 12px;
    .inp-answer {
      font-size: 16px;

      .el-input__inner {
        line-height: 38px;
        height: 38px;
        padding: 0 8px;
        background: #fff;
        color: #393939;
      }
    }

    .option-btn {
      display: flex;
      justify-content: space-between;

      ul {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        width: 240px;

        li {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          margin: 0 16px 16px 0;
          border: 1px solid #CFCFCF;
          border-radius: 3px;
          font-size: 26px;

          &:hover {
            color: #13A449;
            border-color: #13A449;
            cursor: pointer
          }

          &.active {
            color: #fff;
            border-color: #13A449;
            background: #13A449
          }
        }
      }
    }

    .num-btn {
      height: 40px;
      display: flex;
      align-items: center;

      button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        border-radius: 4px;
        border: 1px solid @--color-public;
        color: @--color-public;

        i {
          &.cut {
            width: 10px;
            height: 2.86px;
            background: @--color-public;
          }

          &.add {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            width: 10px;
            height: 2.86px;
            background: @--color-public;

            &:after {
              transform: rotate(180deg);
              content: "";
              width: 2px;
              height: 10px;
              background: @--color-public;
            }
          }
        }

        &:first-child {
          margin-right: 12px;
        }

        &:hover {
          border-color: @--color-primary;

          i {
            background: @--color-primary;

            &.add:after {
              background: @--color-primary;
            }
          }
        }

        &:focus {
          border-color: @--color-primary;
          background: @--color-primary;

          i {
            background: #fff;

            &.add:after {
              background: #fff;
            }
          }
        }

        &:disabled {
          border-color: @--color-disable;

          i {
            background: @--color-disable;

            &.add:after {
              background: @--color-disable;
            }
          }
        }
      }
    }

    .time-answer {
      margin-top: 40px;
      height: 30px;

      .el-checkbox {
        vertical-align: middle;
        line-height: 30px;

        &__label {
          font-size: 16px;
          color: #333;
          padding-left: 6px;
        }

        &__inner {
          transform: scale(1.15)
        }

        &__input {
          margin-top: -1px;
        }

        &__input.is-checked .el-checkbox__inner, &__input.is-indeterminate .el-checkbox__inner {
          background-color: @--color-primary;
          border-color: @--color-primary;
        }

        &__input.is-focus .el-checkbox__inner {
          border-color: @--color-primary;
        }

        &__input:hover .el-checkbox__inner {
          border-color: @--color-primary;
        }
      }

      .el-select {
        vertical-align: middle;
        margin-left: 4px;

        .el-input__inner {
          line-height: 30px;
          height: 30px;
          width: 90px;
          padding: 0 25px 0 8px;
          font-size: 16px;
          color: #333
        }

        .el-input__icon {
          line-height: 1
        }

        .el-input__suffix {
          right: 2px
        }

        .el-select__caret {
          color: @--color-public;
          font-weight: bold;

          &.is-reverse {
            color: @--color-primary;
          }
        }

        .el-input.is-focus .el-input__inner {
          color: @--color-primary;
          border-color: @--color-primary
        }
      }
    }
  }

  /*提问结果*/

  .an-result {
    font-size: 16px;

    .title {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-bottom: 12px;
      line-height: 22px;
      color: #393939;

      label {
        font-weight: 600;
        word-break: keep-all;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: flex;
        justify-content: center;
      }

      > span {
        font-weight: 400;
        margin-top: 1px;
        display: flex;
        justify-content: center;
        white-space: pre;
      }
    }

    .many {
      ul {
        display: flex;
        align-items: center;
        justify-content: space-around;
        margin: 0 8px;

        li {
          display: flex;
          flex-direction: column;
          align-items: center;

          label {
            font-family: Number;
            font-size: 30px;
            line-height: 37px;
            margin-bottom: 6px;

            i {
              font-size: 14px;
              font-weight: bold;

              &.num {
                font-family: Number
              }
            }
          }

          span {
            color: #B8B8B8;
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
          }
        }
      }
    }

    .an-tabs {
      margin-top: 24px;

      .el-tabs__nav-scroll {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .el-tabs__item {
        font-size: 16px;
        text-align: center;
        padding: 0 40px;
        width: 120px;

        &:focus.is-active.is-active:not(:active) {
          box-shadow: none
        }

        color: #000;

        &.is-active {
          color: @--color-primary;
        }
      }

      .el-tabs__nav-wrap::after {
        height: 1px
      }

      .el-tabs__active-bar {
        background-color: @--color-primary;
      }
    }

    .an-result-scrollbar {
      .el-scrollbar__wrap {
        height: 154px;
      }
    }

    .empty-tips {
      display: flex;
      align-content: center;
      justify-content: center;
      align-items: center;
      font-size: 16px;
      color: #b8b8b8;
    }

    .tab-statistics {
      width: 100%;

      table {
        width: 100%;

        .max-content {
          display: table-cell;
          width: 100%;
        }

        .fit-content {
          display: table-cell;
          width: fit-content;
        }

        .txt-right {
          color: #13A449;
        }
      }

      .sta-item {
        align-items: center;
        margin: 6px 0 14px 0;
        height: 25px;

        &:last-child {
          margin-bottom: 0
        }

        label {
          flex-shrink: 0;
          font-family: Number;
          font-size: 20px;
          margin-right: 10px;
          min-width: 12px;
          letter-spacing: 4px;
          word-break: keep-all;
        }

        .sta-progress-td {
          display: table-cell;
        }

        .sta-progress {
          .el-progress-bar__inner {
            background-color: @--color-primary
          }

          .el-progress-bar__outer {
            background-color: #DFDFDF;
          }
        }

        .sta-btn {
          min-width: 50px;
          flex-shrink: 0;
          margin: 0 0 0 10px;
          font-size: 16px;

          i {
            font-weight: bold
          }

          &.el-button--text {
            color: #333
          }

          &:hover, &:focus {
            color: @--color-primary;
          }
        }
      }
    }

    .tab-detail {
      .detail-scrollbar {
        .el-scrollbar__wrap {
          height: 122px;
        }
      }

      ul {
        li {
          display: flex;
          align-items: center;
          height: 32px;
          width: 100%;

          label {
            &.l-name {
              width: 114px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              margin-right: 24px;
            }

            &.l-answer {
              flex: 1;
            }

            &.l-time {
              min-width: 60px;
            }
          }

          &.li-title {
            color: #B8B8B8;
          }

          &.txt-right {
            margin-right: 8px;
            label.l-name {
              color: #13A449;
            }

            label.l-answer {
              color: #13A449;
              letter-spacing: 4px
            }
          }
        }
      }
    }
  }

  /*正确答案*/

  .right-key {
    color: #b8b8b8;
    margin: 24px 0 15px 0;

    label {
      margin-left: 10px;
      font-family: Number;
      font-size: 30px;
      color: #13A449;
      letter-spacing: 6px;
      line-height: 38px;
    }
  }

  .text-length {
    text-align: right;
    height: 40px;
    line-height: 26px;
    font-size: 14px;
    padding: 2px;
  }

  .tip {
    font-size: 16px;
    color: #B8B8B8;
  }

  .el-select {
    width: unset;
  }
}

.el-popover.sta-popover {
  font-size: 16px;
  padding: 16px 10px 16px 10px;
  color: #333;

  .title {
    color: #B8B8B8;
    margin-bottom: 4px;
  }

  .el-scrollbar__wrap {
    max-height: 122px;
    overflow: scroll;
  }

  ul {
    display: flex;
    flex-direction: column;

    li {
      display: flex;
      align-items: center;
      height: 30px;

      label {
        flex: 1;
        word-break: keep-all;
        text-overflow: ellipsis;
        overflow: hidden;
        line-height: 30px;
      }

      span {
        min-width: 52px;
        flex-shrink: 0;
      }
    }
  }
}
</style>
