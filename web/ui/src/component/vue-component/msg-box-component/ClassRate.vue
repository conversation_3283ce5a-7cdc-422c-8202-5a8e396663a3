<template>
  <div class="class-rate-component">
    <el-drawer
      v-if="isSmallScreen"
      :visible.sync="dialogVisible"
      direction="btt"
      :show-close="false"
      :close-on-click-modal="false"
      size="400"
      custom-class="class-rate-dialog small-screen"
      @close="showCloseModal"
    >
      <div class="rate-body drawer">
        <div class="rate-title">
          {{ className }}
        </div>
        <div class="rate-sub-title">
          {{ $t('课程已结束，请完成课后评价') }}
        </div>
        <el-rate
          v-model="rate"
          :max="10"
          void-color="#006CFF"
          :colors="{10: '#006CFF'}"
        />
        <div class="el-tate-text">
          {{ rateText }}
        </div>
        <el-input
          v-model="remark"
          type="textarea"
          :placeholder="$t('请输入课堂评语（可选）')"
          maxlength="140"
          rows="4"
          show-word-limit
        />
      </div>
      <div class="rate-footer drawer">
        <el-button
          type="primary"
          :disabled="rate === 0"
          @click="submit"
        >
          {{ $t('提交评价') }}
        </el-button>
      </div>
      <div
        v-show="showResult"
        class="rate-result"
      >
        <img
          src="./assets/icon-success.svg"
          alt=""
        >
        <div>{{ $t('课程评价提交成功') }}</div>
        <el-button
          type="primary"
          @click="handleClose"
        >
          {{ $t('关闭') }}
        </el-button>
      </div>
    </el-drawer>
    <el-dialog
      v-else
      :title="$t('课后评价')"
      custom-class="class-rate-dialog"
      :close-on-click-modal="false"
      top="20vh"
      :visible.sync="dialogVisible"
      width="438px"
      @close="handleClose"
    >
      <div class="rate-body">
        <el-divider />
        <div class="rate-title">
          {{ className }}
        </div>
        <div class="rate-sub-title">
          {{ $t('课程已结束，请完成课后评价') }}
        </div>
        <el-rate
          v-model="rate"
          :max="10"
          void-color="#006CFF"
          :colors="{10: '#006CFF'}"
        />
        <div class="el-tate-text">
          {{ rateText }}
        </div>
        <el-divider />
        <el-input
          v-model="remark"
          type="textarea"
          :placeholder="$t('请输入课堂评语（可选）')"
          maxlength="140"
          rows="4"
          show-word-limit
        />
      </div>
      <div class="rate-footer">
        <el-button
          type="primary"
          :disabled="rate === 0"
          round
          @click="submit"
        >
          {{ $t('提交评价') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import BaseComponent from '../../core/BaseComponent';
import i18next from '@util/i18nextByKey';
export default {
  extends: BaseComponent,
  data() {
    return {
      showResult: false,
      dialogVisible: false,
      isSmallScreen: TCIC.SDK.instance.isMobile() && !TCIC.SDK.instance.isPad(),
      rate: 1,
      remark: '',
      className: TCIC.SDK.instance.getClassInfo().className,
    };
  },
  computed: {
    rateText() {
      if (this.rate <= 1) {
        return this.$t('非常差');
      } if (this.rate < 5) {
        return this.$t('差');
      } if (this.rate === 5) {
        return this.$t('一般');
      } if (this.rate < 10) {
        return this.$t('好');
      }
      return this.$t('非常好');
    },
  },
  methods: {
    show() {
      this.dialogVisible = true;
    },
    handleClose() {
      TCIC.SDK.instance.unInitialize();
    },
    showCloseModal() {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      const { roomInfo } = TCIC.SDK.instance.getNameConfig();
      TCIC.SDK.instance.showErrorMsgBox({
        title: classInfo.className,
        message: i18next.t('roomStatusDesc.hasEnded', roomInfo),
        report: false,
      });
    },
    submit() {
      TCIC.SDK.instance.submitClassRate(this.rate, this.remark)
        .then((res) => {
        // 提交成功
          if (this.isSmallScreen) {
            this.showResult = true;
          } else {
            window.showToast(this.$t('课程评价提交成功'));
            setTimeout(() => {
              TCIC.SDK.instance.unInitialize();
            }, 1500);
          }
        })
        .catch((err) => {
        // 提交失败
          TCIC.SDK.instance.unInitialize();
        });
    },
  },
};
</script>

<style lang="less">
@media screen and (min-width: 600px) {
  .small-screen.el-drawer{
    .rate-body{
      padding-left: 45px;
      padding-right: 45px;
    }
    .rate-footer .el-button{
      width: calc(100vw - 90px);
    }
  }
}
.class-rate-component{
  .rate-result{
    width: 100vw;
    height: 100vh;
    background: #090917;
    font-size: 18px;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    img{
      margin-bottom: 12px;
    }
    .el-button{
      width: 92vw;
      margin-top: 90px;
      max-width: 300px;
    }
  }
  .class-rate-dialog{
    background: rgb(29, 32, 41);
    color: #CFD4E5;
  }
  .el-tate-text {
    text-align: center;
    margin-top: 18px;
    color: #A3AEC7;
  }
  .el-drawer__header{
    display: none;
  }
  .rate-body {
    &.drawer{
      padding-top: 16px;
      .el-tate-text {
        margin-bottom: 12px;
        font-size: 12px;
      }
      .rate-title{
        font-size: 18px;
        padding-left: 16px;
      }
      .rate-sub-title{
        padding-left: 16px;
        font-size: 12px;
      }
    }
    .rate-title{
      font-size: 22px;
      margin-bottom: 4px;
    }
    .rate-sub-title{
      display: block;
      &:after {
        content: '*';
        color: #F24D43;
        vertical-align: middle;
        margin-left: 5px;
      }
    }
    .el-divider:first-child{
      margin-top: 10px;
    }
  }
  .el-dialog__body{
    padding-top: 0;
  }
  .el-rate{
    margin-top: 20px;
    text-align: center;
    white-space: nowrap;
  }
  .el-rate__icon{
    font-size: 28px;
  }
  .el-divider--horizontal{
    opacity: .2;
  }
  .el-dialog__title{
    color: #CFD4E5;
    font-size: 16px;
  }
  .el-textarea{
    .el-input__count{
      background: none;
    }
    textarea.el-textarea__inner  {
      background-color: rgba(126, 142, 154, 0.10);
      padding: 10px 15px;
      border: none;
      &::placeholder{
        color: #A3AEC7;
      }
    }
  }

  .rate-footer  {
    margin-top: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    .el-button{
      width: 92vw;
    }
    &.drawer{
      margin-bottom: 20px;
    }
  }
}
</style>
