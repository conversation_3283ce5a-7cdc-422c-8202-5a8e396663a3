<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="msg-box-component">
    <el-drawer
      v-if="type === 'actionSheet'"
      :visible.sync="dialogVisible"
      :with-header="false"
      direction="btt"
      :before-close="handleClose"
      destroy-on-close
      :size="(buttons.length + 1) * 70"
      @close="dialogVisible = false"
    >
      <div class="action-item action-msg">
        <span
          v-if="isSafeInput"
          class="message"
          v-html="titleMsg"
        />
        <span
          v-else
          v-dompurify-html="message"
          class="message"
        />
      </div>
      <div
        v-for="(button, index) in buttons"
        :key="index"
        class="action-item"
        :class="{primary: index === 0}"
        @click="close(index)"
      >
        {{ button }}
      </div>
    </el-drawer>
    <el-dialog
      v-else
      :title="title"
      class="msg-box-dialog"
      :visible.sync="dialogVisible"
      width="420px"
      :close-on-click-modal="false"
      :before-close="handleClose"
      destroy-on-close
    >
      <span
        v-if="isSafeInput"
        class="message"
        v-html="message"
      />
      <span
        v-else
        v-dompurify-html="message"
        class="message"
      />
      <div
        v-for="(option, index) in options"
        :key="index"
        class="option"
      >
        <el-checkbox v-model="option.value">
          {{ option.text }}
        </el-checkbox>
      </div>
      <div
        v-if="exceptionId && exceptionId !== ''"
        class="exception"
      >
        {{ exceptionId }}
        <el-button
          v-clipboard:copy="exceptionId"
          v-clipboard:success="onCopySuccess"
          v-clipboard:error="onCopyError"
          size="small"
          type="text"
        >
          <i class="icon icon-copy" />
        </el-button>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          v-for="(button, index) in buttons"
          :key="index"
          :data-user-event="`MsgBox-${button}`"
          :type="index === 0 ? 'primary': 'secondary'"
          @click="close(index)"
        >
          {{ button }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>


<script>
import i18next from 'i18next';
import BaseComponent from '../../core/BaseComponent';

export default {
  filters: {},
  extends: BaseComponent,

  data() {
    return {
      title: '',
      message: '',
      buttons: [i18next.t('确定')],
      callback: null,
      options: [],
      exceptionId: '',
      dialogVisible: false,
      isSafeInput: false,
      dialogWinId: -1,
      /**
       * @type {'dialog' | 'actionSheet'}
       */
      type: 'dialog',
    };
  },

  computed: {
    titleMsg() {
      return `${this.title} ${this.message}`;
    },
  },

  // watch: {},

  mounted() {

  },

  methods: {
    show(info) {
      this.title = info.title;
      this.message = info.message;
      this.buttons = info.buttons.slice();
      this.callback = info.callback;
      this.options = info.options;
      this.exceptionId = info.exceptionId;
      this.dialogVisible = true;
      this.isSafeInput = info.isSafeInput;
      this.type = info.msgBoxType || 'dialog';
      // this.updateTbmTarget();
      this.$nextTick(() => {
        const dlgWinName = `msgDialg-${new Date().getTime()}-${Math.floor(Math.random() * 1024)}`;
        this.dialogWinId = window.tbm.pushWindow(dlgWinName);
        window.tbm.pushTarget('msgDialog', ['header', 'content'], this.dialogWinId);
        const btnClose = this.$el.querySelector('.el-dialog__headerbtn');
        window.tbm.updateTarget('msgDialog', [window.tbm.generateNode(btnClose)], 'header', this.dialogWinId);
        const ctxBtns = [];
        Array.from(this.$el.querySelectorAll('.el-button')).forEach(item => ctxBtns.push(window.tbm.generateNode(item)));
        window.tbm.updateTarget('msgDialog', ctxBtns, 'content', this.dialogWinId, ctxBtns.length - 1);
        window.tbm.activeWindow(this.dialogWinId, 'msgDialog', 'content');
      });
    },
    close(index) {
      this.dialogVisible = false;
      this.exceptionId = '';
      window.tbm.removeWindow(this.dialogWinId);
      if (this.callback) {
        let optionsChecked = [];
        if (typeof this.options === 'object' && Array.isArray(this.options)) {
          optionsChecked = this.options.map(option => option.value);
        }
        this.callback(index, optionsChecked);
      }
    },
    handleClose(done) {
      done();
      // 强制关闭时返回-1
      this.close(-1);
    },
    // 复制成功时的回调函数
    onCopySuccess() {
      window.showToast(i18next.t('复制成功'), 'success');
    },
    // 复制失败时的回调函数
    onCopyError() {
      window.showToast(i18next.t('复制失败请重试'));
    },
  },
};
</script>
<style lang="less">
.msg-box-component {
  word-break: normal;
  .el-drawer{
    border-radius: 10px 10px 0 0;
    .action-item{
      color: #007AFF;
      text-align: center;
      font-size: 20px;
      padding: 18px 16px;
      border-bottom: 0.5px solid rgba(0, 0, 0, 0.3);
      &.primary{
        color: #FF3B30;
      }
      &.action-msg{
        color: rgba(0,0,0,0.5);
        font-size: 13px;
      }
    }
  }

  .el-dialog__wrapper {
    display: flex;
    align-items: center;
  }
  .msg-box-dialog{
    .el-dialog__header, .el-dialog__body {
      text-align: center;
    }
    .el-button{
      min-width: 88px;
    }
  }

  .el-dialog {
    border-radius: 4px;
    max-width: 80%;
    margin-top: 0 !important;

    .el-dialog__header {
      padding: 32px 25px 10px 25px;

      .el-dialog__title {
        font-weight: bold;
        font-size: 16px;
        color: #0D0F15;
      }

      .el-dialog__headerbtn {
        top: 25px;
        right: 25px;

        .el-dialog__close {
          font-size: 16px;
          color: #D5D5D5;
          font-weight: bold;
        }

        .el-dialog__close:hover {
          color: #0067ED;
        }
      }

    }

    .el-dialog__body {
      padding: 0 25px 30px 25px;

      .message {
        font-size: 12px;
        color: #A3AEC7;
        line-height: 16px;
        word-break: break-word;
      }

      .option {
        padding-top: 20px;

        .el-checkbox__input {
          .el-checkbox__inner {
            background: #FFFFFF;
            border-color: #D5D5D5;
          }
        }

        .el-checkbox__input:hover {
          .el-checkbox__inner {
            background: #FFFFFF;
            border-color: #0067ED;
          }
        }

        .el-checkbox__input.is-checked {
          .el-checkbox__inner {
            background-color: #006EFF;
            border-color: #006EFF;;
          }
        }

        .el-checkbox__input.is-checked:hover {
          .el-checkbox__inner {
            background-color: #4C99FF;
            border-color: #4C99FF;;
          }
        }

        .el-checkbox__label {
          font-size: 12px;
          color: #333333;
          white-space: normal;
        }
      }

      .exception {
        text-align: left;
        color: #cccccc;
        font-size: 12px;
      }
    }

    .el-dialog__footer {
      padding: 0 25px 25px 25px;
      text-align: center;

      .el-button {
        padding: 8px 19px;
        font-size: 12px;
        background-color: #FFF;
        border-color: #CFD4E6;
        color: #006EFF;
      }

      .el-button:hover {
        background-color: #ebeef2;
        border-color: #cfd5de;
      }

      .el-button--primary {
        background-color: #006EFF;
        border-color: #006EFF;
        color: #FFFFFF;
      }

      .el-button--secondary {
        background-color: #FFF;
        border-color: #CFD4E6;
        color: #006EFF;
      }

      .el-button--secondary:hover {
        background-color: #ebeef2;
        border-color: #cfd5de;
      }

      .el-button--primary:hover {
        background-color: #338bff;
        border-color: #338bff;
        color: #FFFFFF;
      }
    }
  }
  .icon-copy {
      background-image: url('./assets/icon_copy.svg');
      background-repeat: no-repeat;
      background-size: contain;
      width: 16px;
      height: 10px;
      display: inline-block;
    }
}
</style>
