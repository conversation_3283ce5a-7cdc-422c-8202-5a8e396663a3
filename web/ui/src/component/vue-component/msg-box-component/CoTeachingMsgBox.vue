<template>
  <div class="co-teaching-msg-box-component">
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="570px"
      :close-on-click-modal="false"
      :before-close="handleClose"
      destroy-on-close
    >
      <span
        v-dompurify-html="message"
        class="message"
      />
      <div
        v-for="(option, index) in options"
        :key="index"
        class="option"
      >
        <el-checkbox v-model="option.value">
          {{ option.text }}
        </el-checkbox>
      </div>
      <!-- <div
        v-if="exceptionId && exceptionId !== ''"
        class="exception"
      >
        {{ $t('运维ID:') }}{{ exceptionId }}
      </div> -->
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          v-for="(button, index) in buttons"
          :key="index"
          :type="(buttons.length === 1 || index > 0) ? 'primary': (buttons.length === 3 ? 'secondary' : '')"
          @click="close(buttons.length - index - 1)"
        >
          {{ button }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>


<script>
import i18next from 'i18next';
import BaseComponent from '../../core/BaseComponent';

export default {
  filters: {},
  extends: BaseComponent,

  data() {
    return {
      title: '',
      message: '',
      buttons: [i18next.t('确定')],
      callback: null,
      options: [],
      exceptionId: '',
      dialogVisible: false,
      dlgWinId: -1,
    };
  },

  computed: {},

  watch: {},

  mounted() {

  },

  methods: {
    show(info) {
      this.title = info.title;
      this.message = info.message;
      this.buttons = info.buttons.reverse();
      this.callback = info.callback;
      this.options = info.options;
      this.exceptionId = info.exceptionId;
      this.dialogVisible = true;
      // 注册按键控制窗口
      this.$nextTick(() => {
        const dlgWinName = `dialg-${new Date().getTime()}-${Math.floor(Math.random() * 1024)}`;
        this.dlgWinId = window.tbm.pushWindow(dlgWinName);
        window.tbm.pushTarget('dialog', ['header', 'content'], this.dlgWinId);
        const btnClose = this.$el.querySelector('.el-dialog__headerbtn');
        window.tbm.updateTarget('dialog', [window.tbm.generateNode(btnClose)], 'header', this.dlgWinId);
        const ctxBtns = [];
        Array.from(this.$el.querySelectorAll('.el-button')).forEach(item => ctxBtns.push(window.tbm.generateNode(item)));
        window.tbm.updateTarget('dialog', ctxBtns, 'content', this.dlgWinId);
        window.tbm.activeWindow(this.dlgWinId, 'dialog', 'content');
      });
    },
    close(index) {
      this.dialogVisible = false;
      window.tbm.removeWindow(this.dlgWinId);
      if (this.callback) {
        let optionsChecked = [];
        if (typeof this.options === 'object' && Array.isArray(this.options)) {
          optionsChecked = this.options.map(option => option.value);
        }
        this.callback(index, optionsChecked);
      }
    },
    handleClose(done) {
      done();
      // 强制关闭时返回-1
      this.close(-1);
    },
  },
};
</script>
<style lang="less">
.co-teaching-msg-box-component {
  word-break: normal;

  .el-dialog__wrapper {
    display: flex;
    align-items: center;
    background: rgba(0,0,0,0.40);
    // backdrop-filter: blur(16px);
  }

  .el-dialog {
    max-width: 80%;
    min-height: 330px;
    margin-top: 0 !important;
    background: #FFFFFF;
    border: 1px solid rgba(0,0,0,0.05);
    box-shadow: 0 4px 17px 0 rgba(0,0,0,0.24);
    border-radius: 12px;
    padding: 32px;
    .el-dialog__header {
      padding: 0;
      margin-bottom: 12px;

      .el-dialog__title {
        font-family: PingFangSC-Medium;
        font-weight: 500;
        font-size: 32px;
        color: #000000;
        line-height: 40px;
      }

      .el-dialog__headerbtn {
        top: 25px;
        right: 25px;

        .el-dialog__close {
          font-size: 16px;
          color: #D5D5D5;
          font-weight: bold;
        }

        .el-dialog__close:hover {
          color: #0067ED;
        }
      }

    }

    .el-dialog__body {
      padding: 0;

      .message {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 24px;
        color: #000000;
        line-height: 32px;
        word-break: break-word;
      }

      .option {
        margin-top: 20px;
        .el-checkbox__input {
          line-height: 1.5;
          .el-checkbox__inner {
            background: #FFFFFF;
            width: 24px;
            height: 24px;
            border: 1px solid #000000;
            &:after {
              height: 14px;
              left: 8px;
            }
          }
        }

        .el-checkbox__input:hover {
          .el-checkbox__inner {
            background: #FFFFFF;
            border-color: #0067ED;
          }
        }

        .el-checkbox__input.is-checked {
          .el-checkbox__inner {
            background-color: #006EFF;
            border-color: #006EFF;;
          }
        }

        .el-checkbox__input.is-checked:hover {
          .el-checkbox__inner {
            background-color: #4C99FF;
            border-color: #4C99FF;;
          }
        }

        .el-checkbox__label {
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 20px;
          color: #000000;
          line-height: 28px;
        }
      }

      .exception {
        text-align: left;
        padding-top: 20px;
        color: #cccccc;
        font-size: 12px;
      }
    }

    .el-dialog__footer {
      position: absolute;
      bottom: 32px;
      right: 32px;
      padding: 0px;
      .el-button {
        min-width: 128px;
        height: 56px;
        padding: 8px 19px;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        font-size: 24px;
        background: #fff;
        border-color: #D5D5D5;
        color: #333333;
      }

      .el-button:hover {
        border-color: #0067ED;
        color: #0067ED;
      }

      .el-button--primary {
        background-color: #006EFF;
        border-color: #006EFF;
        color: #FFFFFF;
      }

      .el-button--primary:hover {
        background-color: #4D9AFF;
        border-color: #4D9AFF;
        color: #FFFFFF;
      }

      .el-button--secondary {
        background-color: #FA6400;
        border-color: #FA6400;
        color: #FFFFFF;
        opacity: 1;
      }

      .el-button--secondary:hover {
        color: #FFFFFF;
        background-color: #FA6400;
        border-color: #FA6400;
        opacity: 0.7;
      }

      .el-button--kickout {
        background-color: #fff;
        border-color: #FA6400;;
        color: #FA6400;
      }

      .el-button--kickout:hover {
        background-color: #FA6400;
        border-color: #FA6400;
        color: #FFFFFF;
      }
    }
  }
}
</style>
