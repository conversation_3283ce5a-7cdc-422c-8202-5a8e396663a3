<template>
  <div
    class="rand-choose-dialog"
  >
    <div class="rand-choose-dialog-hd">
      <div>{{ $t('随机选人') }}</div>
      <i
        v-if="isTeacher || isAssistant"
        class="el-icon-close"
        @click="onClose"
      />
    </div>
    <div
      class="random-choose-tool"
      :class="{ 'random-choose-tool__student' : (!isTeacher && !isAssistant) }"
    >
      <span>{{ $t('选人范围') }}</span>
      <el-radio
        v-model="studentType"
        :disabled="currentStatus === 2"
        :label="1"
      >
        {{ $t('全部学生') }}({{ studentCount }})
      </el-radio>
      <el-radio
        v-model="studentType"
        :disabled="currentStatus === 2"
        :label="2"
      >
        {{ $t('在线学生') }}({{ onlineStudents.length }})
      </el-radio>
    </div>
    <div
      v-if="!isAssistant && !isTeacher && currentStatus === 1"
      class="slot-machine waiting"
    >
      <div>{{ $t('暂未开始') }}</div>
      <div>{{ $t('随机选人即将开始，请稍等') }}</div>
    </div>
    <div
      v-else
      class="slot-machine"
    >
      <div
        v-if="studentPool.length > 0"
        ref="scroller"
        class="student-wrapper"
      >
        <div
          :style="{transform: `translateY(${translateY}px)`}"
        >
          <div
            v-for="(item, index) in renderStudents"
            :key="index"
            class="student-item"
          >
            {{ item.userName }}
            {{ item.currentStatus === 2 ? `(${$t('离线')})` : '' }}
          </div>
        </div>
      </div>
      <div
        v-if="studentPool.length > 0"
        class="slot-machine-label"
      >
        <div
          v-show="currentStatus === 2 && showArrow"
          class="slot-machine-label-left"
        />
        <div
          v-show="currentStatus === 2 && showArrow"
          class="slot-machine-label-right"
        />
      </div>
      <div
        v-else
        class="slot-machine-label empty"
      >
        {{ $t('暂无学生') }}
      </div>
    </div>
    <div
      v-if="isTeacher || isAssistant"
      class="random-choose-ft"
    >
      <el-button
        v-if="currentStatus === 1"
        type="primary"
        round
        :disabled="studentPool.length === 0 || currentStatus !== remoteStatus"
        @click="startChoose"
      >
        {{ $t('开始选人') }}
      </el-button>
      <div
        v-else
        class="button-wrapper"
      >
        <el-button
          type="primary"
          round
          :disabled="!chosedStudent || chosedStudent.currentStatus === 2 || !showArrow"
          @click="onStage"
        >
          {{ $t('上台') }}
        </el-button>
        <el-button
          type="default"
          round
          :disabled="currentStatus !== remoteStatus || !showArrow"
          @click="onStart"
        >
          {{ $t('重启选人') }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import BaseComponent from '../../core/BaseComponent';
import Lodash from 'lodash';
import * as TWEEN from '@tweenjs/tween.js';

const getTaskString = (task) => {
  if (!task) {
    return '';
  }
  return `${task.status}-${task.userId}`;
};

export default {
  name: 'RandomChooseTool',
  extends: BaseComponent,
  data() {
    return {
      // 时钟状态
      ChooseStatus: {
        Start: 1,
        Finish: 2,
      },
      translateY: 0,
      chosedUserId: '',
      showArrow: false,
      memberList: [],
      studentType: 1, // 全部学生
      currentStatus: 0, // 当前状态
      remoteStatus: 0,
      taskId: 'random_choose', // 任务 ID
      // 任务协议
      taskContent: {
        status: 1,  // 当前状态
        userId: '', // 被选中的 userId
        trigger: '', // 老师、助教 id
      },
      hasJoinClass: false, // 组件是否收到进房通知
      isTeacher: false,
      isAssistant: false,
      memberJoined: false,
    };
  },
  computed: {
    amIStudent() {
      return TCIC.SDK.instance.isStudent();
    },
    students() {
      return this.memberList.filter(item => TCIC.SDK.instance.isStudent(item.userId));
    },
    chosedIndex() {
      const index = this.studentPool.findIndex(item => item.userId === this.chosedUserId);
      return index;
    },
    onlineStudents() {
      return this.students.filter(stu => stu.currentStatus === 1);
    },
    chosedStudent() {
      return this.studentPool[this.chosedIndex] || {};
    },
    studentPool() {
      return this.studentType === 1 ? this.students : this.onlineStudents;
    },
    renderStudents() {
      if (this.studentPool.length < 2) {
        return (new Array(20)).fill(this.studentPool[0]);
      }
      if (this.studentPool.length < 20) {
        return [
          ...this.studentPool.slice(-2),
          ...new Array(Math.ceil(20 / this.studentPool.length)).fill(this.studentPool)
            .flat(),
          ...this.studentPool.slice(0, 3),
        ];
      }
      return [...this.studentPool.slice(-2), ...this.studentPool, ...this.studentPool.slice(0, 2)];
    },
    studentCount() {
      return this.students.length;
    },
  },
  watch: {
    studentType() {
      this.onStart();
    },
    // 滚动动画
    chosedIndex(val) {
      if (val === -1) {
        this.translateY = 0;
        return;
      }
      const RowHeight = 38;
      const maxTranslateY = (this.renderStudents.length - 5) * RowHeight;
      // 先转 1 圈再停到选中的地方
      const round = 1;
      const targetTranslateY = this.chosedIndex * RowHeight + round * maxTranslateY;
      if (this.memberJoined) {
        this.memberJoined = false;
        this.translateY = -targetTranslateY;
        return;
      }
      const tween = new TWEEN.Tween({ x: 0, y: 0 })
        .to({ y: -targetTranslateY }, 3000)
        .easing(TWEEN.Easing.Cubic.Out)
        .onUpdate((object) => {
          this.translateY = object.y % maxTranslateY;
        })
        .onComplete(() => {
          this.showArrow = true;
        })
        .start();
      function animate(time) {
        tween.update(time);
        requestAnimationFrame(animate);
      }
      requestAnimationFrame(animate);
    },
  },
  mounted() {
    if (this.amIStudent) {
      return;
    }
    this.resetData();
    this.isAssistant = TCIC.SDK.instance.isAssistant();
    this.isTeacher = TCIC.SDK.instance.isTeacher();
    this.makeSureClassJoined(this.onJoinClass);
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Task_Updated, this.onTaskUpdate);
    this.getMemberList().then(() => {
      setTimeout(() => {
        this.toggleComponentDrag(true, '.rand-choose-dialog-hd');
      }, 1000);
    });
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Member_Join, (member) => {
      console.warn('[RandomChooseTool] new member:', member);
      if (this.currentStatus === this.ChooseStatus.Finish) {
        this.memberJoined = true;
      }
      this.getMemberList(member);
    });
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Member_Exit, (userId) => {
      console.warn('[RandomChooseTool] exit member:', userId);
      const exitedMem = this.memberList.find(mem => mem.userId === userId);
      exitedMem && (exitedMem.currentStatus = 2);
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Member_List_Offline_Member_Count, (count) => {
      this.offlineMemberCount = count;
    });
    // 更新最初的状态
    TCIC.SDK.instance.getTasks(0).then((result) => {
      result.tasks.forEach((taskInfo) => {
        this.onTaskUpdate(taskInfo, true);
      });
    });
  },
  methods: {
    onStage() {
      // 上台
      this.$EventBus.$emit('member-action', this.chosedStudent, TCIC.TMemberActionType.Stage_Up);
    },
    getMemberList(userId) {
      console.log('getMemberByUserId:', userId);
    //   if (userId) {
    //     const tcic = TCIC.SDK.instance.getInstances();
    //     console.log('getMemberByUserId: tcic', tcic);
    //     tcic.member.getMemberByUserId(userId);
    //   } else {
    //   return TCIC.SDK.instance.getClassMemberList({
    //     page: 1,
    //     limit: 100, // 最大限制 1000;
    //     type: TCIC.TMemberType.All,
    //     keyword: '',
    //   }).then((res) => {
    //     this.memberList = res.members;
    //   });
    // }
    return TCIC.SDK.instance.getClassMemberList({
        page: 1,
        limit: 100, // 最大限制 1000;
        type: TCIC.TMemberType.All,
        keyword: '',
      }).then((res) => {
        this.memberList = res.members;
      });
    },
    startChoose() {
      if (this.studentPool.length === 1) {
        this.chosedUserId = this.studentPool[0].userId;
        this.currentStatus = this.ChooseStatus.Finish;
        this.updateTask(this.ChooseStatus.Finish);
        return;
      }

      const chosedIndex = Lodash.random(0, this.studentPool.length - 1);
      console.log('[RandomChooseTool] start choose', chosedIndex);
      if (chosedIndex === 0) {
        this.chosedUserId = this.studentPool[0]?.userId;
        this.currentStatus = this.ChooseStatus.Finish;
        console.warn(`[RandomChooseTool] not updated ${chosedIndex}`);
        this.updateTask(this.ChooseStatus.Finish);
        return;
      }
      this.chosedUserId = this.studentPool[chosedIndex]?.userId;
      this.currentStatus = this.ChooseStatus.Finish;
      this.updateTask(this.ChooseStatus.Finish);
    },
    onJoinClass() {
      this.hasJoinClass = true;
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      this.getMemberList();
      console.log('[RandomChooseTool] join class');
    },
    /*
     * 开始
     */
    onStart() {
      this.currentStatus = this.ChooseStatus.Start;
      this.showArrow = false;
      this.chosedUserId = '';
      this.updateTask();
    },
    resetData() {
      this.currentStatus = this.ChooseStatus.Start;
    },

    /*
     * 关闭
     */
    onClose() {
      this.stopTask();
      this.resetData();
      this.hide();
    },

    onTaskUpdate(taskInfo, isInit = false) {
      if (taskInfo.taskId !== this.taskId || TCIC.SDK.instance.isSupervisor()) {
        return;
      }
      console.log('[RandomChooseTool] task update:', taskInfo.content, isInit);
      if (taskInfo.status === 0) {
        this.resetData();
        this.hide();
        return;
      }
      this.show();
      const contentJson = JSON.parse(taskInfo.content);
      this.remoteStatus = contentJson.status;
      if ((JSON.stringify(this.taskContent) !== taskInfo.content
        && taskInfo.trigger !== TCIC.SDK.instance.getUserId())
        || isInit
      ) {
        switch (contentJson.status) {
          case this.ChooseStatus.Start: {
            this.currentStatus = this.ChooseStatus.Start;
            this.chosedUserId = '';
            break;
          }
          case this.ChooseStatus.Finish: {
            this.currentStatus = this.ChooseStatus.Finish;
            this.$nextTick(() => {
              const index = this.students.findIndex(item => item.userId === contentJson.userId);
              this.chosedUserId = contentJson.userId;
            });
            break;
          }
          default:
            break;
        }
        this.taskContent = {
          status: this.currentStatus,
          trigger: TCIC.SDK.instance.getUserId(),
        };
      }
    },
    /*
     * 更新任务
     */
    updateTask(status) {
      if (!this.isTeacher && !this.isAssistant) {
        // 学生没有权限更新计时器任务
        return;
      }
      this.taskContent = {
        status: status || this.currentStatus,
        userId: this.chosedUserId,
        trigger: TCIC.SDK.instance.getUserId(),
      };
      console.log(`[RandomChooseTool] updateTask, localTask ${getTaskString(this.taskContent)}`);
      TCIC.SDK.instance.updateTask(this.taskId, JSON.stringify(this.taskContent))
        .then((task) => {
        })
        .catch((error) => {
          window.showToast(error.errorMsg);
          TCIC.SDK.instance.reportEvent('update_random_choose_tool', error, -1);
        });
    },

    // 停止任务
    stopTask() {
      if (!this.isTeacher && !this.isAssistant) {
        // 学生没有权限更新计时器任务
        return;
      }
      TCIC.SDK.instance.stopTask(this.taskId).then((task) => {
      })
        .catch((error) => {
        });
    },
  },
};
</script>

<style lang="less">

.dark .rand-choose-dialog{
  background-color: rgba(29, 32, 41);
  color: #CFD4E5;
  width: 100%;
  //height: 392px;
  padding: 0 20px 20px;
  border-radius: 10px;
  &-hd{
    display: flex;
    cursor: move;
    align-items: center;
    border-bottom: 0.5px solid rgba(238, 238, 238, 0.2);
    padding: 20px 0;
    font-size: 16px;
    div:first-child{
      font-size: 16px;
      flex: 1;
    }
    i{
      cursor: pointer;
    }
  }
  .slot-machine{
    @rowHeight: 38px;
    @rows: 5;
    width: 100%;
    font-size: 14px;
    margin: 20px 0;
    border-radius: 6px;
    height: (@rowHeight * @rows);
    background-color: rgba(255,255,255,0.1);
    color: rgba(163, 174, 199, 1);
    position: relative;
    &.waiting{
      text-align: center;
      background: none;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      >div:first-child{
        font-size: 22px;
        margin-bottom: 8px;
      }
      >div:last-child{
        font-size: 16px;
      }
    }
    >.student-wrapper{
      height: 100%;
      padding: 0px 40px;
      scroll-behavior: smooth;
      overflow: hidden;
      >div{
        //transition: transform 0.5s ease;
        will-change: transform;
      }
    }
    .slot-machine-label{
      left: 0;
      text-align: center;
      color: #fff;
      top: (@rowHeight * ((@rows - 1) / 2));
      display: flex;
      padding: 0 10px;
      background: linear-gradient(270deg, rgba(35, 108, 250, 0.00) 0%, rgba(35, 108, 250, 0.30) 50.88%, rgba(35, 108, 250, 0.00) 100%);
      position: absolute;
      height: 38px;
      width: 100%;
      align-items: center;
      justify-content: space-between;
      &.empty{
        background: none;
        text-align: center;
        font-size: 20px;
        justify-content: center;
      }
      &-left,&-right{
        background-image: url('./assets/target.svg');
        width: 20px;
        height: 20px;
        background-position: bottom;
        transform-origin: center;
        background-size: cover;
      }
      &-left{
        transform: rotate(180deg);
      }
    }
    .student-item{
      text-align: center;
      padding: 8px 0;
      height: 38px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .random-choose-ft{
    display: flex;
    justify-content: space-around;
  }
}

.random-choose-tool {
  width: 100%;
  line-height: 1;
  padding-top: 20px;
  color: #CFD4E5;
  font-size: 14px;
  >span:first-child{
    margin-right: 10px;
  }
  .el-radio__input{
    display: inline-block;
    +.el-radio__label{
      padding-left: 5px;
    }
  }
  &.random-choose-tool__student {
    position: relative;
    display: none;
  }
}
</style>
