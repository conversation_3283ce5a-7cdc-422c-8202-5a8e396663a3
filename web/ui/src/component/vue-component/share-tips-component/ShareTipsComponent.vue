<template>
  <div class="share-tips-component">
    <div class="share-tips__main">
      <div class="share-tips__header">
        <i class="share-tips__icon" />
        <span class="share-tips__title">{{ $t('屏幕共享中') }}</span>
      </div>
      <div class="share-tips__content">
        {{ $t('可切换至其他APP来共享屏幕') }}
      </div>
    </div>
    <div
      class="share-tips__control"
      @click="stopScreenShare"
    >
      <i class="share-tips__button" />
      <span class="share-tips__item">{{ $t('结束共享') }}</span>
    </div>
  </div>
</template>

<script>

import BaseComponent from '@core/BaseComponent';

export default {
  name: 'ShareTipsComponent',
  extends: BaseComponent,
  props: {},
  data() {
    return {
    };
  },

  mounted() {
    this.makeSureClassJoined(this.onJoinClass);
  },
  beforeDestroy() {
  },
  methods: {
    stopScreenShare() {
      if (TCIC.SDK.instance.isElectron()) {
        TCIC.SDK.instance.setFeatureAvailable('ScreenShareAdvanceMode', true);
      }
      const isTeacher = TCIC.SDK.instance.isTeacher();
      TCIC.SDK.instance.reportLog('stopScreenShare', `[ShareTipsComponent] stopScreenShare, isTeacher ${isTeacher}`);
      if (isTeacher) {
        TCIC.SDK.instance.getComponent('teacher-component')
          .getVueInstance()
          .disableScreenShare();
      } else {
        const selfId = TCIC.SDK.instance.getUserId();
        TCIC.SDK.instance.getComponent('student-component', selfId)
          .getVueInstance()
          .disableScreenShare();
      }
    },
  },
};
</script>

<style lang="less">
.share-tips-component {
  position: relative;
  width: 282px;
  height: 64px;
  margin: auto;
  background: #1C2131;
  border-radius: 2px;
  border: 1px solid #006EFF;
  display: flex;
  align-items: center;
  justify-content: space-around;

  .share-tips__main {
    flex: 1;

    .share-tips__header {
      .share-tips__icon {
        margin: 10px 12px 10px 12px;
        width: 16px;
        height: 16px;
        vertical-align: middle;
        content: url('./assets/share_tips.png')
      }

      .share-tips__title {
        vertical-align: middle;
        font-size: 14px;
        font-weight: 500;
        color: #FFFFFF;
        line-height: 20px;
      }
    }

    .share-tips__content {
      margin-left: 40px;
      font-size: 12px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 17px;
    }
  }

  .share-tips__control {
    // width: 48px;
    height: 100%;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-evenly;

    .share-tips__button {
      margin: 6px 6px 0 6px;
      width: 36px;
      height: 36px;
      content: url('./assets/share_stop.png');
    }

    .share-tips__item {
      display: block;
      font-size: 12px;
      transform: scale(0.67);   /** 间隔设置字体大小为8px */
      color: #FFFFFF;
      font-weight: 400;
      line-height: 11px;
    }
  }
}
</style>
