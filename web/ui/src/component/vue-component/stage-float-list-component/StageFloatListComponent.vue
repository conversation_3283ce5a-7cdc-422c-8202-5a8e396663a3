<template>
  <!-- 连麦列表 -->
  <!-- v-if="enableStage && stageMemberList.length > 0" -->
  <div
    v-if="!isDeviceDetect"
    class="stage-list"
  >
    <div
      v-for="(item, index) in stageMemberList"
      ref="stageItem"
      :key="index"
      class="stage-list-item board-back-bg"
    >
      <div
        v-if="item.remain > -1"
        class="stage-remain"
      >
        <div class="filter-blur-bg" />
        <label>
          <span
            :title="item.param.nickname"
            class="remain-name"
          >{{ item.param.nickname }}</span>
          {{ $t('申请上台') }}&nbsp;({{ item.remain }}s)&nbsp;
        </label>
        <div class="stage-list-btn">
          <el-button
            type="primary"
            @click="approve(item)"
          >
            {{ $t('上台') }}&nbsp;1
          </el-button>
          <el-button
            type="border"
            @click="refuse(item)"
          >
            {{ $t('拒绝') }}
          </el-button>
          <i
            class="ic-close"
            @click="closeStage(item)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18next from 'i18next';
import BaseComponent from '@/component/core/BaseComponent';
import Constant from '@/util/Constant';
import Util from '@/util/Util';


export default {
  extends: BaseComponent,
  data() {
    return {
      enableStage: false,   // 连麦消息悬浮列表
      stageMemberList: [],  // 连麦人员, TStageCommand列表
      rawStageList: [],      // 原始数据
      stageHideMemberList: [], // 已隐藏的连麦人员，防止十秒内有人申请，又弹出来了
      stageTimer: 0,        // 刷新连麦相关的UI
      maxShowCount: 4,      // 最大显示人数
      isDeviceDetect: TCIC.SDK.instance.getState(Constant.TStateDeviceDetect),
    };
  },
  mounted() {
    this.addLifecycleTCICStateListener(TCIC.TMainState.Enable_Stage, (enable) => {
      this.enableStage = enable;
      window.clearInterval(this.stageTimer);
      const interval = Util.getProperIntervalByCPUUsage(250, 1000);
      if (this.enableStage) {
        // TODO : 主动去拉取下getAllCommand列表
        this.stageTimer = window.setInterval(this.refreshStage, interval);
        TCIC.SDK.instance.getAllCommand(TCIC.TCommandID.Stage);
      } else {
        this.stageTimer = 0;
        this.stageMemberList = [];
      }
    });

    this.addLifecycleTCICStateListener(TCIC.TMainState.Ask_Stage_List, (list) => {
      this.rawStageList = list;
      this.mergeList(list);
    });
    this.addLifecycleTCICStateListener(Constant.TStateDeviceDetect, this.deviceDetectHandler);
  },
  methods: {
    deviceDetectHandler(state) {
      this.isDeviceDetect = state;
    },
    mergeList(list) {
      window.clearInterval(this.stageTimer);
      const interval = Util.getProperIntervalByCPUUsage(250, 1000);
      this.mergeStageList(list);
      if (this.stageMemberList.length > 0 || this.stageHideMemberList.length > 0) {
        this.stageTimer = window.setInterval(this.refreshStage, interval);
      }
    },
    mergeStageList(newStageList) {
      // console.log(`====>>>> : mergeStageList : ${JSON.stringify(newStageList)}`);
      // 转成UI需要的信息
      const array = [];
      {
        if (newStageList.cmds instanceof Array) {
          const svrTime = TCIC.SDK.instance.getServerTimestamp();
          newStageList.cmds.forEach((item) => {
            const hideItem = this.stageHideMemberList.find(hideUser => hideUser.userId === item.userId);
            if (hideItem) {
              // 说明刚老师点击关闭过，不处理
            } else {
              const obj = item.serialize();
              const remainTime = Math.ceil((obj.timeout * 1000 - svrTime) / 1000);
              obj.remain = remainTime <= 10 ? remainTime : 10;
              array.push(obj);
            }
          });
        }
      }

      // 开始合并
      if (array.length > this.maxShowCount) {
        this.stageMemberList = array.slice(0, this.maxShowCount);
      } else {
        this.stageMemberList = array;
      }
      this.refreshStage();
    },
    refreshStage() {
      const svrTime = TCIC.SDK.instance.getServerTimestamp();
      // 清理上台列表中过期的
      for (let index = 0; index < this.stageMemberList.length; index++) {
        const item = this.stageMemberList[index];
        const remainTime = Math.ceil((item.timeout * 1000 - svrTime) / 1000);
        item.remain = remainTime <= 10 ? remainTime : 10;
        if (item.remain < 0) {
          // 本地主动删掉
          this.stageMemberList.splice(index, 1);
          index -= 1;
          // this.refuse(item);
        }
      }

      // 清理隐藏的列表中过期的
      for (let index = 0; index < this.stageHideMemberList.length; index++) {
        const item = this.stageHideMemberList[index];
        item.remain = Math.round((item.timeout * 1000 - svrTime) / 1000);
        if (item.remain < 0) {
          // 本地主动删掉
          this.stageHideMemberList.splice(index, 1);
          index -= 1;
          // this.refuse(item);
        }
      }
    },
    closeStage(item) {
      console.log(`===>>> : onCloseStage : ${item.userId}`);
      for (let index = 0; index < this.stageMemberList.length; index++) {
        const mem = this.stageMemberList[index];
        if (mem.userId === item.userId) {
          // this.refuse(item);
          this.stageHideMemberList.push(item);
          this.stageMemberList.splice(index, 1);
          break;
        }
      }
      console.log(`===>>> : closeStage : 开始merge : ${this.rawStageList}`);
      this.mergeStageList(this.rawStageList);
    },
    // 拒绝连麦
    refuse(item) {
      console.log(`====>>>> : refuse : ${JSON.stringify(item)}`);
      const req = new TCIC.TCommandReq();
      req.cmd = TCIC.TCommandID.Stage;
      req.userId = item.userId;
      req.classId = TCIC.SDK.instance.getClassInfo().classId;
      req.type = TCIC.TCommandStatus.Reject;
      TCIC.SDK.instance.sendCommand(req)
        .catch((error) => {
          // TODO: 从本地删除，并刷新
          window.showToast(error.errorMsg, 'error');
          console.log(`===>>> : refuse : ${item.userId}`);
        });
    },
    // 同意连麦
    approve(item) {
      const stageCount = TCIC.SDK.instance.getState(TCIC.TMainState.Stage_Count, 0);
      if (stageCount >= 4) {  // 上台人数已满后不允许上台
        window.showToast(i18next.t('上台人数已达上限'));
        return;
      }
      console.log(`====>>>> : approve : ${JSON.stringify(item)}`);
      const req = new TCIC.TCommandReq();
      req.cmd = TCIC.TCommandID.Stage;
      req.userId = item.userId;
      const classInfo = TCIC.SDK.instance.getClassInfo();
      req.classId = classInfo.classId;
      req.type = TCIC.TCommandStatus.Approve;
      TCIC.SDK.instance.sendCommand(req)
        .then(() => {
          // 延时一秒等学生那边进TRTC房间
          setTimeout(() => {
            const param = {
              classId: classInfo.classId,
              classType: classInfo.classType,
              userId: item.userId,
              actionType: TCIC.TMemberActionType.Stage_Up,
            };
            TCIC.SDK.instance.memberAction(param)
              .then(() => {
                TCIC.SDK.instance.promiseState(TCIC.TMainState.Wait_Stage_UserId, item.userId).then(() => {
                  window.showToast(i18next.t('上台成功'));
                });
              })
              .catch((err) => {
                window.showToast(err.errorMsg, 'error');
              });
          }, 1000);
        })
        .catch((error) => {
          // TODO: 从本地删除，并刷新
          window.showToast(error.errorMsg, 'error');
          console.log(`===>>> : approve : ${item.userId}`);
        });
    },
  },
};
</script>

<style lang="less">
@import 'StageFloadListPC.less';
</style>
