<template>
  <div
    v-if="((isStudent && (enableStage || isOnStage)) || isAssistant) && isLiveClass "
    class="stage-access-component"
  >
    <!-- 连麦入口 -->
    <div class="stage-access-entrance">
      <!-- 开始连麦 -->
      <el-tooltip
        v-show="!isOnStage && (stageStatus !== 0 && stageStatus !== 2)"
        class="item"
        effect="dark"
        :content="toolContent"
        placement="top"
      >
        <div
          class="stage-access"
          @click="onAskStage"
        >
          <i />
        </div>
      </el-tooltip>

      <!-- 连麦申请中 -->
      <el-tooltip
        v-show="!isOnStage && stageStatus === 0"
        :value="false"
        class="item"
        effect="dark"
        disabled
      >
        <div
          class="stage-access dynamic"
        >
          <i />
          <div class="s-stage-loading">
            <span />
            <span />
            <span />
          </div>
        </div>
      </el-tooltip>

      <!-- 连麦连接中 -->
      <el-tooltip
        v-show="!isOnStage && stageStatus === 2"
        :value="false"
        class="item"
        effect="dark"
        disabled
      >
        <div
          class="stage-access dynamic disabled"
        >
          <i />
          <div class="s-stage-loading">
            <span />
            <span />
            <span />
          </div>
        </div>
      </el-tooltip>

      <!-- 结束连麦 -->
      <el-tooltip
        v-show="isOnStage"
        class="item"
        effect="dark"
        :content="toolContent"
        placement="top"
      >
        <div
          class="stage-access hang"
          @click="onHangUpStage"
        >
          <i />
        </div>
      </el-tooltip>
    </div>
  </div>
</template>
<script>
import i18next from 'i18next';
import BaseComponent from '@/component/core/BaseComponent';

export default {
  name: 'StageAccessComponent',
  components: {
  },
  extends: BaseComponent,
  data() {
    return {
      isStudent: false, // 是否学生
      isAssistant: false,  // 是否助教
      isLiveClass: false,  // 是否是直播课
      enableStage: false, // 是否允许上台
      stageStatus: TCIC.TCommandStatus.None, // 是否正在请求连麦
      isOnStage: false, // 权限列表中stage
    };
  },
  computed: {
    toolContent() {
      if (!this.isOnStage && (this.stageStatus !== 0 && this.stageStatus !== 2)) {
        return i18next.t('上台');
      }
      if (this.isOnStage) {
        return i18next.t('下台');
      }
      return i18next.t('上台');
    },
  },
  mounted() {
    // this.show(); // 展示本组件
    this.makeSureClassJoined(() => {
      this.isStudent = TCIC.SDK.instance.isStudent();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      this.isLiveClass = TCIC.SDK.instance.isLiveClass();
      if (this.isStudent) {
        // 是否开启连麦监听
        this.enableStage = TCIC.SDK.instance.getState(TCIC.TMainState.Enable_Stage, false);
        this.addLifecycleTCICStateListener(TCIC.TMainState.Enable_Stage, (enable) => {
          console.log(`===>>> stageAccess: Intro : Enable_Stage : ${enable}, ${this.enableStage}`);
          this.enableStage = enable;
          if (enable || this.isOnStage) {
            this.show();
          } else {
            this.hide();
          }
          if (!this.enableStage) {
            TCIC.SDK.instance.setCommandStatus(TCIC.TCommandID.Stage, TCIC.TMainState.Ask_Stage_Status, TCIC.TCommandStatus.None);
          }
        });
        // 上台相关的状态监听
        this.stageStatus = TCIC.SDK.instance.getState(TCIC.TMainState.Ask_Stage_Status, TCIC.TCommandStatus.None);
        this.addLifecycleTCICStateListener(TCIC.TMainState.Ask_Stage_Status, (status) => {
          if (this.stageStatus === status) {
            // 状态相同。先忽略
            return;
          }
          this.stageStatus = status;
          switch (this.stageStatus) {
            case TCIC.TCommandStatus.Create: {
              window.showToast(i18next.t('正在上台'));
              break;
            }
            case TCIC.TCommandStatus.Cancel: {
              window.showToast(i18next.t('上台超时'));
              window.setTimeout(() => {
                TCIC.SDK.instance.setCommandStatus(TCIC.TCommandID.Stage, TCIC.TMainState.Ask_Stage_Status, TCIC.TCommandStatus.None);
              }, 3000);
              break;
            }
            case TCIC.TCommandStatus.Reject: {
              window.showToast(i18next.t('{{arg_0}}拒绝了你的上台请求'));
              window.setTimeout(() => {
                TCIC.SDK.instance.setCommandStatus(TCIC.TCommandID.Stage, TCIC.TMainState.Ask_Stage_Status, TCIC.TCommandStatus.None);
              }, 3000);
              break;
            }
            case TCIC.TCommandStatus.Approve: {
              TCIC.SDK.instance.approveToStage({}).then(() => {
                const isTeacherInviteMe = TCIC.SDK.instance.getState(TCIC.TMainState.Invite_Stage_Status, false);
                if (!isTeacherInviteMe) {
                  window.showToast(i18next.t('{{arg_0}}同意了你的上台请求', { arg_0: this.roleInfo.teacher }));
                }
                TCIC.SDK.instance.updateComponent('stage-video-list-component', { display: 'block' }).then();
              })
                .catch(() => {
                  window.showToast(i18next.t('上台遇到问题'));
                  TCIC.SDK.instance.hangupToStage();
                });
              break;
            }
          }
          console.log(`===>>> : Ask_Stage_Status : ${status}`);
        });
        this.isOnStage = TCIC.SDK.instance.getState(TCIC.TMainState.Stage_Status, false);
        this.addLifecycleTCICStateListener(TCIC.TMainState.Stage_Status, (status) => {
          this.isOnStage = status;
          if (this.enableStage || this.isOnStage) {
            this.show();
          } else {
            this.hide();
          }
        });
      } else if (this.isAssistant) {
        // 上台相关的状态监听
        this.stageStatus = TCIC.SDK.instance.getState(TCIC.TMainState.Ask_Stage_Status, TCIC.TCommandStatus.None);
        this.addLifecycleTCICStateListener(TCIC.TMainState.Ask_Stage_Status, (status) => {
          if (this.stageStatus === status) {
            // 状态相同。先忽略
            return;
          }
          this.stageStatus = status;
          if (TCIC.TCommandStatus.Approve === this.stageStatus) TCIC.SDK.instance.approveToStage({});
        });

        this.isOnStage = TCIC.SDK.instance.getState(TCIC.TMainState.Stage_Status, false);
        this.addLifecycleTCICStateListener(TCIC.TMainState.Stage_Status, (status) => {
          this.isOnStage = status;
        });
        this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, (status) => {
          if (status === TCIC.TClassStatus.Already_Start) {
            this.show();
          }
          this.isClassStarted = (status !== TCIC.TClassStatus.Not_Start);
        });
      }
    });
  },
  methods: {
    onAskStage() {
      if (this.isStudent) {
        // 学生操作
        if (this.stageStatus === TCIC.TCommandStatus.None) {
          TCIC.SDK.instance.getUserInfo(TCIC.SDK.instance.getUserId())
            .then((userInfo) => {
              const req = new TCIC.TCommandReq();
              req.cmd = TCIC.TCommandID.Stage;
              // req.userId = item.userId; // 请求连麦不用填userId
              req.classId = TCIC.SDK.instance.getClassInfo().classId;
              req.type = TCIC.TCommandStatus.Create;

              // 自定义参数
              const customData = new TCIC.TStageCommandParam();
              customData.nickname = userInfo.nickname;
              customData.device = TCIC.SDK.instance.getDeviceType();
              req.param = customData;
              TCIC.SDK.instance.sendCommand(req).then((result) => {
              // this.isAskStaging = true;
                console.log(`===>>> : ${userInfo.userId} :开始请求上台`);
              })
                .catch((error) => {
                // TODO: 从本地删除，并刷新
                // this.isAskStaging = false;
                  window.showToast(error.errorMsg, 'error');
                  console.log(`===>>> : askStage : ${userInfo.userId}`);
                });
            });
        }
      } else if (this.isAssistant) {
        TCIC.SDK.instance.approveToStage({}).then(() => {
          const classInfo = TCIC.SDK.instance.getClassInfo();
          const param = {
            classId: classInfo.classId,
            classType: classInfo.classType,
            userId: TCIC.SDK.instance.getUserId(),
            actionType: TCIC.TMemberActionType.Stage_Up,
          };
          TCIC.SDK.instance.memberAction(param)
            .catch((err) => {
              window.showToast(err.errorMsg, 'error');
            });
        });
      }
    },
    // onCancelStage() {
    //   const req = new TCIC.TCommandReq();
    //   req.cmd = TCIC.TCommandID.Stage;
    //   // req.userId = item.userId; // 请求连麦不用填userId
    //   req.classId = TCIC.SDK.instance.getClassInfo().classId;
    //   req.type = TCIC.TCommandStatus.Cancel;
    //   // 取消不用发自定义参数
    //   TCIC.SDK.instance.sendCommand(req).then((result) => {
    //     // this.isAskStaging = false;
    //     console.log('===>>> : 主动取消上台');
    //     window.showToast(i18next.t('已取消上台'));
    //   })
    //     .catch((error) => {
    //       // TODO: 从本地删除，并刷新
    //       // this.isAskStaging = false;
    //       console.log('===>>> : 主动取消上台');
    //     });
    // },
    onHangUpStage() {
      if (this.isStudent || this.isAssistant) {
        // 学生操作
        console.log('===>>> : 学生挂断 : trtc关麦克风/摄像头');
        // TODO : 调用下台接口
        TCIC.SDK.instance.hangupToStage({}).then(() => {
          TCIC.SDK.instance.setCommandStatus(TCIC.TCommandID.Stage, TCIC.TMainState.Ask_Stage_Status, TCIC.TCommandStatus.None);
        });
      }
    },
  },
};
</script>
<style lang="less">
.stage-access-component {
  pointer-events: none;
  // 提示
  .stage-access-tips {
    position: absolute;
    top: 204px;
    left: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 62px;
    padding: 0 30px;
    transform: translateX(-50%);
    label {
      position: relative;
      color: #fff;
      font-size: 20px;
      z-index: 9;
      letter-spacing: 1px;
      i {
        position: relative;
        margin-right: 10px;
        color: #13A449;
        font-size: 24px;
      }
    }
  }

  // 连麦入口
  .stage-access-entrance {
    position: absolute;
    right: 44px;
    bottom: 56px;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 80px;
    pointer-events: auto;
    .stage-access {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 48px;
      background: linear-gradient(130deg, #50E083 0%, #27BE4C 100%);
      box-shadow: 0 4px 12px 0 rgba(56, 204, 98, 0.32);
      border-radius: 50%;
      cursor: pointer;
      &.disabled {
        opacity: .4;
      }
      &.hang {
        background: linear-gradient(134deg, #FF824B 0%, #FF5910 100%);
        box-shadow: 0 4px 12px 0 rgba(250, 100, 0, 0.32);
      }
      &.dynamic {
        flex-direction: column;
        i {
          background-size: 70%;
          background-position: center 4px;
        }
      }
      i {
        display: flex;
        width: 100%;
        height: 100%;
        background: url("./assets/ic_stage.svg") no-repeat center;
      }
    }
    // 连麦中
    .s-stage-loading {
      position: absolute;
      display: flex;
      justify-content: center;
      bottom: 6px;
      span {
        display: inline-block;
        width: 4px;
        height: 4px;
        margin: 0 2px;
        border-radius: 50%;
        background: #fff;
        animation: stageLoad 1s ease infinite;
        &:nth-child(1) {
          animation-delay: 0.25s;
        }
        &:nth-child(2) {
          animation-delay: 0.5s;
        }
        &:nth-child(3) {
          animation-delay: 0.75s;
        }
      }
    }

    .stage-access-dynamic {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-around;
      width: 80px;
      height: 92px;
      margin-bottom: 16px;
      background: linear-gradient(141deg, rgba(21, 27, 48, 0.7) 0%, rgba(28, 33, 49, 0.9) 100%);
      box-shadow: 0 0 3px 0 rgba(32, 32, 32, 0.4);
      border-radius: 6px;
      label {
        margin-top: 12px;
        color: #fff;
      }

    }
  }
}
</style>
