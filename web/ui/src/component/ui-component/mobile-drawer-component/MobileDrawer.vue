<template>
  <el-drawer
    ref="drawer"
    :visible="visible"
    :direction="direction"
    :custom-class="`mobile-drawer mobile-drawer--${direction} ${shouldContentGrow ? 'mobile-drawer--grow' : ''} ${isSmallScreen ? 'mobile-drawer--mobile' : ''} ${customClass}`"
    :size="direction === 'btt' ? bttHeight : rtlWidth"
    :show-close="false"
    destroy-on-close
    append-to-body
    :with-header="false"
    :modal="isSmallScreen"
    :wrapper-closable="wrapperClosable"
    @update:visible="handleVisbileChange"
    @open="$emit('open')"
    @opened="$emit('opened')"
    @close="$emit('close')"
    @closed="$emit('closed')"
  >
    <div class="mobile-drawer__container">
      <div
        v-if="showHeader"
        class="mobile-drawer__header"
      >
        <slot name="title">
          <span>{{ title }}</span>
        </slot>
        <div
          class="mobile-drawer__close-btn"
          @click="triggerClose"
        />
      </div>
      <div
        v-if="showHeader && showHeaderSplitLine"
        class="mobile-drawer__header-split"
      />
      <div class="mobile-drawer__content">
        <slot />
      </div>
      <div class="mobile-drawer__footer">
        <slot name="footer" />
      </div>
    </div>
  </el-drawer>
</template>

<script>
import BaseComponent from '@/component/core/BaseComponent';

export default {
  name: 'MobileDrawer',
  extends: BaseComponent,
  props: {
    visible: {
      type: Boolean,
    },
    title: {
      type: String,
      default: '',
    },
    showHeaderSplitLine: {
      type: Boolean,
      default: true,
    },
    showHeader: {
      type: Boolean,
      default: true,
    },
    modal: {
      type: Boolean,
      default: true,
    },
    rtlWidth: {
      type: [Number, String],
      default: '100vmin',
    },
    bttHeight: {
      type: [Number, String],
      default: 'auto',
    },
    customClass: {
      type: String,
      default: '',
    },
    wrapperClosable: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isPortrait: false,
    };
  },
  computed: {
    direction() {
      return this.isPortrait ? 'btt' : 'rtl';
    },
    shouldContentGrow() {
      return ((this.direction === 'btt' && this.bttHeight !== 'auto') || (this.direction === 'rtl'));
    },
  },
  mounted() {
    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.isPortrait = orientation === TCIC.TDeviceOrientation.Portrait;
    });
  },
  methods: {
    triggerClose() {
      this.$refs.drawer.closeDrawer();
    },
    handleVisbileChange(visible) {
      this.$emit('update:visible', visible);
    },
    handleFooterBtnClick(btnIndex) {
      this.$emit('footer-btn-click', btnIndex);
    },
  },
};
</script>

<style lang="less">
.el-drawer.mobile-drawer {
  background: var(--bg-color, #1B1E26);
  box-sizing: border-box;

  &:not(.mobile-drawer--mobile) {
    top: 64px;
    &.mobile-drawer--grow {
      .el-drawer__body {
        max-height: calc(100% - 64px);
      }
    }
  }

  .mobile-drawer__container {
    display: flex;
    flex-direction: column;
    justify-content: stretch;
  }

  .mobile-drawer__header {
    font-size: 18px;
    line-height: 25px;
    font-weight: 500;
    color: #DEE7FC;
    flex-grow: 0;
    flex-shrink: 0;
    min-height: 37px;
  }

  .mobile-drawer__content {
    flex-grow: 0;
    overflow-y: auto;
  }

  .mobile-drawer__footer {
    flex-grow: 0;
    flex-shrink: 0;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }

  .mobile-drawer__header-split {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  }

  .mobile-drawer__close-btn {
    width: 20px;
    height: 20px;
    position: absolute;
    right: 16px;
    background-image: url('./close.svg');
    background-size: 20px 20px;
    background-repeat: no-repeat;
    cursor: pointer;
  }

  &--grow {
    .el-drawer__body {
      max-height: 100%;
    }

    .mobile-drawer__container {
      height: 100%;
    }

    .mobile-drawer__header-split {
      margin-top: 8px;
    }

    .mobile-drawer__close-btn {
      top: 20px;
    }

    .mobile-drawer__header {
      padding: 17px 16px 0;
    }

    .mobile-drawer__content {
      flex-grow: 1;
      overflow-y: auto;
    }
  }

  &--rtl {
    .mobile-drawer__header-split {
      margin-top: 8px;
    }

    .mobile-drawer__close-btn {
      top: 13px;
    }

    .mobile-drawer__header {
      font-size: 16px;
      padding: 12px 16px 0;
    }
  }

  &--btt {
    border-radius: 10px 10px 0px 0px;

    .mobile-drawer__header-split {
      margin-top: 16px;
    }

    .mobile-drawer__close-btn {
      top: 27px;
    }

    .mobile-drawer__header {
      padding: 24px 16px 0;
    }
  }

  .mobile-drawer__btn-group {
    display: flex;
    justify-content: stretch;
    margin: 16px;

    .el-button {
      flex: 1;
      border-radius: 10px;
      background: #292D38;
      padding: 12px;
      text-align: center;
      color: #FFF;
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      border: none;
    }

    .el-button.el-button--primary {
      background: linear-gradient(315deg, #006EFF 0%, #0C59F2 100%);
    }
  }
}
</style>
