import moment from 'moment';
import Constant from '@/util/Constant';
import { LayoutFactory } from '@/pages/class/layout/layout_factory';


export default {
  isString(o) {
    return Object.prototype.toString.call(o) === '[object String]';
  },

  isObject(o) {
    return Object.prototype.toString.call(o) === '[object Object]';
  },

  isArray(o) {
    return Object.prototype.toString.call(o) === '[object Array]';
  },

  isFunction(o) {
    return Object.prototype.toString.call(o) === '[object Function]';
  },

  formatDuration(seconds, format) {
    return moment.utc(seconds * 1000).format(format);
  },

  formatLocalDuration(seconds, format) {
    return moment.utc(seconds * 1000).local().format(format);
  },

  formatTime(time, format) {
    return moment.unix(time).format(format);
  },

  getQuery(n, search) {
    const m = (search || window.location.search).match(new RegExp(`(\\?|&)${n}=([^&]*)(&|$)`));
    return !m ? '' : decodeURIComponent(m[2]);
  },

  replaceUrlParam(name, value, url = '') {
    const newUrl = new window.URL(url || location.href);
    newUrl.searchParams.set(name, value);
    return newUrl.href;
  },

  getHash(n) {
    const m = window.location.hash.match(new RegExp(`(#|&)${n}=([^&]*)(&|$)`));
    return !m ? '' : decodeURIComponent(m[2]);
  },

  isPrintableKeyEvent(event) {
    const keycode = event.keyCode;

    return (keycode > 47 && keycode < 58)  // number keys
      || keycode === 32 || keycode === 13  // spacebar & return key(s) (if you want to allow carriage returns)
      || (keycode > 64 && keycode < 91)  // letter keys
      || (keycode > 95 && keycode < 112)  // numpad keys
      || (keycode > 185 && keycode < 193)  // ;=,-./` (in order)
      || (keycode > 218 && keycode < 223)  // [\]' (in order)
      || keycode === 229;  // 输入法输入
  },

  getCustomProperty(className, property) {
    const val = getComputedStyle(document.querySelector(className)).getPropertyValue(property);
    if (val) {
      return val.trim();
    }
    return '';
  },

  openURL(url) {
    if (TCIC.SDK.instance.isElectron()) {
      window.Electron.openUrl(url);
    } else {
      window.open(url);
    }
  },

  getParams(key, defaultValue) {
    const params = TCIC.SDK.instance.getParams(key);
    return params || defaultValue;
  },

  getIntParams(key, defValue = undefined) {
    const params = this.getParams(key, defValue);
    return params ? parseInt(params, 10) : defValue;
  },

  getBooleanParams(key, defValue = false) {
    const params = this.getParams(key, defValue);
    return params ? (params === 'true' || params === true) : false;
  },

  addStyle(content) {
    const style = document.createElement('style');
    style.innerHTML = content;
    document.head.appendChild(style);
  },

  // 是否正在屏幕共享(忽略课件或辅助摄像头)
  isScreenShare() {
    const vodPlay = TCIC.SDK.instance.getState(TCIC.TMainState.Vod_Play, 2);
    const screenShare = TCIC.SDK.instance.getState(TCIC.TMainState.Screen_Share, 2);
    const isSubCameraStarted = TCIC.SDK.instance.getState(Constant.TStateStartSubCamera, false);
    const isTeacher = TCIC.SDK.instance.isTeacher();
    const isAssistant = TCIC.SDK.instance.isAssistant();
    const isVodPlayOnElectron = (isTeacher || isAssistant)
      && TCIC.SDK.instance.isElectron()
      && (vodPlay < 2);
    const isTeacherScreenShare = (isTeacher || isAssistant)
      && (screenShare < 2)
      && !isSubCameraStarted
      && !isVodPlayOnElectron;
    return isTeacherScreenShare;
  },

  // 可以翻页的课件
  isFileHasPage(fileInfo) {
    /*
    TODO 这个判断标准是不是有问题？
    相关文档
    - FileInfo: https://doc.qcloudtiw.com/web/official/global.html#FileInfo
    - TEduBoardFileType: https://doc.qcloudtiw.com/web/official/TEduBoard.html#.TEduBoardFileType
    */
    // 会设置给多个组件的 Boolean 属性，需要保证是 Boolean 类型
    return Boolean(fileInfo && ((fileInfo.type === 0 && fileInfo.downloadURL) || fileInfo.type === 1));
  },

  isPdfOrDoc(fileInfo) {
    const type = `${fileInfo.title}`.toLowerCase();
    const isPdfOrDoc = /\.(pdf|doc|docx)$/.test(type);
    return isPdfOrDoc;
  },
  getProperIntervalByCPUUsage(min, max) {
    let interval = min;
    try {
      if (TCIC.SDK.instance.isElectron() && window && window.Electron && window.Electron.getTotalCPUUsage() > 0.7) {
        interval = max;
      }
    } catch (e) {
      console.error('getProperIntervalByCPUUsage error', e);
    }
    return interval;
  },

  isUrlMsg(content) {
    // TODO 这个正则不太规范
    if (/^(https?:)\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\\.,@?^=%&:/~\\+#]*[\w\-\\@?^=%&/~\\+#])?/ig.test(content)) {
      return true;
    }
    return false;
  },

  setRightSideWidth(w) {
    rightSideWidth = w;
    setTimeout(() => {
      LayoutFactory.getInstance().updateLayout();
    }, 0);
  },

  setRightVideoHeight(h) {
    rightVideoHeight = h;
    setTimeout(() => {
      LayoutFactory.getInstance().updateLayout();
    }, 0);
  },

  // 1v1 右侧宽度
  getRightSideWidth() {
    if (rightSideWidth) {
      return rightSideWidth;
    }
    const defaultWidth = 286;
    const defaultWidth1 = 322;
    const defaultWidth2 = 452;
    const sw = window.innerWidth;
    const sh = window.innerHeight;
    if (sw > sh) { // 宽屏
      if (sw < 1440) {
        return defaultWidth;
      } if (sw >= 1440 && sw < 1920) {
        return defaultWidth1;
      } if (sw >= 1920) {
        return defaultWidth2;
      }
    }
    return defaultWidth;
  },
  // 1v1 右侧单个视频高度
  getRightVideoHeight() {
    if (rightVideoHeight) {
      return rightVideoHeight;
    }
    const defaultHeight = 160;
    const defaultWidth1 = 182;
    const defaultWidth2 = 252;
    const w = this.getRightSideWidth();
    const sw = window.innerWidth;
    const sh = window.innerHeight;
    if (sw > sh) {
      if (w === 452) {
        return defaultWidth2;
      } if (w === 322) {
        return defaultWidth1;
      }
    }
    return defaultHeight;
  },
  updateLayout() {
    LayoutFactory.getInstance().updateLayout();
  },
  shouldShowQrcode() {
    const schoolInfo = TCIC.SDK.instance.getSchoolInfo();
    if (!schoolInfo) {
      return false;
    }
    return schoolInfo.packageType === 1 || (schoolInfo.schoolName === '现网Demo使用' && schoolInfo.schoolId === 3923193);
  },
};

let rightSideWidth = undefined;
let rightVideoHeight = undefined;
