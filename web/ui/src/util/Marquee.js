import i18next from 'i18next';
import DomObserver from './DomObserver';

const marquee = {
    domObserVer: new DomObserver(),
    flashMarqueeInterval: 0,
    marqueeDomInterval: 0,
    shouldStopMarqueeAnimation: false,
    /**
     * 检查是否配置了对应跑马灯配置及配置是否正确
     * @param opts TCIC.SDK.instance.getMarqueeParam() 返回的数据
     * @returns {boolean} 是否配置正确
     */
    validParams(opts) {
        // 校验 marqueeType 和 displayMode 的范围
        const validMarqueeType = opts.marqueeType >= 1 && opts.marqueeType <= 6;
        const validDisplayMode = opts.displayMode >= 1 && opts.displayMode <= 2;

        return validMarqueeType && validDisplayMode;
    },

    hexToRgba(hex, opacity) {
        if (!hex) {
            return '';
        }
        let r = 0;
        let g = 0;
        let b = 0;
        if (hex.length === 4) {
            r = parseInt(hex[1] + hex[1], 16);
            g = parseInt(hex[2] + hex[2], 16);
            b = parseInt(hex[3] + hex[3], 16);
        } else if (hex.length === 7) {
            r = parseInt(hex[1] + hex[2], 16);
            g = parseInt(hex[3] + hex[4], 16);
            b = parseInt(hex[5] + hex[6], 16);
        }
        return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    },

    /**
     * 根据不同的 marqueeType 生成展示文本
     * @param opts TClassMarquee 配置
     * @param userInfo 用户信息
     * @returns {string} 生成的跑马灯文本
     */
    generateDisplayText(opts, userInfo) {
        const fixedText = opts.content || '';  // 固定值内容
        const userNickname = userInfo.nickname || '';  // 用户昵称
        const userId = userInfo.userId || '';  // 用户ID

        switch (opts.marqueeType) {
            case 1:
                return fixedText;  // 仅固定值
            case 2:
                return userNickname;  // 仅用户昵称
            case 3:
                return `${fixedText} ${userNickname}`;  // 固定值 + 用户昵称
            case 4:
                return userId;  // 仅用户ID
            case 5:
                return `${userId} ${fixedText}`;  // 用户ID + 固定值
            case 6:
                return `${userNickname}(${userId})`;  // 用户昵称（用户ID）
            default:
                return '';
        }
    },

    /**
     * 添加滚动跑马灯
     * @param dom 需要添加跑马灯的DOM (position: relative / absolute)
     * @param opts 跑马灯属性 (TClassMarquee 类型参数)
     * @param userInfo 用户信息
     */
    renderRollingMarquee(dom, opts, userInfo) {
        if (!dom) {
            console.error('添加跑马灯失败 , dom 不存在', opts);
            return;
        }
        this.disconnectObserver();
        if (dom.querySelector('.tcic-marquee')) {
            dom.removeChild(dom.querySelector('.tcic-marquee'));
        }

        const fragment = document.createElement('div');
        fragment.className = 'tcic-marquee';
        fragment.style.position = 'absolute';
        fragment.style.pointerEvents = 'none';
        fragment.style.width = '100%';
        fragment.style.height = '100%';
        fragment.style.zIndex = 10000;
        fragment.style.top = '0';
        const displayText = this.generateDisplayText(opts, userInfo);

        // 遍历生成每一行跑马灯
        for (let i = 0; i < opts.marqueeCount; i++) {
            const marqueeLine = document.createElement('div');
            marqueeLine.style.position = 'absolute';
            marqueeLine.style.left = '0';
            marqueeLine.style.whiteSpace = 'nowrap';
            marqueeLine.style.fontSize = `${opts.fontSize}px`;
            marqueeLine.style.pointerEvents = 'none';
            marqueeLine.style.fontWeight = opts.fontWeight ? 'bold' : 'normal';
            marqueeLine.style.color = opts.fontColor;
            marqueeLine.style.opacity = opts.fontOpacity;
            marqueeLine.style.backgroundColor = this.hexToRgba(opts.backgroundColor, opts.backgroundOpacity);
            marqueeLine.innerText = displayText;

            // 先将元素暂时隐藏，等待宽度计算
            marqueeLine.style.visibility = 'hidden';
            fragment.appendChild(marqueeLine);
        }

        // 将跑马灯元素添加到 DOM 后再获取正确的宽度
        dom.appendChild(fragment);

        // 延迟获取每个 line 的宽度并启动动画
        setTimeout(() => {
            const marqueeLines = fragment.children;
            const parentHeight = dom.clientHeight;

            for (let i = 0; i < marqueeLines.length; i++) {
                const marqueeLine = marqueeLines[i];
                const lineWidth = marqueeLine.offsetWidth;

                // 设置初始位置（右侧外部）
                marqueeLine.style.visibility = 'visible';
                marqueeLine.style.right = `-${lineWidth}px`;

                // 计算垂直位置
                if (opts.marqueeCount === 1) {
                    // 如果只有一行，垂直居中显示
                    marqueeLine.style.top = `${(parentHeight - opts.fontSize) / 2}px`;
                } else if (opts.marqueeCount === 2) {
                    const maxOffset = parentHeight / 4;
                    const offsetY = Math.min(opts.fontSize * 4, maxOffset);

                    const topPosition = (parentHeight - opts.fontSize) / 2 + (i === 0 ? -offsetY : offsetY);

                    marqueeLine.style.top = `${Math.max(0, Math.min(parentHeight - opts.fontSize, topPosition))}px`;
                } else {
                    // 多行情况（默认处理方式）
                    marqueeLine.style.top = `${i * opts.fontSize * 3 + 100}px`;
                }


                const toLeft = [
                    { transform: `translateX(${lineWidth + dom.offsetWidth}px)` },
                    { transform: `translateX(${-2 * lineWidth}px)` },
                ];
                const baseSpeed = 50;
                const durationInSeconds = (lineWidth + dom.offsetWidth) / (opts.fontSize * (baseSpeed / opts.duration));
                const newspaperTiming = {
                    duration: durationInSeconds * 1000,
                    iterations: Infinity,
                };
                marqueeLine.animate(toLeft, newspaperTiming);
                this.marqueeObserver();

                // marqueeLine.addEventListener('transitionend', () => {
                //     // 每次动画开始需要取消Dom 监听，否则自己样式的更改也会被监听到
                //     this.disconnectObserver();
                //     // 将元素重置到右侧外部，再次触发动画
                //     marqueeLine.style.transition = 'none'; // 取消过渡效果
                //     marqueeLine.style.transform = 'unset'; // 移动回右侧
                //     if (!this.shouldStopMarqueeAnimation) {
                //         setTimeout(() => {
                //             startAnimation(); // 等待一小段时间后再次开始动画
                //         }, 0);
                //     }
                // });
            }
        }, 100);
    },
    disconnectObserver() {
        this.domObserVer.destroy();
    },
    marqueeObserver() {
        this.domObserVer.destroy();
        this.domObserVer.init({
            targetDomClassName: '.tcic-marquee',
            domChangeCallback: () => {
                this.shouldStopMarqueeAnimation = true;
                TCIC.SDK.instance.showErrorMsgBox({
                    title: i18next.t('异常提醒'),
                    message: i18next.t('未经许可, 不允许修改水印'),
                });
                this.domObserVer.destroy();
            },
        });
    },

    /**
     * 添加闪烁跑马灯
     * @param dom 需要添加跑马灯的DOM (position: relative / absolute)
     * @param opts 跑马灯属性 (TClassMarquee 类型参数)
     * @param userInfo 用户信息
     */
    renderFlashingMarquee(dom, opts, userInfo) {
        if (!dom) {
            console.error('添加跑马灯失败 , dom 不存在', opts);
            return;
        }
        this.disconnectObserver();
        if (dom.querySelector('.tcic-marquee')) {
            dom.removeChild(dom.querySelector('.tcic-marquee'));
        }
        const fragment = document.createElement('div');
        const displayText = this.generateDisplayText(opts, userInfo);

        // 创建跑马灯行
        for (let i = 0; i < opts.marqueeCount; i++) {
            const marqueeLine = document.createElement('div');
            marqueeLine.style.position = 'absolute';
            marqueeLine.style.whiteSpace = 'nowrap';
            marqueeLine.style.pointerEvents = 'none';
            marqueeLine.style.fontSize = `${opts.fontSize}px`;
            marqueeLine.style.fontWeight = opts.fontWeight ? 'bold' : 'normal';
            marqueeLine.style.color = opts.fontColor;
            marqueeLine.style.opacity = opts.fontOpacity;
            marqueeLine.style.backgroundColor = this.hexToRgba(opts.backgroundColor, opts.backgroundOpacity);
            marqueeLine.innerText = displayText;

            // 添加到 fragment
            fragment.appendChild(marqueeLine);

            // 设置闪烁动画
            const randomizePosition = (index) => {
                this.disconnectObserver();
                const parentWidth = dom.clientWidth;
                const parentHeight = dom.clientHeight;

                let randomY;

                if (opts.marqueeCount === 2) {
                    if (index === 0) {
                        // 第一个在上半屏幕
                        randomY = Math.max(0, Math.random() * (parentHeight / 2 - opts.fontSize));
                    } else if (index === 1) {
                        // 第二个在下半屏幕
                        randomY = (parentHeight / 2) + Math.random() * (parentHeight / 2 - opts.fontSize);
                    }
                } else {
                    // 单个跑马灯时，随机出现在整个屏幕
                    randomY = Math.random() * (parentHeight - opts.fontSize);
                }

                // 随机X轴位置，确保跑马灯不会超出屏幕边界
                const randomX = Math.random() * (parentWidth - marqueeLine.offsetWidth);

                marqueeLine.style.left = `${randomX}px`;
                marqueeLine.style.top = `${randomY}px`;
                this.marqueeObserver();
            };

            // 定期换位
           this.flashMarqueeInterval = setInterval(() => {
                if (this.shouldStopMarqueeAnimation) {
                    clearInterval(this.flashMarqueeInterval);
                } else {
                    randomizePosition(i);
                }
            }, opts.duration * 1000);
        }
        fragment.className = 'tcic-marquee';
        fragment.style.position = 'absolute';
        fragment.style.pointerEvents = 'none';
        fragment.style.width = '100%';
        fragment.style.height = '100%';
        fragment.style.top = '0';
        fragment.style.zIndex = 10000;

        // 将闪烁效果应用到 DOM
        dom.appendChild(fragment);
    },

    /**
     * 渲染跑马灯
     * @param dom 需要渲染的 DOM
     * @param opts TClassMarquee 配置
     * @param userInfo 当前用户信息
     */
    render(dom, opts, userInfo) {
        if (this.validParams(opts)) {
            switch (opts.displayMode) {
                case 1:
                    this.renderRollingMarquee(dom, opts, userInfo);  // 渲染滚动跑马灯
                    break;
                case 2:
                    this.renderFlashingMarquee(dom, opts, userInfo);  // 渲染闪烁跑马灯
                    break;
                default:
                    console.warn('未知的跑马灯显示模式');
            }
        }
    },

    destroy(dom) {
        this.disconnectObserver();
        if (dom.querySelector('.tcic-marquee')) {
            dom.removeChild(dom.querySelector('.tcic-marquee'));
        }
    },
};

export default marquee;
