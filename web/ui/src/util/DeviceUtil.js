import i18next from 'i18next';
import { MediaPermissionsErrorType, requestMediaPermissions } from 'mic-check';
import Constant from './Constant';

let deviceErrMsgMap;
function initDeviceErrMsgMap() {
  deviceErrMsgMap = {
    camera: {
      [MediaPermissionsErrorType.SystemPermissionDenied]: i18next.t('系统权限未开启，浏览器权限未开启，'),
      [MediaPermissionsErrorType.UserPermissionDenied]: i18next.t('浏览器权限未开启，'),
      [MediaPermissionsErrorType.CouldNotStartVideoSource]: i18next.t('摄像头被占用，'),
      [MediaPermissionsErrorType.Generic]: i18next.t('摄像头打开失败，'),
      '-1001': i18next.t('摄像头正在打开'),
      default: i18next.t('打开本地视频采集遇到一些问题'),
    },
    mic: {
      [MediaPermissionsErrorType.SystemPermissionDenied]: i18next.t('系统权限未开启，浏览器权限未开启，'),
      [MediaPermissionsErrorType.UserPermissionDenied]: i18next.t('浏览器权限未开启，'),
      [MediaPermissionsErrorType.CouldNotStartVideoSource]: i18next.t('麦克风被占用，'),
      [MediaPermissionsErrorType.Generic]: i18next.t('麦克风打开失败，'),
      '-1001': i18next.t('麦克风正在打开'),
      default: i18next.t('打开本地音频采集遇到一些问题'),
    },
  };
}

function showErrorMessage(deviceType, err) {
  if (!deviceErrMsgMap) {
    initDeviceErrMsgMap();
  }
  const msgMap = deviceErrMsgMap[deviceType];
  if (!msgMap) {
    return;
  }
  let errMsg = '';
  let showHelpLink = false;
  // 中文web的才显示帮助链接，其他的找到地址再更新
  if (err?.type
    && TCIC.SDK.instance.isWeb()
    && /zh(-\w+)?/g.test(TCIC.SDK.instance.getLanguage())
  ) {
    errMsg = msgMap[err?.type];
    showHelpLink = !!errMsg;
  }
  if (showHelpLink) {
    TCIC.SDK.instance.showMessageBox(
      i18next.t('提示'),
      // eslint-disable-next-line quotes, max-len
      `${errMsg}${i18next.t('请点击<a href=\'https://cloud.tencent.com/document/product/1639/79920#717dccbf-6d18-4bab-967b-3fac3ecfa864\' target=\'_blank\' style=\'color: #006eff;\'>摄像头、麦克风权限问题</a>查看开启方式。')}`,
      [i18next.t('确定')],
      (index) => {},
      [],
      `${i18next.t('错误码')}: ${err.type}`,
      true, // isSafeInput
    );
  } else {
    if (err?.errorCode && msgMap[err.errorCode]) {
      errMsg = msgMap[err.errorCode];
    } else if (err && err.errorMsg) {
      errMsg = err.errorMsg || err.errorDetail;
    } else {
      errMsg = msgMap.default;
    }
    window.showToast(errMsg, 'error');
  }
  return errMsg;
}

function getDeviceStatusFromMediaPermissionsError(err) {
  let deviceStatus = TCIC.TDeviceStatus.Fail;
  switch (err?.type) {
    case MediaPermissionsErrorType.SystemPermissionDenied:
    case MediaPermissionsErrorType.UserPermissionDenied:
      deviceStatus = TCIC.TDeviceStatus.No_Permission;
      break;
    case MediaPermissionsErrorType.CouldNotStartVideoSource:
      deviceStatus = TCIC.TDeviceStatus.Busy;
      break;
    default:
      if (err?.name === 'NotFoundError') {
        deviceStatus = TCIC.TDeviceStatus.Not_Found;
      }
  }
  return deviceStatus;
}

function getReportDeviceState(open, deviceStatus) {
  if (open) {
    return TCIC.TDeviceStatus.Open;
  }
  return TCIC.SDK.instance.isDeviceAbnormal(deviceStatus) ? deviceStatus : TCIC.TDeviceStatus.Closed;
}

const deviceLoadingEventConfig = {
  camera: {
    eventName: Constant.TStateLoadingCameraState,
    eventDataType: {
      self: 'loadingOwnCamera',
      other: 'authorizingCamera',
    },
    deviceStatusName: TCIC.TMainState.Video_Device_Status,
  },
  mic: {
    eventName: Constant.TStateLoadingMicState,
    eventDataType: {
      self: 'loadingOwnMic',
      other: 'authorizingMic',
    },
    deviceStatusName: TCIC.TMainState.Audio_Device_Status,
  },
  board: {
    eventName: Constant.TStateLoadingBoardState,
    eventDataType: {
      self: 'loadingOwnBoard',
      other: 'authorizingBoard',
    },
    deviceStatusName: TCIC.TMainState.Board_Permission,
  },
  stage: {
    eventName: Constant.TStateLoadingStageState,
    eventDataType: {
      self: 'loadingOwnState',
      other: 'authorizingState',
    },
    deviceStatusName: TCIC.TMainState.Stage_Status,
  },
  speak: {
    eventName: Constant.TStateLoadingSpeakState,
    eventDataType: {
      self: 'loadingOwnSpeak',
      other: 'authorizingSpeak',
    },
    deviceStatusName: TCIC.TMainState.Silence,
  },
  share: {
    eventName: Constant.TStateLoadingShareState,
    eventDataType: {
      self: 'loadingOwnShare',
      other: 'authorizingShare',
    },
    deviceStatusName: TCIC.TMainState.Screen_Share_Open,
  },
};
const selfDeviceLoadingState = {
  camera: null,
  mic: null,
  board: null,
  stage: null,
  share: null,
};

const selfDeviceState = {
  camera: false,
  mic: false,
  board: false,
  stage: false,
  share: false,
};


/**
 * 触发更新本地设备loading状态的事件
 * @param {string} userId string
 * @param {string} deviceType 'camera' | 'mic'
 * @param {object} value { loading: string | false; open?: boolean }
 * @param {string} trigger `${caller}-${reason}`
 */
function notifyDeviceLoadingEvent(userId, deviceType, value, trigger) {
  const loadingEventCfg = deviceLoadingEventConfig[deviceType];
  if (!loadingEventCfg) {
    return;
  }
  const isSelf = userId === TCIC.SDK.instance.getUserId();
  const type = isSelf ? loadingEventCfg.eventDataType.self : loadingEventCfg.eventDataType.other;
  if (isSelf) {
    console.log(
      'notifyDeviceLoadingEvent', userId, type, value, trigger,
      `\nself ${deviceType} loading: ${value?.loading || 'notLoading'}`,
    );
    selfDeviceLoadingState[deviceType] = value?.loading ? value : null;
    selfDeviceState[deviceType] = typeof value?.open === 'boolean' ? value.open : selfDeviceState[deviceType];
  } else {
    console.log('notifyDeviceLoadingEvent', userId, type, value, trigger, loadingEventCfg.eventName);
  }
  TCIC.SDK.instance.notify(loadingEventCfg.eventName, {
    userId,
    type,
    value,
    trigger,
  });
}

/**
 * 更新本地设备并触发loading事件
 * @param {string} deviceType 'camera' | 'mic'
 * @param {boolean} flag boolean
 * @param {object} getPromise () => Promise
 * @param {object} options { caller, reason, reportAction }
 */
async function toggleLocalDeviceWithLoadingEvent(
  deviceType,
  flag,
  getPromise,
  { caller, reason, reportAction },
) {
  const loadingEventCfg = deviceLoadingEventConfig[deviceType];
  if (!loadingEventCfg) {
    return;
  }
  const { deviceStatusName } = loadingEventCfg;
  const selfUserId = TCIC.SDK.instance.getUserId();
  const loadingType = flag ? 'startingLocal' : 'stoppingLocal';
  let loadingTimer;

  function startLoading(params, trigger) {
    const value = {
      loading: loadingType,
      ...params,
    };
    notifyDeviceLoadingEvent(selfUserId, deviceType, value, trigger);
  }
  function stopLoading(params, trigger) {
    if (loadingTimer) {
      clearTimeout(loadingTimer);
      loadingTimer = null;
    }
    const value = {
      loading: false,
      ...params,
    };
    notifyDeviceLoadingEvent(selfUserId, deviceType, value, trigger);
  }

  // 打开loading
  startLoading(null, `${caller}-${reason}`);

  // 默认10秒超时，关闭loading
  loadingTimer = setTimeout(() => {
    clearTimeout(loadingTimer);
    loadingTimer = null;
    if (selfDeviceLoadingState[deviceType]?.loading === loadingType) {
      stopLoading(null, `${caller}-${reason}-timeout`);
    }
  }, 10000);

  let toggleRes;
  let toggleErr;
  const isMobile = TCIC.SDK.instance.isMobile();
  // if (flag) {
    // const needRequestMediaPermissions = TCIC.SDK.instance.isWeb() && !TCIC.SDK.instance.isRtmpMode();
    // if (needRequestMediaPermissions) {
    //   try {
    //     reportAction && TCIC.SDK.instance.reportLog(reportAction, `[${caller}] ${reason}, requestMediaPermissions`);
    //     await requestMediaPermissions({ audio: deviceType === 'mic', video: deviceType === 'camera' });
    //     reportAction && TCIC.SDK.instance.reportLog(reportAction, `[${caller}] ${reason}, requestMediaPermissions success`);
    //   } catch (err) {
    //     console.error(`[${caller}] ${reason}, requestMediaPermissions error =>`, err);
    //     // 对齐 trtc.startLocalXXX，更新设备状态
    //     TCIC.SDK.instance.setState(
    //       deviceStatusName,
    //       getDeviceStatusFromMediaPermissionsError(err),
    //       caller,
    //     );
    //     reportAction && TCIC.SDK.instance.reportLog(reportAction, `[${caller}] ${reason}, requestMediaPermissions error, ${JSON.stringify({
    //       deviceStatus: TCIC.SDK.instance.getState(deviceStatusName),
    //       ...err,
    //     })}`);
    //     if (!isMobile) {
    //       toggleErr = err;
    //     }
    //   }
    // }
  // }

  if (!toggleErr) {
    try {
      reportAction && TCIC.SDK.instance.reportLog(reportAction, `[${caller}] ${reason}, call trtc`);
      toggleRes = await getPromise();
      reportAction && TCIC.SDK.instance.reportLog(reportAction, `[${caller}] ${reason}, call trtc success`);
    } catch (err) {
      reportAction && TCIC.SDK.instance.reportLog(reportAction, `[${caller}] ${reason}, call trtc error, ${JSON.stringify({
        deviceStatus: TCIC.SDK.instance.getState(deviceStatusName),
        ...err,
      })}`);
      toggleErr = err;
    }
  }

  if (!toggleErr) {
    // 成功，停止loading
    stopLoading({ open: flag }, `${caller}-${reason}-success`);
  } else {
    // 出错，停止loading，不用把 open 设为 !flag，保持原值就好
    stopLoading(null, `${caller}-${reason}-error`);
    // pc端抛出错误， 移动端走完流程
    if (!isMobile) {
      throw toggleErr;
    }
  }

  return toggleRes;
}

/**
 * 更新远端设备并触发loading事件
 * @param {string} userId string
 * @param {string} deviceType 'camera' | 'mic'
 * @param {boolean} flag boolean
 * @param {object} getPromise () => Promise
 * @param {object} options { caller, reason, reportAction }
 */
async function toggleRemoteDeviceWithLoadingEvent(
  userId,
  deviceType,
  flag,
  getPromise,
  { caller, reason, reportAction, waitIMInstruction },
) {
  const loadingEventCfg = deviceLoadingEventConfig[deviceType];
  if (!loadingEventCfg) {
    return;
  }
  const selfUserId = TCIC.SDK.instance.getUserId();
  const isSelf = userId === selfUserId;
  // eslint-disable-next-line no-nested-ternary
  const loadingType = isSelf
    ? (flag ? 'startingRemote' : 'stoppingRemote')
    : (flag ? 'authorizing' : 'unauthorizing');

  function startLoading(params, trigger) {
    const value = {
      loading: loadingType,
      ...params,
    };
    notifyDeviceLoadingEvent(userId, deviceType, value, trigger);
  }
  function stopLoading(params, trigger) {
    const value = {
      loading: false,
      ...params,
    };
    notifyDeviceLoadingEvent(userId, deviceType, value, trigger);
  }

  // 打开loading
  startLoading(null, `${caller}-${reason}`);

  let toggleRes;
  let toggleErr;
  try {
    reportAction && TCIC.SDK.instance.reportLog(reportAction, `[${caller}] ${reason}, member action`);
    toggleRes = await getPromise();
    reportAction && TCIC.SDK.instance.reportLog(reportAction, `[${caller}] ${reason}, member action success`);
  } catch (err) {
    reportAction && TCIC.SDK.instance.reportLog(reportAction, `[${caller}] ${reason}, member action error, ${JSON.stringify(err)}`);
    toggleErr = err;
  }

  if (!toggleErr) {
    // 成功，不用把 open 设为 flag
    if (isSelf) {
      // 如果是自己
      if (selfDeviceLoadingState[deviceType]?.loading === loadingType) {
        // loading状态没变，继续loading，等待permission更新的事件来触发切换本地设备状态
        notifyDeviceLoadingEvent(
          userId,
          deviceType,
          { loading: flag ? 'waitingStartLocal' : 'waitingStopLocal' },
          `${caller}-${reason}-success`,
        );
      } else {
        // 可能先收到permission更新的事件，已经在切换本地设备状态了，那就不用改成waiting
      }
    } else {
      if (!waitIMInstruction) {
        // 如果是他人，停止loading，需要等待他人上报state后才能更新
        stopLoading(null, `${caller}-${reason}-success`);
      }
    }
  } else {
    // 出错，停止loading，不用把 open 设为 !flag，保持原值就好
    stopLoading(null, `${caller}-${reason}-error`);
    throw toggleErr;
  }

  return toggleRes;
}

export default {
  showErrorMessage,
  getDeviceStatusFromMediaPermissionsError,
  getReportDeviceState,
  notifyDeviceLoadingEvent,
  toggleLocalDeviceWithLoadingEvent,
  toggleRemoteDeviceWithLoadingEvent,
  selfDeviceState,
};
