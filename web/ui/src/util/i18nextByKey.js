/* eslint-disable max-len */
// class页用
import i18next from 'i18next';

// 仅兜底用，优先用语言包里配的
const CLASS_MESSAGE_MAP_ZH = {
  'roomStatusDesc.notStart.simple': '未开始',
  'roomStatusDesc.notStart.today': '未开始{{startRoom}}（距离{{startRoom}}还有 {{arg_0}}）',
  'roomStatusDesc.notStart.otherday': '未开始{{startRoom}}（{{startRoom}}时间为 {{arg_0}}）',
  'roomStatusDesc.entering': '正在进入{{room}}...',
  'roomStatusDesc.hasEnded': '{{room}}已结束',
  'roomStatusDesc.hasExpired': '{{room}}已过期',
  'roomStatusDesc.hasExpiredWithTeacherGuide': '{{room}}已过期<br>当前{{room}}预约的时间为：{{arg_0}}~{{arg_1}}，已超过预约结束时间，请重新预约',
  'roomStatusDesc.hasExpiredWithCommonGuide': '{{room}}已过期<br>当前{{room}}预约的时间为：{{arg_0}}~{{arg_1}}，{{room}}未开始，请与{{teacher}}确认最新的{{room}}信息',
  'roomStatusDesc.hasDelayed': '未开始{{startRoom}}',
  'roomStatusDesc.hasStarted': '开始{{startRoom}}',
  'roomStatusDesc.hasStartedWithExtraInfo': '{{startRoom}}中：',
  'roomRemind.prepare': '准备{{startRoom}}',
  'roomRemind.timeRemain': '距离{{startRoom}}还有',
  'teacherEndRoomConfirm.title': '确认{{endRoom}}？',
  'teacherEndRoomConfirm.message': '点击“{{endRoom}}将结束课程，无法返回',
  'commonLeaveRoomConfirm.title': '是否确认{{leaveRoom}}？',
  'commonLeaveRoomConfirm.message': '如果{{room}}未结束，您可以在离开后重新进入{{room}}',
};

const CLASS_MESSAGE_MAP_EN = {
  'roomStatusDesc.notStart.simple': 'Not started',
  'roomStatusDesc.notStart.today': 'Not started ({{arg_0}} remaining)',
  'roomStatusDesc.notStart.otherday': 'Not started ({{room}} time is {{arg_0}})',
  'roomStatusDesc.entering': 'Entering the {{room}}...',
  'roomStatusDesc.hasEnded': 'The {{room}} has ended',
  'roomStatusDesc.hasExpired': 'The {{room}} has expired',
  'roomStatusDesc.hasExpiredWithTeacherGuide': 'The {{room}} has expired.<br>The reservation time is: {{arg_0}}~{{arg_1}}, the end time of the reservation has been exceeded, please reschedule the {{room}}.',
  'roomStatusDesc.hasExpiredWithCommonGuide': 'The {{room}} has expired.<br>The reservation time is: {{arg_0}}~{{arg_1}}, the {{room}} is not started, please confirm the latest {{room}} information with the {{teacher}}.',
  'roomStatusDesc.hasDelayed': 'The {{room}} is not started',
  'roomStatusDesc.hasStarted': 'The {{room}} is in progress',
  'roomStatusDesc.hasStartedWithExtraInfo': 'In progress: ',
  'roomRemind.prepare': 'Get ready for the {{room}}',
  'roomRemind.timeRemain': 'Time remain',
  'teacherEndRoomConfirm.title': 'Are you sure you want to End/Leave the {{room}}?',
  'teacherEndRoomConfirm.message': 'Click on \'{{endRoom}}\' to end the on-going {{room}}.<br>Click on \'{{leaveRoom}}\', if you want to re-join the {{room}} later again.',
  'commonLeaveRoomConfirm.title': 'Are you sure you want to {{leaveRoom}}?',
  'commonLeaveRoomConfirm.message': 'You can re-enter the {{room}} after leaving, if the {{room}} is still in-progress.',
};

export default {
  t: (key, params) => {
    const msgMap = /zh(-\w+)?/g.test(i18next.language) ? CLASS_MESSAGE_MAP_ZH : CLASS_MESSAGE_MAP_EN;
    const isKey = key in msgMap;
    // 优先用语言包里配的
    const msg = i18next.t(key, params);
    if (!isKey || msg !== key) {
      // 翻译正常
      return msg;
    }
    // 没配或者没加载到语言包才走这里
    const backup = msgMap[key] ? i18next.t(msgMap[key], params) : key;
    // console.warn('i18nextByKey error', key, msg, backup);
    return backup || key;
  },
};
