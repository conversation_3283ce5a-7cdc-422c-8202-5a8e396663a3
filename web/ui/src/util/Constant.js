
export default {
  // 是否处于录制模式
  TStateRecordMode: 'TStateRecordMode',
  // 是否正在展示Loading页面
  TStateIsLoading: 'TStateIsLoading',
  // 屏幕共享是否混合系统声音
  TStateEnableSystemAudioLoopback: 'TStateEnableSystemAudioLoopback',
  // 选择共享窗口组件是否打开
  TStateShowSelectShareWindowComponent: 'TStateShowSelectShareWindowComponent',
  // 课堂分享信息
  TStateShareClassroomInfo: 'TStateShareClassroomInfo',
  TStateSkipTeacherUpdateLayout: 'TStateSkipTeacherUpdateLayout',
  // 是否全屏
  TStateFullScreen: 'TStateFullScreen',
  // 是否还在设备检测中
  TStateDeviceDetect: 'TStateDeviceDetect',
  // 是否显示白板工具条
  TStateIsShowBoardToolComponent: 'TStateIsShowBoardToolComponent',
  // 是否显示底部工具条
  TStateIsShowFooterComponent: 'TStateIsShowFooterComponent',
  // 是否隐藏Footer
  TStateIsHideFooterComponent: 'TStateIsHideFooterComponent',
  // 是否显示缩略图组件
  TStateIsShowThumbnailComponent: 'TStateIsShowThumbnailComponent',
  // 课件模式是否显示缩略图
  TStateShowThumbnailForCourseware: 'TStateShowThumbnailForCourseware',
  // 是否有正在进行的答题器
  TStateHasQuizInProgress: 'TStateHasQuizInProgress',
  // 导航栏是否可见
  TStateHeaderVisible: 'TStateHeaderVisible',
  // 屏幕分享播放器是否可见
  TStateScreenPlayerVisible: 'TStateScreenPlayerVisible',
  // 颠簸播放器是否可见
  TStateVodPlayerVisible: 'TStateVodPlayerVisible',
  // 组件更新布局事件
  TEventComponentUpdateLayout: 'TEventComponentUpdateLayout',
  // 当前随堂测是否可以直接关闭
  TStateQuizClosable: 'TStateQuizClosable',
  // 白板工具类型
  TStateBoardToolType: 'TStateBoardToolType',
  // 白板画笔颜色
  TStateBoardBrushColor: 'TStateBoardBrushColor',
  // 是否禁止聊天
  TStateDisableChat: 'TStateDisableChat',
  // 禁止聊天时的提示语
  TStateDisableChatTips: 'TStateDisableChatTips',
  // 是否展示弹幕消息
  TStateShowLiveCommenting: 'TStateShowLiveCommenting',
  // 是否展示弹幕聊天窗
  TStateShowQuickIM: 'TStateShowQuickIM',
  // 聊天盒子提示文体
  TStateChatBoxTips: 'TStateChatBoxTips',
  // 上课前打开本地音视频
  TStateLocalAVBeforeClassBegin: 'TStateLocalAVBeforeClassBegin',
  // 点击上课时的3s倒计时
  TStateClassStartCountDowning: 'TStateClassStartCountDowning',
  // 屏幕共享白板撤销状态
  TStateScreenShareBoardUndo: 'TStateScreenShareBoardUndo',
  // 点击共享白板重做状态
  TStateScreenShareBoardRedo: 'TStateScreenShareBoardRedo',
  // 点名板header是否激活
  TStateHeaderMemberActive: 'TStateHeaderMemberActive',
  // 消息盒子显示状态
  TStateShowChatBox: 'TStateShowChatBox',
  // 当前视频悬浮窗用户id(保证同一时间只有一个视频悬浮窗)
  TStateVideoCtrlUserId: 'TStateVideoCtrlUserId',
  // 纯音频模式(不拉远端视频流)
  TStateAudioMode: 'TStateAudioMode',
  // 正在获取系统信息
  TStateIsGettingSystemInfo: 'TStateIsGettingSystemInfo',
  // 是否双排模式
  TStateVideoDubbleLine: 'TStateVideoDubbleLine',
  // 视频归位
  TStateResetVideoPosition: 'TStateResetVideoPosition',
  // 视频播放组件位置变化状态
  TStateVodBoundsChanged: 'TStateVodBoundsChanged',
  // 大教学模式视频全屏变化, 参数为userid，退出全屏则userid为''
  TStateCollClassVodFullScreen: 'TStateCollClassVodFullScreen',
  // 直播简介
  TStateLiveIntroduce: 'TStateLiveIntroduce',
  // 移动端关闭键盘
  TStateCloseKeyboard: 'TStateCloseKeyboard',
  // 组件加载完成事件
  TStateComponentLoaded: 'TStateComponentLoaded',
  // 图片发送状态
  TStateImgSendStatus: 'TStateImgSendStatus',
  // 文件发送状态
  TStateFileSendStatus: 'TStateFileSendStatus',
  // 循环上台
  TStateCarousel: 'TStateCarousel',
  // 循环上台间隔
  TStateCarouselInterval: 'TStateCarouselInterval',
  // 聊天弹幕
  TStateChatTipsEnable: 'TStateChatTipsEnable',
  // 编辑状态不隐藏底部栏
  TStateQucikIMEdit: 'TStateQucikIMEdit',
  // 是否正在分享辅路摄像头
  TStateStartSubCamera: 'TStateStartSubCamera',
  // 抛出消息让气泡下移
  TStateVideoTipsComponentDown: 'TStateVideoTipsComponentDown',
  // 沉浸模式
  TStateImmerseMode: 'TStateImmerseMode',
  // 网络提示界面开启音频上课
  TStateNetWorkTipsAudio: 'TStateNetWorkTipsAudio',
  // 老师视频是否加载
  TStateTeacherComponentLoaded: 'TStateTeacherComponentLoaded',
  // 在白板区域最大化显示老师视频
  TStateShowTeacherVideoInWhiteBoardArea: 'TStateShowTeacherVideoInWhiteBoardArea',
  // 登录时是否打开麦克风
  TStateOpenMic: 'TStateOpenMic',
  // 登录时是否打开扬声器
  TStateOpenSpeaker: 'TStateOpenSpeaker',
  // 自定义 Doc 的组件
  TStateDocCustom: 'TStateDocCustom',
  // 登录时是否打开摄像头
  TStateOpenCamera: 'TStateOpenCamera',
  /**
   * 摄像头loading状态，包括打开/关闭自己的或者别人的
   * type: loadingOwnCamera / authorizingCamera
   * userId: userId
   * value:
   *   loadingOwnCamera 时，{ loading: 'startingRemote'| 'stoppingRemote' | 'startingLocal'  | 'stoppingLocal' | false, open: boolean }
   *   authorizingCamera 时，没有 open 字段，{ loading: 'authorizing' | 'unauthorizing' | false }
   */
  TStateLoadingCameraState: 'TStateLoadingCameraState',
  /**
   * 麦克风loading状态，包括打开/关闭自己的或者别人的
   * type: loadingOwnMic / authorizingMic
   * userId: userId
   * value:
   *   loadingOwnMic 时，{ loading: 'startingRemote' | 'stoppingRemote' | 'startingLocal' | 'stoppingLocal' | false, open: boolean }
   *   authorizingMic 时，没有 open 字段，{ loading: 'authorizing' | 'unauthorizing' | false }
   */
  TStateLoadingMicState: 'TStateLoadingMicState',
  TStateLoadingBoardState: 'TStateLoadingBoardState',
  TStateLoadingHandupState: 'TStateLoadingHandupState',
  TStateLoadingStageState: 'TStateLoadingStageState',
  TStateLoadingSpeakState: 'TStateLoadingSpeakState',
  TStateLoadingShareState: 'TStateLoadingShareState',
  /*
  *************************** 大教学是联奕给高校合作伙伴定制的版本,属于深度自定义,普通用户无法使用 **********************************
  */
  // (大教学模式)学生视频列表是否可见
  TStateShowStudentsVideoWrap: 'TStateShowStudentsVideoWrap',
  // (大教学模式)右边栏是否可见
  TStateShowRightColumn: 'TStateShowRightColumn',
  // (大教学模式)透传点击状态变化
  TStatePassThroughStatus: 'TStatePassThroughStatus',
  // (大教学模式)视频布局
  TStateCollegeVideoLayout: 'TStateCollegeVideoLayout',
  // (大教学模式)是否双屏模式
  TStateCollegeDoubleScreenMode: 'TStateCollegeDoubleScreenMode',
  // (大教学模式)是否允许学生自己解除禁言
  TStateCollegeEnableStudentMicRequest: 'TStateCollegeEnableStudentMicRequest',
  // (大教学模式)学生自己解除禁音
  TStateCollegeRequestMicPermission: 'TStateCollegeRequestMicPermission',
  // 公开课视频是否全屏展示
  TStateBigVideoMode: 'TStateBigVideoMode',
  // 视频墙模式
  TStateVideoWallMode: 'TStateVideoWallMode',
  // 当前加载视频数量
  TStateVideoLoaderCount: 'TStateVideoLoaderCount',
  // 当前视频加载页码
  TStateVideoLoaderPage: 'TStateVideoLoaderPage',
  // 最大视频加载数量
  TStateVideoMaxLoadCount: 'TStateVideoMaxLoadCount',
  // ppt分享状态切换
  TStatePPtSharingState: 'TStatePPtSharingState',
  // 是否hook内部toast消息
  TStateHookToastMessage: 'TStateHookToastMessage',
  // 是否启用默认的布局更新逻辑
  TStateEnableUpdateLayout: 'TStateEnableUpdateLayout',
  // 双师课堂成员列表
  TStateCoTeachingMemberList: 'TStateCoTeachingMemberList',
  // 美颜配置
  TStateVideoBeautyConfig: 'TStateVideoBeautyConfig',
  // 双师是否在显示举手列表
  TStateCoTeachingShowHandupList: 'TStateCoTeachingShowHandupList',
  // 双师连麦用户数组
  TStateCoTeachingLinkUsers: 'TStateCoTeachingLinkUsers',
  // 双师连麦次数
  TStateCoTeachingLinkUserCount: 'TStateCoTeachingLinkUserCount',
  // 双师学生没连麦时的布局
  TStateCoTeachingStudentLayout: 'TStateCoTeachingStudentLayout',
  // 双师课堂麦克风设置的音量, 用于修复windows上trtc无法正确获取麦克风音量问题。
  TStateCoMicVolume: 'TStateCoMicVolume',
  // 屏幕共享授权
  TStateScreenPermissionGranted: 'TStateScreenPermissionGranted',
  // 直播课老师正在邀请当前学生上麦
  TStateLiveTeacherInvitingMe: 'TStateLiveTeacherInvitingMe',
  // 是否屏蔽私人课件
  TStateHidePrivateDocument: 'TStateHidePrivateDocument',
  // 是否显示辅助摄像头
  TStateShowSubCameraComponent: 'TStateShowSubCameraComponent',
  // 辅助摄像头镜像
  TStateSubCameraMirror: 'TStateSubCameraMirror',
  // 布局缩放比例
  TStateWebScale: 'TStateWebScale',
  // 麦克风采集音量
  TStateMicVolume: 'TStateMicVolume',
  // 是否去除下课按钮
  TStateNoEndClass: 'TStateNoEndClass',
  // 大班课自定义布局
  TStateBigClassLayout: 'TStateBigClassLayout',
  /*
   ******************************************************** 事件 *****************************************************
   */
  // 添加课件上传事件
  TEventAddDocumentUpload: 'TEventAddDocumentUpload',
  // 加载课件
  TEventLoadDocument: 'TEventLoadDocument',
  // 关闭屏幕共享
  TEventShutDownScreenShare: 'TEventShutDownScreenShare',
  // 创建视频组件
  TEventAddVideoComponent: 'TEventAddVideoComponent',
  // 移除视频组件
  TEventRemoveVideoComponent: 'TEventRemoveVideoComponent',
  // toast消息
  TEventToastMessage: 'TEventToastMessage',
  // 布局更新
  TEventUpdateLayout: 'TEventUpdateLayout',
  // 布局更新
  TEventUpdateLayoutAfterJoinClass: 'TEventUpdateLayoutAfterJoinClass',

  /*
   ******************************************************** 常量 *****************************************************
   */
  TConstantVideoWidth: 160,
  TConstantVideoHeight: 90,
  // 大教学 中的布局模式
  TConstantCollegeLayoutNormal: 'normal',      // 普通布局
  TConstantCollegeLayoutCombo: 'combo',        // 组合布局
  TConstantCollegeLayoutWall: 'wall',          // 宫格布局
  TConstantCollegeTaskLayout: 'college-layout',   // 大教学布局任务
  TConstantCollegeTaskEnableMicRequest: 'college-enable-mic-request',   // 大教学是否允许学生自主解除禁音任务

  TConstantVideoWallTaskId: 'video-wall-task',  // 视频墙任务id
  TConstantCoTeachingLinkTaskId: 'coteaching-link-task', // 双师连麦任务id
  TConstantLiveInviteTaskId: 'live-invite-task', // 直播课，老师邀请学生上台任务id
  // 直播课，老师邀请学生上台状态
  TConstantLiveInviteCreate: 'live-invite-create', // 直播课，老师邀请学生开始状态,
  TConstantLiveInviteApprove: 'live-invite-approve', // 直播课，老师邀请学生，学生同意开始状态,
  TConstantLiveInviteReject: 'live-invite-reject', // 直播课，老师邀请学生，学生拒绝状态,
  TConstantLiveInviteCancel: 'live-invite-cancel', // 直播课，老师邀请学生，学生超时未处理,
  TConstantCTVideoList: 'ct-video-list',  // 双师课实际推流用户列表
  TConstantCoTeachingMuteAll: 'coteaching-mute-all', // 双师课堂禁麦全体事件
  TConstantVideoWallPage: 'video-wall-page', // 视频墙页数
  /*
   ************************************************ 页面初始化配置 *****************************************************
   */

  /**
   * ******************************** 功能开关 ************************************************
  */
  TFunctionDeviceDetect: true, // 设备检测开启
  TEffectsEnabled: true, // 开启特效功能
  TEffectsBeauty: true, // 是否开启美颜(磨皮,美白，瘦脸，大眼，削下巴)
  TEffectsMakeup: true, // 是否开启美妆特效(口红，腮红，画眉毛)
  TEffectsFilter: true, // 是否开启滤镜
  TEffectsSticker: true, // 是否开启贴纸
  TEffectsVirtualBackground: true, // 虚拟背景
  TEffectsVirtualFace: true, // 虚拟表情
};

