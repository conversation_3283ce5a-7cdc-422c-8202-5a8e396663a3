import i18next from 'i18next';
const definition = {
  NetworkMap: {
    0: 'unknown',
    1: 'excellent',
    2: 'good',
    3: 'poor',
    4: 'bad',
    5: 'vbad',
    6: 'break',
  },
  GetNetworkTips(status) {
    if (status === 'vbad') {
      return {
        icon: 'poor',
        label: i18next.t('极差'),
        tips: i18next.t('网络状况很差'),
        class: 'error',
      };
    } if (status === 'bad') {
      return {
        icon: 'poor',
        label: i18next.t('差'),
        tips: i18next.t('网络状况较差'),
        class: 'error',
      };
    } if (status === 'poor') {
      return {
        icon: 'poor',
        label: i18next.t('中'),
        tips: i18next.t('网络状况一般'),
        class: 'error',
      };
    } if (status === 'good') {
      return {
        icon: 'good',
        label: i18next.t('良'),
        tips: i18next.t('网络状况正常'),
        class: 'success',
      };
    } if (status === 'excellent') {
      return {
        icon: 'good',
        label: i18next.t('优'),
        tips: i18next.t('网络状况正常'),
        class: 'success',
      };
    } if (status === 'break') {
      return {
        icon: 'break',
        label: i18next.t('网络异常'),
        tips: i18next.t('当前无网络，请检查网络设置'),
        class: 'error',
      };
    }
    return {
      icon: 'break',
      label: i18next.t('未知'),
      tips: i18next.t('未知'),
      class: 'error',
    };
  },
};

export const {
  GetNetworkTips,
  NetworkMap,
} = definition;
