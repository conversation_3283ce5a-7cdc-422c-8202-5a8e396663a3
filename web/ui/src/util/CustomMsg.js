import i18next from 'i18next';
import DeviceUtil from './DeviceUtil';

function processUICtrl(msg) {
  // 老师/助教收到消息后，同步学生授权状态
  if (!TCIC.SDK.instance.isTeacherOrAssistant()) {
    return;
  }
  const info = JSON.parse(msg.data);
  console.log('[CustomMsg] processUICtrl', info);
  let deviceType;
  let toastMsg;
  switch (info.action) {
    case 'open_camera':
      // 学生同意打开摄像头，正在打开设备，成功会更新permission，老师侧不用处理
      break;
    case 'camera_fail':
      deviceType = 'camera';
      toastMsg = `${info.nick}${info.reason}`;
      break;
    case 'stay_camera':
      deviceType = 'camera';
      toastMsg = i18next.t('{{arg_0}} 选择保持视频关闭', { arg_0: info.nick });
      break;

    case 'open_mic':
      // 学生同意打开麦克风，正在打开设备，成功会更新permission，老师侧不用处理
      break;
    case 'mic_fail':
      deviceType = 'mic';
      toastMsg = `${info.nick}${info.reason}`;
      break;
    case 'mic_limit':
      deviceType = 'mic';
      toastMsg = i18next.t('{{arg_0}}内打开麦克风数量超过限制，出于性能考虑，请您先关闭部分{{arg_1}}的麦克风', {
        arg_0: TCIC.SDK.instance.getNameConfig().roomInfo.name,
        arg_1: TCIC.SDK.instance.getNameConfig().roleInfo.student,
      });
      break;
    case 'stay_mic':
      deviceType = 'mic';
      toastMsg = i18next.t('{{arg_0}} 选择保持静音', { arg_0: info.nick });
      break;

    default:
      // 不认识的打个warning
      if (info.action.includes('camera') || info.action.includes('mic')) {
        console.warn(`[CustomMsg] processUICtrl, unknown camera/mic action "${info.action}", may change TStateLoadingXxxState`, info);
      }
      return;
  }

  const needShowToast = !info.permissionOperatorId || info.permissionOperatorId === TCIC.SDK.instance.getUserId();
  if (needShowToast && toastMsg) {
    window.showToast(toastMsg);
  }

  if (deviceType) {
    const value = {
      loading: false,
      open: false,
    };
    if (typeof info.deviceState === 'number') {
      value.deviceState = DeviceUtil.getReportDeviceState(false, info.deviceStatus);
    }
    DeviceUtil.notifyDeviceLoadingEvent(info.userId, deviceType, value, `CustomMsg-${info.action}`);
  }
}

export default {
  processCustomMsg(msg) {
    if (!msg) {
      return;
    }
    switch (msg.ext) {
      case 'ui_ctrl':
        processUICtrl(msg);
        break;
    }
  },
};
