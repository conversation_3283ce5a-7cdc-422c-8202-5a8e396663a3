import Lodash from 'lodash';


function VideoWall(rect, maxCount = VideoWall.LayoutRect.length, padding = 2) {
  this.in_rect = rect;
  this.in_padding = padding;
  this.in_video_arr = [];
  this.in_lock_arr = [];
  this.in_page_size = maxCount;
  this.in_cur_page = 0;
}

VideoWall.VideoTypeMain = 'main';
VideoWall.VideoTypeSub = 'aux';

// 更新视频墙尺寸
VideoWall.prototype.updateRect = function (rect, update = true) {
  console.log(`XDBG-VideoWall::updateRect=>${JSON.stringify(rect)}`);
  this.in_rect = rect;
  update && this.inUpdateRender();
};

/* 尾部添加组件到视频墙
 * @param id 用户id
 * @param dom Dom节点
 * @param update 是否更新布局
 * @param type 视频类型
 */
VideoWall.prototype.pushVideo = function (id, dom, update = true, type = VideoWall.VideoTypeMain) {
  if (!this.inFindVideo(id, type)) {
    console.log(`XDBG-VideoWall::pushVideo=>${id}, ${update}`);
    this.in_video_arr.push({
      uid: id,
      vid: this.inGetVideoId(id, type),
      dom,
    });
    update && this.inUpdateRender();
  }
};

/* 头部添加组件到视频墙
 * @param id 用户id
 * @param dom Dom节点
 * @param update 是否更新布局
 * @param type 视频类型
 */
VideoWall.prototype.unshiftVideo = function (id, dom, update = true, type = VideoWall.VideoTypeMain) {
  if (!this.inFindVideo(id, type)) {
    console.log(`XDBG-VideoWall::unshiftVideo=>${id}, ${update}`);
    this.in_video_arr.unshift({
      uid: id,
      vid: this.inGetVideoId(id, type),
      dom,
    });
    update && this.inUpdateRender();
  }
};

/**
 * 判断组件是否存在
 * @param {string} id 用户id
 * @param {*} type 类型
 * @returns
 */
VideoWall.prototype.checkVideo = function (id, type = VideoWall.VideoTypeMain) {
  return !!this.inFindVideo(id, type);
};

/**
 * 主动刷新布局
 */
VideoWall.prototype.updateRender = function () {
  this.inUpdateRender();
  this.inUpdateRender.flush();
};

/**
 * 从视频墙移除视频组件
 * @param id 用户id
 * @param update 是否更新布局
 * @param type 视频类型
 * @return {boolean}  是否已清空
 */
VideoWall.prototype.removeVideo = function (id, update = true, type = VideoWall.VideoTypeMain) {
  console.log(`XDBG-VideoWall::removeVideo=>${id}, ${update}`);
  const videoId = this.inGetVideoId(id, type);
  this.in_video_arr = this.in_video_arr.filter(info => info.vid !== videoId);
  update && this.inUpdateRender();
  return this.in_video_arr.length === 0;
};

// 锁定用户
VideoWall.prototype.lockVideo = function (id, update = true, type = VideoWall.VideoTypeMain) {
  console.log(`XDBG-VideoWall::lockVideo=>${id}, ${update}`);
  const videoId = this.inGetVideoId(id, type);
  if (!this.in_lock_arr.includes(videoId)) {
    this.in_lock_arr.push(videoId);
    update && this.inUpdateRender();
  }
};
// 解锁用户
VideoWall.prototype.unlockVideo = function (id, update = true, type = VideoWall.VideoTypeMain) {
  const videoId = this.inGetVideoId(id, type);
  console.log(`XDBG-VideoWall::unlockVideo=>${id}, ${update}`);
  if (this.in_lock_arr.includes(videoId)) {
    this.in_lock_arr = this.in_lock_arr.filter(vid => vid !== videoId);
    update && this.inUpdateRender();
  }
};
// 设置排序方法
VideoWall.prototype.setSortFunc = function (sortFunc) {
  this.in_sort_func = sortFunc;
};

/**
 * 翻页
 */
VideoWall.prototype.canPageUp = function () {
  return this.in_cur_page > 0;
};
VideoWall.prototype.canPageDown = function () {
  return (this.in_video_arr.length - this.in_lock_arr.length) > (this.in_cur_page + 1) * (this.in_page_size - this.in_lock_arr.length);
};
VideoWall.prototype.pageUp = function () {
  if (this.canPageUp()) {
    this.in_cur_page -= 1;
  }
  this.inUpdateRender();
};
VideoWall.prototype.pageDown = function () {
  if (this.canPageDown()) {
    this.in_cur_page += 1;
  }
  this.inUpdateRender();
};
VideoWall.prototype.firstPage = function () {
  if (this.in_cur_page !== 0) {
    this.in_cur_page = 0;
    this.inUpdateRender();
  }
};
VideoWall.prototype.needScroll = function () {
  if (TCIC.SDK.instance.isPortrait() && TCIC.SDK.instance.isPortraitClass()) {
    return false;
  }
  return this.in_video_arr.length > this.in_page_size;
};
/**
 * 清空视频组件
 */
VideoWall.prototype.clearVideos = function () {
  this.in_video_arr = [];
};

/**
 * 设置更新组件位置回调
 */
VideoWall.prototype.setLayoutCallback = function (callback) {
  this.in_update_callback = callback;
};


// 更新视频墙渲染
VideoWall.prototype.inUpdateRender = Lodash.throttle(function () {
  let count = this.in_video_arr.length;
  if (count < 1) {
    return;
  }
  if (count >= this.in_page_size) {
    count = this.in_page_size;
  }
  let layoutInfo = VideoWall.LayoutRect[count - 1];

  if (TCIC.SDK.instance.isPortraitClass() && TCIC.SDK.instance.isPortrait()) {
    if (TCIC.SDK.instance.isOneOnOneClass()) {
      layoutInfo = VideoWall.LayoutRectPortraitOneOne(count);
    } else {
      layoutInfo = VideoWall.LayoutRectPortrait(count);
    }
  }
  console.log(`XDBG-VideoWall::inUpdateRender=>count: ${count}`, layoutInfo);
  const lockArr = [];
  const waitArr = [];
  this.in_video_arr.forEach((info) => {
    if (this.in_lock_arr.includes(info.vid)) {
      lockArr.push(info);
    } else {
      waitArr.push(info);
    }
  });
  const realPageSize = this.in_page_size - lockArr.length;
  // 加载锁定用户
  for (let i = 0; i < lockArr.length; i ++) {
    this.inUpdateVideoLayout(
      lockArr[i],
      layoutInfo.size,
      layoutInfo.pos[i],
      layoutInfo.type,
    );
  }
  count -= lockArr.length;
  // 处理剩余用户
  let startPos = this.in_cur_page * realPageSize;
  const emptyCount = startPos + realPageSize - waitArr.length;
  if (emptyCount > 0) {
    startPos -= emptyCount;
    if (startPos < 0) {
      startPos = 0;
    }
  }
  if (this.in_sort_func) {    // 添加排序
    waitArr.sort(this.in_sort_func);
  }
  for (let i = 0; i < waitArr.length; i++) {
    if (i >= startPos && i < startPos + count) {
      this.inUpdateVideoLayout(
        waitArr[i],
        layoutInfo.size,
        layoutInfo.pos[i - startPos + lockArr.length],
        layoutInfo.type,
      );
    } else {
      this.in_update_callback && this.in_update_callback(waitArr[i].dom, -20, -20, 16, 9);
    }
  }
}, 200, {
  leading: false,
  trailing: true,
});

// 更新视频组件位置
VideoWall.prototype.inUpdateVideoLayout = function (info, size, pos = [], type) {
  let left = this.in_rect.left + this.in_rect.width * pos[0] + this.in_padding;
  let top = this.in_rect.top + this.in_rect.height * pos[1] + this.in_padding;
  let width = this.in_rect.width * size[0] - this.in_padding;

  // 如果没有提供size[1], 表明是正方形
  const height = size[1] === undefined ? width : this.in_rect.height * size[1] - this.in_padding;
  if (TCIC.SDK.instance.isMobile() && type === 2) {
    const isOneOnOneClass = TCIC.SDK.instance.isOneOnOneClass();
    const isTeacher =  TCIC.SDK.instance.isTeacher();
    const app = document.getElementById('app');
    const appRect = app.getBoundingClientRect();
    if (isOneOnOneClass) {
      width = appRect.width * size[0] - this.in_padding;
      if (pos[0] === 0) {
        left = 5;
      }
    } else if (TCIC.SDK.instance.isPortraitClass()) {
      // 不做修改
    } else if (isTeacher) {
      width = appRect.width * size[0] - this.in_padding;
      if (pos[0] === 0) {
        left = 5;
      }
      top = this.in_rect.top + this.in_rect.height * 0.2 + this.in_padding;
    }
  }
  this.in_update_callback && this.in_update_callback(info.dom, left, top, width, height);
};

// 查找视频组件
VideoWall.prototype.inFindVideo = function (id, type = VideoWall.VideoTypeMain) {
  const videoId = this.inGetVideoId(id, type);
  return this.in_video_arr.find(info => info.vid === videoId);
};

// 获取视频唯一标识
VideoWall.prototype.inGetVideoId = function (id, type = VideoWall.VideoTypeMain) {
  return `${id}_${type}`;
};

VideoWall.LayoutRect = [
  /** 单人布局 */
  {
    size: [1, 1],
    pos: [[0, 0]],
    type: 1,
  },
  /** 双人布局 */
  {
    size: [0.5, 0.7],
    pos: [
      [0, 0.1], [0.5, 0.1],
    ],
    type: 2,
  },
  /** 三人布局 */
  {
    size: [0.5, 0.5],
    pos: [
      [0, 0], [0.5, 0],
      [0.25, 0.5],
    ],
    type: 3,
  },
  /** 四人布局 */
  {
    size: [0.5, 0.5],
    pos: [
      [0, 0], [0.5, 0],
      [0, 0.5], [0.5, 0.5],
    ],
    type: 4,
  },
  /** 五人布局 */
  {
    size: [0.33, 0.33],
    pos: [
      [0, 0.17], [0.33, 0.17], [0.66, 0.17],
      [0.17, 0.5], [0.5, 0.5],
    ],
    type: 5,
  },
  /** 六人布局 */
  {
    size: [0.33, 0.33],
    pos: [
      [0, 0.17], [0.33, 0.17], [0.66, 0.17],
      [0, 0.5], [0.33, 0.5], [0.66, 0.5],
    ],
    type: 6,
  },
  /** 七人布局 */
  {
    size: [0.33, 0.33],
    pos: [
      [0, 0], [0.33, 0], [0.66, 0],
      [0, 0.33], [0.33, 0.33], [0.66, 0.33],
      [0.33, 0.66],
    ],
    type: 7,
  },
  /** 八人布局 */
  {
    size: [0.33, 0.33],
    pos: [
      [0, 0], [0.33, 0], [0.66, 0],
      [0, 0.33], [0.33, 0.33], [0.66, 0.33],
      [0.17, 0.66], [0.5, 0.66],
    ],
    type: 8,
  },
  /** 九人布局 */
  {
    size: [0.33, 0.33],
    pos: [
      [0, 0], [0.33, 0], [0.66, 0],
      [0, 0.33], [0.33, 0.33], [0.66, 0.33],
      [0, 0.66], [0.33, 0.66], [0.66, 0.66],
    ],
    type: 9,
  },
  /** 十人布局 */
  {
    size: [0.25, 0.25],
    pos: [
      [0, 0.12], [0.25, 0.12], [0.5, 0.12], [0.75, 0.12],
      [0, 0.37], [0.25, 0.37], [0.5, 0.37], [0.75, 0.37],
      [0.25, 0.62], [0.5, 0.62],
    ],
    type: 10,
  },
  /** 十一人布局 */
  {
    size: [0.25, 0.25],
    pos: [
      [0, 0.12], [0.25, 0.12], [0.5, 0.12], [0.75, 0.12],
      [0, 0.37], [0.25, 0.37], [0.5, 0.37], [0.75, 0.37],
      [0.12, 0.62], [0.37, 0.62], [0.62, 0.62],
    ],
    type: 11,
  },
  /** 十二人布局 */
  {
    size: [0.25, 0.25],
    pos: [
      [0, 0.12], [0.25, 0.12], [0.5, 0.12], [0.75, 0.12],
      [0, 0.37], [0.25, 0.37], [0.5, 0.37], [0.75, 0.37],
      [0, 0.62], [0.25, 0.62], [0.5, 0.62], [0.75, 0.62],
    ],
    type: 12,
  },
  /** 十三人布局 */
  {
    size: [0.25, 0.25],
    pos: [
      [0, 0], [0.25, 0], [0.5, 0], [0.75, 0],
      [0, 0.25], [0.25, 0.25], [0.5, 0.25], [0.75, 0.25],
      [0, 0.5], [0.25, 0.5], [0.5, 0.5], [0.75, 0.5],
      [0.37, 0.75],
    ],
    type: 13,
  },
  /** 十四人布局 */
  {
    size: [0.25, 0.25],
    pos: [
      [0, 0], [0.25, 0], [0.5, 0], [0.75, 0],
      [0, 0.25], [0.25, 0.25], [0.5, 0.25], [0.75, 0.25],
      [0, 0.5], [0.25, 0.5], [0.5, 0.5], [0.75, 0.5],
      [0.25, 0.75], [0.5, 0.75],
    ],
    type: 14,
  },
  /** 十五人布局 */
  {
    size: [0.25, 0.25],
    pos: [
      [0, 0], [0.25, 0], [0.5, 0], [0.75, 0],
      [0, 0.25], [0.25, 0.25], [0.5, 0.25], [0.75, 0.25],
      [0, 0.5], [0.25, 0.5], [0.5, 0.5], [0.75, 0.5],
      [0.12, 0.75], [0.37, 0.75], [0.62, 0.75],
    ],
    type: 15,
  },
  /** 十六人布局 */
  {
    size: [0.25, 0.25],
    pos: [
      [0, 0], [0.25, 0], [0.5, 0], [0.75, 0],
      [0, 0.25], [0.25, 0.25], [0.5, 0.25], [0.75, 0.25],
      [0, 0.5], [0.25, 0.5], [0.5, 0.5], [0.75, 0.5],
      [0, 0.75], [0.25, 0.75], [0.5, 0.75], [0.75, 0.75],
    ],
    type: 16,
  },
  /** 十七人布局 */
  {
    size: [0.2, 0.2],
    pos: [
      [0, 0.1], [0.2, 0.1], [0.4, 0.1], [0.6, 0.1], [0.8, 0.1],
      [0, 0.3], [0.2, 0.3], [0.4, 0.3], [0.6, 0.3], [0.8, 0.3],
      [0, 0.5], [0.2, 0.5], [0.4, 0.5], [0.6, 0.5], [0.8, 0.5],
      [0.3, 0.7], [0.5, 0.7],
    ],
    type: 17,
  },
];

VideoWall.LayoutRectPortrait = (count) => {
  if (count < 3) {
    return [
      /** 单人布局 */
      {
        size: [1, 1],
        pos: [[0, 0]],
        type: 1,
      },
      /** 双人布局 */
      {
        size: [1, 0.5],
        pos: [
          [0, 0], [0, 0.5],
        ],
        type: 2,
      }][count - 1];
  }
  return {
    size: [0.5],
    type: count,
    pos: [],
  };
};

VideoWall.LayoutRectPortraitOneOne = (count) => {
  return [
    /** 单人布局 */
    {
      size: [1, 1],
      pos: [[0, 0]],
      type: 1,
    },
    /** 双人布局 */
    {
      size: [1, 1],
      pos: [
        [0, 0], [0, 0],
      ],
      type: 2,
    }][count - 1];
};


export default VideoWall;
