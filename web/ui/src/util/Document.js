import i18next from 'i18next';
const TRANSCODE_STATE = {
  // 0为无需转码，1为正在转码，2为转码失败，3为转码成功
  NO: 0,
  ING: 1,
  FAIL: 2,
  SUCCESS: 3,
};
const TRANSCODE_ERR_MSG = {
  '-1': i18next.t('PPT下载的url格式错误 请检查PPT下载URL'),
  '-2': i18next.t('PPT打开过程中发生未知错误 请检查PPT格式是否正确'),
  '-3': i18next.t('PPT打开超时 请检查 PPT格式是否正确'),
  '-4': i18next.t('PPT打开无响应 请检查 PPT格式是否正确'),
  '-5': i18next.t('PPT文件被加密 不支持转换已加密的PPT'),
  '-6': i18next.t('未知PPT格式 请检查 PPT格式是否正确'),
  '-7': i18next.t('PPT打开时发生异常 请检查 PPT格式是否正确'),
  '-8': i18next.t('PPT为只读格式 不支持只读PPT，请检查 PPT格式是否正确'),
  '-9': i18next.t('PPT转码失败 请检查 PPT格式是否正确'),
  '-10': i18next.t('PPTX格式解析错误 请检查 PPT格式是否正确'),
  '-11': i18next.t('PPT下载失败，未知错误 请检查PPT下载 URL 是否有效'),
  '-12': i18next.t('h5上传失败 请联系客服人员或重试'),
  '-13': i18next.t('PPT转换服务未加载 请联系客服人员'),
  '-15': i18next.t('PPT转码文件生成失败 请联系客服人员或重试'),
  '-16': i18next.t('PPT转换模式异常 PPT格式不支持或联系客服人员'),
  '-17': i18next.t('PPT超过最大的转换页数限制（目前为500） 不支持转换超过500页的PPT'),
  // '-18': 'PPT转换失败，错误未知PPT格式不支持或联系客服人员',
  '-19': i18next.t('PPT被加密 不支持加密的PPT'),
  '-20': i18next.t('PPT未知属性错误 请检查 PPT格式是否正确'),
  '-21': i18next.t('PPT检测属性超时 请检查 PPT格式是否正确'),
  '-22': i18next.t('PPT转换异常 请检查 PPT格式是否正确或重试'),
  '-23': i18next.t('PPT下载链接含有非法字符 请检查PPT下载链接是否正确'),
  '-24': i18next.t('PPT下载链接打开失败 请检查PPT下载地址是否有效'),
  '-25': i18next.t('PPT下载链接打开超时 请检查PPT下载链接是否有效或下载服务器网络状况'),
  '-26': i18next.t('PPT下载数据失败 请检查PPT下载服务器网络状况'),
  '-27': i18next.t('PPT下载数据超时 请检查PPT下载服务器网络状况或是否PPT过大，导致下载超时'),
  '-101': i18next.t('task info文件读取失败 请联系维护人员'),
  '-102': i18next.t('task info格式不正确 请联系维护人员'),
  '-103': i18next.t('task info的error_code不等于0 请联系维护人员'),
  '-104': i18next.t('task 的url为空 请联系维护人员'),
  '-105': i18next.t('task 的下载url错误 请检查转码文件下载链接'),
  '-106': i18next.t('不支持的文件名后缀 请检查文件名后缀名'),
  '-107': i18next.t('文件下载失败，未知错误 请检查下载链接地址是否有效'),
  '-108': i18next.t('下载链接非法 请检查转码文件下载链接'),
  '-109': i18next.t('下载链接打开失败 请检查下载链接地址是否有效'),
  '-110': i18next.t('下载链接打开超时 请检查下载链接地址是否有效'),
  '-111': i18next.t('下载失败 请检查下载链接地址是否有效'),
  '-112': i18next.t('下载超时 请检查下载链接带宽'),
  '-113': i18next.t('本地临时文件夹创建失败 请联系维护人员'),
  '-114': i18next.t('文件上传转码后台失败 请联系维护人员'),
  '-115': i18next.t('cos 请求转码失败 文件格式不支持'),
  '-116': i18next.t('cos 请求转码失败 文件格式不支持'),
  '-117': i18next.t('cos 转码失败超时 文件格式不支持'),
  '-118': i18next.t('cos 转码过程中失败 文件格式不支持'),
  '-119': i18next.t('上传转码后的文件，到cdn cos失败 请联系维护人员或重试'),
  '-120': i18next.t('cos转码，未知错误 文件格式不支持'),
  '-121': i18next.t('超过最大页数 不支持超过500页的文件转码'),
  '-122': i18next.t('转码服务器打开本地文件失败，请联系维护人员或重试'),
  '-123': i18next.t('转码服务器写本地文件失败，请联系维护人员或重试'),
  '-124': i18next.t('转码服务器文件内容为空，请联系维护人员或重试'),
  '-125': i18next.t('获取图片分辨率失败，请联系维护人员或重试'),
  '-126': i18next.t('pdf打开失败,pdf文件格式错误或pdf加密，不支持转码'),
  '-127': i18next.t('pdf文件加密,不支持加密pdf文件转码'),
  '-128': i18next.t('获取pdf页数失败，pdf文件格式不支持'),
  '-129': i18next.t('pdf转码未知错误 ，pdf文件格式不支持'),
  '-130': i18next.t('office转码本地错误；包括转码任务格式，任务完成格式等，请联系维护人员或重试'),
  '-131': i18next.t('office文件加密 ，文件格式不支持'),
  '-132': i18next.t('office文件未知属性错误，文件格式不支持'),
  '-133': i18next.t('office文件打开超时，文件格式不支持或重试'),
  '-134': i18next.t('office文件打开异常，文件格式不支持'),
  '-135': i18next.t('office文件转存异常，文件格式不支持'),
  '-136': i18next.t('office转换taskid对应失败，请联系维护人员或重试'),
  '-137': i18next.t('获取office文件分辨率失败，请联系维护人员或重试'),
  '-138': i18next.t('转码后的本地文件错误，请联系维护人员或重试'),
  '-139': i18next.t('转码丢失了页数，请联系维护人员或重试'),
};

let _lastChangePageTime = +new Date();

export default {
  getTranscodeError(info) {
    const matches = info.transcodeInfo.match(/^code: (.*), msg: (.*)/);
    if (matches && matches.length >= 2) {
      const code = Number(matches[1]);
      const msg = matches[2];
      const page = msg.match(/\[(.+?)]/g) ? msg.match(/\[(.+?)]/g)[0] : '*';
      switch (code) {
        case -14:
          return i18next.t('PPT转换发生错误！第{{page}}页可能含有不支持的元素或动画；请修改该页的元素或动画', { page });
        case -18:
          return i18next.t('PPT转码，在 第{{page}}页出现错误；请修改该页不必要的动画或元素', { page });
        case -29:
          return i18next.t('PPT转换发生错误！第{{page}}页可能含有不支持的元素或动画；请修改该页的元素或动画', { page });
        case -31:
          return i18next.t('PPT中，第{{page}}页中可能含有jpeg病毒，请修改该页中的jpeg图片', { page });
        default:
          return TRANSCODE_ERR_MSG[code] || msg || '';
      }
    }
  },
  intervalChangePage(teduBoard, type) {
    const now = + new Date();
    if (now - _lastChangePageTime < 400) {
      console.log('intervalChangePage limit ');
      return;
    }
    _lastChangePageTime = now;
    if (type === 'prev') {
      teduBoard.prevStep();
    } else if (type === 'next') {
      teduBoard.nextStep();
    }
  },
  addSnapshotMark(reason) {
    if (TCIC.SDK.instance.isFeatureAvailable('WhiteBoardSnapShot')) {
      const teduBoard = TCIC.SDK.instance.getBoard();
      TCIC.SDK.instance.reportLog('addSnapshotMark', reason);
      teduBoard.addSnapshotMark();
      return new Promise(resolve => setTimeout(() => resolve(), 300));
    }
  },
};
