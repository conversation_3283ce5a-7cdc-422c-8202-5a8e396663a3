export default {
  // 音量放大器
  // @volume 音量大小
  // @denominator 分母，以此为最大值
  // 利用sin函数对音量做非线性变换，让低音量的时候变换幅度更明显，做归一化处理，音量放大2倍显示
  amplifier(volume, denominator = 0) {
    if (volume === 0) {
      return 0;
    }
    const y = Math.round((Math.log(volume + 1) / Math.log(10001)) * denominator);

    // 确保输出在0-22之间
    return Math.max(0, Math.min(denominator, y));
  },
  mediaDevices() {
    // 老的浏览器可能根本没有实现 mediaDevices，所以我们可以先设置一个空的对象
    if (navigator.mediaDevices === undefined) {
      navigator.mediaDevices = {};
    }
    // 一些浏览器部分支持 mediaDevices。我们不能直接给对象设置 getUserMedia
    // 因为这样可能会覆盖已有的属性。这里我们只会在没有 getUserMedia 属性的时候添加它。
    if (navigator.mediaDevices.getUserMedia === undefined) {
      navigator.mediaDevices.getUserMedia = function (constraints) {
        // 首先，如果有 getUserMedia 的话，就获得它
        const getUserMedia = navigator.webkitGetUserMedia || navigator.mozGetUserMedia;

        // 一些浏览器根本没实现它 - 那么就返回一个 error 到 promise 的 reject 来保持一个统一的接口
        if (!getUserMedia) {
          return Promise.reject(new Error('getUserMedia is not implemented in this browser'));
        }

        // 否则，为老的 navigator.getUserMedia 方法包裹一个 Promise
        return new Promise((resolve, reject) => {
          getUserMedia.call(navigator, constraints, resolve, reject);
        });
      };
    }
    // 返回mediaDevices 方法
    return navigator.mediaDevices;
  },
};
