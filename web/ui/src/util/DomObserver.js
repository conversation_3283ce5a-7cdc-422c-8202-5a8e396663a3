export default class DomObserver {
     mutationObserver = null;
     domInterval = null;
     targetDom = null;

     init({
        targetDomClassName,
        domChangeCallback,
        useDomCheck,
    }) {
        this.targetDom = document.querySelector(targetDomClassName);

        if (!this.targetDom) {
            console.error('目标 DOM 不存在');
            return;
        }

        if (useDomCheck) {
            this.domInterval = setInterval(() => {
                const isVisible = this.areChildrenVisible(this.targetDom);
                const domExisted = document.querySelector(targetDomClassName);
                if (!isVisible || !domExisted) {
                    domChangeCallback();
                }
            }, 1000);
        } else {
            const config = { attributeFilter: ['style', 'class'], childList: true, subtree: true };

            const callback = (mutationsList) => {
                for (const mutation of mutationsList) {
                    const changeList = ['childList', 'attributes'];
                    if (changeList.includes(mutation.type)) {
                        domChangeCallback();
                    }
                }
            };

            this.mutationObserver = new MutationObserver(callback);
            this.mutationObserver.observe(this.targetDom, config);

            this.domInterval = setInterval(() => {
                if (!document.querySelector(targetDomClassName)) {
                    domChangeCallback();
                }
            }, 1000);
        }
    }

    destroy() {
        clearInterval(this.domInterval);
        if (this.mutationObserver) {
            this.mutationObserver.disconnect();
            this.mutationObserver = null;
        }
    }

    areChildrenVisible(element) {
        if (!element) {
            return true;
        }

        if (element.getAttribute('role') === 'tooltip' && element.id.startsWith('el-popover')) {
            return true;
        }

        const style = window.getComputedStyle(element);
        if (style.display === 'none' || style.visibility === 'hidden') {
            return false;
        }

        for (let i = 0; i < element.children.length; i++) {
            if (!this.areChildrenVisible(element.children[i])) {
                return false;
            }
        }

        return true;
    }
}
