

window.ResourcesLoadSuccessForTCIC = [];



window.addEventListener("message",function(e){
    if(e.data == "getTCICReportData"){
        const performanceEntries = performance.getEntriesByType('resource');
        performanceEntries.forEach((entry) => {
            if (entry.responseStatus !== 200 && entry.responseStatus !== 204) {
                // 失败的资源不处理
            } else {
                if (entry.initiatorType != 'xmlhttprequest') {
                    window.ResourcesLoadSuccessForTCIC.push({
                        name: entry.name,
                        status: entry.responseStatus,
                        startTime: entry.startTime, // 加载开始时间（毫秒）
                        duration: entry.duration, // 加载持续时间（毫秒）
                        transferSize: entry.transferSize,  // 传输的字节数
                        encodedBodySize: entry.encodedBodySize,// 编码后的主体大小（字节）
                        decodedBodySize: entry.decodedBodySize,// 解码后的主体大小（字节）
                    })
                }
            }
        });
        window.parent.postMessage(getTCICReportData());            
    }
})
function getTCICReportData() {
    const res = {
        "getTCICReportDataAPIVersion" : "1.0.0",
    };
    const htmls = document.getElementsByTagName("html");
    if (htmls.length) {
        res["innerHtml"] = htmls[0].innerHTML;
    }
    if (window.ResourcesLoadSuccessForTCIC.length) {
        res['resourceLoadInfo'] = window.ResourcesLoadSuccessForTCIC;
    }
    return res;
}