/**
 *  ==========================================================================
 * @tencent/aegis-web-sdk@1.43.5 (c) 2024 TencentCloud Real User Monitoring.
 * Author pumpkincai.
 * Last Release Time Fri Jan 05 2024 11:43:38 GMT+0800 (GMT+08:00).
 * Released under the MIT License.
 * Thanks for supporting RUM & Aegis!
 * ==========================================================================
 **/
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).Aegis=t()}(this,function(){"use strict";var q,j;function _(e){this.name="__st"+(1e9*Math.random()>>>0)+q+"__",null!=e&&e.forEach(this.add,this),q+=1}Array.prototype.find||Object.defineProperty(Array.prototype,"find",{configurable:!0,writable:!0,value:function(e){if(null===this)throw new TypeError('"this" is null or not defined');var t=Object(this),n=t.length>>>0;if("function"!=typeof e)throw new TypeError("predicate must be a function");for(var r=arguments[1],o=0;o<n;){var i=t[o];if(e.call(r,i,o,t))return i;o+=1}}}),window.WeakSet||(q=Date.now()%1e9,_.prototype.add=function(e){var t=this.name;return e[t]||Object.defineProperty(e,t,{value:!0,writable:!0}),this},_.prototype.delete=function(e){return!!e[this.name]&&!(e[this.name]=void 0)},_.prototype.has=function(e){return!!e[this.name]},j=_,Object.defineProperty(window,"WeakSet",{value:function(e){return new j(e)}})),Object.assign||Object.defineProperty(Object,"assign",{enumerable:!1,configurable:!0,writable:!0,value:function(e){if(null==e)throw new TypeError("Cannot convert first argument to object");for(var t=Object(e),n=1;n<arguments.length;n++)if(null!=(r=arguments[n]))for(var r=Object(r),o=Object.keys(Object(r)),i=0,a=o.length;i<a;i++){var s=o[i],c=Object.getOwnPropertyDescriptor(r,s);null!=c&&c.enumerable&&(t[s]=r[s])}return t}});var D=function(e,t){return(D=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},l=function(){return(l=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function u(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;for(var r=Array(e),o=0,t=0;t<n;t++)for(var i=arguments[t],a=0,s=i.length;a<s;a++,o++)r[o]=i[a];return r}var M=/_?t(\d)?(imestamp)?=\d+&?/g,B=["aegis.qq.com","tamaegis.com","/aegis-sdk","rumt-","/flog.core.min.js","pingfore.qq.com","pingfore.tencent.com","zhiyan.tencent-cloud.net","h.trace.qq.com","btrace.qq.com","beacon.qq.com","dmplog.qq.com","qq.com/report","svibeacon.onezapp.com","cube.weixinbridge.com","doubleclick.net","pcmgrmonitor.3g.qq.com","tdm.qq.com","report.qqweb.qq.com","tpstelemetry.tencent.com","insight.cloud.tencent.com","facebook.com","facebook.net","google","yahoo.com","twitter.com","ga-audiences","report.idqqimg.com","arms-retcode.aliyuncs.com","px.effirst.com","sentry","baidu.com","hot-update.json","u.c.b.r.o.w.s.e.r","report.url.cn","sockjs-node","m3u8"],F=["ResizeObserver loop limit exceeded","ResizeObserver loop completed","Failed to execute 'transaction'","window.indexedDB.deleteDatabase is not a function"],W=["ext1","ext2","ext3","level","trace","tag","seq","code"],G=["static","fetch"],b="unknown",X=(e.prototype.indexOf=function(e,t){for(var n=0;n<e.length;n++)if(e[n].callback===t)return n;return-1},e.prototype.on=function(e,t,n){var r;if(void 0===n&&(n=0),this)return(r=this.eventsList[e])||(this.eventsList[e]=[],r=this.eventsList[e]),-1===this.indexOf(r,t)&&r.push({name:e,type:n||0,callback:t}),this},e.prototype.one=function(e,t){this.on(e,t,1)},e.prototype.remove=function(e,t){if(this){var n=this.eventsList[e];if(n){if(t)return n.length&&(t=this.indexOf(n,t),n.splice(t,1)),this;try{delete this.eventsList[e]}catch(e){}}return null}},e.prototype.clear=function(){this.eventsList={}},e),V={generateTraceId:J(16),generateSpanId:J(8)},$=Array(32);function e(){var a=this;this.emit=function(e,t){if(a){var n;if(null!=(r=a.eventsList[e])&&r.length)for(var r=r.slice(),o=0;o<r.length;o++){n=r[o];try{var i=n.callback.apply(a,[t]);if(1===n.type&&a.remove(e,n.callback),!1===i)break}catch(e){throw e}}return a}},this.eventsList={}}function J(t){return function(){for(var e=0;e<2*t;e++)$[e]=Math.floor(16*Math.random())+48,58<=$[e]&&($[e]+=39);return String.fromCharCode.apply(null,$.slice(0,2*t))}}function K(){return([1e7]+1e3+4e3+8e3+1e11).replace(/[018]/g,function(e){return(e^(16*Math.random()&15)>>e/4).toString(16)})}function z(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}function Q(e){for(var t,n,r,o="",i=0,a=(e=String(e)).length%3;i<e.length;){if(255<(t=e.charCodeAt(i++))||255<(n=e.charCodeAt(i++))||255<(r=e.charCodeAt(i++)))throw new TypeError("Failed to execute 'btoa': The string to be encoded contains characters outside of the Latin1 range.");o+=s.charAt((t=t<<16|n<<8|r)>>18&63)+s.charAt(t>>12&63)+s.charAt(t>>6&63)+s.charAt(63&t)}return a?o.slice(0,a-3)+"===".substring(a):o}function Y(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!ee.test(e))throw new TypeError("Failed to execute 'atob': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,r,o="",i=0;i<e.length;)t=s.indexOf(e.charAt(i++))<<18|s.indexOf(e.charAt(i++))<<12|(n=s.indexOf(e.charAt(i++)))<<6|(r=s.indexOf(e.charAt(i++))),o+=64===n?String.fromCharCode(t>>16&255):64===r?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return o}function Z(e){var t,n="";return n="object"==typeof e&&(t=(e=function(e,t){for(var n=0;n<t.length;n++){var r=t[n],o=e[r]||"function"==typeof e.get&&e.get(r);if(o)return[r,o]}return["",""]}(e,Object.keys(ne)))[0],e=e[1],t)?ne[t](e):n}var n,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",ee=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,te=(t.prototype.generate=function(e,t,n){if(void 0===t&&(t={}),this.url=e,!this.isUrlIgnored()&&this.isUrlInTraceUrls()&&this.traceType){switch(this.traceType){case"traceparent":this.traceId=this.createTraceparent();break;case"b3":this.traceId=this.createB3();break;case"sw8":this.traceId=this.createSw8(n);break;case"sentry-trace":this.traceId=this.createSentryTrace();break;default:return console.warn("this trace key "+this.traceType+" is not supported"),void(this.traceId="")}return t[this.traceType]&&(this.traceId=t[this.traceType]),{name:this.traceType,value:this.traceId}}},t.prototype.createTraceparent=function(){var e=V.generateSpanId();return"00-"+V.generateTraceId()+"-"+e+"-0"+Number(1).toString(16)},t.prototype.createB3=function(){var e=V.generateSpanId();return V.generateTraceId()+"-"+e+"-1"},t.prototype.createSw8=function(e){var t="function"==typeof btoa?btoa:Q,e=e||{},n=e.host,n=void 0===n?"":n,e=e.pathname,e=void 0===e?"":e,r=z(),o=z();return"1-"+String(t(o))+"-"+String(t(r))+"-1-"+String(t("aegis"))+"-"+String(t("1.43.5"))+"-"+String(t(encodeURI(e)))+"-"+String(t(n))},t.prototype.createSentryTrace=function(){var e=K().substring(16);return K()+"-"+e+"-1"},t.prototype.isUrlIgnored=function(){if(Array.isArray(this.ignoreUrls)&&0!==this.ignoreUrls.length)for(var e=0,t=this.ignoreUrls;e<t.length;e++){var n=t[e];if(this.urlMatches(this.url,n))return!0}return!1},t.prototype.isUrlInTraceUrls=function(){if(!this.urls)return!0;if(Array.isArray(this.urls)){if(0===this.urls.length)return!1;for(var e=0,t=this.urls;e<t.length;e++){var n=t[e];if(this.urlMatches(this.url,n))return!0}}return!1},t.prototype.urlMatches=function(e,t){return"string"==typeof t?e===t:!!e.match(t)},t),ne={sw8:function(e){var t="function"==typeof atob?atob:Y,e=e.split("-")[1];return e?t(e):""},traceparent:function(e){return e.split("-")[1]},b3:function(e){return e.split("-")[0]},"sentry-trace":function(e){return e.split("-")[0]}},re=function(e){if(!e||0===e.length)return"{}";e=Array.isArray(e)?e:[e];var t=Object.keys(e[0]),n={};return t.forEach(function(t){n[t]=e.map(function(e){return e[t]})}),n.count=e.length,be(n)};function t(e,t,n){void 0===n&&(n=null),this.traceType=e,this.ignoreUrls=t,this.urls=n}(o=n=n||{})[o.number=-1]="number",o.string="";function oe(e,t){return"number"==typeof e||"string"==typeof e?e:t?n.string:n.number}function E(e,t){return"string"==typeof e?e.split("?")[t?1:0]||"":e}function ie(e,t){return void 0===t&&(t=2048),String(e).replace(M,"").slice(0,t)}function ae(e){return"string"==typeof e&&/^\//.test(e)?"https:"===(null===location||void 0===location?void 0:location.protocol):/^https/.test(e)}function se(e,t,n){var r,o,i,a;try{if("function"==typeof(null==t?void 0:t.retCodeHandler))return i=(o=t.retCodeHandler(e,null==n?void 0:n.url,null==n?void 0:n.ctx,null==n?void 0:n.payload)||{}).code,a=o.isErr,{code:void 0===i?b:i,isErr:a};if(!(e="string"==typeof e?JSON.parse(e):e))return{code:b,isErr:!1};"function"==typeof(null==(r=null==t?void 0:t.ret)?void 0:r.join)&&(ye=[].concat(t.ret.map(function(e){return e.toLowerCase()})));var s=Object.getOwnPropertyNames(e).filter(function(e){return-1!==ye.indexOf(e.toLowerCase())});return s.length?{code:""+(i="未知"!==(i=e[s[0]])&&""!==i?i:b),isErr:0!==i&&"0"!==i&&i!==b}:{code:b,isErr:!1}}catch(e){return{code:b,isErr:!1}}}function ce(e,t,n){try{var r="function"==typeof t?t(e,null==n?void 0:n.url)||"":e;return Ee(r).slice(0,102400)}catch(e){return""}}function R(t,e){return"string"!=typeof t||!t||e&&-1<t.indexOf(e)||Te.test(t)||B.some(function(e){return-1<t.indexOf(e)})}function ue(n,r){var o,i=[],a=n.config;return n.lifeCycle.on("destroy",function(){i.length=0}),function(e,t){Array.isArray(e)?i=i.concat(e):i.push(e),r&&i.length>=r||n.sendNow&&0<i.length?(i=Se(i),t(i.splice(0,i.length)),o&&clearTimeout(o)):(o&&clearTimeout(o),o=setTimeout(function(){o=null,0<(i=Se(i)).length&&t(i.splice(0,i.length))},a.delay))}}function le(e,t){return Array.isArray(e)?t(e.map(function(e){return t=l(l({},e),{msg:"string"==typeof e.msg?e.msg:[].concat(e.msg).map(y).join(" ")}),W.forEach(function(e){t[e]||delete t[e]}),t;var t})):t([l(l({},e),{msg:"string"==typeof e.msg?e.msg:y(e.msg)})])}function fe(c,u){return function(e,t){var n,r,o,i=Array.isArray(e),a=i?e:[e],s=(c.lifeCycle.emit("beforeRequest",e),c.config.beforeRequest);(a="function"==typeof s?a.map(function(t){try{var e=s({logs:t,logType:u});return(null==e?void 0:e.logType)===u&&null!=e&&e.logs?e.logs:!1!==e&&t}catch(e){return t}}).filter(function(e){return!1!==e}):a).length&&(n=a,e=W,!Array.isArray(n)||n.length<=1||(r=[],o=[],!(o="string"==typeof e?[e]:e))||o.length<=0||(o.forEach(function(t){n.forEach(function(e){null!=e&&e[t]&&r.push(t)})}),0<r.length&&(n=n.map(function(e){var t={};return r.forEach(function(e){t[e]=""}),l(l({},t),e)}))),a=n,t(i?a:a[0]))}}function de(o){return function(e,t){o.lifeCycle.emit("modifyRequest",e);var n=o.config.modifyRequest;if("function"==typeof n)try{var r=n(e);"object"==typeof r&&"url"in r&&(e=r)}catch(e){console.error(e)}t(e)}}function pe(r){return function(e,t){null!=(n=r.lifeCycle)&&n.emit("afterRequest",e);var n=(r.config||{}).afterRequest;"function"==typeof n&&!1===n(e)||t(e)}}function m(n){if(n&&n.reduce&&n.length)return 1===n.length?function(e,t){n[0](e,t||Ce)}:n.reduce(function(n,r){return function(e,t){return void 0===t&&(t=Ce),n(e,function(e){return null==r?void 0:r(e,t)})}});throw new TypeError("createPipeline need at least one function param")}function he(t,n){Object.getOwnPropertyNames(t).forEach(function(e){"function"==typeof t[e]&&"constructor"!==e&&(n?n[e]="sendPipeline"===e?function(){return function(){}}:function(){}:t[e]=function(){})})}function ge(){return void 0!==window.performance&&"function"==typeof performance.getEntriesByType&&"function"==typeof performance.now}function r(e){return-1!==He.indexOf(e)}var w,v,f,me=["application/xhtml+xml","application/xml","application/pdf","application/pkcs12","application/javascript","application/x-javascript","application/ecmascript","application/vnd.mspowerpoint","application/vnd.apple.mpegurl","application/ogg","text/css","text/javascript","image","audio","video","video/mp2t"],ve=/\.(json|js|css|jpg|jpeg|png|svg|apng|webp|gif|bmp|mp4|mp3|ts|mpeg|wav|webm|ogg|flv|m3u8|ttf|woff2|otf|eot|woff|html|htm|shtml|shtm|)$/i,ye=["ret","retcode","code","errcode"],we=function(){var n=new WeakSet;return function(e,t){if(t instanceof Error)return"Error.message: "+t.message+" \n  Error.stack: "+t.stack;if("object"==typeof t&&null!==t){if(n.has(t))return"[Circular "+(e||"root")+"]";n.add(t)}return t}},y=function(e){if("string"==typeof e)return e;try{return e instanceof Error?(JSON.stringify(e,we(),4)||"undefined").replace(/"/gim,""):JSON.stringify(e,we(),4)||"undefined"}catch(e){return"error happen when aegis stringify: \n "+e.message+" \n "+e.stack}},be=function(e){if("string"==typeof e)return e;try{return JSON.stringify(e,we())||"undefined"}catch(e){return"error happen when aegis stringify: \n "+e.message+" \n "+e.stack}},Ee=function(n,r){void 0===r&&(r=3);var o,i,a,s="";return Array.isArray(n)?(s+="[",o=n.length,n.forEach(function(e,t){s=(s+="object"==typeof e&&1<r?Ee(e,r-1):Oe(e))+(t===o-1?"":",")}),s+="]"):n instanceof Object?(s="{",i=Object.keys(n),a=i.length,i.forEach(function(e,t){"object"==typeof n[e]&&1<r?s+='"'+e+'":'+Ee(n[e],r-1):s+=Re(e,n[e]),s+=t===a-1||t<a-1&&void 0===n[i[t+1]]?"":","}),s+="}"):s+=n,s},Re=function(e,t){var n=typeof t,r="";return"string"==n||"object"==n?r+='"'+e+'":"'+t+'"':"function"==typeof t?r+='"'+e+'":"function '+t.name+'"':"symbol"==typeof t?r+='"'+e+'":"symbol"':"number"!=typeof t&&"boolean"!=n||(r+='"'+e+'": '+t),r},Oe=function(e){var t=typeof e;return""+("undefined"==t||"symbol"==t||"function"==t?"null":"string"==t||"object"==t?'"'+e+'"':e)},Te=/data:(image|text|application|font)\/.*;base64/,Se=((o=w=w||{}).INFO_ALL="-1",o.API_RESPONSE="1",o.INFO="2",o.ERROR="4",o.PROMISE_ERROR="8",o.AJAX_ERROR="16",o.SCRIPT_ERROR="32",o.IMAGE_ERROR="64",o.CSS_ERROR="128",o.CONSOLE_ERROR="256",o.MEDIA_ERROR="512",o.RET_ERROR="1024",o.REPORT="2048",o.PV="4096",o.EVENT="8192",o.PAGE_NOT_FOUND_ERROR="16384",o.WEBSOCKET_ERROR="32768",o.BRIDGE_ERROR="65536",o.LAZY_LOAD_ERROR="131072",(o=v=v||{}).LOG="log",o.SPEED="speed",o.PERFORMANCE="performance",o.OFFLINE="offline",o.WHITE_LIST="whiteList",o.VITALS="vitals",o.PV="pv",o.CUSTOM_PV="customPV",o.EVENT="event",o.CUSTOM="custom",o.SDK_ERROR="sdkError",o.SET_DATA="setData",o.LOAD_PACKAGE="loadPackage",(o=f=f||{}).production="production",o.development="development",o.gray="gray",o.pre="pre",o.daily="daily",o.local="local",o.test="test",o.others="others",function(e){return e.filter(function(n,r){return"static"!==n.type||!e.find(function(e,t){return n.url===e.url&&200===n.status&&r<t})})}),xe=function(e){e.level===w.INFO_ALL&&(e.level=w.INFO)},O={},T={},Pe=function(e){return O[e]||(O[e]=setTimeout(function(){T[e]={},O[e]=null},6e4)),O[e]},Le=function(e){return(Array.isArray(e)?e:[e]).map(function(n){return Object.getOwnPropertyNames(n).reduce(function(e,t){return"ctx"!==t&&(e[t]=n[t]),e},{level:w.INFO,msg:""})})},ke=function(r){return function(e){return r.sendPipeline([function(e,n){return n({url:r.config.url||"",data:re(Le(e)),method:"post",contentType:"application/json",type:v.LOG,log:e,requestConfig:{timeout:5e3},success:function(){var t=r.config.onReport;"function"==typeof t&&e.forEach(function(e){t(e)}),"function"==typeof n&&n([])}})}],v.LOG)(e)}},Ce=function(){},o=(S.use=function(e){-1===S.installedPlugins.indexOf(e)&&e.aegisPlugin&&S.installedPlugins.push(e)},S.unuse=function(e){e=S.installedPlugins.indexOf(e);-1!==e&&S.installedPlugins.splice(e,1)},S.prototype.init=function(e){this.setConfig(e);for(var t=0;t<S.installedPlugins.length;t++)try{S.installedPlugins[t].patch(this)}catch(e){this.sendSDKError(e)}this.lifeCycle.emit("onInited")},S.prototype.destroy=function(e){void 0===e&&(e=!1);var t,n,r=S.instances.indexOf(this);-1!==r&&S.instances.splice(r,1);for(var o=S.installedPlugins.length-1;0<=o;o--)try{S.installedPlugins[o].unpatch(this)}catch(e){this.sendSDKError(e)}if(this.lifeCycle.emit("destroy"),this.lifeCycle.clear(),e)t=this,n=Object.getOwnPropertyDescriptors(t),Object.keys(n).forEach(function(e){n[e].writable&&(t[e]=null)}),Object.setPrototypeOf(this,null);else{for(var i=this;i.constructor!==Object&&he(i,this),i=Object.getPrototypeOf(i););0===S.instances.length&&(r=Object.getPrototypeOf(this).constructor,he(r),he(S))}},S.prototype.setConfig=function(e){Object.assign(this.config,e);var e=this.config,t=e.id,n=e.uin,r=e.version,o=e.ext1,i=e.ext2,a=e.ext3,s=e.aid,c=e.env,u=void 0===c?"production":c,c=e.pageUrl,e=this.bean.id!==t||this.bean.uin!==n||this.bean.aid!==s;return this.bean.id=t||"",this.bean.uin=n||"",this.bean.version=r||"1.43.5",this.bean.aid=s||"",this.bean.env=function(){switch(u){case f.production:case f.development:case f.gray:case f.pre:case f.daily:case f.local:case f.test:case f.others:return 1;default:return}}()?u:f.others,c&&this.extendBean("from",encodeURIComponent(c.slice(0,2048))),o&&this.extendBean("ext1",encodeURIComponent(o)),i&&this.extendBean("ext2",encodeURIComponent(i)),a&&this.extendBean("ext3",encodeURIComponent(a)),e&&this.lifeCycle.emit("onConfigChange",this.config),this.config},S.prototype.extendBean=function(e,t){this.bean[e]=t},S.prototype.send=function(e,o,i){var t=this;return m([de(this),function(n,r){t.request(n,function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];r({isErr:!1,result:e,logType:n.type,logs:n.log}),null!=o&&o.apply(void 0,e)},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];r({isErr:!0,result:e,logType:n.type,logs:n.log}),null!=i&&i.apply(void 0,e)})},pe(this)])(e)},S.prototype.sendSDKError=function(e){var n=this;this.sendPipeline([function(e,t){t({url:n.config.url+"?id=1085&msg[0]="+encodeURIComponent(y(e))+"&level[0]=2&from="+n.config.id+"&count=1&version="+n.config.id+"(1.43.5)",addBean:!1,method:"get",type:v.SDK_ERROR,log:e})}],v.SDK_ERROR)(e)},S.prototype.sendPipeline=function(e,t){var n,i=this;return m(u([function(e,t){if("number"!=typeof n.config.random&&(console.warn("random must in [0, 1], default is 1."),n.config.random=1),!n.isHidden||!n.isGetSample)if(n.isGetSample)n.isHidden||t(e);else{if(n.isGetSample=!0,Math.random()<n.config.random)return n.isHidden=!1,t(e);n.isHidden=!0}},fe(n=this,t)],e,[de(this),function(r,o){i.request(r,function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=!1;-1<(""+e[i.failRequestCount=0]).indexOf("403 forbidden")&&(n=!0,i.destroy()),o({isErr:n,result:e,logType:null==r?void 0:r.type,logs:null==r?void 0:r.log}),null!=(n=null==r?void 0:r.success)&&n.call.apply(n,u([r],e))},function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];60<=++i.failRequestCount&&i.destroy(),-1<(""+t[0]).indexOf("403 forbidden")&&i.destroy(),o({isErr:!0,result:t,logType:null==r?void 0:r.type,logs:null==r?void 0:r.log}),null!=(e=null==r?void 0:r.fail)&&e.call.apply(e,u([r],t))})},pe(this)]))},S.prototype.info=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n={level:w.INFO,msg:e};1===e.length&&e[0].msg&&Object.assign(n,l({},e[0]),{level:w.INFO}),this.normalLogPipeline(n)},S.prototype.infoAll=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n={level:w.INFO_ALL,msg:e};1===e.length&&e[0].msg&&Object.assign(n,l({},e[0]),{level:w.INFO_ALL}),this.normalLogPipeline(n)},S.prototype.report=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n={level:w.REPORT,msg:e};1===e.length&&e[0].msg&&Object.assign(n,l({},e[0])),this.normalLogPipeline(n)},S.prototype.error=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n={level:w.ERROR,msg:e};1===e.length&&e[0].msg&&Object.assign(n,l({},e[0]),{level:w.ERROR}),this.normalLogPipeline(n)},S.prototype.reportEvent=function(e){e&&((e="string"==typeof e?{name:e,ext1:this.config.ext1||"",ext2:this.config.ext2||"",ext3:this.config.ext3||""}:e).name?("string"!=typeof e.name&&(console.warn("reportEvent params name must be string"),e.name=String(e.name)),this.eventPipeline(e)):console.warn("reportEvent params error"))},S.prototype.reportT=function(e){var t=e.name,n=e.duration,r=e.ext1,r=void 0===r?"":r,o=e.ext2,o=void 0===o?"":o,i=e.ext3,i=void 0===i?"":i,e=e.from;if("string"==typeof t&&"number"==typeof n&&"string"==typeof r&&"string"==typeof o&&"string"==typeof i){if(!(n<0||6e4<n))return this.submitCustomTime(t,n,r,o,i,void 0===e?"":e);console.warn("reportTime: duration must between 0 and 60000")}else console.warn("reportTime: params error")},S.prototype.reportTime=function(e,t){if("object"==typeof e)return this.reportT(e);"string"==typeof e?"number"==typeof t?t<0||6e4<t?console.warn("reportTime: duration must between 0 and 60000"):this.submitCustomTime(e,t):console.warn("reportTime: second param must be number"):console.warn("reportTime: first param must be a string")},S.prototype.time=function(e){"string"==typeof e?this.timeMap[e]?console.warn("Timer "+e+" already exists"):this.timeMap[e]=Date.now():console.warn("time: first param must be a string")},S.prototype.timeEnd=function(e){"string"==typeof e?this.timeMap[e]?(this.submitCustomTime(e,Date.now()-this.timeMap[e]),delete this.timeMap[e]):console.warn("Timer "+e+" does not exist"):console.warn("timeEnd: first param must be a string")},S.prototype.ready=function(e,t,n){throw new Error('You need to override "ready" method')},S.prototype.request=function(e,t,n){throw new Error('You need to override "request" method')},S.prototype.speedLogPipeline=function(e){throw new Error('You need to override "speedLogPipeline" method')},Object.defineProperty(S.prototype,"__version__",{get:function(){return"1.43.5"},enumerable:!1,configurable:!0}),Object.defineProperty(S.prototype,"LogType",{get:function(){return w},enumerable:!1,configurable:!0}),S.prototype.reportPv=function(e){e&&console.warn("reportPv is deprecated, please use reportEvent")},S.prototype.submitCustomTime=function(e,t,n,r,o,i){this.customTimePipeline({name:e,duration:t,ext1:n||this.config.ext1,ext2:r||this.config.ext2,ext3:o||this.config.ext3,from:i||void 0})},S.version="1.43.5",S.instances=[],S.logType=w,S.environment=f,S.installedPlugins=[],S),i=(c.prototype.patch=function(e){this.canUse(e)&&this.exist(e)&&(this.instances.push(e),this.triggerInit(e),this.triggerOnNewAegis(e))},c.prototype.unpatch=function(e){var t=this.instances.indexOf(e);-1!==t&&(this.instances.splice(t,1),0===this.instances.length)&&this.uninstall(e)},c.prototype.countInstance=function(){return this.instances.length},c.prototype.uninstall=function(e){var t;null!=(t=null==(t=this.option)?void 0:t.destroy)&&t.apply(this,[e])},c.prototype.walk=function(n){var r=this;this.instances.forEach(function(e){var t=r.canUse(e);t&&n(e,t)})},c.prototype.canUse=function(e){e=this.getConfig(e);return!(!e||"object"!=typeof e)||!!e},c.prototype.getConfig=function(e){return null==(e=e.config)?void 0:e[this.name]},c.prototype.exist=function(e){return-1===this.instances.indexOf(e)},c.prototype.triggerInit=function(e){var t;this.inited||(this.inited=!0,null==(t=null==(t=this.option)?void 0:t.init))||t.call(this.option,this.getConfig(e))},c.prototype.triggerOnNewAegis=function(e){var t;null!=(t=null==(t=this.option)?void 0:t.onNewAegis)&&t.call(this.option,e,this.getConfig(e))},c),Ae=new i({name:"aid",aid:"",init:function(e){try{var t=!0!==e&&e||window.localStorage.getItem("AEGIS_ID");t||(t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}),window.localStorage.setItem("AEGIS_ID",t)),this.aid=t}catch(e){}},onNewAegis:function(e){e.bean.aid=this.aid,e.config.aid=this.aid}}),Ie=function(t){var n;return t.payload?(n={},Object.keys(t).forEach(function(e){"payload"!==e&&(n[e]=t[e])}),n):t},Ne=new i({name:"reportAssetSpeed"}),Ue=Ne=new i({name:"reportAssetSpeed",collectCur:0,collectEntryType:"resource",ASSETS_INITIATOR_TYPE:["img","css","script","link","audio","video"],onNewAegis:function(e){var t=this;ge()&&(this.collectSuccessLog(e),this.collectFailLog(e),performance.onresourcetimingbufferfull=function(){"function"==typeof performance.clearResourceTimings&&(t.collectCur=0,performance.clearResourceTimings())})},publish:function(t,n){this.$walk(function(e){e===n&&e.speedLogPipeline(t)})},publishMany:function(e,t){for(var n=t.config,r=0,o=e.length;r<o;r++){var i=e[r];-1===this.ASSETS_INITIATOR_TYPE.indexOf(i.initiatorType)||R(i.name,n.hostUrl)||this.publish(this.generateLog(i,n),t)}},collectSuccessLog:function(n){var e,t,r=this;"function"==typeof window.PerformanceObserver?(this.publishMany(performance.getEntriesByType(this.collectEntryType),n),(e=new window.PerformanceObserver(function(e){r.publishMany(e.getEntries(),n)})).observe({entryTypes:[this.collectEntryType]}),n.lifeCycle.on("destroy",function(){0===Ne.countInstance()&&e.disconnect()})):(t=setInterval(function(){var e=performance.getEntriesByType(r.collectEntryType),t=e.slice(r.collectCur);r.collectCur=e.length,r.publishMany(t,n)},3e3),n.lifeCycle.on("destroy",function(){0===Ne.countInstance()&&clearInterval(t)}))},collectFailLog:function(r){function e(e){var t,n;e&&(e=e.target||e.srcElement,!(t=(null==e?void 0:e.src)||(null==e?void 0:e.href))||"string"!=typeof t||-1<window.location.href.indexOf(t)||(e="function"==typeof(null==(e=i.api)?void 0:e.resourceTypeHandler)?null==(e=i.api)?void 0:e.resourceTypeHandler(t):"",n=performance.getEntriesByType(o.collectEntryType).find(function(e){return e.name===t}),R(t,i.hostUrl))||(n={url:ie(t),status:400,duration:Number(((null==n?void 0:n.duration)||0).toFixed(2)),method:"get",type:e||"static",isHttps:ae(t),urlQuery:E(t,!0),nextHopProtocol:"",domainLookup:0,connectTime:0},o.publish(n,r)))}var o=this,i=r.config;window.document.addEventListener("error",e,!0),r.lifeCycle.on("destroy",function(){0===Ne.countInstance()&&window.document.removeEventListener("error",e,!0)})},generateLog:function(e,t){var t="function"==typeof(null==(n=t.api)?void 0:n.resourceTypeHandler)?null==(n=t.api)?void 0:n.resourceTypeHandler(e.name):"",n=e.transferSize;return{url:ie(e.name),method:"get",duration:Number(e.duration.toFixed(2)),status:200,type:t||"static",isHttps:ae(e.name),nextHopProtocol:e.nextHopProtocol||"",urlQuery:E(e.name,!0),domainLookup:oe(e.domainLookupEnd-e.domainLookupStart),connectTime:oe(e.connectEnd-e.connectStart),transferSize:0<n?n:-1}},collectNotReportedLog:function(e){var t,n;ge()&&(t=(n=performance.getEntriesByType(this.collectEntryType)).length,"function"!=typeof window.PerformanceObserver)&&this.collectCur!==t&&(n=n.slice(this.collectCur),this.collectCur=t,this.publishMany(n,e,!0))},destroy:function(){this.option.publish=function(){}}}),He=window.navigator.userAgent.toLowerCase(),a={};function c(e){this.aegisPlugin=!0,this.name="",this.instances=[],this.inited=!1,e.$walk=this.walk.bind(this),e.$getConfig=this.getConfig.bind(this),this.option=e,this.name=e.name}function S(e){var n,t,r,o,a,i,s,c,u,l,f,d,p,h,g=this;this.isGetSample=!1,this.isHidden=!1,this.config={version:0,delay:1e3,onError:!0,repeat:60,random:1,aid:!0,device:!0,pagePerformance:!0,webVitals:!0,speedSample:!0,onClose:!0,reportLoadPackageSpeed:!0,hostUrl:"https://aegis.qq.com",env:"production",url:"",offlineUrl:"",whiteListUrl:"",pvUrl:"",speedUrl:"",customTimeUrl:"",performanceUrl:"",performanceUrlForHippy:"",webVitalsUrl:"",eventUrl:"",setDataReportUrl:"",reportImmediately:!0},this.isWhiteList=!1,this.lifeCycle=new X,this.bean={},this.normalLogPipeline=m([ue(this,5),le,function(e,t){var r=n.config;t(e=e.map(function(e){var t,n=r.maxLength||102400;try{if(!e.msg||e.msg.length<=n)return e;e.msg=null==(t=e.msg)?void 0:t.substring(0,n)}catch(t){e.msg=y(e.msg).substring(0,r.maxLength)}return e}))},(h=(n=this).config,function(e,t){var n="number"==typeof h.repeat?h.repeat:60;if(n<=0)return t(e);var r=(null==h?void 0:h.id)+"_error",o=T[r]||{};t(e.filter(function(e){if(e.level===w.ERROR||e.level===w.PROMISE_ERROR||e.level===w.AJAX_ERROR||e.level===w.SCRIPT_ERROR||e.level===w.IMAGE_ERROR||e.level===w.CSS_ERROR||e.level===w.MEDIA_ERROR||e.level===w.RET_ERROR||e.level===w.BRIDGE_ERROR||e.level===w.PAGE_NOT_FOUND_ERROR||e.level===w.WEBSOCKET_ERROR||e.level===w.LAZY_LOAD_ERROR){e=e.msg.slice(0,200);if(o[e]>n)return O[r]||Pe(r),!1;o[e]=1+~~o[e],T[r]=o}return!0}))}),(d=this.lifeCycle.emit,p=this.config,function(e,t){var n,r=p.logCreated;return"function"==typeof r?(n=e.filter(function(e){return!1!==r(e)}),d("beforeWrite",n),t(n)):(d("beforeWrite",e),t(e))}),(f=this,setTimeout(function(){var e=f.config,t=e.pvUrl,n=void 0===t?"":t,t=e.spa,e=-1<["web-sdk","mp-sdk"].indexOf("web-sdk");n&&(e&&!t||!e)&&f.sendPipeline([function(e,t){t({url:n,type:v.PV})}],v.PV)(null)},100),function(e,t){t(e)}),(u=c=s=!1,l=[],(a=this).lifeCycle.on("onConfigChange",function(){i&&clearTimeout(i),i=setTimeout(function(){var e,n;!u&&a.config&&(u=!0,e=a.config.whiteListUrl,(n=void 0===e?"":e)&&a.sendPipeline([function(e,t){t({url:n,type:v.WHITE_LIST,success:function(e){c=!0;try{var t=e.data||JSON.parse(e),n=t.retcode,r=t.result,o=void 0===r?{}:r,i=(0===n&&(s=o.is_in_white_list,a.isWhiteList=s,0<=o.rate)&&o.rate<=1&&(a.config.random=o.rate,a.isGetSample=!1),a.isWhiteList&&l.length?ke(a)(l.splice(0),function(){}):!a.isWhiteList&&l.length&&(l.length=0),a.config.onWhitelist);"function"==typeof i&&i(s)}catch(e){}},fail:function(){c=!0}})}],v.WHITE_LIST)(null),u=!1)},a.config.uin?50:500)}),a.lifeCycle.on("destroy",function(){l.length=0}),function(e,t){var n;s||null!=(n=null==(n=a.config)?void 0:n.api)&&n.reportRequest?t(e.concat(l.splice(0)).map(function(e){return xe(e),e})):(n=e.filter(function(e){return e.level!==w.INFO&&e.level!==w.API_RESPONSE?(xe(e),!0):(c||(l.push(e),200<=l.length&&(l.length=200)),!1)})).length&&t(n)}),function(e,t){try{var n=JSON.parse(JSON.stringify(e)),r=(g.lifeCycle.emit("beforeReport",n),g.config.beforeReport);(e="function"==typeof r?e.filter(function(e){return!1!==r(e)}):e).length&&t(e)}catch(e){}},ke(this)]),this.eventPipeline=m([ue(this,10),(o=this,function(e){o.sendPipeline([function(e,t){var n=e.map(function(e){return{name:e.name,ext1:e.ext1||o.config.ext1||"",ext2:e.ext2||o.config.ext2||"",ext3:e.ext3||o.config.ext3||""}});t({url:o.config.eventUrl+"?payload="+encodeURIComponent(JSON.stringify(n)),type:v.EVENT,log:e})}],v.EVENT)(e)})]),this.customTimePipeline=m([ue(this,10),(r=this,function(e){return r.sendPipeline([function(e,t){t({url:r.config.customTimeUrl+"?payload="+encodeURIComponent(JSON.stringify({custom:e})),type:v.CUSTOM,log:e})}],v.CUSTOM)(e)})]),this.timeMap={},this.failRequestCount=0,this.config=(t=this.config,void 0===(e=e.hostUrl)&&(e="https://aegis.qq.com"),t.url=t.url||e+"/collect",t.offlineUrl=t.offlineUrl||e+"/offline",t.whiteListUrl=t.whiteListUrl||e+"/collect/whitelist",t.pvUrl=t.pvUrl||e+"/collect/pv",t.eventUrl=t.eventUrl||e+"/collect/events",t.speedUrl=t.speedUrl||e+"/speed",t.customTimeUrl=t.customTimeUrl||e+"/speed/custom",t.performanceUrl=t.performanceUrl||e+"/speed/performance",t.performanceUrlForHippy=t.performanceUrlForHippy||e+"/speed/hippyPerformance",t.webVitalsUrl=t.webVitalsUrl||e+"/speed/webvitals",t.setDataReportUrl=t.SetDataReportUrl||e+"/speed/miniProgramData",t),S.instances.push(this)}a.macos=function(){return r("mac")},a.ios=function(){return a.iphone()||a.ipod()||a.ipad()},a.iphone=function(){return!a.windows()&&r("iphone")},a.ipod=function(){return r("ipod")},a.ipad=function(){var e="MacIntel"===navigator.platform&&1<navigator.maxTouchPoints;return r("ipad")||e},a.android=function(){return!a.windows()&&r("android")},a.androidPhone=function(){return a.android()&&r("mobile")},a.androidTablet=function(){return a.android()&&!r("mobile")},a.blackberry=function(){return r("blackberry")||r("bb10")},a.blackberryPhone=function(){return a.blackberry()&&!r("tablet")},a.blackberryTablet=function(){return a.blackberry()&&r("tablet")},a.windows=function(){return r("windows")},a.windowsPhone=function(){return a.windows()&&r("phone")},a.windowsTablet=function(){return a.windows()&&r("touch")&&!a.windowsPhone()},a.fxos=function(){return(r("(mobile")||r("(tablet"))&&r(" rv:")},a.fxosPhone=function(){return a.fxos()&&r("mobile")},a.fxosTablet=function(){return a.fxos()&&r("tablet")},a.meego=function(){return r("meego")},a.cordova=function(){return window.cordova&&"file:"===location.protocol},a.nodeWebkit=function(){return"object"==typeof window.process},a.mobile=function(){return a.androidPhone()||a.iphone()||a.ipod()||a.windowsPhone()||a.blackberryPhone()||a.fxosPhone()||a.meego()},a.tablet=function(){return a.ipad()||a.androidTablet()||a.blackberryTablet()||a.windowsTablet()||a.fxosTablet()},a.desktop=function(){return!a.tablet()&&!a.mobile()};function qe(){return{host:new URL(location.href).host,pathname:location.pathname}}function je(r,e,o){return null!=e&&e.length&&"object"==typeof r?e.reduce(function(e,t){var n=window.Headers&&r instanceof Headers?r.get(t):r[t];return n?e+(""===e?"":"\n\n")+o+" header "+t+": "+n:e},""):""}function _e(e,t){return e&&-1===["null","undefined"].indexOf(e)?t+": "+e:""}function De(e,t,n){void 0===t&&(t="");var r,o,e="function"==typeof(null==(o=e.api)?void 0:o.resourceTypeHandler)?null==(o=e.api)?void 0:o.resourceTypeHandler(n):"";return-1===G.indexOf(e)&&(r=void 0===t?"":t,o=(void 0===n?"":n).split("?")[0],e=ve.test(o)||me.some(function(e){return-1!==String(r).indexOf(e)})?"static":"fetch"),e}function Me(e,t){return e?w.AJAX_ERROR:t?w.RET_ERROR:w.API_RESPONSE}function Be(e,t){if(null!=(e=e.api)&&e.usePerformanceTiming&&"string"==typeof t.url){e=null==(e=performance.getEntriesByName(t.url))?void 0:e.pop();if(e)return{url:t.url,isHttps:ae(t.url),method:t.method,type:t.type,status:t.status,duration:Number(e.duration.toFixed(2)),nextHopProtocol:e.nextHopProtocol||"",domainLookup:oe(e.domainLookupEnd-e.domainLookupStart),connectTime:oe(e.connectEnd-e.connectStart)}}return{url:t.url,isHttps:ae(t.url),method:t.method,type:t.type,status:t.status,duration:Number(t.duration.toFixed(2)),nextHopProtocol:"",domainLookup:n.number,connectTime:n.number}}var Fe,We,Ge,Xe,d,Ve,$e,Je=!(a.isIE=function(){return"ActiveXObject"in window}),p=[],Ke=/^\/[^/]/,ze=!1,h=[],Qe=(new i({name:"reportApiSpeed"}),new i({name:"reportApiSpeed",override:!1,onNewAegis:function(e){var t,n;this.override||(this.override=!0,null!=(n=e.config.api)&&n.injectTraceHeader&&(this.traceRequestHeader=new te(n.injectTraceHeader,null!=(t=null==n?void 0:n.injectTraceIgnoreUrls)?t:[],null==n?void 0:n.injectTraceUrls)),this.overrideFetch(e.config,e),this.overrideXhr(e.config,e))},overrideFetch:function(m,v){var y=this,e=m.api,e={name:this.name,traceRequestHeader:null!=e&&e.injectTraceHeader?this.traceRequestHeader:null,then:function(f,d,p,h){var e,g;R(p,m.hostUrl)||(e=f.headers?f.headers.get("content-type"):"","fetch"===(g=De(m,e||"",p))?f.clone().text().then(function(n){var e,r=f.status<=0||400<=f.status,t=(null==(t=m.api)?void 0:t.reqHeaders)||[],o=je(null==h?void 0:h.headers,t,"req"),t=(null==(t=m.api)?void 0:t.resHeaders)||[],i=je(f.headers,t,"res"),a=Z(null==h?void 0:h.headers),t=se(n,m.api,{url:p,ctx:f,payload:null==h?void 0:h.body}),s=t.code,c=t.isErr,t=null==(t=m.api)?void 0:t.apiDetail,u=t?ce(null==h?void 0:h.body,null==(e=m.api)?void 0:e.reqParamHandler,{url:p}):"",l=t?ce(n,null==(e=m.api)?void 0:e.resBodyHandler,{url:p,ctx:f}):"";setTimeout(function(){var e=Be(m,{url:p,duration:d,type:g,status:f.status||0,method:(null==h?void 0:h.method)||"get"}),t=[r?"FETCH_ERROR: "+n:"","fetch req url: "+p,"res status: "+(e.status||0),"res duration: "+e.duration+"ms",o,i,"req method: "+(e.method||"GET"),"res retcode: "+s,_e(u,"req param"),_e(l,"res data")].filter(function(e){return e}).join("\n\n");e.payload=null==h?void 0:h.body,e.ret=s,e.isErr=+c,y.publishNormalLog({msg:t,level:Me(r,c),code:s,trace:a},v),y.publishSpeed(e,v)},0)}):setTimeout(function(){var e=Be(m,{url:p,duration:d,type:g,status:f.status||0,method:(null==h?void 0:h.method)||"get"});e.type="static",e.urlQuery=E(p,!0),y.publishSpeed(e,v)},0))},catch:function(t,n,r,o){var i,e,a,s,c;throw R(r,m.hostUrl)||(i=De(m,"",r),e=(null==(e=m.api)?void 0:e.reqHeaders)||[],a=je(null==o?void 0:o.headers,e,"req"),s=Z(null==o?void 0:o.headers),c=null!=(e=m.api)&&e.apiDetail?ce(null==o?void 0:o.body,null==(e=m.api)?void 0:e.reqParamHandler,{url:r}):"",setTimeout(function(){var e=Be(m,{url:r,duration:n,type:i,status:0,method:(null==o?void 0:o.method)||"get"}),e=(y.publishSpeed(e,v),"AJAX_ERROR: "+t+"\n                          \nreq url: "+r+"\n                          \nres status: 0\n                          \nres duration: "+e.duration+"ms\n                          \nreq method: "+((null==o?void 0:o.method)||"get")+"\n                          \nreq param: "+c+"\n                          \n"+a);y.publishNormalLog({msg:e,level:w.AJAX_ERROR,code:-400,trace:s},v)},0)),t}},l=(this.hackFetchOptions=e,this.hackFetchOptions),f=null==(e=m.api)?void 0:e.ignoreHackReg;if(void 0===f&&(f=/\.flv(\?|$)/gi),h.find(function(e){return e.name===l.name}))throw new Error("name '"+l.name+"' is already in hackFetch option list");h.push(l),!ze&&window.fetch&&(ze=!0,Xe=window.fetch,window.fetch=function(e,o){void 0===o&&(o={});var i="string"==typeof e?e:null==e?void 0:e.url;if(null!=(a=null==f?void 0:f.test)&&a.call(f,i))return Xe(i,o);Ke.test(i)&&(i=""+location.origin+i);var t,n,r,a=(l||{}).traceRequestHeader;a&&(t=void 0===(t=(o||{}).headers)?{}:t,n=(r=qe()).host,r=r.pathname,n=(a=a.generate(i,t,{host:n,pathname:r})||{}).name,r=a.value)&&n&&(o.headers=Object.assign(t,((a={})[n]=r,a)));for(var s=0;s<h.length;s++){var c=h[s];try{"function"==typeof c.beforeFetch&&c.beforeFetch(i,o)}catch(e){}}var u=Date.now();return Xe(e,o).then(function(e){for(var t=e.clone(),n=0;n<h.length;n++){var r=h[n];try{"function"==typeof r.then&&r.then(t,Date.now()-u,i,o)}catch(e){}}return t}).catch(function(e){for(var t=0;t<h.length;t++){var n=h[t];try{"function"==typeof n.catch&&n.catch(e,Date.now()-u,i,o)}catch(e){}}throw e})})},overrideXhr:function(v,y){var o,w=this,e={name:this.name,ignoreHackReg:null==(e=v.api)?void 0:e.ignoreHackReg,send:function(d,p){var e,t,r=Date.now(),h=(((null==v?void 0:v.api)||{}).injectTraceHeader&&(t=(e=qe()).host,e=e.pathname,e=(t=w.traceRequestHeader.generate(d.aegisUrl,{},{host:t,pathname:e})||{}).name,t=t.value,e)&&t&&d.setRequestHeader(e,t),d.addEventListener("loadend",function(){var u,n,e,l,f=d.aegisUrl||"";R(f,v.hostUrl)||"abort"===d.failType||(u="",(d.failType||!d.status||400<=d.status)&&(u=d.failType||"failed"),n=Date.now()-r,e=d.getResponseHeader("content-type")||"",l=De(v,e,f),setTimeout(function(){var r=Be(v,{url:f,duration:n,type:l,status:d.status,method:d.aegisMethod||"get"});if("fetch"===l){var e=(null==(e=v.api)?void 0:e.reqHeaders)||[],o=je(d.aegisXhrReqHeader,e,"req"),e=(null==(e=v.api)?void 0:e.resHeaders)||[],t=d.getAllResponseHeaders().split("\r\n").reduce(function(e,t){t=t.split(": ");return t[0]&&t[1]&&(e[t[0]]=t[1]),e},{}),i=je(t,e,"res"),a=Z(d.aegisXhrReqHeader),e=null==(t=v.api)?void 0:t.apiDetail,s=e?ce(p,null==(t=v.api)?void 0:t.reqParamHandler,{url:f}):"",c=e?ce(d.response,null==(t=v.api)?void 0:t.resBodyHandler,{url:f}):"";try{!function(e,t,n,r){var o,i,a,s;try{if("function"==typeof(null==t?void 0:t.retCodeHandlerAsync))return t.retCodeHandlerAsync(e,null==n?void 0:n.url,null==n?void 0:n.ctx,function(e){var t=e.code,e=e.isErr;null!=r&&r({code:void 0===t?b:t,isErr:e})});if("function"==typeof(null==t?void 0:t.retCodeHandler))return a=(i=t.retCodeHandler(e,null==n?void 0:n.url,null==n?void 0:n.ctx,null==n?void 0:n.payload)||{}).code,s=i.isErr,null!=r&&r({code:void 0===a?b:a,isErr:s});if(!(e="string"==typeof e?JSON.parse(e):e))return null!=r&&r({code:b,isErr:!1});"function"==typeof(null==(o=null==t?void 0:t.ret)?void 0:o.join)&&(ye=[].concat(t.ret.map(function(e){return e.toLowerCase()})));var c=Object.getOwnPropertyNames(e).filter(function(e){return-1!==ye.indexOf(e.toLowerCase())});if(c.length)return"未知"!==(a=e[c[0]])&&""!==a||(a=b),null!=r&&r({code:""+a,isErr:0!==a&&"0"!==a&&a!==b});null!=r&&r({code:b,isErr:!1})}catch(e){null!=r&&r({code:b,isErr:!1})}}(d.response,v.api,{url:f,ctx:d,payload:p},function(e){var t=e.code,e=e.isErr,n=[u?"AJAX_ERROR: request "+u:"","fetch req url: "+f,"res status: "+(r.status||0),"res duration: "+r.duration+"ms",o,i,"req method: "+(r.method||"GET"),"res retcode: "+t,_e(s,"req param"),_e(c,"res data")].filter(function(e){return e}).join("\n\n");r.ret=t,r.isErr=+e,r.payload=p,w.publishNormalLog({msg:n,level:Me(!!u,e),code:t,trace:a},y),w.publishSpeed(r,y)})}catch(e){r.ret=b,w.publishSpeed(r,y)}}else r.type="static",r.urlQuery=E(f,!0),w.publishSpeed(r,y);d.removeEventListener("abort",h),d.removeEventListener("error",g),d.removeEventListener("timeout",m)},0))}),function(){d.failType="abort"}),g=function(){d.failType="error"},m=function(){d.failType="timeout"};d.addEventListener("abort",h),d.addEventListener("error",g),d.addEventListener("timeout",m)}};this.hackXHROptions=e,o=this.hackXHROptions,p.find(function(e){return e.name===o.name})||(p.push(o),!Je&&window.XMLHttpRequest&&(Fe=window.XMLHttpRequest.prototype.send,We=window.XMLHttpRequest.prototype.open,Ge=window.XMLHttpRequest.prototype.setRequestHeader,Je=!0,window.XMLHttpRequest.prototype.open=function(){this.aegisMethod=arguments[0];var e,t=arguments[1];if(Ke.test(t)&&(t=""+location.origin+t),this.aegisUrl=t,null==(t=null==(e=o.ignoreHackReg)?void 0:e.test)||!t.call(e,this.aegisUrl))if(this.aegisXhrStartTime=Date.now(),this.sendByAegis)a.isIE()||(this.timeout=5e3);else for(var n=0;n<p.length;n++){var r=p[n];try{"function"==typeof r.open&&r.open(this)}catch(e){}}return We.apply(this,arguments)},window.XMLHttpRequest.prototype.setRequestHeader=function(){var e,t,n;return null!=(t=null==(n=o.ignoreHackReg)?void 0:n.test)&&t.call(n,this.aegisUrl)?Ge.apply(this,arguments):(t=arguments[0],n=arguments[1],this.aegisXhrReqHeader=null!=(e=this.aegisXhrReqHeader)?e:{},-1<["traceparent","b3","sw8","sentry-trace"].indexOf(t)&&(this.aegisXhrReqHeader[t]||(arguments[1]=n),this.aegisXhrReqHeader[t])?void 0:(this.aegisXhrReqHeader[t]=arguments[1],Ge.apply(this,arguments)))},window.XMLHttpRequest.prototype.send=function(){var e,t;if(!(null!=(t=null==(e=o.ignoreHackReg)?void 0:e.test)&&t.call(e,this.aegisUrl)||this.sendByAegis))for(var n=0;n<p.length;n++){var r=p[n];try{"function"==typeof r.send&&r.send(this,arguments[0])}catch(e){}}return Fe.apply(this,arguments)}))},publishSpeed:function(n){var r=this;this.$walk(function(e){var t=r.$getConfig(e);"fetch"!==n.type||"function"!=typeof(null==t?void 0:t.urlHandler)?(n.url=E(n.url),e.speedLogPipeline(n)):e.speedLogPipeline(l(l({},n),{url:E(t.urlHandler(n.url,n.payload))}))})},publishNormalLog:function(t){this.$walk(function(e){e.normalLogPipeline(t)})},destroy:function(){var t,n,e;this.option.publishSpeed=function(){},this.option.publishNormalLog=function(){},this.option.hackXHROptions&&(t=this.option.hackXHROptions,-1!==(e=p.findIndex(function(e){return e.name===t.name})))&&p.splice(e,1),this.option.hackFetchOptions&&(n=this.option.hackFetchOptions,-1!==(e=h.findIndex(function(e){return e.name===n.name})))&&h.splice(e,1),this.option.override=!1}})),Ye={},Ze=new i({name:"reportBridgeSpeed",override:!1,onNewAegis:function(e){this.override||(this.override=!0,this.overrideBridge(e))},publishSpeed:function(t,n){this.$walk(function(e){e===n&&e.speedLogPipeline(t)})},overrideBridge:function(c){var u=this,l=c.config;l.reportBridgeSpeed&&l.h5Bridge&&l.h5BridgeFunc.length&&l.h5BridgeFunc.forEach(function(e){var s=l.h5Bridge[e];Ye[e]=s,l.h5Bridge[e]=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=e[0],o=e[1],n=e[2],i=e[3],a=Date.now();s(r,o,n,function(e){var t=se(e,l.api),n=t.code,t=t.isErr,n={url:r+"-"+o,name:r+"-"+o,duration:Date.now()-a,type:"bridge",ret:n,isErr:+t};u.publishSpeed(n,c),i(e)})}})},unHackBridge:function(t){Object.keys(Ye).forEach(function(e){Ye[e]&&(t.config.h5Bridge[e]=Ye[e])}),Ye={}},destroy:function(e){this.option.publishSpeed=function(){},this.option.unHackBridge(e),this.option.override=!1}});(k=d=d||{})[k.unknown=100]="unknown",k[k.wifi=1]="wifi",k[k.net2g=2]="net2g",k[k.net3g=3]="net3g",k[k.net4g=4]="net4g",k[k.net5g=5]="net5g",k[k.net6g=6]="net6g",(k=Ve=Ve||{})[k.android=1]="android",k[k.ios=2]="ios",k[k.windows=3]="windows",k[k.macos=4]="macos",k[k.linux=5]="linux",k[k.other=100]="other",(k=$e=$e||{})[k.unknown=100]="unknown",k[k.normal=0]="normal",k[k.weak=1]="weak",k[k.disconnected=2]="disconnected";function g(e,t,n,r){return void 0===n&&(n=15e3),void 0===r&&(r=0),(t=void 0===t?0:t)<=e&&e<=n?e:r}var x,P,et,tt,nt,L,rt,ot,it,k=new i({name:"device",onNewAegis:function(e){e.extendBean("platform",this.getPlatform()),e.extendBean("netType",d.unknown),this.getDpi(e),this.refreshNetworkTypeToBean(e),this.refreshNetworkStatusToBean(e)},getDpi:function(e){e.extendBean("vp",Math.round(window.innerWidth)+" * "+Math.round(window.innerHeight)),window.screen&&e.extendBean("sr",Math.round(window.screen.width)+" * "+Math.round(window.screen.height))},getPlatform:function(){var t={android:/\bAndroid\s*([^;]+)/,ios:/\b(iPad|iPhone|iPod)\b.*? OS ([\d_]+)/,windows:/\b(Windows NT)/,macos:/\b(Mac OS)/,linux:/\b(Linux)/i},e=Object.keys(t).find(function(e){return t[e].test(navigator.userAgent)});return e?Ve[e]:Ve.other},refreshNetworkTypeToBean:function(t){var n=this,e=t.config;e&&("function"==typeof e.getNetworkType?e.getNetworkType:at)(function(e){d[e]||(e=d.unknown),t.extendBean("netType",e),n.NetworkRefreshTimer=setTimeout(function(){n.refreshNetworkTypeToBean(t),clearTimeout(n.NetworkRefreshTimer)},1e4)})},refreshNetworkStatusToBean:function(t){var e,n=this,r=t.config;r&&null!=(e="function"==typeof r.getNetworkStatus?r.getNetworkStatus:e)&&e(function(e){void 0===$e[e]&&(e=$e.unknown),t.extendBean("netStatus",e),n.NetworkStatusRefreshTimer=setTimeout(function(){n.refreshNetworkStatusToBean(t),clearTimeout(n.NetworkStatusRefreshTimer)},1e4)})}}),at=function(e){var t="",n=navigator.userAgent.match(/NetType\/(\w+)/);n?t=n[1]:navigator.connection&&(t=navigator.connection.effectiveType||navigator.connection.type),e((n=t=t||b,0<=(n=String(n).toLowerCase()).indexOf("4g")?d.net4g:0<=n.indexOf("wifi")?d.wifi:0<=n.indexOf("5g")?d.net5g:0<=n.indexOf("6g")?d.net6g:0<=n.indexOf("3g")?d.net3g:0<=n.indexOf("2g")?d.net2g:d.unknown))},st=window.WebSocket,C=[],ct={construct:function(e,t){var a=new e(t[0],t[1]);return a.originSend=a.send,a.addEventListener("error",function(e){var e=(null==e?void 0:e.currentTarget)||{},t=e.url,n=e.readyState;null!=C&&C.forEach(function(e){e=e.onErr;null!=e&&e({msg:"无法获知具体错误信息，需在浏览器控制台查看！",readyState:n,connectUrl:t})})}),Object.defineProperty(a,"send",{get:function(){return function(e){null!=(t=a.originSend)&&t.call(a,e);var t=a.readyState,e=WebSocket.OPEN,n=WebSocket.CLOSED,r=WebSocket.CONNECTING,o=WebSocket.CLOSING;if(t!==e){var i={readyState:t,connectUrl:a.url};switch(t){case n:C.forEach(function(e){e=e.sendErr;null!=e&&e(l({msg:"消息发送失败，连接已关闭！"},i))});break;case r:C.forEach(function(e){(0,e.sendErr)(l({msg:"消息发送失败，正在连接中！"},i))});break;case o:C.forEach(function(e){(0,e.sendErr)(l({msg:"消息发送失败，连接正在关闭！"},i))})}}}}}),a}},ut=new i({name:"onError"}),lt=ut=new i({name:"onError",onNewAegis:function(e){this.startListen(e)},startListen:function(o){function e(e){var t,n=e&&y(e.reason);n&&s.publishErrorLog({msg:"PROMISE_ERROR: "+n,errorMsg:null==(t=null==(e=(null==(t=e.reason)?void 0:t.message)||(null==(t=e.reason)?void 0:t.errMsg)||n)?void 0:e.slice)?void 0:t.call(e,0,150),level:w.PROMISE_ERROR},o)}function t(e){var t;if(e=(null==e?void 0:e.target)||(null==e?void 0:e.srcElement)){var n=e.src||e.href||"",e=e.tagName,e=void 0===e?"script":e;if(!(R(t=n,o.config.hostUrl)||-1<window.location.href.indexOf(t))){var r={msg:e+" load fail: "+n,level:w.INFO};if(/\.js$/.test(n))r.level=w.SCRIPT_ERROR;else if(/\.css$/.test(n))r.level=w.CSS_ERROR;else switch(e.toLowerCase()){case"script":r.level=w.SCRIPT_ERROR;break;case"link":r.level=w.CSS_ERROR;break;case"img":r.level=w.IMAGE_ERROR;break;case"audio":case"video":r.level=w.MEDIA_ERROR;break;default:return}s.publishErrorLog(r,o)}}}var n,r,i,a,s=this,c=window.onerror;window.onerror=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n,r=y(e[0]);"string"!=typeof(n=r)||!n||F.some(function(e){return-1<n.indexOf(e)})||B.some(function(e){return-1<n.indexOf(e)})||s.publishErrorLog({msg:(r||"")+" @ ("+(y(e[1])||"")+":"+(e[2]||0)+":"+(e[3]||0)+")\n          \n"+y(e[4]||""),level:w.ERROR},o),null!=c&&c.call.apply(c,u([window],e))},window.addEventListener("unhandledrejection",e);window.document.addEventListener("error",t,!0),o.lifeCycle.on("destroy",function(){0===ut.countInstance()&&(window.document.removeEventListener("unhandledrejection",e),window.document.removeEventListener("error",t,!0))}),o.config.websocketHack&&(n={key:o.config.id+"-"+this.name,onErr:function(e){var t;null!=(t=s.publishWsErrorLog)&&t.call(s,e,o)},sendErr:function(e){var t;null!=(t=s.publishWsErrorLog)&&t.call(s,e,o)}},this.hackWebsocketConfig=n,n=this.hackWebsocketConfig,window.Proxy)&&window.WebSocket&&(r=window.WebSocket,window&&!r.isHack&&(i=new Proxy(WebSocket,ct),r.isHack=!0,window.WebSocket=i),a=n,C.find(function(e){return e.key===a.key})||a&&C.push(a))},publishErrorLog:function(t,n){this.$walk(function(e){e===n&&e.normalLogPipeline(t)})},publishWsErrorLog:function(e,t){var n=e.connectUrl,r=e.msg,e=e.readyState;this.publishErrorLog({msg:"WEBSOCKET_ERROR: \n              connect: "+n+"\n              readyState: "+e+"\n              msg: "+r,level:w.WEBSOCKET_ERROR},t)},destroy:function(){var t,e;this.option.publishErrorLog=function(){},this.option.hackWebsocketConfig&&(t=this.option.hackWebsocketConfig,window.WebSocket=st,-1!==(e=C.findIndex(function(e){return e.key===t.key})))&&C.splice(e,1)}}),ft=(new i({name:"pagePerformance"}),3),dt=new i({name:"pagePerformance",performanceMap:{},onNewAegis:function(e){ge()&&(x?this.publish(x,e):this.startCalcPerformance(e))},publish:function(e,t){var s=this;this.$walk(function(a){a===t&&a.sendPipeline([function(e,t){var n,r=[];for(n in e)r.push(n+"="+e[n]);var o,i=s.$getConfig(a);if(i)return o=-1===(null==(o=a.config.performanceUrl)?void 0:o.indexOf("?"))?"?":"&","function"==typeof i.urlHandler?t({url:a.config.performanceUrl+o+r.join("&")+"&from="+(encodeURIComponent(i.urlHandler())||window.location.href),beanFilter:["from"],type:v.PERFORMANCE,log:e}):t({url:a.config.performanceUrl+o+r.join("&"),type:v.PERFORMANCE,log:e})}],v.PERFORMANCE)(l({},e))})},startCalcPerformance:function(n){var r=this;try{this.getFirstScreenTiming(n,function(e){var t=performance.timing;t&&(x={dnsLookup:g(t.domainLookupEnd-t.domainLookupStart),tcp:g(t.connectEnd-t.connectStart),ssl:g(0===t.secureConnectionStart?0:t.requestStart-t.secureConnectionStart),ttfb:g(t.responseStart-t.requestStart),contentDownload:g(t.responseEnd-t.responseStart),domParse:g(t.domInteractive-t.domLoading,0,15e3,1070),resourceDownload:g(t.loadEventStart-t.domInteractive,0,15e3,1070),firstScreenTiming:g(Math.floor(e),0,15e3,15e3)},(t=n.config).extraPerformanceData&&"{}"!==JSON.stringify(t.extraPerformanceData)&&(t=(e=t.extraPerformanceData).engineInit,e=e.bundleLoad,x=l(l({},x),{engineInit:g(t,0,1e4),bundleLoad:g(e,0,1e4)})),r.publish(x,n))})}catch(n){}},getFirstScreenTiming:function(s,c){var u=this;s.lifeCycle.on("destroy",function(){m&&clearTimeout(m)});var l,f=this,n=["script","style","link","br"],d=[],p={},h=(-1<(null==(e=null==(e=window.PerformanceObserver)?void 0:e.supportedEntryTypes)?void 0:e.indexOf("paint"))&&(l=new PerformanceObserver(function(e){e.getEntries().forEach(function(e){var t;"paint"===e.entryType&&"first-contentful-paint"===e.name&&0<(t=document.querySelectorAll("[AEGIS-FIRST-SCREEN-TIMING]")).length&&(u.setFirstScreenInfo(s,e.startTime,t[0],t),null!=c&&c(e.startTime),h.disconnect(),l.disconnect())})})).observe({entryTypes:["paint"]}),new MutationObserver(function(e){var t={roots:[],ignores:[],rootsDomNum:[],time:performance.now()};e.forEach(function(e){e&&e.addedNodes&&Array.prototype.slice.call(e.addedNodes).forEach(function(e){f.isEleInArray(e,t.ignores)?t.ignores.push(e):1===e.nodeType&&e.hasAttribute("AEGIS-FIRST-SCREEN-TIMING")?(Object.prototype.hasOwnProperty.apply(p,[t.time])||(p[t.time]=[]),p[t.time].push(e)):1===e.nodeType&&(f.hasAncestorOrSelfWithAttribute(e,"AEGIS-IGNORE-FIRST-SCREEN-TIMING")?t.ignores.push(e):-1!==n.indexOf(e.nodeName.toLocaleLowerCase())||f.isEleInArray(e,t.roots)||(t.roots.push(e),t.rootsDomNum.push(f.walkAndCount(e)||0)))})}),t.roots.length&&d.push(t)}));h.observe(document,{childList:!0,subtree:!0});var e,g=function(n){(n=void 0===n?0:n)||(r=0,(e=Object.keys(p)).length?(n=Math.max.apply(null,e),u.setFirstScreenInfo(s,n,null==(e=p[n])?void 0:e[0],p)):d.forEach(function(e){for(var t=0;t<e.roots.length;t++)e.rootsDomNum[t]>r&&f.isInFirstScreen(e.roots[t])&&(r=e.rootsDomNum[t],n=e.time,u.setFirstScreenInfo(s,n,e.roots[t]))}),d.length=0,Object.keys(p).forEach(function(e){p[e]=p[e].map(function(e){var t={tagName:e.tagName},n=e.attributes;if(!n)return e;for(var r=0;r<n.length;r++){var o=n[r];o.name&&(t[o.name]=e.getAttribute(o.name))}return t})}));var r,e=performance.timing,t=e.domInteractive-e.domLoading,e=e.loadEventStart-e.domInteractive,o=n;m=null;for(var i=0,a=[t,e,o];i<a.length;i++)if(a[i]<=0&&0<ft){m=setTimeout(function(){return g(o)},3e3);break}m?--ft:(h.disconnect(),null!=l&&l.disconnect(),null!=c&&c(n))},m=setTimeout(function(){return g()},3e3)},setFirstScreenInfo:function(e,t,n,r){var o;e.config.id&&this.performanceMap[e.config.id]||(e.config.id&&(this.performanceMap[e.config.id]=!0),("object"!=typeof(null==(o=e.config)?void 0:o.pagePerformance)||null!=(o=e.config.pagePerformance)&&o.firstScreenInfo)&&(e.firstScreenInfo={element:n,timing:t,markDoms:r}))},isEleInArray:function(e,t){return!(!e||e===document.documentElement)&&(-1!==t.indexOf(e)||this.isEleInArray(e.parentElement,t))},isInFirstScreen:function(e){var t,n;return!(!e||"function"!=typeof e.getBoundingClientRect)&&(e=e.getBoundingClientRect(),t=window.innerHeight,n=window.innerWidth,0<=e.left)&&e.left<n&&0<=e.top&&e.top<t&&0<e.width&&0<e.height},walkAndCount:function(e){var t=0;if(e&&1===e.nodeType){t+=1;var n=e.children;if(null!=n&&n.length)for(var r=0;r<n.length;r++)1===n[r].nodeType&&n[r].hasAttribute("AEGIS-IGNORE-FIRST-SCREEN-TIMING")||(t+=this.walkAndCount(n[r]))}return t},hasAncestorOrSelfWithAttribute:function(e,t){for(var n=e;n&&n!==document.body;){if(n.hasAttribute(t))return!0;n=n.parentElement}return!1}});function pt(){nt=[],et=-1,P=null,mt(addEventListener)}function ht(e,t){P||(P=t,et=e,tt=new Date,mt(removeEventListener),gt())}function gt(){var t;0<=et&&et<tt-rt&&(t={entryType:"first-input",name:P.type,target:P.target,cancelable:P.cancelable,startTime:P.timeStamp,processingStart:P.timeStamp+et},nt.forEach(function(e){e(t)}),nt=[])}function mt(t){["mousedown","keydown","touchstart","pointerdown"].forEach(function(e){return t(e,ot,L)})}L={passive:!0,capture:!0},rt=new Date,ot=function(e){var t,n,r,o;function i(){ht(n,r),o()}function a(){o()}e.cancelable&&(t=(1e12<e.timeStamp?new Date:performance.now())-e.timeStamp,"pointerdown"==e.type?(n=t,r=e,o=function(){removeEventListener("pointerup",i,L),removeEventListener("pointercancel",a,L)},addEventListener("pointerup",i,L),addEventListener("pointercancel",a,L)):ht(t,e))},it="hidden"===document.visibilityState?0:1/0,addEventListener("visibilitychange",function e(t){"hidden"===document.visibilityState&&(it=t.timeStamp,removeEventListener("visibilitychange",e,!0))},!0),pt(),self.webVitals={firstInputPolyfill:function(e){nt.push(e),gt()},resetFirstInputPolyfill:pt,get firstHiddenTime(){return it}};function A(e,t){var n=qt(),r="navigate";return 0<=Ut?r="back-forward-cache":n&&(document.prerendering||0<jt()?r="prerender":document.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:r}}function vt(e,t,n){try{var r;if(PerformanceObserver.supportedEntryTypes.includes(e))return(r=new PerformanceObserver(function(e){Promise.resolve().then(function(){t(e.getEntries())})})).observe(Object.assign({type:e,buffered:!0},n||{})),r}catch(e){}}function I(t,n,r,o){var i,a;return function(e){0<=n.value&&(e||o)&&((a=n.value-(i||0))||void 0===i)&&(i=n.value,n.delta=a,n.rating=(e=n.value)>r[1]?"poor":e>r[0]?"needs-improvement":"good",t(n))}}function yt(e){requestAnimationFrame(function(){return requestAnimationFrame(function(){return e()})})}function wt(t){function e(e){"pagehide"!==e.type&&"hidden"!==document.visibilityState||t(e)}addEventListener("visibilitychange",e,!0),addEventListener("pagehide",e,!0)}function bt(t){var n=!1;return function(e){n||(t(e),n=!0)}}function Et(){return U<0&&(U=_t(),Mt(),Ht(function(){setTimeout(function(){U=_t(),Mt()},0)})),{get firstHiddenTime(){return U}}}function Rt(e){document.prerendering?addEventListener("prerenderingchange",function(){return e()},!0):e()}function Ot(i,a){a=a||{},Rt(function(){var t,n=Et(),r=A("FCP"),o=vt("paint",function(e){e.forEach(function(e){"first-contentful-paint"===e.name&&(o.disconnect(),e.startTime<n.firstHiddenTime)&&(r.value=Math.max(e.startTime-jt(),0),r.entries.push(e),t(!0))})});o&&(t=I(i,r,Bt,a.reportAllChanges),Ht(function(e){r=A("FCP"),t=I(i,r,Bt,a.reportAllChanges),yt(function(){r.value=performance.now()-e.timeStamp,t(!0)})}))})}function Tt(){var t;0<=kt&&kt<Ct-Gt&&(t={entryType:"first-input",name:N.type,target:N.target,cancelable:N.cancelable,startTime:N.timeStamp,processingStart:N.timeStamp+kt},At.forEach(function(e){e(t)}),At=[])}function St(e){var t,n,r,o;function i(){Xt(n,r),o()}function a(){o()}e.cancelable&&(t=(1e12<e.timeStamp?new Date:performance.now())-e.timeStamp,"pointerdown"==e.type?(n=t,r=e,o=function(){removeEventListener("pointerup",i,Wt),removeEventListener("pointercancel",a,Wt)},addEventListener("pointerup",i,Wt),addEventListener("pointercancel",a,Wt)):Xt(t,e))}function xt(t){["mousedown","keydown","touchstart","pointerdown"].forEach(function(e){return t(e,St,Wt)})}function Pt(e,t){function n(t,n,r,o){function i(e){"visibilitychange"===t&&"hidden"!==document.visibilityState||(o?setTimeout(function(){return n(e)},o):n(e),r&&removeEventListener(t,i,!0))}addEventListener(t,i,!0)}var r;n("visibilitychange",e,null==t?void 0:t.once,null==(r=null==t?void 0:t.delay)?void 0:r.visibilitychange),n("pagehide",e,null==t?void 0:t.once,null==(r=null==t?void 0:t.delay)?void 0:r.pagehide)}function Lt(e){var t=e.name;0<(e=e.value)&&(Kt[t]=e)}var N,kt,Ct,At,It,Nt,Ut=-1,Ht=function(t){addEventListener("pageshow",function(e){e.persisted&&(Ut=e.timeStamp,t(e))},!0)},qt=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},jt=function(){var e=qt();return e&&e.activationStart||0},U=-1,_t=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},Dt=function(e){"hidden"===document.visibilityState&&-1<U&&(U="visibilitychange"===e.type?e.timeStamp:0,removeEventListener("visibilitychange",Dt,!0),removeEventListener("prerenderingchange",Dt,!0))},Mt=function(){addEventListener("visibilitychange",Dt,!0),addEventListener("prerenderingchange",Dt,!0)},Bt=[1800,3e3],Ft=[.1,.25],Wt={passive:!0,capture:!0},Gt=new Date,Xt=function(e,t){N||(N=t,kt=e,Ct=new Date,xt(removeEventListener),Tt())},Vt=[100,300],$t=[2500,4e3],Jt={},Kt=(new i({name:"webVitals"}),{FCP:-1,LCP:-1,FID:-1,CLS:-1}),zt=new i({name:"webVitals",onNewAegis:function(e){if(ge()&&"function"==typeof window.PerformanceObserver&&"function"==typeof performance.getEntriesByName)try{Ot(Lt),l=Lt,f={},Rt(function(){function e(e){(e=e[e.length-1])&&e.startTime<r.firstHiddenTime&&(o.value=Math.max(e.startTime-jt(),0),o.entries=[e],t())}var t,n,r=Et(),o=A("LCP"),i=vt("largest-contentful-paint",e);i&&(t=I(l,o,$t,f.reportAllChanges),n=bt(function(){Jt[o.id]||(e(i.takeRecords()),i.disconnect(),Jt[o.id]=!0,t(!0))}),["keydown","click"].forEach(function(e){addEventListener(e,function(){return setTimeout(n,0)},!0)}),wt(n),Ht(function(e){o=A("LCP"),t=I(l,o,$t,f.reportAllChanges),yt(function(){o.value=performance.now()-e.timeStamp,Jt[o.id]=!0,t(!0)})}))}),c=Lt,u={},Rt(function(){function t(e){e.startTime<n.firstHiddenTime&&(r.value=e.processingStart-e.startTime,r.entries.push(e),i(!0))}function e(e){e.forEach(t)}var n=Et(),r=A("FID"),o=vt("first-input",e),i=I(c,r,Vt,u.reportAllChanges);o&&wt(bt(function(){e(o.takeRecords()),o.disconnect()})),o&&Ht(function(){r=A("FID"),i=I(c,r,Vt,u.reportAllChanges),At=[],kt=-1,N=null,xt(addEventListener),At.push(t),Tt()})}),a=Lt,s={},Ot(bt(function(){function e(e){e.forEach(function(e){var t,n;e.hadRecentInput||(t=o[0],n=o[o.length-1],r&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(r+=e.value,o.push(e)):(r=e.value,o=[e]))}),r>n.value&&(n.value=r,n.entries=o,t())}var t,n=A("CLS",0),r=0,o=[],i=vt("layout-shift",e);i&&(t=I(a,n,Ft,s.reportAllChanges),wt(function(){e(i.takeRecords()),t(!0)}),Ht(function(){n=A("CLS",r=0),t=I(a,n,Ft,s.reportAllChanges),yt(function(){return t()})}),setTimeout(t,0))})),Pt(this.publish.bind(this,e),{once:!0,delay:{visibilitychange:10}})}catch(e){}var a,s,c,u,l,f},publish:function(t){this.$walk(function(i){var e;i===t&&null!=(e=i.sendPipeline)&&e.call(i,[function(e,t){var n,r=[];for(n in e)r.push(n+"="+e[n]);var o=-1===(null==(o=null==(o=i.config)?void 0:o.performanceUrl)?void 0:o.indexOf("?"))?"?":"&";t({url:i.config.webVitalsUrl+o+r.join("&"),type:v.VITALS,log:e,sendBeacon:!0})}],v.VITALS)(l({},Kt))})},destroy:function(){this.option.publish=function(){}}}),Qt=(new i({name:"spa"}),["replaceState","pushState","popstate","hashchange"]),Yt=new i({name:"spa",originFireUrl:"",onNewAegis:function(t){var n=this;history.pushState=this.wr("pushState")||history.pushState,history.replaceState=this.wr("replaceState")||history.replaceState,this.sendPv=this.sendPv.bind(this),t.config.spa&&this.sendPv(t),Qt.forEach(function(e){return window.addEventListener(e,function(){return n.sendPv.call(n,t)})})},wr:function(n){var r=history[n],e="__"+n+"__hasWrittenByTamSpa";return"function"==typeof r&&!history[e]&&(Object.defineProperty(history,e,{value:!0,enumerable:!1}),function(){var e=r.apply(this,arguments),t=null;return"function"==typeof Event?t=new Event(n):(t=document.createEvent("HTMLEvents")).initEvent(n,!1,!0),window.dispatchEvent(t),e})},sendPv:function(r){var o=this;setTimeout(function(){var t=location.pathname+location.hash+r.config.id;o.$walk(function(e){var n;e===r&&(n=e.config.pvUrl)&&t&&t!==o.originFireUrl&&(e.sendPipeline([function(e,t){t({url:""+n,type:v.PV})}],v.PV)(null),o.originFireUrl=t)})},0)},destroy:function(){this.option.sendPv=function(){}}}),o=(D(Nt=H,o=It=o),Nt.prototype=null===o?Object.create(o):(Zt.prototype=o.prototype,new Zt),H.prototype.getBean=function(t){var n=this;return void 0===t&&(t=[]),""+Object.getOwnPropertyNames(this.bean).filter(function(e){return-1===t.indexOf(e)}).map(function(e){return"from"===e?"from="+n.getCurrentPageUrl():e+"="+n.bean[e]}).join("&")},H.prototype.getCurrentPageUrl=function(){var e=this.config.pageUrl||location.href,e=(e="function"==typeof this.config.urlHandler?this.config.urlHandler()||location.href:e).slice(0,2048);return encodeURIComponent(e)},H.prototype.ready=function(){function i(){var e,n,r,o;t.reportRequestQueue.length&&(e=t.reportRequestQueue.splice(0,1)[0],n=e.options,r=e.success,o=e.fail,t.$request(n,function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{return null==r?void 0:r.apply(n,e)}finally{i()}},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{return null==o?void 0:o.apply(n,e)}finally{i()}}))}var t=this;i(),this.isReportReady=!0},H.prototype.request=function(e,t,n){this.config.reportImmediately||this.isReportReady?this.$request(e,t,n):this.reportRequestQueue.push({options:e,success:t,fail:n})},H.prototype.$request=function(s,e,t){var n,r,o,i;if(s&&"string"==typeof s.url&&""!==s.url&&this.bean.id)return i=s.url,!1!==s.addBean&&(i=function(e,t){var n,r,o,i=e,a=s.beanFilter||[];for(n in t)-1===a.indexOf(n)&&((o=(r=new RegExp("([?&])"+n+"(=([^&]*))?(?=&|$)")).exec(i))?o[2]&&""!==o[3]&&"undefined"!==o[3]||(i=i.replace(r,"$1"+n+"="+t[n])):(o=-1<i.indexOf("?")?"&":"?",i+=o+n+"="+t[n]));return i}(i,l(l({},this.bean),{from:this.getCurrentPageUrl()}))),s.url=i,i=s.method||"get",r=this.config.onBeforeRequest,(s=r?r(s,this):s)?s.url?void((null!=s&&s.sendBeacon||this.sendNow)&&"function"==typeof(null===navigator||void 0===navigator?void 0:navigator.sendBeacon)?navigator.sendBeacon(s.url,s.data):((n=new XMLHttpRequest).sendByAegis=!0,n.addEventListener("readystatechange",function(){4===n.readyState&&(400<=n.status||0===n.status?null!=t&&t(n.response):null!=e&&e(n.response))}),"get"===i.toLocaleLowerCase()?(n.open("get",(r=s.url,o=s.data,"string"!=typeof r?"":"object"==typeof o&&o?(i=Object.getOwnPropertyNames(o).map(function(e){var t=o[e];return e+"="+("string"==typeof t?encodeURIComponent(t):encodeURIComponent(JSON.stringify(t)))}).join("&").replace(/eval/gi,"evaI"),r+(-1===r.indexOf("?")?"?":"&")+i):r)),n.send()):(n.open("post",s.url),s.contentType&&n.setRequestHeader("Content-Type",s.contentType),"string"==typeof s.data&&(s.data=s.data.replace(/eval/gi,"evaI")),n.send(s.data)))):console.warn("Please handle the parameters reasonably, options.url is necessary"):console.warn("Sending request blocked")},H.prototype.publishPluginsLogs=function(){var e=H.installedPlugins.find(function(e){return"reportAssetSpeed"===e.name});null!=e&&e.option.collectNotReportedLog(this)},H.prototype.uploadLogs=function(e,t){var n;void 0===e&&(e={}),void 0===t&&(t={}),null!=(n=this.lifeCycle)&&n.emit("uploadLogs",e,t)},H.sessionID="session-"+Date.now(),H);function H(e){var t,n,r,a,s=It.call(this,e)||this;s.sendNow=!1,s.isReportReady=!1,s.reportRequestQueue=[],s.speedLogPipeline=m([ue(s),(a=s.config,function(e,t){var n,r,o,i="number"==typeof a.repeat?a.repeat:60;!a.speedSample||i<=0?t(e):(n=(null==a?void 0:a.id)||"0",r=T[n]||{},Array.isArray(e)?(o=e.filter(function(e){var t=!r[e.url]||r[e.url]<i;return t?(r[e.url]=1+~~r[e.url],T[n]=r):O[n]||Pe(n),t})).length&&t(o):!r[e.url]||r[e.url]<i?(r[e.url]=1+~~r[e.url],T[n]=r,t(e)):O[n]||Pe(n))}),(r=s,function(t,n){at(function(e){r.extendBean("netType",e),n(t)})}),function(e,t){null!=(n=s.lifeCycle)&&n.emit("beforeReportSpeed",e);var n,r=s.config.beforeReportSpeed;if((e="function"==typeof r?e.filter(function(e){return!1!==r(e)}):e).length)return t(e)},function(e,t){t(e.map(function(e){return void 0!==e.payload&&delete e.payload,e}))},function(e){return s.sendPipeline([function(e,t){var n,r,o,i;t({type:v.SPEED,url:""+s.config.speedUrl,method:"post",data:(t=e,n=l(l({},s.bean),{from:s.getCurrentPageUrl()}),o={fetch:[],static:[],bridge:[]},i=new FormData,Array.isArray(t)?t.forEach(function(e){var t=Ie(e);o[e.type].push(t)}):(r=Ie(t),o[t.type].push(r)),i.append("payload",be(l({duration:o},n))),i),log:e})}],v.SPEED)(e)}]),e.asyncPlugin=!0;try{"undefined"!=typeof document&&(e.uin=e.uin||(null!=(t=document.cookie.match(/\buin=\D+(\d*)/))?t:[])[1]||(null!=(n=document.cookie.match(/\bilive_uin=\D*(\d+)/))?n:[])[1]||""),s.init(e),s.extendBean("sessionId",H.sessionID),s.extendBean("from",s.getCurrentPageUrl()),"undefined"!=typeof document&&s.extendBean("referer",encodeURIComponent(document.referrer||"")),e.ext1&&s.extendBean("ext1",encodeURIComponent(e.ext1)),e.ext2&&s.extendBean("ext2",encodeURIComponent(e.ext2)),e.ext3&&s.extendBean("ext3",encodeURIComponent(e.ext3))}catch(e){console.warn(e),console.log("%cThe above error occurred in the process of initializing Aegis, which will affect your normal use of Aegis.\nIt is recommended that you contact us for feedback and thank you for your support.","color: red"),s.sendSDKError(e)}return s}function Zt(){this.constructor=Nt}new i({name:"ie"}),new i({name:"onClose"});var en=new i({name:"onClose",onNewAegis:function(n){var r,o=this;a.desktop()?(r=window.onunload,window.onunload=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];o.publishNotReportedLog(n),null!=r&&r.call.apply(r,u([window],e))}):Pt(this.publishNotReportedLog.bind(this,n),{once:!0})},publishNotReportedLog:function(t){var n=this;this.$walk(function(e){e===t&&(e.sendNow=!0,e.publishPluginsLogs(),n.publishThrottlePipeLogs(e))})},publishThrottlePipeLogs:function(e){null!=e&&e.speedLogPipeline([]),null!=e&&e.eventPipeline([]),null!=e&&e.customTimePipeline([]),null!=e&&e.normalLogPipeline([])}});return new i({name:"aid"}),o.use(lt),o.use(Qe),o.use(Ue),o.use(dt),o.use(zt),o.use(Ae),o.use(k),o.use(Yt),o.use(en),o.use(Ze),o});
