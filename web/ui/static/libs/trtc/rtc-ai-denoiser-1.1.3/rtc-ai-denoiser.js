!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("trtc-js-sdk")):"function"==typeof define&&define.amd?define(["trtc-js-sdk"],e):(t="undefined"!=typeof globalThis?globalThis:t||self).RTCAIDenoiser=e(t.TRTC)}(this,(function(t){function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=e(t);function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function o(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n(Object(r),!0).forEach((function(e){l(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function i(){i=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",a=n.asyncIterator||"@@asyncIterator",u=n.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(S){c=function(t,e,r){return t[e]=r}}function s(t,e,r,n){var o=e&&e.prototype instanceof p?e:p,i=Object.create(o.prototype),a=new x(n||[]);return i._invoke=function(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return k()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=A(a,r);if(u){if(u===l)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=f(t,e,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===l)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}(t,r,a),i}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(S){return{type:"throw",arg:S}}}t.wrap=s;var l={};function p(){}function h(){}function d(){}var v={};c(v,o,(function(){return this}));var y=Object.getPrototypeOf,g=y&&y(y(O([])));g&&g!==e&&r.call(g,o)&&(v=g);var m=d.prototype=p.prototype=Object.create(v);function w(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function b(t,e){function n(o,i,a,u){var c=f(t[o],t,i);if("throw"!==c.type){var s=c.arg,l=s.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,a,u)}),(function(t){n("throw",t,a,u)})):e.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return n("throw",t,a,u)}))}u(c.arg)}var o;this._invoke=function(t,r){function i(){return new e((function(e,o){n(t,r,e,o)}))}return o=o?o.then(i,i):i()}}function A(t,e){var r=t.iterator[e.method];if(void 0===r){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,A(t,e),"throw"===e.method))return l;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return l}var n=f(r,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,l;var o=n.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,l):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,l)}function _(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function T(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function x(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(_,this),this.reset(!0)}function O(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:k}}function k(){return{value:void 0,done:!0}}return h.prototype=d,c(m,"constructor",d),c(d,"constructor",h),h.displayName=c(d,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,c(t,u,"GeneratorFunction")),t.prototype=Object.create(m),t},t.awrap=function(t){return{__await:t}},w(b.prototype),c(b.prototype,a,(function(){return this})),t.AsyncIterator=b,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new b(s(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},w(m),c(m,u,"Generator"),c(m,o,(function(){return this})),c(m,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=[];for(var r in t)e.push(r);return e.reverse(),function r(){for(;e.length;){var n=e.pop();if(n in t)return r.value=n,r.done=!1,r}return r.done=!0,r}},t.values=O,x.prototype={constructor:x,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(T),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var u=r.call(i,"catchLoc"),c=r.call(i,"finallyLoc");if(u&&c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,l):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),T(r),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;T(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:O(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},t}function a(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(s){return void r(s)}u.done?e(c):Promise.resolve(c).then(n,o)}function u(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function u(t){a(i,n,o,u,c,"next",t)}function c(t){a(i,n,o,u,c,"throw",t)}u(void 0)}))}}function c(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function s(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function f(t,e,r){return e&&s(t.prototype,e),r&&s(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function l(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var p="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},h=function(t){try{return!!t()}catch(e){return!0}},d=!h((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),v=d,y=Function.prototype,g=y.call,m=v&&y.bind.bind(g,g),w=function(t){return v?m(t):function(){return g.apply(t,arguments)}},b=w,A=b({}.toString),_=b("".slice),T=function(t){return _(A(t),8,-1)},x=T,O=w,k=function(t){if("Function"===x(t))return O(t)},S=h,E=T,C=Object,I=k("".split),j=S((function(){return!C("z").propertyIsEnumerable(0)}))?function(t){return"String"==E(t)?I(t,""):C(t)}:C,P=function(t){return null==t},M=P,L=TypeError,R=function(t){if(M(t))throw L("Can't call method on "+t);return t},D=j,N=R,F=function(t){return D(N(t))},U=function(t){return t&&t.Math==Math&&t},B=U("object"==typeof globalThis&&globalThis)||U("object"==typeof window&&window)||U("object"==typeof self&&self)||U("object"==typeof p&&p)||function(){return this}()||Function("return this")(),G={exports:{}},V=B,W=Object.defineProperty,Y=function(t,e){try{W(V,t,{value:e,configurable:!0,writable:!0})}catch(r){V[t]=e}return e},z=Y,H="__core-js_shared__",q=B[H]||z(H,{}),K=q;(G.exports=function(t,e){return K[t]||(K[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var J,X,$=R,Q=Object,Z=function(t){return Q($(t))},tt=Z,et=k({}.hasOwnProperty),rt=Object.hasOwn||function(t,e){return et(tt(t),e)},nt=k,ot=0,it=Math.random(),at=nt(1..toString),ut=function(t){return"Symbol("+(void 0===t?"":t)+")_"+at(++ot+it,36)},ct="object"==typeof document&&document.all,st={all:ct,IS_HTMLDDA:void 0===ct&&void 0!==ct},ft=st.all,lt=st.IS_HTMLDDA?function(t){return"function"==typeof t||t===ft}:function(t){return"function"==typeof t},pt=B,ht=lt,dt=function(t){return ht(t)?t:void 0},vt=function(t,e){return arguments.length<2?dt(pt[t]):pt[t]&&pt[t][e]},yt=vt("navigator","userAgent")||"",gt=B,mt=yt,wt=gt.process,bt=gt.Deno,At=wt&&wt.versions||bt&&bt.version,_t=At&&At.v8;_t&&(X=(J=_t.split("."))[0]>0&&J[0]<4?1:+(J[0]+J[1])),!X&&mt&&(!(J=mt.match(/Edge\/(\d+)/))||J[1]>=74)&&(J=mt.match(/Chrome\/(\d+)/))&&(X=+J[1]);var Tt=X,xt=Tt,Ot=h,kt=!!Object.getOwnPropertySymbols&&!Ot((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&xt&&xt<41})),St=kt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Et=B,Ct=G.exports,It=rt,jt=ut,Pt=kt,Mt=St,Lt=Ct("wks"),Rt=Et.Symbol,Dt=Rt&&Rt.for,Nt=Mt?Rt:Rt&&Rt.withoutSetter||jt,Ft=function(t){if(!It(Lt,t)||!Pt&&"string"!=typeof Lt[t]){var e="Symbol."+t;Pt&&It(Rt,t)?Lt[t]=Rt[t]:Lt[t]=Mt&&Dt?Dt(e):Nt(e)}return Lt[t]},Ut=lt,Bt=st.all,Gt=st.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:Ut(t)||t===Bt}:function(t){return"object"==typeof t?null!==t:Ut(t)},Vt=Gt,Wt=String,Yt=TypeError,zt=function(t){if(Vt(t))return t;throw Yt(Wt(t)+" is not an object")},Ht={},qt=!h((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),Kt=qt&&h((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Jt={},Xt=Gt,$t=B.document,Qt=Xt($t)&&Xt($t.createElement),Zt=function(t){return Qt?$t.createElement(t):{}},te=Zt,ee=!qt&&!h((function(){return 7!=Object.defineProperty(te("div"),"a",{get:function(){return 7}}).a})),re=d,ne=Function.prototype.call,oe=re?ne.bind(ne):function(){return ne.apply(ne,arguments)},ie=k({}.isPrototypeOf),ae=vt,ue=lt,ce=ie,se=Object,fe=St?function(t){return"symbol"==typeof t}:function(t){var e=ae("Symbol");return ue(e)&&ce(e.prototype,se(t))},le=String,pe=function(t){try{return le(t)}catch(e){return"Object"}},he=lt,de=pe,ve=TypeError,ye=function(t){if(he(t))return t;throw ve(de(t)+" is not a function")},ge=ye,me=P,we=function(t,e){var r=t[e];return me(r)?void 0:ge(r)},be=oe,Ae=lt,_e=Gt,Te=TypeError,xe=oe,Oe=Gt,ke=fe,Se=we,Ee=function(t,e){var r,n;if("string"===e&&Ae(r=t.toString)&&!_e(n=be(r,t)))return n;if(Ae(r=t.valueOf)&&!_e(n=be(r,t)))return n;if("string"!==e&&Ae(r=t.toString)&&!_e(n=be(r,t)))return n;throw Te("Can't convert object to primitive value")},Ce=TypeError,Ie=Ft("toPrimitive"),je=function(t,e){if(!Oe(t)||ke(t))return t;var r,n=Se(t,Ie);if(n){if(void 0===e&&(e="default"),r=xe(n,t,e),!Oe(r)||ke(r))return r;throw Ce("Can't convert object to primitive value")}return void 0===e&&(e="number"),Ee(t,e)},Pe=je,Me=fe,Le=function(t){var e=Pe(t,"string");return Me(e)?e:e+""},Re=qt,De=ee,Ne=Kt,Fe=zt,Ue=Le,Be=TypeError,Ge=Object.defineProperty,Ve=Object.getOwnPropertyDescriptor,We="enumerable",Ye="configurable",ze="writable";Jt.f=Re?Ne?function(t,e,r){if(Fe(t),e=Ue(e),Fe(r),"function"==typeof t&&"prototype"===e&&"value"in r&&ze in r&&!r.writable){var n=Ve(t,e);n&&n.writable&&(t[e]=r.value,r={configurable:Ye in r?r.configurable:n.configurable,enumerable:We in r?r.enumerable:n.enumerable,writable:!1})}return Ge(t,e,r)}:Ge:function(t,e,r){if(Fe(t),e=Ue(e),Fe(r),De)try{return Ge(t,e,r)}catch(n){}if("get"in r||"set"in r)throw Be("Accessors not supported");return"value"in r&&(t[e]=r.value),t};var He=Math.ceil,qe=Math.floor,Ke=Math.trunc||function(t){var e=+t;return(e>0?qe:He)(e)},Je=function(t){var e=+t;return e!=e||0===e?0:Ke(e)},Xe=Je,$e=Math.max,Qe=Math.min,Ze=function(t,e){var r=Xe(t);return r<0?$e(r+e,0):Qe(r,e)},tr=Je,er=Math.min,rr=function(t){return t>0?er(tr(t),9007199254740991):0},nr=rr,or=function(t){return nr(t.length)},ir=F,ar=Ze,ur=or,cr=function(t){return function(e,r,n){var o,i=ir(e),a=ur(i),u=ar(n,a);if(t&&r!=r){for(;a>u;)if((o=i[u++])!=o)return!0}else for(;a>u;u++)if((t||u in i)&&i[u]===r)return t||u||0;return!t&&-1}},sr={includes:cr(!0),indexOf:cr(!1)},fr={},lr=rt,pr=F,hr=sr.indexOf,dr=fr,vr=k([].push),yr=function(t,e){var r,n=pr(t),o=0,i=[];for(r in n)!lr(dr,r)&&lr(n,r)&&vr(i,r);for(;e.length>o;)lr(n,r=e[o++])&&(~hr(i,r)||vr(i,r));return i},gr=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],mr=yr,wr=gr,br=Object.keys||function(t){return mr(t,wr)},Ar=qt,_r=Kt,Tr=Jt,xr=zt,Or=F,kr=br;Ht.f=Ar&&!_r?Object.defineProperties:function(t,e){xr(t);for(var r,n=Or(e),o=kr(e),i=o.length,a=0;i>a;)Tr.f(t,r=o[a++],n[r]);return t};var Sr,Er=vt("document","documentElement"),Cr=G.exports,Ir=ut,jr=Cr("keys"),Pr=function(t){return jr[t]||(jr[t]=Ir(t))},Mr=zt,Lr=Ht,Rr=gr,Dr=fr,Nr=Er,Fr=Zt,Ur=Pr("IE_PROTO"),Br=function(){},Gr=function(t){return"<script>"+t+"</"+"script>"},Vr=function(t){t.write(Gr("")),t.close();var e=t.parentWindow.Object;return t=null,e},Wr=function(){try{Sr=new ActiveXObject("htmlfile")}catch(n){}var t,e;Wr="undefined"!=typeof document?document.domain&&Sr?Vr(Sr):((e=Fr("iframe")).style.display="none",Nr.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(Gr("document.F=Object")),t.close(),t.F):Vr(Sr);for(var r=Rr.length;r--;)delete Wr.prototype[Rr[r]];return Wr()};Dr[Ur]=!0;var Yr=Object.create||function(t,e){var r;return null!==t?(Br.prototype=Mr(t),r=new Br,Br.prototype=null,r[Ur]=t):r=Wr(),void 0===e?r:Lr.f(r,e)},zr=Ft,Hr=Yr,qr=Jt.f,Kr=zr("unscopables"),Jr=Array.prototype;null==Jr[Kr]&&qr(Jr,Kr,{configurable:!0,value:Hr(null)});var Xr,$r,Qr,Zr={},tn=lt,en=B.WeakMap,rn=tn(en)&&/native code/.test(String(en)),nn=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},on=Jt,an=nn,un=qt?function(t,e,r){return on.f(t,e,an(1,r))}:function(t,e,r){return t[e]=r,t},cn=rn,sn=B,fn=Gt,ln=un,pn=rt,hn=q,dn=Pr,vn=fr,yn="Object already initialized",gn=sn.TypeError,mn=sn.WeakMap;if(cn||hn.state){var wn=hn.state||(hn.state=new mn);wn.get=wn.get,wn.has=wn.has,wn.set=wn.set,Xr=function(t,e){if(wn.has(t))throw gn(yn);return e.facade=t,wn.set(t,e),e},$r=function(t){return wn.get(t)||{}},Qr=function(t){return wn.has(t)}}else{var bn=dn("state");vn[bn]=!0,Xr=function(t,e){if(pn(t,bn))throw gn(yn);return e.facade=t,ln(t,bn,e),e},$r=function(t){return pn(t,bn)?t[bn]:{}},Qr=function(t){return pn(t,bn)}}var An={set:Xr,get:$r,has:Qr,enforce:function(t){return Qr(t)?$r(t):Xr(t,{})},getterFor:function(t){return function(e){var r;if(!fn(e)||(r=$r(e)).type!==t)throw gn("Incompatible receiver, "+t+" required");return r}}},_n={},Tn={},xn={}.propertyIsEnumerable,On=Object.getOwnPropertyDescriptor,kn=On&&!xn.call({1:2},1);Tn.f=kn?function(t){var e=On(this,t);return!!e&&e.enumerable}:xn;var Sn=qt,En=oe,Cn=Tn,In=nn,jn=F,Pn=Le,Mn=rt,Ln=ee,Rn=Object.getOwnPropertyDescriptor;_n.f=Sn?Rn:function(t,e){if(t=jn(t),e=Pn(e),Ln)try{return Rn(t,e)}catch(r){}if(Mn(t,e))return In(!En(Cn.f,t,e),t[e])};var Dn={exports:{}},Nn=qt,Fn=rt,Un=Function.prototype,Bn=Nn&&Object.getOwnPropertyDescriptor,Gn=Fn(Un,"name"),Vn={EXISTS:Gn,PROPER:Gn&&"something"===function(){}.name,CONFIGURABLE:Gn&&(!Nn||Nn&&Bn(Un,"name").configurable)},Wn=lt,Yn=q,zn=k(Function.toString);Wn(Yn.inspectSource)||(Yn.inspectSource=function(t){return zn(t)});var Hn=Yn.inspectSource,qn=h,Kn=lt,Jn=rt,Xn=qt,$n=Vn.CONFIGURABLE,Qn=Hn,Zn=An.enforce,to=An.get,eo=Object.defineProperty,ro=Xn&&!qn((function(){return 8!==eo((function(){}),"length",{value:8}).length})),no=String(String).split("String"),oo=Dn.exports=function(t,e,r){"Symbol("===String(e).slice(0,7)&&(e="["+String(e).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(e="get "+e),r&&r.setter&&(e="set "+e),(!Jn(t,"name")||$n&&t.name!==e)&&(Xn?eo(t,"name",{value:e,configurable:!0}):t.name=e),ro&&r&&Jn(r,"arity")&&t.length!==r.arity&&eo(t,"length",{value:r.arity});try{r&&Jn(r,"constructor")&&r.constructor?Xn&&eo(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(o){}var n=Zn(t);return Jn(n,"source")||(n.source=no.join("string"==typeof e?e:"")),t};Function.prototype.toString=oo((function(){return Kn(this)&&to(this).source||Qn(this)}),"toString");var io=lt,ao=Jt,uo=Dn.exports,co=Y,so=function(t,e,r,n){n||(n={});var o=n.enumerable,i=void 0!==n.name?n.name:e;if(io(r)&&uo(r,i,n),n.global)o?t[e]=r:co(e,r);else{try{n.unsafe?t[e]&&(o=!0):delete t[e]}catch(a){}o?t[e]=r:ao.f(t,e,{value:r,enumerable:!1,configurable:!n.nonConfigurable,writable:!n.nonWritable})}return t},fo={},lo=yr,po=gr.concat("length","prototype");fo.f=Object.getOwnPropertyNames||function(t){return lo(t,po)};var ho={};ho.f=Object.getOwnPropertySymbols;var vo,yo,go,mo=vt,wo=fo,bo=ho,Ao=zt,_o=k([].concat),To=mo("Reflect","ownKeys")||function(t){var e=wo.f(Ao(t)),r=bo.f;return r?_o(e,r(t)):e},xo=rt,Oo=To,ko=_n,So=Jt,Eo=h,Co=lt,Io=/#|\.prototype\./,jo=function(t,e){var r=Mo[Po(t)];return r==Ro||r!=Lo&&(Co(e)?Eo(e):!!e)},Po=jo.normalize=function(t){return String(t).replace(Io,".").toLowerCase()},Mo=jo.data={},Lo=jo.NATIVE="N",Ro=jo.POLYFILL="P",Do=jo,No=B,Fo=_n.f,Uo=un,Bo=so,Go=Y,Vo=function(t,e,r){for(var n=Oo(e),o=So.f,i=ko.f,a=0;a<n.length;a++){var u=n[a];xo(t,u)||r&&xo(r,u)||o(t,u,i(e,u))}},Wo=Do,Yo=function(t,e){var r,n,o,i,a,u=t.target,c=t.global,s=t.stat;if(r=c?No:s?No[u]||Go(u,{}):(No[u]||{}).prototype)for(n in e){if(i=e[n],o=t.dontCallGetSet?(a=Fo(r,n))&&a.value:r[n],!Wo(c?n:u+(s?".":"#")+n,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Vo(i,o)}(t.sham||o&&o.sham)&&Uo(i,"sham",!0),Bo(r,n,i,t)}},zo=!h((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),Ho=rt,qo=lt,Ko=Z,Jo=zo,Xo=Pr("IE_PROTO"),$o=Object,Qo=$o.prototype,Zo=Jo?$o.getPrototypeOf:function(t){var e=Ko(t);if(Ho(e,Xo))return e[Xo];var r=e.constructor;return qo(r)&&e instanceof r?r.prototype:e instanceof $o?Qo:null},ti=h,ei=lt,ri=Gt,ni=Zo,oi=so,ii=Ft("iterator"),ai=!1;[].keys&&("next"in(go=[].keys())?(yo=ni(ni(go)))!==Object.prototype&&(vo=yo):ai=!0);var ui=!ri(vo)||ti((function(){var t={};return vo[ii].call(t)!==t}));ui&&(vo={}),ei(vo[ii])||oi(vo,ii,(function(){return this}));var ci={IteratorPrototype:vo,BUGGY_SAFARI_ITERATORS:ai},si=Jt.f,fi=rt,li=Ft("toStringTag"),pi=function(t,e,r){t&&!r&&(t=t.prototype),t&&!fi(t,li)&&si(t,li,{configurable:!0,value:e})},hi=ci.IteratorPrototype,di=Yr,vi=nn,yi=pi,gi=Zr,mi=function(){return this},wi=lt,bi=String,Ai=TypeError,_i=k,Ti=zt,xi=function(t){if("object"==typeof t||wi(t))return t;throw Ai("Can't set "+bi(t)+" as a prototype")},Oi=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,r={};try{(t=_i(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(r,[]),e=r instanceof Array}catch(n){}return function(r,n){return Ti(r),xi(n),e?t(r,n):r.__proto__=n,r}}():void 0),ki=Yo,Si=oe,Ei=lt,Ci=function(t,e,r,n){var o=e+" Iterator";return t.prototype=di(hi,{next:vi(+!n,r)}),yi(t,o,!1),gi[o]=mi,t},Ii=Zo,ji=Oi,Pi=pi,Mi=un,Li=so,Ri=Zr,Di=Vn.PROPER,Ni=Vn.CONFIGURABLE,Fi=ci.IteratorPrototype,Ui=ci.BUGGY_SAFARI_ITERATORS,Bi=Ft("iterator"),Gi="keys",Vi="values",Wi="entries",Yi=function(){return this},zi=function(t,e,r,n,o,i,a){Ci(r,e,n);var u,c,s,f=function(t){if(t===o&&v)return v;if(!Ui&&t in h)return h[t];switch(t){case Gi:case Vi:case Wi:return function(){return new r(this,t)}}return function(){return new r(this)}},l=e+" Iterator",p=!1,h=t.prototype,d=h[Bi]||h["@@iterator"]||o&&h[o],v=!Ui&&d||f(o),y="Array"==e&&h.entries||d;if(y&&(u=Ii(y.call(new t)))!==Object.prototype&&u.next&&(Ii(u)!==Fi&&(ji?ji(u,Fi):Ei(u[Bi])||Li(u,Bi,Yi)),Pi(u,l,!0)),Di&&o==Vi&&d&&d.name!==Vi&&(Ni?Mi(h,"name",Vi):(p=!0,v=function(){return Si(d,this)})),o)if(c={values:f(Vi),keys:i?v:f(Gi),entries:f(Wi)},a)for(s in c)(Ui||p||!(s in h))&&Li(h,s,c[s]);else ki({target:e,proto:!0,forced:Ui||p},c);return h[Bi]!==v&&Li(h,Bi,v,{name:o}),Ri[e]=v,c},Hi=function(t,e){return{value:t,done:e}},qi=F,Ki=function(t){Jr[Kr][t]=!0},Ji=Zr,Xi=An,$i=Jt.f,Qi=zi,Zi=Hi,ta=qt,ea="Array Iterator",ra=Xi.set,na=Xi.getterFor(ea),oa=Qi(Array,"Array",(function(t,e){ra(this,{type:ea,target:qi(t),index:0,kind:e})}),(function(){var t=na(this),e=t.target,r=t.kind,n=t.index++;return!e||n>=e.length?(t.target=void 0,Zi(void 0,!0)):Zi("keys"==r?n:"values"==r?e[n]:[n,e[n]],!1)}),"values"),ia=Ji.Arguments=Ji.Array;if(Ki("keys"),Ki("values"),Ki("entries"),ta&&"values"!==ia.name)try{$i(ia,"name",{value:"values"})}catch(rA){}var aa={exports:{}},ua={},ca=Le,sa=Jt,fa=nn,la=function(t,e,r){var n=ca(e);n in t?sa.f(t,n,fa(0,r)):t[n]=r},pa=Ze,ha=or,da=la,va=Array,ya=Math.max,ga=function(t,e,r){for(var n=ha(t),o=pa(e,n),i=pa(void 0===r?n:r,n),a=va(ya(i-o,0)),u=0;o<i;o++,u++)da(a,u,t[o]);return a.length=u,a},ma=T,wa=F,ba=fo.f,Aa=ga,_a="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];ua.f=function(t){return _a&&"Window"==ma(t)?function(t){try{return ba(t)}catch(rA){return Aa(_a)}}(t):ba(wa(t))};var Ta=h((function(){if("function"==typeof ArrayBuffer){var t=new ArrayBuffer(8);Object.isExtensible(t)&&Object.defineProperty(t,"a",{value:8})}})),xa=h,Oa=Gt,ka=T,Sa=Ta,Ea=Object.isExtensible,Ca=xa((function(){Ea(1)}))||Sa?function(t){return!!Oa(t)&&((!Sa||"ArrayBuffer"!=ka(t))&&(!Ea||Ea(t)))}:Ea,Ia=!h((function(){return Object.isExtensible(Object.preventExtensions({}))})),ja=Yo,Pa=k,Ma=fr,La=Gt,Ra=rt,Da=Jt.f,Na=fo,Fa=ua,Ua=Ca,Ba=Ia,Ga=!1,Va=ut("meta"),Wa=0,Ya=function(t){Da(t,Va,{value:{objectID:"O"+Wa++,weakData:{}}})},za=aa.exports={enable:function(){za.enable=function(){},Ga=!0;var t=Na.f,e=Pa([].splice),r={};r[Va]=1,t(r).length&&(Na.f=function(r){for(var n=t(r),o=0,i=n.length;o<i;o++)if(n[o]===Va){e(n,o,1);break}return n},ja({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:Fa.f}))},fastKey:function(t,e){if(!La(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!Ra(t,Va)){if(!Ua(t))return"F";if(!e)return"E";Ya(t)}return t[Va].objectID},getWeakData:function(t,e){if(!Ra(t,Va)){if(!Ua(t))return!0;if(!e)return!1;Ya(t)}return t[Va].weakData},onFreeze:function(t){return Ba&&Ga&&Ua(t)&&!Ra(t,Va)&&Ya(t),t}};Ma[Va]=!0;var Ha=ye,qa=d,Ka=k(k.bind),Ja=function(t,e){return Ha(t),void 0===e?t:qa?Ka(t,e):function(){return t.apply(e,arguments)}},Xa=Zr,$a=Ft("iterator"),Qa=Array.prototype,Za=function(t){return void 0!==t&&(Xa.Array===t||Qa[$a]===t)},tu={};tu[Ft("toStringTag")]="z";var eu="[object z]"===String(tu),ru=eu,nu=lt,ou=T,iu=Ft("toStringTag"),au=Object,uu="Arguments"==ou(function(){return arguments}()),cu=ru?ou:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(rA){}}(e=au(t),iu))?r:uu?ou(e):"Object"==(n=ou(e))&&nu(e.callee)?"Arguments":n},su=cu,fu=we,lu=P,pu=Zr,hu=Ft("iterator"),du=function(t){if(!lu(t))return fu(t,hu)||fu(t,"@@iterator")||pu[su(t)]},vu=oe,yu=ye,gu=zt,mu=pe,wu=du,bu=TypeError,Au=function(t,e){var r=arguments.length<2?wu(t):e;if(yu(r))return gu(vu(r,t));throw bu(mu(t)+" is not iterable")},_u=oe,Tu=zt,xu=we,Ou=Ja,ku=oe,Su=zt,Eu=pe,Cu=Za,Iu=or,ju=ie,Pu=Au,Mu=du,Lu=function(t,e,r){var n,o;Tu(t);try{if(!(n=xu(t,"return"))){if("throw"===e)throw r;return r}n=_u(n,t)}catch(rA){o=!0,n=rA}if("throw"===e)throw r;if(o)throw n;return Tu(n),r},Ru=TypeError,Du=function(t,e){this.stopped=t,this.result=e},Nu=Du.prototype,Fu=function(t,e,r){var n,o,i,a,u,c,s,f=r&&r.that,l=!(!r||!r.AS_ENTRIES),p=!(!r||!r.IS_RECORD),h=!(!r||!r.IS_ITERATOR),d=!(!r||!r.INTERRUPTED),v=Ou(e,f),y=function(t){return n&&Lu(n,"normal",t),new Du(!0,t)},g=function(t){return l?(Su(t),d?v(t[0],t[1],y):v(t[0],t[1])):d?v(t,y):v(t)};if(p)n=t.iterator;else if(h)n=t;else{if(!(o=Mu(t)))throw Ru(Eu(t)+" is not iterable");if(Cu(o)){for(i=0,a=Iu(t);a>i;i++)if((u=g(t[i]))&&ju(Nu,u))return u;return new Du(!1)}n=Pu(t,o)}for(c=p?t.next:n.next;!(s=ku(c,n)).done;){try{u=g(s.value)}catch(rA){Lu(n,"throw",rA)}if("object"==typeof u&&u&&ju(Nu,u))return u}return new Du(!1)},Uu=ie,Bu=TypeError,Gu=function(t,e){if(Uu(e,t))return t;throw Bu("Incorrect invocation")},Vu=Ft("iterator"),Wu=!1;try{var Yu=0,zu={next:function(){return{done:!!Yu++}},return:function(){Wu=!0}};zu[Vu]=function(){return this},Array.from(zu,(function(){throw 2}))}catch(rA){}var Hu=function(t,e){if(!e&&!Wu)return!1;var r=!1;try{var n={};n[Vu]=function(){return{next:function(){return{done:r=!0}}}},t(n)}catch(rA){}return r},qu=lt,Ku=Gt,Ju=Oi,Xu=function(t,e,r){var n,o;return Ju&&qu(n=e.constructor)&&n!==r&&Ku(o=n.prototype)&&o!==r.prototype&&Ju(t,o),t},$u=Yo,Qu=B,Zu=k,tc=Do,ec=so,rc=aa.exports,nc=Fu,oc=Gu,ic=lt,ac=P,uc=Gt,cc=h,sc=Hu,fc=pi,lc=Xu,pc=so,hc=function(t,e,r){for(var n in e)pc(t,n,e[n],r);return t},dc=vt,vc=Jt,yc=qt,gc=Ft("species"),mc=function(t){var e=dc(t),r=vc.f;yc&&e&&!e[gc]&&r(e,gc,{configurable:!0,get:function(){return this}})},wc=Jt.f,bc=Yr,Ac=hc,_c=Ja,Tc=Gu,xc=P,Oc=Fu,kc=zi,Sc=Hi,Ec=mc,Cc=qt,Ic=aa.exports.fastKey,jc=An.set,Pc=An.getterFor,Mc={getConstructor:function(t,e,r,n){var o=t((function(t,o){Tc(t,i),jc(t,{type:e,index:bc(null),first:void 0,last:void 0,size:0}),Cc||(t.size=0),xc(o)||Oc(o,t[n],{that:t,AS_ENTRIES:r})})),i=o.prototype,a=Pc(e),u=function(t,e,r){var n,o,i=a(t),u=c(t,e);return u?u.value=r:(i.last=u={index:o=Ic(e,!0),key:e,value:r,previous:n=i.last,next:void 0,removed:!1},i.first||(i.first=u),n&&(n.next=u),Cc?i.size++:t.size++,"F"!==o&&(i.index[o]=u)),t},c=function(t,e){var r,n=a(t),o=Ic(e);if("F"!==o)return n.index[o];for(r=n.first;r;r=r.next)if(r.key==e)return r};return Ac(i,{clear:function(){for(var t=a(this),e=t.index,r=t.first;r;)r.removed=!0,r.previous&&(r.previous=r.previous.next=void 0),delete e[r.index],r=r.next;t.first=t.last=void 0,Cc?t.size=0:this.size=0},delete:function(t){var e=this,r=a(e),n=c(e,t);if(n){var o=n.next,i=n.previous;delete r.index[n.index],n.removed=!0,i&&(i.next=o),o&&(o.previous=i),r.first==n&&(r.first=o),r.last==n&&(r.last=i),Cc?r.size--:e.size--}return!!n},forEach:function(t){for(var e,r=a(this),n=_c(t,arguments.length>1?arguments[1]:void 0);e=e?e.next:r.first;)for(n(e.value,e.key,this);e&&e.removed;)e=e.previous},has:function(t){return!!c(this,t)}}),Ac(i,r?{get:function(t){var e=c(this,t);return e&&e.value},set:function(t,e){return u(this,0===t?0:t,e)}}:{add:function(t){return u(this,t=0===t?0:t,t)}}),Cc&&wc(i,"size",{get:function(){return a(this).size}}),o},setStrong:function(t,e,r){var n=e+" Iterator",o=Pc(e),i=Pc(n);kc(t,e,(function(t,e){jc(this,{type:n,target:t,state:o(t),kind:e,last:void 0})}),(function(){for(var t=i(this),e=t.kind,r=t.last;r&&r.removed;)r=r.previous;return t.target&&(t.last=r=r?r.next:t.state.first)?Sc("keys"==e?r.key:"values"==e?r.value:[r.key,r.value],!1):(t.target=void 0,Sc(void 0,!0))}),r?"entries":"values",!r,!0),Ec(e)}},Lc=function(t,e,r){var n=-1!==t.indexOf("Map"),o=-1!==t.indexOf("Weak"),i=n?"set":"add",a=Qu[t],u=a&&a.prototype,c=a,s={},f=function(t){var e=Zu(u[t]);ec(u,t,"add"==t?function(t){return e(this,0===t?0:t),this}:"delete"==t?function(t){return!(o&&!uc(t))&&e(this,0===t?0:t)}:"get"==t?function(t){return o&&!uc(t)?void 0:e(this,0===t?0:t)}:"has"==t?function(t){return!(o&&!uc(t))&&e(this,0===t?0:t)}:function(t,r){return e(this,0===t?0:t,r),this})};if(tc(t,!ic(a)||!(o||u.forEach&&!cc((function(){(new a).entries().next()})))))c=r.getConstructor(e,t,n,i),rc.enable();else if(tc(t,!0)){var l=new c,p=l[i](o?{}:-0,1)!=l,h=cc((function(){l.has(1)})),d=sc((function(t){new a(t)})),v=!o&&cc((function(){for(var t=new a,e=5;e--;)t[i](e,e);return!t.has(-0)}));d||((c=e((function(t,e){oc(t,u);var r=lc(new a,t,c);return ac(e)||nc(e,r[i],{that:r,AS_ENTRIES:n}),r}))).prototype=u,u.constructor=c),(h||v)&&(f("delete"),f("has"),n&&f("get")),(v||p)&&f(i),o&&u.clear&&delete u.clear}return s[t]=c,$u({global:!0,constructor:!0,forced:c!=a},s),fc(c,t),o||r.setStrong(c,t,n),c};Lc("Map",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),Mc);var Rc=cu,Dc=eu?{}.toString:function(){return"[object "+Rc(this)+"]"};eu||so(Object.prototype,"toString",Dc,{unsafe:!0});var Nc=cu,Fc=String,Uc=function(t){if("Symbol"===Nc(t))throw TypeError("Cannot convert a Symbol value to a string");return Fc(t)},Bc=k,Gc=Je,Vc=Uc,Wc=R,Yc=Bc("".charAt),zc=Bc("".charCodeAt),Hc=Bc("".slice),qc=function(t){return function(e,r){var n,o,i=Vc(Wc(e)),a=Gc(r),u=i.length;return a<0||a>=u?t?"":void 0:(n=zc(i,a))<55296||n>56319||a+1===u||(o=zc(i,a+1))<56320||o>57343?t?Yc(i,a):n:t?Hc(i,a,a+2):o-56320+(n-55296<<10)+65536}},Kc={codeAt:qc(!1),charAt:qc(!0)}.charAt,Jc=Uc,Xc=An,$c=zi,Qc=Hi,Zc="String Iterator",ts=Xc.set,es=Xc.getterFor(Zc);$c(String,"String",(function(t){ts(this,{type:Zc,string:Jc(t),index:0})}),(function(){var t,e=es(this),r=e.string,n=e.index;return n>=r.length?Qc(void 0,!0):(t=Kc(r,n),e.index+=t.length,Qc(t,!1))}));var rs=Zt("span").classList,ns=rs&&rs.constructor&&rs.constructor.prototype,os=ns===Object.prototype?void 0:ns,is=B,as={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},us=os,cs=oa,ss=un,fs=Ft,ls=fs("iterator"),ps=fs("toStringTag"),hs=cs.values,ds=function(t,e){if(t){if(t[ls]!==hs)try{ss(t,ls,hs)}catch(rA){t[ls]=hs}if(t[ps]||ss(t,ps,e),as[e])for(var r in cs)if(t[r]!==cs[r])try{ss(t,r,cs[r])}catch(rA){t[r]=cs[r]}}};for(var vs in as)ds(is[vs]&&is[vs].prototype,vs);ds(us,"DOMTokenList");var ys=T,gs=Array.isArray||function(t){return"Array"==ys(t)},ms=k,ws=h,bs=lt,As=cu,_s=Hn,Ts=function(){},xs=[],Os=vt("Reflect","construct"),ks=/^\s*(?:class|function)\b/,Ss=ms(ks.exec),Es=!ks.exec(Ts),Cs=function(t){if(!bs(t))return!1;try{return Os(Ts,xs,t),!0}catch(rA){return!1}},Is=function(t){if(!bs(t))return!1;switch(As(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Es||!!Ss(ks,_s(t))}catch(rA){return!0}};Is.sham=!0;var js=!Os||ws((function(){var t;return Cs(Cs.call)||!Cs(Object)||!Cs((function(){t=!0}))||t}))?Is:Cs,Ps=h,Ms=Tt,Ls=Ft("species"),Rs=function(t){return Ms>=51||!Ps((function(){var e=[];return(e.constructor={})[Ls]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},Ds=k([].slice),Ns=Yo,Fs=gs,Us=js,Bs=Gt,Gs=Ze,Vs=or,Ws=F,Ys=la,zs=Ft,Hs=Ds,qs=Rs("slice"),Ks=zs("species"),Js=Array,Xs=Math.max;Ns({target:"Array",proto:!0,forced:!qs},{slice:function(t,e){var r,n,o,i=Ws(this),a=Vs(i),u=Gs(t,a),c=Gs(void 0===e?a:e,a);if(Fs(i)&&(r=i.constructor,(Us(r)&&(r===Js||Fs(r.prototype))||Bs(r)&&null===(r=r[Ks]))&&(r=void 0),r===Js||void 0===r))return Hs(i,u,c);for(n=new(void 0===r?Js:r)(Xs(c-u,0)),o=0;u<c;u++,o++)u in i&&Ys(n,o,i[u]);return n.length=o,n}});var $s=Yo,Qs=Date,Zs=k(Qs.prototype.getTime);$s({target:"Date",stat:!0},{now:function(){return Zs(new Qs)}});var tf=k,ef=so,rf=Date.prototype,nf="Invalid Date",of="toString",af=tf(rf.toString),uf=tf(rf.getTime);String(new Date(NaN))!=nf&&ef(rf,of,(function(){var t=uf(this);return t==t?af(this):nf}));var cf=TypeError,sf=gs,ff=js,lf=Gt,pf=Ft("species"),hf=Array,df=function(t){var e;return sf(t)&&(e=t.constructor,(ff(e)&&(e===hf||sf(e.prototype))||lf(e)&&null===(e=e[pf]))&&(e=void 0)),void 0===e?hf:e},vf=function(t,e){return new(df(t))(0===e?0:e)},yf=Yo,gf=h,mf=gs,wf=Gt,bf=Z,Af=or,_f=function(t){if(t>9007199254740991)throw cf("Maximum allowed index exceeded");return t},Tf=la,xf=vf,Of=Rs,kf=Tt,Sf=Ft("isConcatSpreadable"),Ef=kf>=51||!gf((function(){var t=[];return t[Sf]=!1,t.concat()[0]!==t})),Cf=Of("concat"),If=function(t){if(!wf(t))return!1;var e=t[Sf];return void 0!==e?!!e:mf(t)};yf({target:"Array",proto:!0,arity:1,forced:!Ef||!Cf},{concat:function(t){var e,r,n,o,i,a=bf(this),u=xf(a,0),c=0;for(e=-1,n=arguments.length;e<n;e++)if(If(i=-1===e?a:arguments[e]))for(o=Af(i),_f(c+o),r=0;r<o;r++,c++)r in i&&Tf(u,c,i[r]);else _f(c+1),Tf(u,c++,i);return u.length=c,u}});var jf,Pf,Mf,Lf,Rf="process"==T(B.process),Df=js,Nf=pe,Ff=TypeError,Uf=function(t){if(Df(t))return t;throw Ff(Nf(t)+" is not a constructor")},Bf=zt,Gf=Uf,Vf=P,Wf=Ft("species"),Yf=function(t,e){var r,n=Bf(t).constructor;return void 0===n||Vf(r=Bf(n)[Wf])?e:Gf(r)},zf=d,Hf=Function.prototype,qf=Hf.apply,Kf=Hf.call,Jf="object"==typeof Reflect&&Reflect.apply||(zf?Kf.bind(qf):function(){return Kf.apply(qf,arguments)}),Xf=TypeError,$f=/(?:ipad|iphone|ipod).*applewebkit/i.test(yt),Qf=B,Zf=Jf,tl=Ja,el=lt,rl=rt,nl=h,ol=Er,il=Ds,al=Zt,ul=function(t,e){if(t<e)throw Xf("Not enough arguments");return t},cl=$f,sl=Rf,fl=Qf.setImmediate,ll=Qf.clearImmediate,pl=Qf.process,hl=Qf.Dispatch,dl=Qf.Function,vl=Qf.MessageChannel,yl=Qf.String,gl=0,ml={},wl="onreadystatechange";try{jf=Qf.location}catch(rA){}var bl=function(t){if(rl(ml,t)){var e=ml[t];delete ml[t],e()}},Al=function(t){return function(){bl(t)}},_l=function(t){bl(t.data)},Tl=function(t){Qf.postMessage(yl(t),jf.protocol+"//"+jf.host)};fl&&ll||(fl=function(t){ul(arguments.length,1);var e=el(t)?t:dl(t),r=il(arguments,1);return ml[++gl]=function(){Zf(e,void 0,r)},Pf(gl),gl},ll=function(t){delete ml[t]},sl?Pf=function(t){pl.nextTick(Al(t))}:hl&&hl.now?Pf=function(t){hl.now(Al(t))}:vl&&!cl?(Lf=(Mf=new vl).port2,Mf.port1.onmessage=_l,Pf=tl(Lf.postMessage,Lf)):Qf.addEventListener&&el(Qf.postMessage)&&!Qf.importScripts&&jf&&"file:"!==jf.protocol&&!nl(Tl)?(Pf=Tl,Qf.addEventListener("message",_l,!1)):Pf=wl in al("script")?function(t){ol.appendChild(al("script")).onreadystatechange=function(){ol.removeChild(this),bl(t)}}:function(t){setTimeout(Al(t),0)});var xl,Ol,kl,Sl,El,Cl,Il,jl,Pl={set:fl,clear:ll},Ml=B,Ll=/ipad|iphone|ipod/i.test(yt)&&void 0!==Ml.Pebble,Rl=/web0s(?!.*chrome)/i.test(yt),Dl=B,Nl=Ja,Fl=_n.f,Ul=Pl.set,Bl=$f,Gl=Ll,Vl=Rl,Wl=Rf,Yl=Dl.MutationObserver||Dl.WebKitMutationObserver,zl=Dl.document,Hl=Dl.process,ql=Dl.Promise,Kl=Fl(Dl,"queueMicrotask"),Jl=Kl&&Kl.value;Jl||(xl=function(){var t,e;for(Wl&&(t=Hl.domain)&&t.exit();Ol;){e=Ol.fn,Ol=Ol.next;try{e()}catch(rA){throw Ol?Sl():kl=void 0,rA}}kl=void 0,t&&t.enter()},Bl||Wl||Vl||!Yl||!zl?!Gl&&ql&&ql.resolve?((Il=ql.resolve(void 0)).constructor=ql,jl=Nl(Il.then,Il),Sl=function(){jl(xl)}):Wl?Sl=function(){Hl.nextTick(xl)}:(Ul=Nl(Ul,Dl),Sl=function(){Ul(xl)}):(El=!0,Cl=zl.createTextNode(""),new Yl(xl).observe(Cl,{characterData:!0}),Sl=function(){Cl.data=El=!El}));var Xl=Jl||function(t){var e={fn:t,next:void 0};kl&&(kl.next=e),Ol||(Ol=e,Sl()),kl=e},$l=B,Ql=function(t){try{return{error:!1,value:t()}}catch(rA){return{error:!0,value:rA}}},Zl=function(){this.head=null,this.tail=null};Zl.prototype={add:function(t){var e={item:t,next:null};this.head?this.tail.next=e:this.head=e,this.tail=e},get:function(){var t=this.head;if(t)return this.head=t.next,this.tail===t&&(this.tail=null),t.item}};var tp=Zl,ep=B.Promise,rp="object"==typeof Deno&&Deno&&"object"==typeof Deno.version,np=!rp&&!Rf&&"object"==typeof window&&"object"==typeof document,op=B,ip=ep,ap=lt,up=Do,cp=Hn,sp=Ft,fp=np,lp=rp,pp=Tt;ip&&ip.prototype;var hp=sp("species"),dp=!1,vp=ap(op.PromiseRejectionEvent),yp=up("Promise",(function(){var t=cp(ip),e=t!==String(ip);if(!e&&66===pp)return!0;if(!pp||pp<51||!/native code/.test(t)){var r=new ip((function(t){t(1)})),n=function(t){t((function(){}),(function(){}))};if((r.constructor={})[hp]=n,!(dp=r.then((function(){}))instanceof n))return!0}return!e&&(fp||lp)&&!vp})),gp={CONSTRUCTOR:yp,REJECTION_EVENT:vp,SUBCLASSING:dp},mp={},wp=ye,bp=TypeError,Ap=function(t){var e,r;this.promise=new t((function(t,n){if(void 0!==e||void 0!==r)throw bp("Bad Promise constructor");e=t,r=n})),this.resolve=wp(e),this.reject=wp(r)};mp.f=function(t){return new Ap(t)};var _p,Tp,xp,Op=Yo,kp=Rf,Sp=B,Ep=oe,Cp=so,Ip=Oi,jp=pi,Pp=mc,Mp=ye,Lp=lt,Rp=Gt,Dp=Gu,Np=Yf,Fp=Pl.set,Up=Xl,Bp=function(t,e){var r=$l.console;r&&r.error&&(1==arguments.length?r.error(t):r.error(t,e))},Gp=Ql,Vp=tp,Wp=An,Yp=ep,zp=mp,Hp="Promise",qp=gp.CONSTRUCTOR,Kp=gp.REJECTION_EVENT,Jp=gp.SUBCLASSING,Xp=Wp.getterFor(Hp),$p=Wp.set,Qp=Yp&&Yp.prototype,Zp=Yp,th=Qp,eh=Sp.TypeError,rh=Sp.document,nh=Sp.process,oh=zp.f,ih=oh,ah=!!(rh&&rh.createEvent&&Sp.dispatchEvent),uh="unhandledrejection",ch=function(t){var e;return!(!Rp(t)||!Lp(e=t.then))&&e},sh=function(t,e){var r,n,o,i=e.value,a=1==e.state,u=a?t.ok:t.fail,c=t.resolve,s=t.reject,f=t.domain;try{u?(a||(2===e.rejection&&dh(e),e.rejection=1),!0===u?r=i:(f&&f.enter(),r=u(i),f&&(f.exit(),o=!0)),r===t.promise?s(eh("Promise-chain cycle")):(n=ch(r))?Ep(n,r,c,s):c(r)):s(i)}catch(rA){f&&!o&&f.exit(),s(rA)}},fh=function(t,e){t.notified||(t.notified=!0,Up((function(){for(var r,n=t.reactions;r=n.get();)sh(r,t);t.notified=!1,e&&!t.rejection&&ph(t)})))},lh=function(t,e,r){var n,o;ah?((n=rh.createEvent("Event")).promise=e,n.reason=r,n.initEvent(t,!1,!0),Sp.dispatchEvent(n)):n={promise:e,reason:r},!Kp&&(o=Sp["on"+t])?o(n):t===uh&&Bp("Unhandled promise rejection",r)},ph=function(t){Ep(Fp,Sp,(function(){var e,r=t.facade,n=t.value;if(hh(t)&&(e=Gp((function(){kp?nh.emit("unhandledRejection",n,r):lh(uh,r,n)})),t.rejection=kp||hh(t)?2:1,e.error))throw e.value}))},hh=function(t){return 1!==t.rejection&&!t.parent},dh=function(t){Ep(Fp,Sp,(function(){var e=t.facade;kp?nh.emit("rejectionHandled",e):lh("rejectionhandled",e,t.value)}))},vh=function(t,e,r){return function(n){t(e,n,r)}},yh=function(t,e,r){t.done||(t.done=!0,r&&(t=r),t.value=e,t.state=2,fh(t,!0))},gh=function(t,e,r){if(!t.done){t.done=!0,r&&(t=r);try{if(t.facade===e)throw eh("Promise can't be resolved itself");var n=ch(e);n?Up((function(){var r={done:!1};try{Ep(n,e,vh(gh,r,t),vh(yh,r,t))}catch(rA){yh(r,rA,t)}})):(t.value=e,t.state=1,fh(t,!1))}catch(rA){yh({done:!1},rA,t)}}};if(qp&&(th=(Zp=function(t){Dp(this,th),Mp(t),Ep(_p,this);var e=Xp(this);try{t(vh(gh,e),vh(yh,e))}catch(rA){yh(e,rA)}}).prototype,(_p=function(t){$p(this,{type:Hp,done:!1,notified:!1,parent:!1,reactions:new Vp,rejection:!1,state:0,value:void 0})}).prototype=Cp(th,"then",(function(t,e){var r=Xp(this),n=oh(Np(this,Zp));return r.parent=!0,n.ok=!Lp(t)||t,n.fail=Lp(e)&&e,n.domain=kp?nh.domain:void 0,0==r.state?r.reactions.add(n):Up((function(){sh(n,r)})),n.promise})),Tp=function(){var t=new _p,e=Xp(t);this.promise=t,this.resolve=vh(gh,e),this.reject=vh(yh,e)},zp.f=oh=function(t){return t===Zp||undefined===t?new Tp(t):ih(t)},Lp(Yp)&&Qp!==Object.prototype)){xp=Qp.then,Jp||Cp(Qp,"then",(function(t,e){var r=this;return new Zp((function(t,e){Ep(xp,r,t,e)})).then(t,e)}),{unsafe:!0});try{delete Qp.constructor}catch(rA){}Ip&&Ip(Qp,th)}Op({global:!0,constructor:!0,wrap:!0,forced:qp},{Promise:Zp}),jp(Zp,Hp,!1),Pp(Hp);var mh=ep,wh=gp.CONSTRUCTOR||!Hu((function(t){mh.all(t).then(void 0,(function(){}))})),bh=oe,Ah=ye,_h=mp,Th=Ql,xh=Fu;Yo({target:"Promise",stat:!0,forced:wh},{all:function(t){var e=this,r=_h.f(e),n=r.resolve,o=r.reject,i=Th((function(){var r=Ah(e.resolve),i=[],a=0,u=1;xh(t,(function(t){var c=a++,s=!1;u++,bh(r,e,t).then((function(t){s||(s=!0,i[c]=t,--u||n(i))}),o)})),--u||n(i)}));return i.error&&o(i.value),r.promise}});var Oh=Yo,kh=gp.CONSTRUCTOR,Sh=ep,Eh=vt,Ch=lt,Ih=so,jh=Sh&&Sh.prototype;if(Oh({target:"Promise",proto:!0,forced:kh,real:!0},{catch:function(t){return this.then(void 0,t)}}),Ch(Sh)){var Ph=Eh("Promise").prototype.catch;jh.catch!==Ph&&Ih(jh,"catch",Ph,{unsafe:!0})}var Mh=oe,Lh=ye,Rh=mp,Dh=Ql,Nh=Fu;Yo({target:"Promise",stat:!0,forced:wh},{race:function(t){var e=this,r=Rh.f(e),n=r.reject,o=Dh((function(){var o=Lh(e.resolve);Nh(t,(function(t){Mh(o,e,t).then(r.resolve,n)}))}));return o.error&&n(o.value),r.promise}});var Fh=oe,Uh=mp;Yo({target:"Promise",stat:!0,forced:gp.CONSTRUCTOR},{reject:function(t){var e=Uh.f(this);return Fh(e.reject,void 0,t),e.promise}});var Bh=zt,Gh=Gt,Vh=mp,Wh=Yo,Yh=gp.CONSTRUCTOR,zh=function(t,e){if(Bh(t),Gh(e)&&e.constructor===t)return e;var r=Vh.f(t);return(0,r.resolve)(e),r.promise};vt("Promise"),Wh({target:"Promise",stat:!0,forced:Yh},{resolve:function(t){return zh(this,t)}}),window.AudioContext=window.AudioContext||window.webkitAudioContext||window.mozAudioContext;var Hh,qh=function(){if(Hh)return Hh;Hh=new window.AudioContext({sampleRate:48e3});var t=function t(){"suspended"===Hh.state?(Hh.resume(),document.removeEventListener("click",t)):"interrupted"===Hh.state?Hh.resume():document.removeEventListener("click",t)};return document.addEventListener("click",t),Hh.onstatechange=function(){t()},Hh},Kh="input",Jh="output";function Xh(t){return $h.apply(this,arguments)}function $h(){return($h=u(i().mark((function t(e){var r,n,o,a,u,c,s,f,l,p,h,d,v,y;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=e.sdkAppId,n=e.userId,o=e.userSig,a=e.timestamp,u="https://schedule.rtc.qq.com/api/v1/audioAiAuth?sdkAppId=".concat(r,"&userId=").concat(n,"&userSig=").concat(o,"&timestamp=").concat(a),t.next=4,fetch(u);case 4:return c=t.sent,t.next=7,c.json();case 7:if(s=t.sent,f=s.data,l=f.errCode,p=f.errMsg,h=f.sign,"1"!==(d=f.status)){t.next=15;break}return t.abrupt("return",{auth:!0,sign:h,status:d,message:p});case 15:v="Init RTCAIDenoiser failed.",y="",t.t0=l,t.next=1===t.t0?20:2===t.t0?22:3===t.t0?24:4===t.t0?26:5===t.t0?28:6===t.t0?30:32;break;case 20:return y="Please check your params.",t.abrupt("break",32);case 22:return y="You need to buy packages. Refer to: https://cloud.tencent.com/document/product/647/44247",t.abrupt("break",32);case 24:return y="Server is invalid. Please contact our engineer. ",t.abrupt("break",32);case 26:return y="Your packages is not active. Refer to: https://cloud.tencent.com/document/product/647/44247",t.abrupt("break",32);case 28:return y="Your packages is expired. Refer to: https://cloud.tencent.com/document/product/647/44247",t.abrupt("break",32);case 30:return y="Your version is not supported.",t.abrupt("break",32);case 32:return t.abrupt("return",{auth:!1,status:d,message:p?"".concat(v," Reason: ").concat(p,". ").concat(y):"".concat(v,", ").concat(y)});case 33:case"end":return t.stop()}}),t)})))).apply(this,arguments)}var Qh={exports:{}};!function(t){var e=Object.prototype.hasOwnProperty,r="~";function n(){}function o(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function i(t,e,n,i,a){if("function"!=typeof n)throw new TypeError("The listener must be a function");var u=new o(n,i||t,a),c=r?r+e:e;return t._events[c]?t._events[c].fn?t._events[c]=[t._events[c],u]:t._events[c].push(u):(t._events[c]=u,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function u(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),u.prototype.eventNames=function(){var t,n,o=[];if(0===this._eventsCount)return o;for(n in t=this._events)e.call(t,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},u.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=new Array(i);o<i;o++)a[o]=n[o].fn;return a},u.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},u.prototype.emit=function(t,e,n,o,i,a){var u=r?r+t:t;if(!this._events[u])return!1;var c,s,f=this._events[u],l=arguments.length;if(f.fn){switch(f.once&&this.removeListener(t,f.fn,void 0,!0),l){case 1:return f.fn.call(f.context),!0;case 2:return f.fn.call(f.context,e),!0;case 3:return f.fn.call(f.context,e,n),!0;case 4:return f.fn.call(f.context,e,n,o),!0;case 5:return f.fn.call(f.context,e,n,o,i),!0;case 6:return f.fn.call(f.context,e,n,o,i,a),!0}for(s=1,c=new Array(l-1);s<l;s++)c[s-1]=arguments[s];f.fn.apply(f.context,c)}else{var p,h=f.length;for(s=0;s<h;s++)switch(f[s].once&&this.removeListener(t,f[s].fn,void 0,!0),l){case 1:f[s].fn.call(f[s].context);break;case 2:f[s].fn.call(f[s].context,e);break;case 3:f[s].fn.call(f[s].context,e,n);break;case 4:f[s].fn.call(f[s].context,e,n,o);break;default:if(!c)for(p=1,c=new Array(l-1);p<l;p++)c[p-1]=arguments[p];f[s].fn.apply(f[s].context,c)}}return!0},u.prototype.on=function(t,e,r){return i(this,t,e,r,!1)},u.prototype.once=function(t,e,r){return i(this,t,e,r,!0)},u.prototype.removeListener=function(t,e,n,o){var i=r?r+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var u=this._events[i];if(u.fn)u.fn!==e||o&&!u.once||n&&u.context!==n||a(this,i);else{for(var c=0,s=[],f=u.length;c<f;c++)(u[c].fn!==e||o&&!u[c].once||n&&u[c].context!==n)&&s.push(u[c]);s.length?this._events[i]=1===s.length?s[0]:s:a(this,i)}return this},u.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,t.exports=u}(Qh);var Zh=Qh.exports,td="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView,ed=Je,rd=rr,nd=RangeError,od=function(t){if(void 0===t)return 0;var e=ed(t),r=rd(e);if(e!==r)throw nd("Wrong length or index");return r},id=Array,ad=Math.abs,ud=Math.pow,cd=Math.floor,sd=Math.log,fd=Math.LN2,ld=Z,pd=Ze,hd=or,dd=function(t){for(var e=ld(this),r=hd(e),n=arguments.length,o=pd(n>1?arguments[1]:void 0,r),i=n>2?arguments[2]:void 0,a=void 0===i?r:pd(i,r);a>o;)e[o++]=t;return e},vd=B,yd=k,gd=qt,md=td,wd=Vn,bd=un,Ad=hc,_d=h,Td=Gu,xd=Je,Od=rr,kd=od,Sd={pack:function(t,e,r){var n,o,i,a=id(r),u=8*r-e-1,c=(1<<u)-1,s=c>>1,f=23===e?ud(2,-24)-ud(2,-77):0,l=t<0||0===t&&1/t<0?1:0,p=0;for((t=ad(t))!=t||Infinity===t?(o=t!=t?1:0,n=c):(n=cd(sd(t)/fd),t*(i=ud(2,-n))<1&&(n--,i*=2),(t+=n+s>=1?f/i:f*ud(2,1-s))*i>=2&&(n++,i/=2),n+s>=c?(o=0,n=c):n+s>=1?(o=(t*i-1)*ud(2,e),n+=s):(o=t*ud(2,s-1)*ud(2,e),n=0));e>=8;)a[p++]=255&o,o/=256,e-=8;for(n=n<<e|o,u+=e;u>0;)a[p++]=255&n,n/=256,u-=8;return a[--p]|=128*l,a},unpack:function(t,e){var r,n=t.length,o=8*n-e-1,i=(1<<o)-1,a=i>>1,u=o-7,c=n-1,s=t[c--],f=127&s;for(s>>=7;u>0;)f=256*f+t[c--],u-=8;for(r=f&(1<<-u)-1,f>>=-u,u+=e;u>0;)r=256*r+t[c--],u-=8;if(0===f)f=1-a;else{if(f===i)return r?NaN:s?-Infinity:Infinity;r+=ud(2,e),f-=a}return(s?-1:1)*r*ud(2,f-e)}},Ed=Zo,Cd=Oi,Id=fo.f,jd=Jt.f,Pd=dd,Md=ga,Ld=pi,Rd=wd.PROPER,Dd=wd.CONFIGURABLE,Nd=An.get,Fd=An.set,Ud="ArrayBuffer",Bd="DataView",Gd="Wrong index",Vd=vd.ArrayBuffer,Wd=Vd,Yd=Wd&&Wd.prototype,zd=vd.DataView,Hd=zd&&zd.prototype,qd=Object.prototype,Kd=vd.Array,Jd=vd.RangeError,Xd=yd(Pd),$d=yd([].reverse),Qd=Sd.pack,Zd=Sd.unpack,tv=function(t){return[255&t]},ev=function(t){return[255&t,t>>8&255]},rv=function(t){return[255&t,t>>8&255,t>>16&255,t>>24&255]},nv=function(t){return t[3]<<24|t[2]<<16|t[1]<<8|t[0]},ov=function(t){return Qd(t,23,4)},iv=function(t){return Qd(t,52,8)},av=function(t,e){jd(t.prototype,e,{get:function(){return Nd(this)[e]}})},uv=function(t,e,r,n){var o=kd(r),i=Nd(t);if(o+e>i.byteLength)throw Jd(Gd);var a=Nd(i.buffer).bytes,u=o+i.byteOffset,c=Md(a,u,u+e);return n?c:$d(c)},cv=function(t,e,r,n,o,i){var a=kd(r),u=Nd(t);if(a+e>u.byteLength)throw Jd(Gd);for(var c=Nd(u.buffer).bytes,s=a+u.byteOffset,f=n(+o),l=0;l<e;l++)c[s+l]=f[i?l:e-l-1]};if(md){var sv=Rd&&Vd.name!==Ud;if(_d((function(){Vd(1)}))&&_d((function(){new Vd(-1)}))&&!_d((function(){return new Vd,new Vd(1.5),new Vd(NaN),1!=Vd.length||sv&&!Dd})))sv&&Dd&&bd(Vd,"name",Ud);else{(Wd=function(t){return Td(this,Yd),new Vd(kd(t))}).prototype=Yd;for(var fv,lv=Id(Vd),pv=0;lv.length>pv;)(fv=lv[pv++])in Wd||bd(Wd,fv,Vd[fv]);Yd.constructor=Wd}Cd&&Ed(Hd)!==qd&&Cd(Hd,qd);var hv=new zd(new Wd(2)),dv=yd(Hd.setInt8);hv.setInt8(0,2147483648),hv.setInt8(1,2147483649),!hv.getInt8(0)&&hv.getInt8(1)||Ad(Hd,{setInt8:function(t,e){dv(this,t,e<<24>>24)},setUint8:function(t,e){dv(this,t,e<<24>>24)}},{unsafe:!0})}else Yd=(Wd=function(t){Td(this,Yd);var e=kd(t);Fd(this,{bytes:Xd(Kd(e),0),byteLength:e}),gd||(this.byteLength=e)}).prototype,Hd=(zd=function(t,e,r){Td(this,Hd),Td(t,Yd);var n=Nd(t).byteLength,o=xd(e);if(o<0||o>n)throw Jd("Wrong offset");if(o+(r=void 0===r?n-o:Od(r))>n)throw Jd("Wrong length");Fd(this,{buffer:t,byteLength:r,byteOffset:o}),gd||(this.buffer=t,this.byteLength=r,this.byteOffset=o)}).prototype,gd&&(av(Wd,"byteLength"),av(zd,"buffer"),av(zd,"byteLength"),av(zd,"byteOffset")),Ad(Hd,{getInt8:function(t){return uv(this,1,t)[0]<<24>>24},getUint8:function(t){return uv(this,1,t)[0]},getInt16:function(t){var e=uv(this,2,t,arguments.length>1?arguments[1]:void 0);return(e[1]<<8|e[0])<<16>>16},getUint16:function(t){var e=uv(this,2,t,arguments.length>1?arguments[1]:void 0);return e[1]<<8|e[0]},getInt32:function(t){return nv(uv(this,4,t,arguments.length>1?arguments[1]:void 0))},getUint32:function(t){return nv(uv(this,4,t,arguments.length>1?arguments[1]:void 0))>>>0},getFloat32:function(t){return Zd(uv(this,4,t,arguments.length>1?arguments[1]:void 0),23)},getFloat64:function(t){return Zd(uv(this,8,t,arguments.length>1?arguments[1]:void 0),52)},setInt8:function(t,e){cv(this,1,t,tv,e)},setUint8:function(t,e){cv(this,1,t,tv,e)},setInt16:function(t,e){cv(this,2,t,ev,e,arguments.length>2?arguments[2]:void 0)},setUint16:function(t,e){cv(this,2,t,ev,e,arguments.length>2?arguments[2]:void 0)},setInt32:function(t,e){cv(this,4,t,rv,e,arguments.length>2?arguments[2]:void 0)},setUint32:function(t,e){cv(this,4,t,rv,e,arguments.length>2?arguments[2]:void 0)},setFloat32:function(t,e){cv(this,4,t,ov,e,arguments.length>2?arguments[2]:void 0)},setFloat64:function(t,e){cv(this,8,t,iv,e,arguments.length>2?arguments[2]:void 0)}});Ld(Wd,Ud),Ld(zd,Bd);var vv={ArrayBuffer:Wd,DataView:zd},yv=Yo,gv=k,mv=h,wv=zt,bv=Ze,Av=rr,_v=Yf,Tv=vv.ArrayBuffer,xv=vv.DataView,Ov=xv.prototype,kv=gv(Tv.prototype.slice),Sv=gv(Ov.getUint8),Ev=gv(Ov.setUint8);yv({target:"ArrayBuffer",proto:!0,unsafe:!0,forced:mv((function(){return!new Tv(2).slice(1,void 0).byteLength}))},{slice:function(t,e){if(kv&&void 0===e)return kv(wv(this),t);for(var r=wv(this).byteLength,n=bv(t,r),o=bv(void 0===e?r:e,r),i=new(_v(this,Tv))(Av(o-n)),a=new xv(this),u=new xv(i),c=0;n<o;)Ev(u,c++,Sv(a,n++));return i}});var Cv,Iv,jv,Pv={exports:{}},Mv=td,Lv=qt,Rv=B,Dv=lt,Nv=Gt,Fv=rt,Uv=cu,Bv=pe,Gv=un,Vv=so,Wv=Jt.f,Yv=ie,zv=Zo,Hv=Oi,qv=Ft,Kv=ut,Jv=An.enforce,Xv=An.get,$v=Rv.Int8Array,Qv=$v&&$v.prototype,Zv=Rv.Uint8ClampedArray,ty=Zv&&Zv.prototype,ey=$v&&zv($v),ry=Qv&&zv(Qv),ny=Object.prototype,oy=Rv.TypeError,iy=qv("toStringTag"),ay=Kv("TYPED_ARRAY_TAG"),uy="TypedArrayConstructor",cy=Mv&&!!Hv&&"Opera"!==Uv(Rv.opera),sy=!1,fy={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},ly={BigInt64Array:8,BigUint64Array:8},py=function(t){var e=zv(t);if(Nv(e)){var r=Xv(e);return r&&Fv(r,uy)?r.TypedArrayConstructor:py(e)}},hy=function(t){if(!Nv(t))return!1;var e=Uv(t);return Fv(fy,e)||Fv(ly,e)};for(Cv in fy)(jv=(Iv=Rv[Cv])&&Iv.prototype)?Jv(jv).TypedArrayConstructor=Iv:cy=!1;for(Cv in ly)(jv=(Iv=Rv[Cv])&&Iv.prototype)&&(Jv(jv).TypedArrayConstructor=Iv);if((!cy||!Dv(ey)||ey===Function.prototype)&&(ey=function(){throw oy("Incorrect invocation")},cy))for(Cv in fy)Rv[Cv]&&Hv(Rv[Cv],ey);if((!cy||!ry||ry===ny)&&(ry=ey.prototype,cy))for(Cv in fy)Rv[Cv]&&Hv(Rv[Cv].prototype,ry);if(cy&&zv(ty)!==ry&&Hv(ty,ry),Lv&&!Fv(ry,iy))for(Cv in sy=!0,Wv(ry,iy,{get:function(){return Nv(this)?this[ay]:void 0}}),fy)Rv[Cv]&&Gv(Rv[Cv],ay,Cv);var dy={NATIVE_ARRAY_BUFFER_VIEWS:cy,TYPED_ARRAY_TAG:sy&&ay,aTypedArray:function(t){if(hy(t))return t;throw oy("Target is not a typed array")},aTypedArrayConstructor:function(t){if(Dv(t)&&(!Hv||Yv(ey,t)))return t;throw oy(Bv(t)+" is not a typed array constructor")},exportTypedArrayMethod:function(t,e,r,n){if(Lv){if(r)for(var o in fy){var i=Rv[o];if(i&&Fv(i.prototype,t))try{delete i.prototype[t]}catch(rA){try{i.prototype[t]=e}catch(a){}}}ry[t]&&!r||Vv(ry,t,r?e:cy&&Qv[t]||e,n)}},exportTypedArrayStaticMethod:function(t,e,r){var n,o;if(Lv){if(Hv){if(r)for(n in fy)if((o=Rv[n])&&Fv(o,t))try{delete o[t]}catch(rA){}if(ey[t]&&!r)return;try{return Vv(ey,t,r?e:cy&&ey[t]||e)}catch(rA){}}for(n in fy)!(o=Rv[n])||o[t]&&!r||Vv(o,t,e)}},getTypedArrayConstructor:py,isView:function(t){if(!Nv(t))return!1;var e=Uv(t);return"DataView"===e||Fv(fy,e)||Fv(ly,e)},isTypedArray:hy,TypedArray:ey,TypedArrayPrototype:ry},vy=B,yy=h,gy=Hu,my=dy.NATIVE_ARRAY_BUFFER_VIEWS,wy=vy.ArrayBuffer,by=vy.Int8Array,Ay=!my||!yy((function(){by(1)}))||!yy((function(){new by(-1)}))||!gy((function(t){new by,new by(null),new by(1.5),new by(t)}),!0)||yy((function(){return 1!==new by(new wy(2),1,void 0).length})),_y=Gt,Ty=Math.floor,xy=Number.isInteger||function(t){return!_y(t)&&isFinite(t)&&Ty(t)===t},Oy=Je,ky=RangeError,Sy=function(t){var e=Oy(t);if(e<0)throw ky("The argument can't be less than 0");return e},Ey=RangeError,Cy=function(t,e){var r=Sy(t);if(r%e)throw Ey("Wrong offset");return r},Iy=cu,jy=k("".slice),Py=je,My=TypeError,Ly=function(t){var e=Py(t,"number");if("number"==typeof e)throw My("Can't convert number to bigint");return BigInt(e)},Ry=Ja,Dy=oe,Ny=Uf,Fy=Z,Uy=or,By=Au,Gy=du,Vy=Za,Wy=function(t){return"Big"===jy(Iy(t),0,3)},Yy=dy.aTypedArrayConstructor,zy=Ly,Hy=Ja,qy=j,Ky=Z,Jy=or,Xy=vf,$y=k([].push),Qy=function(t){var e=1==t,r=2==t,n=3==t,o=4==t,i=6==t,a=7==t,u=5==t||i;return function(c,s,f,l){for(var p,h,d=Ky(c),v=qy(d),y=Hy(s,f),g=Jy(v),m=0,w=l||Xy,b=e?w(c,g):r||a?w(c,0):void 0;g>m;m++)if((u||m in v)&&(h=y(p=v[m],m,d),t))if(e)b[m]=h;else if(h)switch(t){case 3:return!0;case 5:return p;case 6:return m;case 2:$y(b,p)}else switch(t){case 4:return!1;case 7:$y(b,p)}return i?-1:n||o?o:b}},Zy={forEach:Qy(0),map:Qy(1),filter:Qy(2),some:Qy(3),every:Qy(4),find:Qy(5),findIndex:Qy(6),filterReject:Qy(7)},tg=Yo,eg=B,rg=oe,ng=qt,og=Ay,ig=dy,ag=vv,ug=Gu,cg=nn,sg=un,fg=xy,lg=rr,pg=od,hg=Cy,dg=Le,vg=rt,yg=cu,gg=Gt,mg=fe,wg=Yr,bg=ie,Ag=Oi,_g=fo.f,Tg=function(t){var e,r,n,o,i,a,u,c,s=Ny(this),f=Fy(t),l=arguments.length,p=l>1?arguments[1]:void 0,h=void 0!==p,d=Gy(f);if(d&&!Vy(d))for(c=(u=By(f,d)).next,f=[];!(a=Dy(c,u)).done;)f.push(a.value);for(h&&l>2&&(p=Ry(p,arguments[2])),r=Uy(f),n=new(Yy(s))(r),o=Wy(n),e=0;r>e;e++)i=h?p(f[e],e):f[e],n[e]=o?zy(i):+i;return n},xg=Zy.forEach,Og=mc,kg=Jt,Sg=_n,Eg=Xu,Cg=An.get,Ig=An.set,jg=An.enforce,Pg=kg.f,Mg=Sg.f,Lg=Math.round,Rg=eg.RangeError,Dg=ag.ArrayBuffer,Ng=Dg.prototype,Fg=ag.DataView,Ug=ig.NATIVE_ARRAY_BUFFER_VIEWS,Bg=ig.TYPED_ARRAY_TAG,Gg=ig.TypedArray,Vg=ig.TypedArrayPrototype,Wg=ig.aTypedArrayConstructor,Yg=ig.isTypedArray,zg="BYTES_PER_ELEMENT",Hg="Wrong length",qg=function(t,e){Wg(t);for(var r=0,n=e.length,o=new t(n);n>r;)o[r]=e[r++];return o},Kg=function(t,e){Pg(t,e,{get:function(){return Cg(this)[e]}})},Jg=function(t){var e;return bg(Ng,t)||"ArrayBuffer"==(e=yg(t))||"SharedArrayBuffer"==e},Xg=function(t,e){return Yg(t)&&!mg(e)&&e in t&&fg(+e)&&e>=0},$g=function(t,e){return e=dg(e),Xg(t,e)?cg(2,t[e]):Mg(t,e)},Qg=function(t,e,r){return e=dg(e),!(Xg(t,e)&&gg(r)&&vg(r,"value"))||vg(r,"get")||vg(r,"set")||r.configurable||vg(r,"writable")&&!r.writable||vg(r,"enumerable")&&!r.enumerable?Pg(t,e,r):(t[e]=r.value,t)};ng?(Ug||(Sg.f=$g,kg.f=Qg,Kg(Vg,"buffer"),Kg(Vg,"byteOffset"),Kg(Vg,"byteLength"),Kg(Vg,"length")),tg({target:"Object",stat:!0,forced:!Ug},{getOwnPropertyDescriptor:$g,defineProperty:Qg}),Pv.exports=function(t,e,r){var n=t.match(/\d+$/)[0]/8,o=t+(r?"Clamped":"")+"Array",i="get"+t,a="set"+t,u=eg[o],c=u,s=c&&c.prototype,f={},l=function(t,e){Pg(t,e,{get:function(){return function(t,e){var r=Cg(t);return r.view[i](e*n+r.byteOffset,!0)}(this,e)},set:function(t){return function(t,e,o){var i=Cg(t);r&&(o=(o=Lg(o))<0?0:o>255?255:255&o),i.view[a](e*n+i.byteOffset,o,!0)}(this,e,t)},enumerable:!0})};Ug?og&&(c=e((function(t,e,r,o){return ug(t,s),Eg(gg(e)?Jg(e)?void 0!==o?new u(e,hg(r,n),o):void 0!==r?new u(e,hg(r,n)):new u(e):Yg(e)?qg(c,e):rg(Tg,c,e):new u(pg(e)),t,c)})),Ag&&Ag(c,Gg),xg(_g(u),(function(t){t in c||sg(c,t,u[t])})),c.prototype=s):(c=e((function(t,e,r,o){ug(t,s);var i,a,u,f=0,p=0;if(gg(e)){if(!Jg(e))return Yg(e)?qg(c,e):rg(Tg,c,e);i=e,p=hg(r,n);var h=e.byteLength;if(void 0===o){if(h%n)throw Rg(Hg);if((a=h-p)<0)throw Rg(Hg)}else if((a=lg(o)*n)+p>h)throw Rg(Hg);u=a/n}else u=pg(e),i=new Dg(a=u*n);for(Ig(t,{buffer:i,byteOffset:p,byteLength:a,length:u,view:new Fg(i)});f<u;)l(t,f++)})),Ag&&Ag(c,Gg),s=c.prototype=wg(Vg)),s.constructor!==c&&sg(s,"constructor",c),jg(s).TypedArrayConstructor=c,Bg&&sg(s,Bg,o);var p=c!=u;f[o]=c,tg({global:!0,constructor:!0,forced:p,sham:!Ug},f),zg in c||sg(c,zg,n),zg in s||sg(s,zg,n),Og(o)}):Pv.exports=function(){},(0,Pv.exports)("Float32",(function(t){return function(e,r,n){return t(this,e,r,n)}}));var Zg=pe,tm=TypeError,em=Z,rm=Ze,nm=or,om=function(t,e){if(!delete t[e])throw tm("Cannot delete property "+Zg(e)+" of "+Zg(t))},im=Math.min,am=[].copyWithin||function(t,e){var r=em(this),n=nm(r),o=rm(t,n),i=rm(e,n),a=arguments.length>2?arguments[2]:void 0,u=im((void 0===a?n:rm(a,n))-i,n-o),c=1;for(i<o&&o<i+u&&(c=-1,i+=u-1,o+=u-1);u-- >0;)i in r?r[o]=r[i]:om(r,o),o+=c,i+=c;return r},um=dy,cm=k(am),sm=um.aTypedArray;(0,um.exportTypedArrayMethod)("copyWithin",(function(t,e){return cm(sm(this),t,e,arguments.length>2?arguments[2]:void 0)}));var fm=Zy.every,lm=dy.aTypedArray;(0,dy.exportTypedArrayMethod)("every",(function(t){return fm(lm(this),t,arguments.length>1?arguments[1]:void 0)}));var pm=dd,hm=Ly,dm=cu,vm=oe,ym=h,gm=dy.aTypedArray,mm=dy.exportTypedArrayMethod,wm=k("".slice);mm("fill",(function(t){var e=arguments.length;gm(this);var r="Big"===wm(dm(this),0,3)?hm(t):+t;return vm(pm,this,r,e>1?arguments[1]:void 0,e>2?arguments[2]:void 0)}),ym((function(){var t=0;return new Int8Array(2).fill({valueOf:function(){return t++}}),1!==t})));var bm=or,Am=Yf,_m=dy.aTypedArrayConstructor,Tm=dy.getTypedArrayConstructor,xm=function(t){return _m(Am(t,Tm(t)))},Om=function(t,e){for(var r=0,n=bm(e),o=new t(n);n>r;)o[r]=e[r++];return o},km=xm,Sm=Zy.filter,Em=function(t,e){return Om(km(t),e)},Cm=dy.aTypedArray;(0,dy.exportTypedArrayMethod)("filter",(function(t){var e=Sm(Cm(this),t,arguments.length>1?arguments[1]:void 0);return Em(this,e)}));var Im=Zy.find,jm=dy.aTypedArray;(0,dy.exportTypedArrayMethod)("find",(function(t){return Im(jm(this),t,arguments.length>1?arguments[1]:void 0)}));var Pm=Zy.findIndex,Mm=dy.aTypedArray;(0,dy.exportTypedArrayMethod)("findIndex",(function(t){return Pm(Mm(this),t,arguments.length>1?arguments[1]:void 0)}));var Lm=Zy.forEach,Rm=dy.aTypedArray;(0,dy.exportTypedArrayMethod)("forEach",(function(t){Lm(Rm(this),t,arguments.length>1?arguments[1]:void 0)}));var Dm=sr.includes,Nm=dy.aTypedArray;(0,dy.exportTypedArrayMethod)("includes",(function(t){return Dm(Nm(this),t,arguments.length>1?arguments[1]:void 0)}));var Fm=sr.indexOf,Um=dy.aTypedArray;(0,dy.exportTypedArrayMethod)("indexOf",(function(t){return Fm(Um(this),t,arguments.length>1?arguments[1]:void 0)}));var Bm=B,Gm=h,Vm=k,Wm=dy,Ym=oa,zm=Ft("iterator"),Hm=Bm.Uint8Array,qm=Vm(Ym.values),Km=Vm(Ym.keys),Jm=Vm(Ym.entries),Xm=Wm.aTypedArray,$m=Wm.exportTypedArrayMethod,Qm=Hm&&Hm.prototype,Zm=!Gm((function(){Qm[zm].call([1])})),tw=!!Qm&&Qm.values&&Qm[zm]===Qm.values&&"values"===Qm.values.name,ew=function(){return qm(Xm(this))};$m("entries",(function(){return Jm(Xm(this))}),Zm),$m("keys",(function(){return Km(Xm(this))}),Zm),$m("values",ew,Zm||!tw,{name:"values"}),$m(zm,ew,Zm||!tw,{name:"values"});var rw=dy.aTypedArray,nw=dy.exportTypedArrayMethod,ow=k([].join);nw("join",(function(t){return ow(rw(this),t)}));var iw=h,aw=Jf,uw=F,cw=Je,sw=or,fw=function(t,e){var r=[][t];return!!r&&iw((function(){r.call(null,e||function(){return 1},1)}))},lw=Math.min,pw=[].lastIndexOf,hw=!!pw&&1/[1].lastIndexOf(1,-0)<0,dw=fw("lastIndexOf"),vw=hw||!dw?function(t){if(hw)return aw(pw,this,arguments)||0;var e=uw(this),r=sw(e),n=r-1;for(arguments.length>1&&(n=lw(n,cw(arguments[1]))),n<0&&(n=r+n);n>=0;n--)if(n in e&&e[n]===t)return n||0;return-1}:pw,yw=Jf,gw=vw,mw=dy.aTypedArray;(0,dy.exportTypedArrayMethod)("lastIndexOf",(function(t){var e=arguments.length;return yw(gw,mw(this),e>1?[t,arguments[1]]:[t])}));var ww=Zy.map,bw=xm,Aw=dy.aTypedArray;(0,dy.exportTypedArrayMethod)("map",(function(t){return ww(Aw(this),t,arguments.length>1?arguments[1]:void 0,(function(t,e){return new(bw(t))(e)}))}));var _w=ye,Tw=Z,xw=j,Ow=or,kw=TypeError,Sw=function(t){return function(e,r,n,o){_w(r);var i=Tw(e),a=xw(i),u=Ow(i),c=t?u-1:0,s=t?-1:1;if(n<2)for(;;){if(c in a){o=a[c],c+=s;break}if(c+=s,t?c<0:u<=c)throw kw("Reduce of empty array with no initial value")}for(;t?c>=0:u>c;c+=s)c in a&&(o=r(o,a[c],c,i));return o}},Ew={left:Sw(!1),right:Sw(!0)},Cw=Ew.left,Iw=dy.aTypedArray;(0,dy.exportTypedArrayMethod)("reduce",(function(t){var e=arguments.length;return Cw(Iw(this),t,e,e>1?arguments[1]:void 0)}));var jw=Ew.right,Pw=dy.aTypedArray;(0,dy.exportTypedArrayMethod)("reduceRight",(function(t){var e=arguments.length;return jw(Pw(this),t,e,e>1?arguments[1]:void 0)}));var Mw=dy.aTypedArray,Lw=dy.exportTypedArrayMethod,Rw=Math.floor;Lw("reverse",(function(){for(var t,e=this,r=Mw(e).length,n=Rw(r/2),o=0;o<n;)t=e[o],e[o++]=e[--r],e[r]=t;return e}));var Dw=B,Nw=oe,Fw=dy,Uw=or,Bw=Cy,Gw=Z,Vw=h,Ww=Dw.RangeError,Yw=Dw.Int8Array,zw=Yw&&Yw.prototype,Hw=zw&&zw.set,qw=Fw.aTypedArray,Kw=Fw.exportTypedArrayMethod,Jw=!Vw((function(){var t=new Uint8ClampedArray(2);return Nw(Hw,t,{length:1,0:3},1),3!==t[1]})),Xw=Jw&&Fw.NATIVE_ARRAY_BUFFER_VIEWS&&Vw((function(){var t=new Yw(2);return t.set(1),t.set("2",1),0!==t[0]||2!==t[1]}));Kw("set",(function(t){qw(this);var e=Bw(arguments.length>1?arguments[1]:void 0,1),r=Gw(t);if(Jw)return Nw(Hw,this,r,e);var n=this.length,o=Uw(r),i=0;if(o+e>n)throw Ww("Wrong length");for(;i<o;)this[e+i]=r[i++]}),!Jw||Xw);var $w=xm,Qw=Ds,Zw=dy.aTypedArray;(0,dy.exportTypedArrayMethod)("slice",(function(t,e){for(var r=Qw(Zw(this),t,e),n=$w(this),o=0,i=r.length,a=new n(i);i>o;)a[o]=r[o++];return a}),h((function(){new Int8Array(1).slice()})));var tb=Zy.some,eb=dy.aTypedArray;(0,dy.exportTypedArrayMethod)("some",(function(t){return tb(eb(this),t,arguments.length>1?arguments[1]:void 0)}));var rb=ga,nb=Math.floor,ob=function(t,e){var r=t.length,n=nb(r/2);return r<8?ib(t,e):ab(t,ob(rb(t,0,n),e),ob(rb(t,n),e),e)},ib=function(t,e){for(var r,n,o=t.length,i=1;i<o;){for(n=i,r=t[i];n&&e(t[n-1],r)>0;)t[n]=t[--n];n!==i++&&(t[n]=r)}return t},ab=function(t,e,r,n){for(var o=e.length,i=r.length,a=0,u=0;a<o||u<i;)t[a+u]=a<o&&u<i?n(e[a],r[u])<=0?e[a++]:r[u++]:a<o?e[a++]:r[u++];return t},ub=ob,cb=yt.match(/firefox\/(\d+)/i),sb=!!cb&&+cb[1],fb=/MSIE|Trident/.test(yt),lb=yt.match(/AppleWebKit\/(\d+)\./),pb=!!lb&&+lb[1],hb=k,db=h,vb=ye,yb=ub,gb=sb,mb=fb,wb=Tt,bb=pb,Ab=dy.aTypedArray,_b=dy.exportTypedArrayMethod,Tb=B.Uint16Array,xb=Tb&&hb(Tb.prototype.sort),Ob=!(!xb||db((function(){xb(new Tb(2),null)}))&&db((function(){xb(new Tb(2),{})}))),kb=!!xb&&!db((function(){if(wb)return wb<74;if(gb)return gb<67;if(mb)return!0;if(bb)return bb<602;var t,e,r=new Tb(516),n=Array(516);for(t=0;t<516;t++)e=t%4,r[t]=515-t,n[t]=t-2*e+3;for(xb(r,(function(t,e){return(t/4|0)-(e/4|0)})),t=0;t<516;t++)if(r[t]!==n[t])return!0}));_b("sort",(function(t){return void 0!==t&&vb(t),kb?xb(this,t):yb(Ab(this),function(t){return function(e,r){return void 0!==t?+t(e,r)||0:r!=r?-1:e!=e?1:0===e&&0===r?1/e>0&&1/r<0?1:-1:e>r}}(t))}),!kb||Ob);var Sb=rr,Eb=Ze,Cb=xm,Ib=dy.aTypedArray;(0,dy.exportTypedArrayMethod)("subarray",(function(t,e){var r=Ib(this),n=r.length,o=Eb(t,n);return new(Cb(r))(r.buffer,r.byteOffset+o*r.BYTES_PER_ELEMENT,Sb((void 0===e?n:Eb(e,n))-o))}));var jb=Jf,Pb=dy,Mb=h,Lb=Ds,Rb=B.Int8Array,Db=Pb.aTypedArray,Nb=Pb.exportTypedArrayMethod,Fb=[].toLocaleString,Ub=!!Rb&&Mb((function(){Fb.call(new Rb(1))}));Nb("toLocaleString",(function(){return jb(Fb,Ub?Lb(Db(this)):Db(this),Lb(arguments))}),Mb((function(){return[1,2].toLocaleString()!=new Rb([1,2]).toLocaleString()}))||!Mb((function(){Rb.prototype.toLocaleString.call([1,2])})));var Bb=dy.exportTypedArrayMethod,Gb=h,Vb=k,Wb=B.Uint8Array,Yb=Wb&&Wb.prototype||{},zb=[].toString,Hb=Vb([].join);Gb((function(){zb.call({})}))&&(zb=function(){return Hb(this)});var qb=Yb.toString!=zb;Bb("toString",zb,qb),Yo({global:!0,constructor:!0,forced:!td},{DataView:vv.DataView});var Kb=mc,Jb="ArrayBuffer",Xb=vv.ArrayBuffer;function $b(t,e){e=e||{};var r=t.numberOfChannels,n=t.sampleRate,o=e.float32?3:1,i=3===o?32:16;return function(t,e,r,n,o){var i=o/8,a=n*i,u=new ArrayBuffer(44+t.length*i),c=new DataView(u);Qb(c,0,"RIFF"),c.setUint32(4,36+t.length*i,!0),Qb(c,8,"WAVE"),Qb(c,12,"fmt "),c.setUint32(16,16,!0),c.setUint16(20,e,!0),c.setUint16(22,n,!0),c.setUint32(24,r,!0),c.setUint32(28,r*a,!0),c.setUint16(32,a,!0),c.setUint16(34,o,!0),Qb(c,36,"data"),c.setUint32(40,t.length*i,!0),1===e?function(t,e,r){for(var n=0;n<r.length;n++,e+=2){var o=Math.max(-1,Math.min(1,r[n]));t.setInt16(e,o<0?32768*o:32767*o,!0)}}(c,44,t):function(t,e,r){for(var n=0;n<r.length;n++,e+=4)t.setFloat32(e,r[n],!0)}(c,44,t);return u}(2===r?function(t,e){var r=t.length+e.length,n=new Float32Array(r),o=0,i=0;for(;o<r;)n[o++]=t[i],n[o++]=e[i],i++;return n}(t.getChannelData(0),t.getChannelData(1)):t.getChannelData(0),o,n,r,i)}function Qb(t,e,r){for(var n=0;n<r.length;n++)t.setUint8(e+n,r.charCodeAt(n))}Yo({global:!0,constructor:!0,forced:B.ArrayBuffer!==Xb},{ArrayBuffer:Xb}),Kb(Jb);var Zb=function(){function t(e){c(this,t),this.audioContext_=e,this.inputPCM_=new Float32Array,this.outputPCM_=new Float32Array}return f(t,[{key:"onDump",value:function(t,e){if(e===Kh){var r=this.inputPCM_.length,n=new Float32Array(r+t[0].length);n.set(this.inputPCM_),n.set(t[0],r),this.inputPCM_=n}if(e===Jh){var o=this.outputPCM_.length,i=new Float32Array(o+t[0].length);i.set(this.outputPCM_),i.set(t[0],o),this.outputPCM_=i}}},{key:"getBlob",value:function(t){var e=t===Kh?this.inputPCM_:this.outputPCM_,r=this.audioContext_.createBuffer(2,e.length,48e3);r.copyToChannel(e,0,0),r.copyToChannel(e,1,0);var n=$b(r);return r=null,new window.Blob([new DataView(n)],{type:"audio/wav"})}},{key:"reset",value:function(){this.inputPCM_=new Float32Array,this.outputPCM_=new Float32Array}},{key:"destroy",value:function(){this.inputPCM_=null,this.outputPCM_=null}}]),t}(),tA=function(){function t(e){var r=this,n=e.sdkAppId,o=e.userId,i=e.audioContext,a=e.sign,u=e.status,s=e.worklet,f=e.timestamp,l=e.logger;c(this,t),this.audioContext_=i,this.destination_=this.audioContext_.createMediaStreamDestination(),this.gainNode_=this.audioContext_.createGain(),this.gainNode_.gain.value=1.1,this.log_=l,this.workletNode_=s,this.workletNode_.connect(this.gainNode_).connect(this.destination_),this.workletNode_.port.postMessage({type:"init",data:{sdkAppId:String(n),userId:o,timestamp:f,sign:a,status:u}}),this.workletNode_.port.onmessage=function(t){var e=t.data,n=e.type,o=e.data;if("ondump"===n&&r.isDumping_){var i=o.inputPCM,a=o.outputPCM;r.dump_.onDump(i,Kh),r.dump_.onDump(a,Jh)}"dumped"===n&&r.dumped()},this.trackConstraint_={},this.localStream_=null,this.audioTrack_=null,this.source_=null,this.denoiserTrack_=null,this.enableDenoise_=!0,this.emitter_=new Zh,this.dump_=new Zb(this.audioContext_)}var e,n,o,a;return f(t,[{key:"dumped",value:function(){this.isDumping_=!1;var t=this.dump_.getBlob(Kh),e=this.dump_.getBlob(Jh);this.emitter_.emit("ondumpend",{blob:t,name:Kh}),this.emitter_.emit("ondumpend",{blob:e,name:Jh}),this.dump_.reset()}},{key:"process",value:(a=u(i().mark((function t(e){var r,n;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.localStream_=e,this.log_.info("RTCAIDenoiser: denoiser process stream ID: ".concat(e.getId(),".")),this.audioTrack_=null==e?void 0:e.getAudioTrack(),this.audioTrack_){t.next=5;break}throw new Error("RTCAIDenoiser: cannot process localStream without audioTrack.");case 5:return(r=new MediaStream).addTrack(this.audioTrack_),this.source_=this.audioContext_.createMediaStreamSource(r),t.next=10,this.source_.connect(this.workletNode_);case 10:return this.trackConstraint_=this.audioTrack_.getConstraints(),this.trackConstraint_.noiseSuppression=!1,t.next=14,this.audioTrack_.applyConstraints(this.trackConstraint_);case 14:return n=this.destination_.stream,this.denoiserTrack_=n.getAudioTracks()[0],t.next=18,this.localStream_.replaceTrack(this.denoiserTrack_);case 18:case"end":return t.stop()}}),t,this)}))),function(t){return a.apply(this,arguments)})},{key:"updateTrack",value:(o=u(i().mark((function t(e){var r,n;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=new MediaStream,t.next=3,r.addTrack(e);case 3:n=this.audioContext_.createMediaStreamSource(r),this.source_.disconnect(),n.connect(this.workletNode_),this.audioTrack_.stop(),this.audioTrack_=e,this.source_=n,this.log_.info("RTCAIDenoiser: ".concat(this.localStream_.getUserId()," updateTrack success."));case 10:case"end":return t.stop()}}),t,this)}))),function(t){return o.apply(this,arguments)})},{key:"disable",value:(n=u(i().mark((function t(){var e;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return this.enableDenoise_=!1,null===(e=this.workletNode_)||void 0===e||e.port.postMessage({type:"disable"}),this.trackConstraint_.noiseSuppression=!0,t.next=5,this.audioTrack_.applyConstraints(this.trackConstraint_);case 5:return this.log_.info("RTCAIDenoiser: disable ai denoiser."),t.abrupt("return",this.enableDenoise_);case 7:case"end":return t.stop()}}),t,this)}))),function(){return n.apply(this,arguments)})},{key:"enable",value:(e=u(i().mark((function t(){var e;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return this.enableDenoise_=!0,null===(e=this.workletNode_)||void 0===e||e.port.postMessage({type:"enable"}),this.trackConstraint_.noiseSuppression=!1,t.next=5,this.audioTrack_.applyConstraints(this.trackConstraint_);case 5:return this.log_.info("RTCAIDenoiser: enable ai denoiser."),t.abrupt("return",this.enableDenoise_);case 7:case"end":return t.stop()}}),t,this)}))),function(){return e.apply(this,arguments)})},{key:"startDump",value:function(){return this.isDumping_?(this.log_.info("RTCAIDenoiser: data is currently being dumped."),!1):(this.workletNode_.port.postMessage({type:"startDump"}),this.dump_?this.dump_.reset():this.dump_=new Zb(this.audioContext_),this.isDumping_=!0,this.log_.info("RTCAIDenoiser: start dump data."),!0)}},{key:"stopDump",value:function(){this.workletNode_.port.postMessage({type:"stopDump"}),this.isDumping_=!1,this.log_.info("RTCAIDenoiser: stop dump data.")}},{key:"getAudioTrack",value:function(){return this.audioTrack_}},{key:"getDenoiserTrack",value:function(){return this.denoiserTrack_}},{key:"enabled",get:function(){return this.enableDenoise_}},{key:"destroy",value:function(){var t,e,r,n,o,i,a;this.log_.info("RTCAIDenoiser: ".concat(this.localStream_.getUserId()," destroy processor.")),null===(t=this.localStream_)||void 0===t||t.stop(),null===(e=this.audioTrack_)||void 0===e||e.stop(),null===(r=this.workletNode_)||void 0===r||r.port.postMessage({type:"destroy"}),this.workletNode_.port.onmessage=null,null===(n=this.source_)||void 0===n||n.disconnect(),null===(o=this.destination_)||void 0===o||o.disconnect(),null===(i=this.workletNode_)||void 0===i||i.disconnect(),null===(a=this.dump_)||void 0===a||a.destroy(),this.emitter_.removeAllListeners()}},{key:"on",value:function(t,e,r){this.emitter_.on(t,e,r)}},{key:"off",value:function(t,e,r){"*"===t?this.emitter_.removeAllListeners():this.emitter_.off(t,e,r)}},{key:"resetDenoiser",value:function(t){if(r.default){var e=r.default.DenoiserMap;null!=e&&e.has(t.getUserId())&&e.delete(t.getUserId())}}}]),t}();r.default&&(r.default.addDenoiserProcessor=function(t){var e=t.processor,n=t.userId;if(r.default.DenoiserMap)r.default.DenoiserMap.set(n,e);else{var o=new Map;o.set(n,e),r.default.DenoiserMap=o}});var eA=function(){function t(e){var r=e.assetsPath;c(this,t),this.audioContext_=qh(),this.assetsPath_=r,this.isLoaded_=!1}var e,n,a;return f(t,[{key:"createProcessor",value:(a=u(i().mark((function t(e){var n,a,u,c,s,f,l,p,h;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=r.default?r.default.Logger.loggerManager.createLogger({id:e.userId,userId:e.userId,sdkAppId:e.sdkAppId}):{info:console.log},a=String(Date.now()).slice(0,-3),t.next=4,Xh(o(o({},e),{},{timestamp:a}));case 4:if(u=t.sent,c=u.auth,s=u.sign,f=u.status,l=u.message,c){t.next=12;break}throw n.info("RTCAIDenoiser: ".concat(e.userId," auth result: ").concat(c,". Message: ").concat(l)),new Error(l);case 12:return t.next=14,this.load();case 14:return t.next=16,this.initWorklet();case 16:return p=t.sent,h=new tA(o(o({},e),{},{audioContext:this.audioContext_,timestamp:a,sign:s,status:f,worklet:p,logger:n})),n.info("RTCAIDenoiser: ".concat(e.userId," create denoiser processor success.")),r.default&&r.default.addDenoiserProcessor({processor:h,userId:e.userId}),t.abrupt("return",h);case 21:case"end":return t.stop()}}),t,this)}))),function(t){return a.apply(this,arguments)})},{key:"load",value:(n=u(i().mark((function t(){return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.isLoaded_){t.next=10;break}return t.prev=1,t.next=4,this.audioContext_.audioWorklet.addModule("".concat(this.assetsPath_,"/denoiser-wasm.js"));case 4:this.isLoaded_=!0,t.next=10;break;case 7:t.prev=7,t.t0=t.catch(1),console.error("Init assets from ".concat(this.assetsPath_," failed! Reason: ").concat(t.t0));case 10:case"end":return t.stop()}}),t,this,[[1,7]])}))),function(){return n.apply(this,arguments)})},{key:"initWorklet",value:(e=u(i().mark((function t(){return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.abrupt("return",new AudioWorkletNode(this.audioContext_,"trtc-denoiser-processor",{numberOfInputs:1,numberOfOutputs:1}));case 4:return t.prev=4,t.t0=t.catch(0),t.next=8,this.load();case 8:return t.abrupt("return",new AudioWorkletNode(this.audioContext_,"trtc-denoiser-processor",{numberOfInputs:1,numberOfOutputs:1}));case 9:case"end":return t.stop()}}),t,this,[[0,4]])}))),function(){return e.apply(this,arguments)})},{key:"destroy",value:function(){var t;null===(t=this.audioContext_)||void 0===t||t.close()}},{key:"isSupported",value:function(){return"AudioWorklet"in window&&"WebAssembly"in window}}]),t}();return eA}));
