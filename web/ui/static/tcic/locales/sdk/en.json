{"Web端本地视频采集必须传入DOM节点": "Local video capture on the web requires passing in a DOM node", "Web端本地音频采集必须传入DOM节点": "Local audio capture on the web requires passing in a DOM node", "Web端麦克风测试必须传入DOM节点": "Microphone testing on the web requires passing in a DOM node", "Web端不支持": "Not supported on web", "{{arg_0}}等待状态超时: {{timeout}}": "{{arg_0}} Pending status timed out: {{timeout}}", "无法访问你的{{name}}设备，请检查系统权限设置": "Cannot access to your {{name}} device, please check the system permission Settings.", "打开{{name}}设备遇到一些问题": "There are some problems with opening the {{name}} device.", "不支持该方法": "This way is not supported", "传入非法参数": "Incoming illegal parameters", "你已被允许发言": "You have been allowed to speak.", "你已被禁言": "You have been muted.", "你已被{{arg_0}}禁言": "You have been muted by the {{arg_0}}.", "你当前使用的版本已不受支持，请先升级": "The current version is no longer supported, please upgrade.", "你当前使用的版本不支持保存图片": "The current version does not support save image.", "停止麦克风测试失败，移动端不支持": "Stopping the microphone test failed, mobile terminal does not support it.", "关闭扬声器失败，移动端不支持": "Failed to turn off the speaker, mobile terminal does not support it.", "关闭摄像头测试失败，移动端不支持": "The test of closing the camera failed because the mobile terminal does not support it.", "关闭本地视频采集遇到一些问题": "There are some problems with shutting down local video capture.", "关闭本地音频采集遇到一些问题": "There are some problems with turning off local audio collection.", "关闭远端视频遇到一些问题": "There are some problems with shutting down remote video.", "关闭麦克风失败": "Failed to turn off the microphone.", "关闭扬声器失败": "Failed to turn off the speaker.", "切换扬声器失败，移动端不支持": "Failed to switch speakers. Mobile does not support it.", "切换扬声器设备失败": "Failed to switch speaker device", "切换摄像头设备失败": "Failed to switch camera equipment", "切换用户角色遇到一些问题": "There are some problems with switching user roles.", "切换麦克风设备失败": "Failed to switch microphone device", "前置摄像头": "Front camera", "后置摄像头": "Rear camera", "加入了{{arg_0}}": "Joined the {{arg_0}}.", "加入信令群组失败": "Failed to join signaling group", "加入群组失败": "Failed to join group", "加入{{arg_0}}遇到问题，请尝试重新进入": "If you have problems joining the {{arg_0}}, please try to re-enter", "加入音视频房间失败": "Failed to join audio / video room", "加入音视频房间超时": "Timeout for joining audio and video room", "加载定制信息失败": "Failed to load customization information", "加载音视频库失败": "Failed to load audio and video library", "加载音视频插件失败": "Failed to load audio and video plugins", "取消发布": "Cancel publish", "后台服务正在维护当中，请稍候重新进入{{arg_0}}，或者联系客服了解进展": "The backstage service is under maintenance. Please re-enter the {{arg_0}} later, or contact customer service for progress.", "屏幕共享失败": "Screen sharing failed", "屏幕共享失败，系统录制被占用": "Screen sharing failed and system recording was occupied", "当前浏览器不支持切换扬声器设备": "Not support switching speaker devices in current browser", "恢复屏幕共享失败，屏幕共享未启动": "Failed to restore screen sharing. Screen sharing did not start.", "恢复屏幕共享遇到一些问题": "There are some problems with restoring screen sharing.", "您的网络有异常，请调整好网络环境后再重新登录！": "Your network is abnormal. Please adjust the network environment and log in again!", "打开屏幕共享失败，请先关掉本地视频推流": "Failed to enable screen sharing. Please turn off local video first.", "打开屏幕共享遇到一些问题": "There are some problems with opening the screen sharing.", "打开屏幕共享遇到一些问题，请检查系统权限设置": "There are some problems with opening the screen sharing, please check the system permission Settings.", "打开扬声器测试失败，移动端不支持": "Failed to turn on the loudspeaker test. The mobile terminal does not support it.", "打开摄像头失败": "Failed to turn on the camera", "打开摄像头失败，未找到设备": "Failed to turn on the camera and no device was found", "打开摄像头测试失败，移动端不支持": "Failed to open the camera test. Mobile does not support it.", "打开本地摄像头遇到一些问题": "There are some problems with turning on the local camera.", "打开本地视频推送失败，请先关掉屏幕共享": "Failed to open local video push. Please turn off the screen to share first.", "打开本地视频采集失败，请先关掉屏幕共享": "Failed to open local video capture. Please turn off the screen to share first.", "打开本地视频采集遇到一些问题": "There are some problems with turning on local video collection.", "打开本地音频采集遇到一些问题": "There are some problems with turning on local audio collection.", "打开本地麦克风遇到一些问题": "There are some problems with turning on the local microphone.", "打开远端视频遇到一些问题": "There are some problems with opening remote video.", "打开麦克风失败": "Failed to turn on the microphone", "打开麦克风失败，未找到设备": "Failed to turn on the microphone and no device was found", "打开麦克风测试失败，移动端不支持": "Failed to turn on the microphone test. Mobile does not support it.", "打开扬声器失败": "Failed to turn on the speaker", "打开扬声器失败，未找到设备": "Failed to turn on the speaker and no device was found", "授权": "Authorization", "控制是否分享系统声音遇到一些问题": "There are some problems in controlling whether to share the sound of the system.", "控制本地视频推送遇到一些问题": "There are some problems in controlling local video push.", "控制本地音频推送遇到一些问题": "There are some problems in controlling local audio push.", "摄像头被占用": "The camera is occupied.", "操作进行中 {{arg_0}}": "Operation in progress {{arg_0}}", "无变化": "No change", "暂停屏幕共享失败，屏幕共享未启动": "Failed to pause screen sharing. Screen sharing did not start.", "暂停屏幕共享遇到一些问题": "There are some problems with pausing screen sharing.", "未知原因导致设备无法被使用": "The device cannot be used for unknown reasons", "自动播放被系统限制": "The ability of automatic video playback is limited by the system", "未知系统": "Unknown system", "正在设备检测，打开摄像头失败": "Device Testing, failed to turn on the camera.", "正在设备检测，打开麦克风失败": "Device testing, failed to turn on microphone", "没有找到对应的设备": "No matching device found", "没有权限打开摄像头": "No permission to turn on the camera", "没有权限打开麦克风": "No permission to turn on the microphone", "独占模式冲突": "Exclusive mode conflict", "白板出现异常，建议退出重新进入{{arg_0}}": "Whiteboard is abnormal, please quit and re-enter the {{arg_0}}.", "白板模块上抛TEB_ERROR事件, 错误码: {{code}}, 错误描述: {{msg}}": "TEB_ERROR event is thrown on the whiteboard module. Error code: {{code}}, error description: {{msg}}", "白板页数已经到达上限": "The number of whiteboard pages has reached the limit", "离开了{{arg_0}}": "Left the {{arg_0}}.", "移动端不支持": "Mobile does not support", "系统不支持": "The system does not support", "结束屏幕共享遇到一些问题": "There are some problems in ending screen sharing.", "缺少classid参数": "Missing class id parameter", "缺少token参数": "Missing token parameter", "缺少userid参数": "Missing userid parameter", "缺少重要参数": "Missing significant parameters", "网络异常，请检查网络后，重新进入{{arg_0}}": "The network is abnormal. Please check the network and re-enter the {{arg_0}}.", "网络异常，请检查网络后，重新进入{{arg_0}} ({{statusCode}})": "The network is abnormal. Please check the network and re-enter the {{arg_0}} ({{statusCode}}).", "网页端不支持": "Web side does not support", "当前浏览器不支持": "Current browser does not support", "{{arg_0}}已解除全员禁言": "The {{arg_0}} has lifted the ban on speech.", "{{arg_0}}已设置全员禁言": "The {{arg_0}} has set a ban on the whole staff.", "获取{{arg_0}}信息失败": "Failed to get {{arg_0}} information", "获取{{arg_0}}信息失败 {{arg_1}}": "Failed to get {{arg_0}} information, {{arg_1}}", "获取当前扬声器失败，移动端不支持": "Failed to get the current speaker. Mobile does not support it.", "获取扬声器失败，移动端不支持": "Failed to get loudspeaker. Mobile does not support it.", "获取摄像头设备失败，无法开启设备": "Failed to get camera device ID, failed to open device", "获取麦克风设备失败，无法开启设备": "Failed to get microphone device ID, failed to open device", "获取麦克风音量失败，移动端不支持": "Failed to get microphone volume, not supported on mobile", "设备参数配置异常，请重新选择设备": "Device parameter configuration is abnormal，please reselect", "请确保没有其他应用占用了{{name}}设备": "Make sure no other apps are using the {{name}} device", "初始化异常，请联系运维人员": "Initialization is abnormal, please contact the operation and maintenance staff", "请求信息异常，请尝试重新进入{{arg_0}}，或者联系客服协助解决": "If the request for information is abnormal, please try to re-enter the {{arg_0}}, or contact customer service to help solve the problem.", "请求视频失败，请检查网络": "Failed to request video. Please check the network.", "请求m3u8文件失败，可能是网络错误或者跨域问题": "Failed to request m3u8 file, possibly due to network error or cross-domain problem", "调用 WebRTC 接口失败": "Failed to call WebRTC API", "调用拉流接口失败": "Failed to call pull API", "连接服务器失败，并且连接重试次数已超过设定值": "Failed to connect to the server, and the number of connection retries has exceeded the set value.", "进入{{arg_0}}失败": "Failed to enter the {{arg_0}}", "进入音视频房间失败": "Failed to enter audio / video room", "进房失败，加入音视频房间失败": "Failed to enter the room. Joining the audio and video room failed.", "进房失败，加入音视频房间超时": "Failed to enter the room. Joining the audio and video room timed out.", "远端视频流不存在": "Remote video stream does not exist", "退出{{arg_0}}": "Exit {{arg_0}}", "退出音视频房间失败": "Failed to exit audio / video room", "鉴权信息异常，请尝试重新进入{{arg_0}}，或者联系客服协助解决": "Authentication information is abnormal, please try to re-enter the {{arg_0}}, or contact customer service to help solve the problem.", "需要您授权以播放音视频": "Please grant permission to play audio and video.", "重新加载": "reload", "麦克风被占用": "The microphone is occupied.", "音频播放设备被占用或损坏": "Audio device in use or damaged.", "默认麦克风": "Default microphone", "摄像头": "Camera", "麦克风": "Microphone", "设备媒体被浏览器禁止而无法使用": "The device media is prohibited by the browser and cannot be used", "代码问题导致设备无法使用": "A problem with the code renders the device unusable", "当前浏览器不支持，请升级或切换其他浏览器": "The current browser does not support it. Please upgrade or switch to another browser", "数据传输通道错误，请检查防火墙端口限制": "Data transmission channel error, please check firewall port restrictions", "当前设备没有可用的{{name}}": "There are no {{name}} available for the current device", "{{name}}采集失败，请检查连接线或设备是否被其他应用占用": "{{name}} acquisition failed. Please check whether the connection line or device is occupied by other applications", "网络异常，请检查网络设置": "The network is abnormal, please check your network connection", "网络异常": "Network abnormal", "音视频自动播放被系统限制": "Automatic playback of audio and video is restricted by the system", "浏览器": "Browser", "小程序": "Mini Program", "移动端": "Mobile", "PC端": "PC", "，": ", ", "、": ", ", "{{arg_0}}或{{arg_1}}": "{{arg_0}} or {{arg_1}}", "请使用{{arg_1}}进入{{arg_0}}": "Please enter the {{arg_0}} with {{arg_1}}", "提示": "Hint", "为了保证最佳体验，{{arg_0}}请使用横屏。": "Please use landscape mode for a better experience.", "为了保证最佳体验，请使用横屏。": "Please use landscape mode for a better experience.", "注意：请先解除手机方向锁定": "Remember to unlock the screen orientation first.", "确定": "OK", "取消": "Cancel", "重试": "Retry", "正在加载音频资源，请勿频繁操作": "Loading audio resources, do not operate frequently", "播放音频被取消": "Playing audio has been cancelled", "开启AI降噪失败，移动端不支持": "Failed to turn on AI Denoise, mobile terminal does not support it.", "抱歉，您已被另一位{{arg_0}}移出。如有疑问，请联系管理员或相关{{arg_0}}。": "Sorry, you have been removed by another {{arg_0}}. If you have any questions, please contact the administrator or the relevant {{arg_0}}.", "出现异常": "An exception occurred", "重新进入": "Re-enter", "白板出现异常，建议退出重新进入": "An exception occurred with the whiteboard. It is recommended to exit and re-enter.", "取消与该用户的互动": "Cancel interaction with this user", "关闭该用户的视频": "Turn off this user's video", "关闭该用户的麦": "Mute this user's microphone", "确认终止与此用户连麦？": "Are you sure you want to end the connection with this user?", "确认关闭此用户摄像头？": "Are you sure you want to turn off this user's camera?", "确认关闭此用户麦克风？": "Are you sure you want to turn off this user's microphone?", "当前无网络，请检查网络设置，或尝试重新进入": "No network. Please check, or try re-entering", "离开": "Leave", "当前无网络，请检查网络设置": "No network. Please check", "无法使用麦克风": "No Access to Microphone", "检测网络": "Detection", "网络不佳，连接已超时。请检查您的网络情况或尝试重新进入。": "Poor network connection; the connection has timed out. Please check your network or try re-entering.", "上传成功": "Upload Success", "网络中断，建议退出重新进入": "Network interruption, it is recommended to exit and re-enter", "异常提示": "Exception prompt", "获取扬声器设备失败，无法开启设备": "Failed to obtain the speaker device and cannot turn on the device", "当前页面出错": "Current page error", "您不在课堂，请重新加入": "You are not in the class, please rejoin", "自由聊天": "Free chat", "仅允许公开聊天": "Public chat only", "仅允许私聊": "Private chat only", "全员禁言": "All muted", "{{arg_0}}已设置{{arg_1}}": "{{arg_0}} has changed to {{arg_1}}", "为最佳体验，请使用横屏。": "For the best experience, please use landscape mode.", "用户信息验证失败，请重新进入{{arg_0}}": "Failed to verify your user information. Please rejoin {{arg_0}}.", "摄像头开启异常": "Camera Access Issue", "请重启设备或应用后重试": "Please restart your device or app and try again"}