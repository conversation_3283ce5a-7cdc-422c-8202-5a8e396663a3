const { merge } = require('webpack-merge');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const HtmlReplaceWebpackPlugin = require('html-replace-webpack-plugin');
// const UglifyJSPlugin = require('uglifyjs-webpack-plugin');

const versionConfig = require('../version');
const baseConfig = require('./webpack.config.base.js');
const replaceHelper = require('./webpack-replace-helper.js');
const htmlReplaceOptions = replaceHelper.getHtmlReplaceReplaceOptions('development');

const productionConfig = {
  mode: 'development',
  // devtool: 'source-map', // 开发环境用于调试
  plugins: [
    new HtmlWebpackPlugin({
      chunks: ['main'],
      minify: {
        collapseWhitespace: true, // 折叠空白区域 也就是压缩代码
      },
      title: '空课',
      template: 'src/pages/class/class.html',
      filename: 'class.html',
    }),
    new HtmlWebpackPlugin({
      chunks: ['login'],
      title: '空课登录',
      template: 'src/pages/login/login.html',
      filename: 'login.html',
    }),
    new HtmlWebpackPlugin({
      chunks: ['mainIndex'],
      title: '空课',
      template: 'src/pages/main/index.html',
      filename: 'index.html',
    }),
    new HtmlReplaceWebpackPlugin(htmlReplaceOptions),

    new HtmlWebpackPlugin({
      chunks: ['externalCourseware'],
      title: '外部课件',
      template: 'src/pages/externalCourseware/externalCourseware.html',
      filename: 'externalCourseware.html',
    }),

    new CopyWebpackPlugin({
      patterns: [{
        from: 'static',
        to: 'static/',
        globOptions: {
          dot: false, // 是否包括以 . 开头的文件, 比如 .gitignore
          gitignore: true, // 是否包括 .gitignore 中忽略的文件
          ignore: ['**/tcic/**/*'],
        },
      }, {
        from: 'static/tcic',
        to: `static/tcic/${versionConfig.mainVersion}/`,
      }],
    }),
  ],
};
module.exports = merge(baseConfig, productionConfig);
