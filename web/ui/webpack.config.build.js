const { merge } = require('webpack-merge');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const HtmlReplaceWebpackPlugin = require('html-replace-webpack-plugin');
const UglifyJSPlugin = require('uglifyjs-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const OptimizeCssAssetsPlugin = require('optimize-css-assets-webpack-plugin');

const versionConfig = require('../version');
const baseConfig = require('./webpack.config.base.js');
const replaceHelper = require('./webpack-replace-helper.js');
const htmlReplaceOptions = replaceHelper.getHtmlReplaceReplaceOptions('production');

// css加hash
baseConfig.plugins.forEach((plugin, index) => {
  if (plugin.key === 'MiniCssExtractPlugin') {
    plugin.options.filename = 'tcic-ui-[name].[contenthash:8].css';
  }
});

const productionConfig = {
  mode: 'production',
  devtool: 'none',
  output: {
    filename: 'tcic-ui-[name].[contenthash:8].js',
  },
  plugins: [
    new OptimizeCssAssetsPlugin(),
    new CleanWebpackPlugin(),
    new UglifyJSPlugin({
      uglifyOptions: {
        compress: {
          // drop_console: true,
          drop_console: false,
          pure_funcs: ['console.log', 'console.debug'], // 只去掉console.log和debug, 保留warn, error等console的调用.
          drop_debugger: true,
        },
      },
    }),

    new HtmlWebpackPlugin({
      chunks: ['main'],
      minify: {
        collapseWhitespace: true, // 折叠空白区域 也就是压缩代码
      },
      title: '空课',
      inject: false,
      template: 'src/pages/class/class.html',
      filename: 'class-[contenthash:8].html',
    }),
    new HtmlWebpackPlugin({
      chunks: ['login'],
      title: '空课登录',
      template: 'src/pages/login/login.html',
      filename: 'login.html',
    }),
    new HtmlWebpackPlugin({
      chunks: ['mainIndex'],
      title: '空课',
      template: 'src/pages/main/index.html',
      filename: 'index.html',
    }),
    new HtmlWebpackPlugin({
      chunks: ['main'],
      inject: false,
      title: 'LCIC Network Test',
      template: 'src/pages/network/index.html',
      filename: 'network.html',
    }),
    new HtmlReplaceWebpackPlugin(htmlReplaceOptions),

    new HtmlWebpackPlugin({
      chunks: ['externalCourseware'],
      title: '外部课件',
      template: 'src/pages/externalCourseware/externalCourseware.html',
      filename: 'externalCourseware.html',
    }),

    new CopyWebpackPlugin({
      patterns: [{
        from: 'static',
        to: 'static/',
        globOptions: {
          dot: false, // 是否包括以 . 开头的文件, 比如 .gitignore
          gitignore: true, // 是否包括 .gitignore 中忽略的文件
          ignore: ['**/tcic/**/*'],
        },
      }, {
        from: 'static/tcic',
        to: `static/tcic/${versionConfig.mainVersion}/`,
      }],
    }),
  ],
  optimization: {
    moduleIds: 'hashed',
    chunkIds: 'named',
  },
};
module.exports = merge(baseConfig, productionConfig);
