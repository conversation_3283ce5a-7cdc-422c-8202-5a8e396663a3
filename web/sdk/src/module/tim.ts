import i18next from 'i18next';
import TencentCloudChat, { ChatSDK } from '@tencentcloud/chat';
import { TEvent } from '../base/tevent';
import {
  IMError as TCICError,
  TModule,
} from '../base/tmodule';
import { TUserInfo } from './business/tbusiness_user';
import { TMain } from './tmain';
import { TMainEvent, TMainState } from '../constants/main';
import { TSession } from './tsession';
import { TState } from './tstate';
import { TClassHistroyMessage, TClassSilenceMode, TClassStatus } from './business/tbusiness_class';
import TIMUploadPlugin from 'tim-upload-plugin';
import { TUtil } from './tutil_inner';
import Lodash from 'lodash';
import {
  TIMEvent,
  TIMConvType,
  TIMMsgType,
  TIMMsg,
} from '../constants/im';
import { TBusinessMember, TMemberActionType, TMemberRole } from './business/tbusiness_member';
import { TBusiness } from './business/tbusiness';
import { TLevel } from './tlogger';

const JSTIM = TencentCloudChat;

const costActionLabel = 'tim.loginCost';


/**
 * 消息统计
 */
class TIMStatsItem {
  /**
   * 成功发送的条数
   */
  succ: number;
  /**
   * 总发送条数
   */
  total: number;

  constructor() {
    this.succ = 0;
    this.total = 0;
  }
  public serialize(): any {
    let rate = 1;
    if (this.total !== 0) {
      rate = this.succ / this.total;
    }
    return {
      succ: this.succ,
      total: this.total,
      rate,
    };
  }
}
class TIMStats {
  /**
   * 用户Id
   */
  userId: string;
  /**
   * 当前班级
   */
  classId: string;
  /**
   * C2C信令
   */
  c2cCmdItem: TIMStatsItem;
  /**
   * 群信令
   */
  groupCmdItem: TIMStatsItem;
  /**
   * C2C普通消息
   */
  c2cMsgItem: TIMStatsItem;
  /**
   * 群普通消息
   */
  groupMsgItem: TIMStatsItem;

  constructor() {
    this.userId = '';
    this.classId = '';
    this.c2cCmdItem = new TIMStatsItem();
    this.groupCmdItem = new TIMStatsItem();
    this.c2cMsgItem = new TIMStatsItem();
    this.groupMsgItem = new TIMStatsItem();
  }

  public serialize(): any {
    return {
      userId: this.userId,
      classId: this.classId,
      c2cCmdItem: this.c2cCmdItem.serialize(),
      groupCmdItem: this.groupCmdItem.serialize(),
      c2cMsgItem: this.c2cMsgItem.serialize(),
      groupMsgItem: this.groupMsgItem.serialize(),
    };
  }
}

/**
 * IM 消息列表
 * @param {boolean} isCompleted 是否拉完所有消息
 * @param {string} nextMsgId 用于续拉，分页续拉时需传入该字段
 * @param {TIMMsg[]} msgList 消息列表
 */
export class TIMMsgList {
  public isCompleted = false;
  public nextMsgId = '';
  public msgList: TIMMsg[] = [];
}

type TIMMsgSendResolve = (rsp: any) => void;
type TIMMsgSendReject = (error: any) => void;

class TIMMsgSender {
  private static globalSeq = 0;

  public msg: any = null;
  public resolve: TIMMsgSendResolve = null;
  public reject: TIMMsgSendReject = null;
  public seq = 0;
  public retryTimes = 0;

  constructor(msg: any, resolve: TIMMsgSendResolve, reject: TIMMsgSendReject) {
    TIMMsgSender.globalSeq += 1;
    this.msg = msg;
    this.resolve = resolve;
    this.reject = reject;
    this.seq = TIMMsgSender.globalSeq;
    this.retryTimes = 0;
  }
}

export type TSendProgressCallback = (imgId: number, percent: number) => void;

/**
 * IM模块
 */
export class TIM extends TModule {
  static readonly EVENT = JSTIM.EVENT;
  static readonly TYPES = JSTIM.TYPES;

  /**
   * 返回该类实例
   */
  public static get instance(): TIM {
    return this.getInstance();
  }

  private static _addMsgSenderToQueue(sender: TIMMsgSender, senderList: TIMMsgSender[]) {
    let index = senderList.length - 1;
    while (index >= 0 && sender.seq < senderList[index].seq) {
      index -= 1;
    }
    // 按seq排序插入队列
    senderList.splice(index + 1, 0, sender);
  }


  private _tim: ChatSDK;
  private _unreadSeqSet: Set<number>;
  private _userId: string;
  private _userSig: string;
  private _chatGroupId: string;
  private _cmdGroupId: string;
  private _chatGroupLastMsgSeq = 0;
  private _cmdGroupLastMsgSeq = 0;
  private _customMsgSenderList: TIMMsgSender[] = [];
  private _maxMsgListSize = 1000;       // 允许保存的最多消息条数
  private _msgList: TIMMsg[] = [];
  private _pendingMsgList: TIMMsg[] = [];
  private _sendTextLogEnabled = false;
  private _sendCustomLogEnabled = false;
  private _recvTextLogEnabled = false;
  private _recvCustomLogEnabled = false;
  private _currentImgSendID = 0;
  private _stats: TIMStats;
  private _statsTimer: NodeJS.Timer;
  private _handlePendingMsgListFun: any = null;
  private _latestNotifyQuickMsg: any = null;
  private _quickIMWords: string[] = [];
  /**
   * 模块初始化接口
   * @param sdkAppId    应用标识SDKAPPID
   * @param userId      用户名
   * @param userSig     用户签名
   * @param chatGroupId 聊天群组ID
   * @param cmdGroupId  信令群组ID
   */
  public init(
    sdkAppId: number,
    userId: string,
    userSig: string,
    chatGroupId: string,
    cmdGroupId: string,
  ): Promise<void> {
    this._logStart(costActionLabel);
    return new Promise((resolve, reject) => {
      const checkIfAssistant = async (msg: any) => {
        // 如果是学生
        if (TMain.instance.isStudent()) {
          return false;
        }
        try {
          const comeInfos: any = JSON.parse(msg.data);
          for (let i = 0; i < comeInfos.data.data.length; ++i) {
            const comeInfo = comeInfos.data.data[i];
            // const result = await TBusinessMember.instance.getMemberByUserName(comeInfo.nickname, {
            //   cid: Number(msg.to),
            //   token: TSession.instance.getToken(),
            // });
            let result = await TBusinessMember.instance.getMemberByUserIdFromCache(comeInfo.user_id);
            if (!result) {
              result = await TBusinessMember.instance.getMemberByUserId(comeInfo.user_id);
            }
            /**
             * 助教不展示进出通知
             */
            // @ts-ignore
            if (result && result.role === TMemberRole.Assistant) {
              return true;
            }
          }
        } catch (err) {
          console.error('getClassMemberList: comeInfo: err', err);
        }
        return false;
      };


      setTimeout(() => {
        this._info('tim_init', `sdkAppId: ${sdkAppId}, chatGroupId: ${chatGroupId}, cmdGroupId: ${cmdGroupId}`);

        // 内部维护的状态
        TState.instance.registerState(TMainState.IM_Logined, 'IM已登录', false, [this.constructor.name]);
        TState.instance.registerState(TMainState.IM_Kicked, 'IM已被踢', false, [this.constructor.name]);
        TState.instance.registerState(TMainState.IM_SIG_EXPIRED, 'IM签名过期', false, [this.constructor.name]);
        TState.instance.registerState(TMainState.IM_Cmd_Ready, 'IM信令通道可用', false, [this.constructor.name]);
        TState.instance.registerState(TMainState.IM_Chat_Ready, 'IM聊天通道可用', false, [this.constructor.name]);
        TState.instance.registerState(TMainState.IM_Msg_List, '消息消息列表', this._msgList, [this.constructor.name]);
        TState.instance.registerState(TMainState.Message_Unread_Count, '消息未读计数', 0, [this.constructor.name]);

        // 外部开关
        TState.instance.registerState(TMainState.Join_Quit_Tips, '显示成员进出房通知', true);
        // 外部开关
        TState.instance.registerState(TMainState.Hand_Up_Tips, '显示成员举手通知', true);

        // 全员禁言通知处理
        TState.instance.subscribeState(TMainState.Silence_All, (silenceAll) => {
          this.addMsgToList({
            time: +new Date() / 1000,
            id: (+new Date() / 1000).toString(),
            seq: 0,
            convType: TIMConvType.Tips,
            msgType: TIMMsgType.SilenceAllTips,
            from: '',
            to: '',
            data: silenceAll ? i18next.t('{{arg_0}}已设置全员禁言', { arg_0: TSession.instance.getRoleInfo().roleInfo.teacher }) : i18next.t('{{arg_0}}已解除全员禁言', { arg_0: TSession.instance.getRoleInfo().roleInfo.teacher }),
          });
        }, { noEmitWhileSubscribe: true, noEmitWhileRegister: true });

        // 全员发言状态通知处理
        TState.instance.subscribeState(TMainState.Silence_Mode, (silenceMode) => {
          const silenceModeTextMap: Record<TClassSilenceMode, string> = {
            [TClassSilenceMode.Free_Chat]: i18next.t('自由聊天'),
            [TClassSilenceMode.Public_Only]: i18next.t('仅允许公开聊天'),
            [TClassSilenceMode.Private_Only]: i18next.t('仅允许私聊'),
            [TClassSilenceMode.All_Mute]: i18next.t('全员禁言'),
          };

          this.addMsgToList({
            time: Math.floor(+new Date() / 1000),
            seq: 0,
            id: Math.floor(+new Date() / 1000).toString(),
            convType: TIMConvType.Tips,
            msgType: TIMMsgType.SilenceAllTips,
            from: '',
            to: '',
            data: i18next.t('{{arg_0}}已设置{{arg_1}}', {
              arg_0: TSession.instance.getRoleInfo().roleInfo.teacher,
              arg_1: silenceModeTextMap[silenceMode as 0 | 1 | 2 | 3] || '',
            }),
          });
        }, { noEmitWhileSubscribe: true, noEmitWhileRegister: true });

        // 单独禁言通知处理
        TState.instance.subscribeState(TMainState.Chat_Permission, (chatEnabled) => {
          if (chatEnabled) {
            this.addMsgToList({
              time: +new Date() / 1000,
              seq: 0,
              convType: TIMConvType.Tips,
              id: (+new Date() / 1000).toString(),
              msgType: TIMMsgType.SilenceUserTips,
              from: '',
              to: '',
              data: i18next.t('你已被允许发言'),
            });
          }
        }, { noEmitWhileSubscribe: true, noEmitWhileRegister: true });
        TEvent.instance.on(TMainEvent.Recv_Member_Action, ({ action, operatorId }) => {
          const isTeacherOperator = TMain.instance.isTeacher(operatorId);
          const isAssistantOperator = TMain.instance.isAssistant(operatorId);
          const roleInfo = TSession.instance.getRoleInfo().roleInfo;
          if (action === TMemberActionType.Silence) {
            let msgData = i18next.t('你已被禁言');
            if (isTeacherOperator) {
              msgData = i18next.t('你已被{{arg_0}}禁言', { arg_0: roleInfo.teacher });
            } else if (isAssistantOperator) {
              msgData = i18next.t('你已被{{arg_0}}禁言', { arg_0: roleInfo.assistant });
            }
            this.addMsgToList({
              time: +new Date() / 1000,
              seq: 0,
              id: (+new Date() / 1000).toString(),
              convType: TIMConvType.Tips,
              msgType: TIMMsgType.SilenceUserTips,
              from: '',
              to: '',
              data: msgData,
            });
          }
        });

        // 成员进出通知处理
        TEvent.instance.on(TMainEvent.Member_Join, async (userId: string, msg: TIMMsg) => {
          // 设置了不显示成员进出通知，则不通知
          if (!TState.instance.getState(TMainState.Join_Quit_Tips)) return;

          // 当前是 fake1v1 场景，学生之间互相不可见，也不显示其他学生进出通知
          if (TMain.instance.isFake1v1() && TMain.instance.isStudent()) return;
          /**
           * 助手进出不展示
           */
          const isAssistan = await checkIfAssistant(msg);
          if (isAssistan) {
            return;
          }
          const memberJoinExitFilter = TMain.instance.getMemberJoinExitRoomInfoFilter();
          if (memberJoinExitFilter) {
            const shouldFilter = await memberJoinExitFilter({
              userId,
              msg,
            });
            if (shouldFilter) {
              return;
            }
          }

          this.addMsgToList(Object.assign({
            time: +new Date() / 1000,
            seq: 0,
          }, msg, {
            convType: TIMConvType.Tips,
            msgType: TIMMsgType.JoinTips,
            from: userId,
            to: '',
            data: i18next.t('加入了{{arg_0}}', { arg_0: TSession.instance.getRoleInfo().roomInfo.name }),
          }));
        });
        TEvent.instance.on(TMainEvent.Member_Exit, async (userId: string, msg: TIMMsg) => {
          // 设置了不显示成员进出通知，则不通知
          if (!TState.instance.getState(TMainState.Join_Quit_Tips)) return;

          // 当前是 fake1v1 场景，学生之间互相不可见，也不显示其他学生进出通知
          if (TMain.instance.isFake1v1() && TMain.instance.isStudent()) return;
          /**
           * 助手进出不展示
           */
          const isAssistan = await checkIfAssistant(msg);
          if (isAssistan) {
            return;
          }
          const memberJoinExitFilter = TMain.instance.getMemberJoinExitRoomInfoFilter();
          if (memberJoinExitFilter) {
            const shouldFilter = memberJoinExitFilter({
              userId,
              msg,
            });
            if (shouldFilter) {
              return;
            }
          }
          this.addMsgToList(Object.assign({
            time: +new Date() / 1000,
            seq: 0,
          }, msg, {
            convType: TIMConvType.Tips,
            msgType: TIMMsgType.QuitTips,
            from: userId,
            to: '',
            data: i18next.t('离开了{{arg_0}}', { arg_0: TSession.instance.getRoleInfo().roomInfo.name }),
          }));
        });

        // 成员举手通知处理
        TEvent.instance.on(TMainEvent.Member_Hand_Up, ({ userId, stage }, msg: TIMMsg) => {
          if (!TState.instance.getState(TMainState.Hand_Up_Tips)) return;
          this.addMsgToList(Object.assign({
            time: +new Date() / 1000,
            seq: 0,
          }, msg, {
            convType: TIMConvType.Tips,
            msgType: TIMMsgType.HandUpTips,
            from: userId,
            to: '',
            data: '',
          }));
        });
        // 成员取消举手通知处理
        TEvent.instance.on(TMainEvent.Member_Hand_Up_Cancel, ({ userId, stage }, msg: TIMMsg) => {
          if (!TState.instance.getState(TMainState.Hand_Up_Tips)) return;
          this.addMsgToList(Object.assign({
            time: +new Date() / 1000,
            seq: 0,
          }, msg, {
            convType: TIMConvType.Tips,
            msgType: TIMMsgType.HandUpCancelTips,
            from: userId,
            to: '',
            data: '',
          }));
        });
        this._tim = JSTIM.create({
          SDKAppID: sdkAppId,
        });
        this._tim.setLogLevel(3);  // 只允许打印错误日志
        this._userId = userId;
        this._userSig = userSig;
        this._chatGroupId = chatGroupId;
        this._cmdGroupId = cmdGroupId;
        this._unreadSeqSet = new Set<number>();

        this._tim.registerPlugin({ 'tim-upload-plugin': TIMUploadPlugin });

        this._initEventListener();

        TState.instance.promiseState(TMainState.IM_Cmd_Ready, true)
          .then(() => {  // IM可用后启动消息发送计时器
            setInterval(() => {
              if (TState.instance.getState(TMainState.Network_Broken, false)) {  // 网络不可用时，不尝试发送消息
                return;
              }
              const isReady = TState.instance.getState(TMainState.IM_Logined, false);
              if (!isReady) {
                return;
              }
              const sender = this._customMsgSenderList.shift();
              if (sender) {
                this._trySendMsg(sender, this._customMsgSenderList, true);  // 马上尝试发送
              }
            }, 100);  // 每秒最多发送10条消息
          });
        TState.instance.setState(TMainState.Project_Core_Process, TMainEvent.Core_process_Start_Login_IM);
        this.login(this._userId, this._userSig)
          .then(() => {
            TState.instance.setState(TMainState.Project_Core_Process, TMainEvent.Core_process_Start_Login_IM_Success);
            resolve();
          })
          .catch((err: any) => {
            err = err || {};
            this._error('tim_login', `userId: ${userId}, userSig: ${userSig}, errorCode: ${err.code}, message: ${err.message}`);
            TState.instance.setState(TMainState.Project_Core_Process, TMainEvent.Core_process_Start_Login_IM_Error);
            // 这行抛出异常和login本身的重复了，因此注释掉
            // TEvent.instance.notify(TMainEvent.Tim_Error,
            // new TCICError(-1, i18next.t('您的网络有异常，请调整好网络环境后再重新登录！'),
            // JSON.stringify(err)));
            reject(err);
          });
      }, 0);
    });
  }

  /**
   * 控制台日志打印开关（for 自动化测试使用）
   * @param sendTextLog     是否打印发送文本消息日志
   * @param sendCustomLog   是否打印发送自定义消息日志
   * @param recvTextLog     是否打印接收文本消息日志
   * @param recvCustomLog   是否打印接收自定义消息日志
   */
  public enableLog(
    sendTextLog = false,
    sendCustomLog = true,
    recvTextLog = false,
    recvCustomLog = true,
  ) {
    this._sendTextLogEnabled = sendTextLog;
    this._sendCustomLogEnabled = sendCustomLog;
    this._recvTextLogEnabled = recvTextLog;
    this._recvCustomLogEnabled = recvCustomLog;
  }

  /**
   * 登录接口
   * @param userId 用户ID
   * @param userSig  用户签名
   * @param tryCount  尝试次数，默认为1
   */
  public login(userId: string, userSig: string, tryCount = 1): Promise<void> {
    this._stats = new TIMStats();
    if (this._statsTimer) {
      clearInterval(this._statsTimer);
      this._statsTimer = null;
    }
    this._statsTimer = setInterval(this.imStatsReport, 120 * 1000);
    this._info('tim_login', `userId: ${userId}, userSig: ${userSig}, tryCount: ${tryCount}`);
    return new Promise(async (resolve, reject) => {
      const errBlock = (err: any) => {
        err = err || {};
        // 重试两次，一共调用三次
        if (tryCount > 2) {
          this._error('tim_login', `userId: ${userId}, userSig: ${userSig}, errorCode: ${err.code}, message: ${err.message}`);
          TEvent.instance.notify(TMainEvent.Tim_Error, new TCICError(err.code, `${i18next.t('网络不佳，连接已超时。请检查您的网络情况或尝试重新进入。')}`, JSON.stringify(err)));
          reject(err);
        } else {
          setTimeout(() => {
            this.login(userId, userSig, tryCount + 1)
              .then(resolve)
              .catch(reject);
          }, 1000 * tryCount);
        }
      };

      try {
        let timeoutId: number | NodeJS.Timeout | any;
        const timeoutPromise = new Promise((_, reject) => {
          timeoutId = setTimeout(() => {
            const timeoutError = {
              code: 9510,
              message: '登录超时',
            };
            reject(timeoutError);
          }, 10000);
        });

        await Promise.race([
          this._tim.login({
            userID: userId,
            userSig,
          }).finally(() => clearTimeout(timeoutId)),
          timeoutPromise,
        ]);

        resolve();
      } catch (err) {
        errBlock(err);
      }
    });
  }

  /**
   * 注销接口
   */
  public logout() {
    this.imStatsReport();
    clearInterval(this._statsTimer);
    this._info('tim_logout', 'enter');
    return this._tim.logout();
  }

  /**
   * 加入群组
   * @param groupId 群组ID
   */
  public async joinGroup(groupId: string) {
    this._info('tim_joinGroup', `groupId: ${groupId}`);
    TState.instance.setState(TMainState.Project_Core_Process, TMainEvent.Core_process_Start_Join_IM_Group);
    const ret = await this._tim.joinGroup({
      groupID: groupId,
    }).catch((error: any) => {
      if (error.code === 10013) {  // 可以忽略的错误
        this._info('tim_joinGroup', `groupId: ${groupId} success 10013`);
        return Promise.resolve();
      }
      TState.instance.setState(TMainState.Project_Core_Process, TMainEvent.Core_process_Join_IM_Group_Error);
      this._error('tim_joinGroup', `join ${groupId} fail: ${error}`);
      // 加入失败抛出错误
      throw error;
    });
    TState.instance.setState(TMainState.Project_Core_Process, TMainEvent.Core_process_Join_IM_Group_Success);
    this._info('tim_joinGroup', `groupId: ${groupId} success`);
    TCIC.SDK.instance.updateClassInfo().then(r => this._info('tim_joinGroup', `groupId: ${groupId} success. Class Info Updated.`));
    return ret;
  }

  /**
   * 修改用户信息
   * @param info
   */
  public setProfile(info: Object) {
    this._info('tim_updateProfile', `info: ${JSON.stringify(info)}`);
    return this._tim.updateMyProfile(info);
  }
  /**
   * 退出群组
   * @param groupId 群组ID
   */
  public quitGroup(groupId: string) {
    this._info('tim_quitGroup', `groupId: ${groupId}`);
    return this._tim.quitGroup(groupId);
  }
  /**
   * 发送群组文本消息
   * @param text 文本消息
   * @param dataExt 扩展数据，透传
   */
  public sendGroupTextMessage(text: string, dataExt?: any, priority?: TencentCloudChat.TYPES): Promise<TIMMsg> {
    this._info('tim_sendGroupTextMessage', `groupId: ${this._chatGroupId}, msg: ${text} ${dataExt} ${priority}`);
    return new Promise((resolve, reject) => {
      if (!this.checkIMLogin()) {
        resolve(null);
        this._info('tim_sendGroupTextMessage', 'sdk not ready');
        return;
      }
      let needUploadLogs = false;
      if (text === '上传日志') {
        needUploadLogs = true;
      }
      this._tim.sendMessage(this._tim.createTextMessage({
        to: this._chatGroupId,
        conversationType: JSTIM.TYPES.CONV_GROUP,
        payload: {
          text,
        },
        cloudCustomData: dataExt ? JSON.stringify(dataExt) : '',
        priority: priority ?? JSTIM.TYPES.MSG_PRIORITY_LOW, // 普通文本消息，优先级低
      })).then((rsp: any) => {
        console.log('tim_sendGroupTextMessage success', rsp);
        if (needUploadLogs) {
          this.logUpload();
          this.iframeLogUpload();
        }
        this._stats.groupMsgItem.total += 1;
        this._stats.groupMsgItem.succ += 1;
        const msgList = this._convertMsg(rsp.data.message);
        const msg = msgList[0];
        console.log('tim_sendGroupTextMessage success, localMsg', msg);
        this._sendMsgHandler(msgList);
        resolve(msg);
      })
        .catch((err: any) => {
          this._stats.groupMsgItem.total += 1;
          this._error('tim_sendGroupTextMessage', `code: ${err.code}, message: ${err.message}`);
          reject(err);
        });
    });
  }

  /**
   * 获取发送图片 ID
   */
  public getCurrnetImgId(): number {
    return this._currentImgSendID;
  }

  /**
   * 发送群组图片消息
   * @param fileObj
   * @param fileUrl
   * @param progressCallback
   * @param dataExt 扩展数据，透传
   */
  public sendGroupFileMessage(
    fileObj: any,
    fileUrl: string,
    progressCallback: TSendProgressCallback,
    dataExt?: any,
  ): Promise<TIMMsg> {
    const msgSeq = (this._msgList.length + 1);
    const msgTime = TUtil.getTimestamp() / 1000;
    console.log('tim_sendGroupFileMessage', `groupId: ${this._chatGroupId}, msgSeq: ${msgSeq}, msgTime: ${msgTime}`, dataExt);
    const targetFile = fileObj?.files?.[0] ?? fileObj;
    return new Promise((resolve, reject) => {
      if (!this.checkIMLogin()) {
        resolve(null);
        this._info('tim_sendGroupFileMessage', 'sdk not ready');
        return;
      }
      this._currentImgSendID = msgSeq;
      const msg: TIMMsg = {
        id: '',
        seq: this._currentImgSendID,
        from: this._userId,
        to: this._chatGroupId,
        time: msgTime,
        convType: JSTIM.TYPES.CONV_GROUP.toLowerCase() as any,
        msgType: TIMMsgType.FileMessage,
        data: fileUrl,
        dataExt,
        ext: targetFile ? targetFile.size.toString() : '0',
        desc: targetFile?.name ?? 'File',
        tipsMsg: true,
        localImgSeq: this._currentImgSendID,
        imgSendSucessFlag: false,
      };
      const msgList: TIMMsg[] = [];
      msgList.push(msg);
      this._sendMsgHandler(msgList);

      // ios 添加模拟进度
      let simTimer: any = null;
      if (TSession.instance.isIOSNative()) {
        let simProgress = 0;
        simTimer = setInterval(() => {
          progressCallback(this._currentImgSendID, simProgress);
          if (simProgress < 0.9) {
            simProgress += (Math.random() > 0.5 ? 0.1 : 0.0);
          } else {
            simProgress = 0.99;
          }
        }, 500);
      }
      this._tim.sendMessage(this._tim.createFileMessage({
        to: this._chatGroupId,
        conversationType: JSTIM.TYPES.CONV_GROUP,
        payload: {
          file: targetFile,
        },
        cloudCustomData: dataExt ? JSON.stringify(dataExt) : '',
        priority: JSTIM.TYPES.MSG_PRIORITY_NORMAL,
        onProgress: (progress: any) => {
          if (!TSession.instance.isIOSNative()) { // ios忽略避免干扰
            progressCallback(this._currentImgSendID, progress);
          }
        },
      }))
        .then((rsp: any) => {
          console.log('tim_sendGroupFileMessage success', rsp);
          this._stats.groupMsgItem.total += 1;
          this._stats.groupMsgItem.succ += 1;
          let sendTime;
          let localFileUrl;
          if (simTimer) { // ios关闭模拟进度
            progressCallback(this._currentImgSendID, 1);
            clearInterval(simTimer);
          }
          const index = this._msgList.findIndex(item => item.localImgSeq === this._currentImgSendID);
          if (index !== -1) {
            localFileUrl = this._msgList[index].data;
            sendTime = this._msgList[index].time;
            const deleteMsgs = this._msgList.splice(index, 1);
            TEvent.instance.notify(TMainEvent.Delete_IM_Msgs, deleteMsgs, false);
          } else {
            sendTime = TUtil.getTimestamp() / 1000;
          }
          const { downloadFlag, fileName, fileSize, fileUrl, uuid } = rsp.data.message.payload;
          const msgData = fileUrl;
          const msg: TIMMsg = {
            id: '',
            seq: rsp.data.message.sequence,
            from: this._userId,
            to: this._chatGroupId,
            time: rsp.data.message.time,
            convType: JSTIM.TYPES.CONV_GROUP.toLowerCase() as any,
            msgType: TIMMsgType.FileMessage,
            data: fileUrl,
            dataExt,
            ext: fileSize.toString(),
            desc: fileName,
            preview1: localFileUrl,
            tipsMsg: true,
            localImgSeq: this._currentImgSendID,
            imgSendSucessFlag: true,
          };
          console.log('tim_sendGroupFileMessage success, localMsg', msg);
          const msgList: TIMMsg[] = [];
          msgList.push(msg);
          this._sendMsgHandler(msgList);
          resolve(msg);
        })
        .catch((err: any) => {
          this._stats.groupMsgItem.total += 1;
          if (simTimer) { // ios关闭模拟进度
            clearInterval(simTimer);
          }
          this._error('tim_sendGroupFileMessage', `code: ${err.code}, message: ${err.message}`);
          reject(err);
        });
    });
  }

  /**
   * 发送群组图片消息
   * @param fileObj
   * @param fileUrl
   * @param reSendImgMsg
   * @param progressCallback
   * @param dataExt 扩展数据，透传
   */
  public sendGroupImgMessage(
    fileObj: any,
    fileUrl: string,
    reSendImgMsg: any,
    progressCallback: TSendProgressCallback,
    dataExt?: any,
    priority?: TencentCloudChat.TYPES,
  ): Promise<TIMMsg> {
    const isReSend = reSendImgMsg !== null && typeof reSendImgMsg === 'object';
    const msgSeq = isReSend ? reSendImgMsg.localImgSeq : (this._msgList.length + 1);
    const msgTime = isReSend ? reSendImgMsg.time : (TUtil.getTimestamp() / 1000);
    console.log('tim_sendGroupImgMessage', `groupId: ${this._chatGroupId}, isReSend ${isReSend}, msgSeq: ${msgSeq}, msgTime: ${msgTime}`, dataExt);
    return new Promise((resolve, reject) => {
      if (!this.checkIMLogin()) {
        resolve(null);
        this._info('tim_sendGroupImgMessage', 'sdk not ready');
        return;
      }
      if (isReSend) {
        const index = this._msgList.findIndex(item => item.localImgSeq === reSendImgMsg.localImgSeq);
        if (index !== -1) {
          const deleteMsgs = this._msgList.splice(index, 1);
          TEvent.instance.notify(TMainEvent.Delete_IM_Msgs, deleteMsgs, false);
        }
      }
      this._currentImgSendID = msgSeq;
      const msg: TIMMsg = {
        id: '',
        seq: this._currentImgSendID,
        from: this._userId,
        to: this._chatGroupId,
        time: msgTime,
        convType: JSTIM.TYPES.CONV_GROUP.toLowerCase() as any,
        msgType: TIMMsgType.ImgMessage,
        data: fileUrl,
        dataExt,
        ext: '',
        desc: '',
        preview1: fileUrl,
        preview2: fileUrl,
        tipsMsg: true,
        localImgSeq: this._currentImgSendID,
        imgSendSucessFlag: false,
      };
      const msgList: TIMMsg[] = [];
      msgList.push(msg);
      this._sendMsgHandler(msgList);

      // ios 添加模拟进度
      let simTimer: any = null;
      if (TSession.instance.isIOSNative()) {
        let simProgress = 0;
        simTimer = setInterval(() => {
          progressCallback(this._currentImgSendID, simProgress);
          if (simProgress < 0.9) {
            simProgress += (Math.random() > 0.5 ? 0.1 : 0.0);
          } else {
            simProgress = 0.99;
          }
        }, 500);
      }
      this._tim.sendMessage(this._tim.createImageMessage({
        to: this._chatGroupId,
        conversationType: JSTIM.TYPES.CONV_GROUP,
        payload: {
          file: fileObj,
        },
        cloudCustomData: dataExt ? JSON.stringify(dataExt) : '',
        priority: priority ?? JSTIM.TYPES.MSG_PRIORITY_NORMAL,
        onProgress: (progress: any) => {
          if (!TSession.instance.isIOSNative()) { // ios忽略避免干扰
            progressCallback(this._currentImgSendID, progress);
          }
        },
      }))
        .then((rsp: any) => {
          console.log('tim_sendGroupImgMessage success', rsp);
          this._stats.groupMsgItem.total += 1;
          this._stats.groupMsgItem.succ += 1;
          let msgData;
          let msgPreview1;
          let msgPreview2;
          let localImgUrl;
          let sendTime;
          if (simTimer) { // ios关闭模拟进度
            progressCallback(this._currentImgSendID, 1);
            clearInterval(simTimer);
          }
          const index = this._msgList.findIndex(item => item.localImgSeq === this._currentImgSendID);
          if (index !== -1) {
            localImgUrl = this._msgList[index].data;
            sendTime = this._msgList[index].time;
            const deleteMsgs = this._msgList.splice(index, 1);
            TEvent.instance.notify(TMainEvent.Delete_IM_Msgs, deleteMsgs, false);
          } else {
            sendTime = TUtil.getTimestamp() / 1000;
          }
          const { imageInfoArray } = rsp.data.message.payload;
          switch (imageInfoArray.length) {
            case 1: {
              msgData = imageInfoArray[0].imageUrl;
            }
              break;
            case 2: {
              msgData = imageInfoArray[0].imageUrl;
              msgPreview1 = imageInfoArray[1].imageUrl;
            }
              break;
            case 3: {
              msgData = imageInfoArray[0].imageUrl;
              msgPreview1 = imageInfoArray[1].imageUrl;
              msgPreview2 = imageInfoArray[2].imageUrl;
            }
              break;
          }
          const msg: TIMMsg = {
            id: '',
            seq: rsp.data.message.sequence,
            from: this._userId,
            to: this._chatGroupId,
            time: rsp.data.message.time,
            convType: JSTIM.TYPES.CONV_GROUP.toLowerCase() as any,
            msgType: TIMMsgType.ImgMessage,
            data: localImgUrl,
            dataExt,
            ext: '',
            desc: '',
            preview1: msgData,
            preview2: msgPreview1,
            tipsMsg: true,
            localImgSeq: this._currentImgSendID,
            imgSendSucessFlag: true,
          };
          console.log('tim_sendGroupImgMessage success, localMsg', msg);
          const msgList: TIMMsg[] = [];
          msgList.push(msg);
          this._sendMsgHandler(msgList);
          resolve(msg);
        })
        .catch((err: any) => {
          this._stats.groupMsgItem.total += 1;
          if (simTimer) { // ios关闭模拟进度
            clearInterval(simTimer);
          }
          this._error('tim_sendGroupImgMessage', `code: ${err.code}, message: ${err.message}`);
          reject(err);
        });
    });
  }

  public callExperimentalAPI(key: string, val: any) {
    return this._tim.callExperimentalAPI(key, val);
  }

  /**
   * 发送群组自定义消息
   * @param ext 扩展标识
   * @param data 内容
   */
  public sendGroupCustomMessage(
    ext: string, data: string,
    priority?: TencentCloudChat.TYPES,
  ): Promise<TIMMsg> {
    this._info('tim_sendGroupCustomMessage', `groupId: ${this._cmdGroupId}, ext: ${ext}, data: ${data}`);
    return new Promise((resolve, reject) => {
      if (!this.checkIMLogin()) {
        resolve(null);
        this._error('tim_sendGroupCustomMessage', 'sdk not ready');
        return;
      }

      const sender = new TIMMsgSender(
        this._tim.createCustomMessage({
          to: this._cmdGroupId,
          conversationType: JSTIM.TYPES.CONV_GROUP,
          payload: {
            data,
            extension: ext,
          },
          priority: priority ?? JSTIM.TYPES.MSG_PRIORITY_HIGH, // 自定义消息，优先级高
        }),
        (rsp: any) => {
          this._stats.groupCmdItem.total += 1;
          this._stats.groupCmdItem.succ += 1;
          const msgList = this._convertMsg(rsp.data.message);
          this._sendCustomMsgHandler(msgList);
          resolve(msgList[0]);
        },
        (err: any) => {
          this._stats.groupCmdItem.total += 1;
          this._error('tim_sendGroupCustomMessage', `code: ${err.code}, message: ${err.message}`);
          reject(err);
        },
      );
      this._trySendMsg(sender, this._customMsgSenderList);
    });
  }

  /**
   * 发送C2C文本消息
   * @param userId 用户ID
   * @param text 文本内容
   */
  public sendC2CTextMessage(userId: string, text: string): Promise<TIMMsg> {
    // this._debug("tim_sendC2CTextMessage", `userId: ${userId}, msg: ${text}`);
    return new Promise((resolve, reject) => {
      if (!this.checkIMLogin()) {
        resolve(null);
        this._info('tim_sendC2CTextMessage', 'sdk not ready');
        return;
      }
      this._tim.sendMessage(this._tim.createTextMessage({
        to: userId,
        conversationType: JSTIM.TYPES.CONV_C2C,
        payload: {
          text,
        },
      })).then((rsp: any) => {
        this._stats.c2cMsgItem.total += 1;
        this._stats.c2cMsgItem.succ += 1;
        const msgList = this._convertMsg(rsp.data.message);
        this._sendMsgHandler(msgList);
        resolve(msgList[0]);
      })
        .catch((err: any) => {
          this._stats.c2cMsgItem.total += 1;
          this._error('tim_sendC2CTextMessage', `code: ${err.code}, message: ${err.message}`);
          reject(err);
        });
    });
  }

  /**
   * 发送C2C自定义消息
   * @param userId  用户ID
   * @param ext 扩展标识
   * @param data 内容
   */
  public sendC2CCustomMessage(userId: string, ext: string, data: string): Promise<TIMMsg> {
    this._info('tim_sendC2CCustomMessage', `userId: ${userId}, ext: ${ext}, data: ${data}`);
    return new Promise((resolve, reject) => {
      if (!this.checkIMLogin()) {
        resolve(null);
        this._info('tim_sendC2CCustomMessage', 'sdk not ready');
        return;
      }
      this._tim.sendMessage(this._tim.createCustomMessage({
        to: userId,
        conversationType: JSTIM.TYPES.CONV_C2C,
        payload: {
          data,
          extension: ext,
        },
      })).then((rsp: any) => {
        this._stats.c2cCmdItem.total += 1;
        this._stats.c2cCmdItem.succ += 1;
        const msgList = this._convertMsg(rsp.data.message);
        this._sendCustomMsgHandler(msgList);
        resolve(msgList[0]);
      })
        .catch((err: any) => {
          this._stats.c2cCmdItem.total += 1;
          this._error('tim_sendC2CCustomMessage', `code: ${err.code}, message: ${err.message}`);
          reject(err);
        });
    });
  }

  /**
   * 拉取消息列表
   * @param groupId 群组id
   * @param count 消息数量
   * @param nextMsgId 起始消息id
   */
  public getMessageList(groupId: string, count: number, nextMsgId: string = null): Promise<TIMMsgList> {
    this._debug('tim_getMessageList', `convId: ${groupId}, count: ${count}, nextMsgId: ${nextMsgId}`);
    const option: any = {
      conversationID: `GROUP${groupId}`,
      count,
    };
    if (nextMsgId) {
      option.nextReqMessageID = nextMsgId;
    }
    return new Promise<TIMMsgList>((resolve, reject) => {
      this._tim.getMessageList(option).then((rsp: any) => {
        const msgList = new TIMMsgList();
        msgList.isCompleted = rsp.data.isCompleted;
        msgList.nextMsgId = rsp.data.nextReqMessageID;
        msgList.msgList = [];
        for (const msgInfo of rsp.data.messageList) {
          // 转成一样的格式，方便统一处理
          const msg = this._convertSingleIMMsgElement(msgInfo, {
            type: msgInfo.type,
            content: msgInfo.payload,
          });
          if (!msg) {
            continue;
          }
          // TODO 为什么这里不处理 ImgMessage ？
          if (msg.msgType === TIMMsgType.ImgMessage || msg.msgType === TIMMsgType.FileMessage) {
            continue;
          }
          msgList.msgList.push(msg);
        }
        resolve(msgList);
      })
        .catch((err: any) => {
          reject(err);
        });
    });
  }

  /**
   * 标记指定消息为已读消息
   * @param msgSeq 消息序列号
   */
  public markMessageAsRead(msgSeq: number) {
    if (this._unreadSeqSet && this._unreadSeqSet.has(msgSeq)) {
      this._unreadSeqSet.delete(msgSeq);
      this._updateMessageUnreadCount();
    }
  }

  /**
   * 标记所有消息为已读消息
   */
  public markAllMessagesAsRead() {
    if (this._unreadSeqSet) {
      this._unreadSeqSet.clear();
      this._updateMessageUnreadCount();
    }
  }

  /**
   * 添加消息到消息列表
   * @param msg 要添加的消息
   * @param notifyQuickMsg
   */
  public addMsgToList(msg: TIMMsg, notifyQuickMsg = true) {
    return new Promise((resolve) => {
      TState.instance.promiseState(TMainState.Joined_Class, true)
        .then(async () => {
          let uniqueSeq = true;  // 用于记录消息的seq是否唯一
          const msgListLastIndex = this._msgList.length - 1;
          if (!msg.seq) {
            msg.tipsMsg = true;
            if (msgListLastIndex >= 0) {
              msg.seq = this._msgList[msgListLastIndex].seq;
              if (!msg.time) {
                msg.time = this._msgList[msgListLastIndex].time;
              }
            } else {
              msg.seq = 0;
              msg.time = Math.ceil(TMain.instance.getServerTimestamp() / 1000);
            }
          } else {
            const findIndex = this._msgList.findIndex(item => !item.tipsMsg && item.seq === msg.seq);
            if (findIndex === -1) {
              uniqueSeq = true;
            } else {
              if (this._msgList[findIndex].msgType === TIMMsgType.ImgMessage
                  || this._msgList[findIndex].msgType === TIMMsgType.FileMessage) {
                uniqueSeq = true;
              } else {
                uniqueSeq = false;
              }
              if (msg.msgType === TIMMsgType.ImgMessage || msg.msgType === TIMMsgType.FileMessage) {
                uniqueSeq = true;
              }
            }
          }

          // 组合一个全局唯一的key
          const now = new Date().getTime();
          const currentUserId = TSession.instance.getUserId();
          msg.key = `${msg.seq}_${now}_${Math.ceil(Math.random() * 9999999)}`;
          if (msg.tipsMsg || uniqueSeq) {  // 自带seq的消息按seq去重
            msg = Object.assign(msg, {
              ownMsg: msg.from === currentUserId,
              teacherMsg: msg.from === TSession.instance.getClassInfo().teacherId,
              assistantMsg: TSession.instance.getClassInfo().assistants.includes(msg.from),
              silenceMsg: false,
            });
            // 巡课消息过滤
            if (TSession.instance.isSupervisor() && msg.from === currentUserId) {
              console.log('ignore msg from supervisor', msg);
              resolve(null);
              return;
            }
            // 如果有发送者，就去尝试获取发送者的用户信息
            if (msg.from && !msg.nickname) {
              let result: TUserInfo;
              let retryTimes = 10;
              while (retryTimes > 0) {  // 重试获取用户信息
                try {
                  result = await TMain.instance.getUserInfo(msg.from);
                  break;
                } catch (e) {  // 获取用户信息失败，1秒后重试
                  await new Promise(r => setTimeout(r, 1000));
                  retryTimes -= 1;
                }
              }
              msg = Object.assign(msg, {
                nickname: result.nickname,
                avatar: result.avatar ? result.avatar : '',
              });
            }
            // 录制机器人和tic_push的消息不处理，注意要在获取nickname之后
            const isRecord = (/^(tic_push_user|webrecord_[\d_]+)/ig.test(msg.nickname) || /^(tic_push_user|webrecord_[\d_]+)/ig.test(msg.from));
            if (isRecord) {
              console.log('ignore msg from record', msg);
              resolve(null);
              return;
            }

            this._pendingMsgList.push(msg);
            // 创建一个100ms的节流函数，防止消息频发带来的性能损耗
            if (!this._handlePendingMsgListFun) {
              this._handlePendingMsgListFun = Lodash.throttle(
                () => this._handlePendingMsgList(this._latestNotifyQuickMsg),
                300,
                {
                  leading: true,
                  trailing: true,
                },
              );
            }
            this._latestNotifyQuickMsg = notifyQuickMsg;
            this._handlePendingMsgListFun();

            // 本地通知的语言检查
            if (msg.convType === TIMConvType.Tips) {
              const language = TMain.instance.getLanguage();
              if (!/zh(-\w+)?/g.test(language) && /[\u4e00-\u9fa5]+/g.test(msg.data)) {
                // 不是中文，如果内容中出现了中文就上报
                TMain.instance.reportLog('messageLanguageError', `tim.addMsgToList, ${msg.convType} ${msg.msgType}: ${msg.data}`);
              }
            }

            resolve(null);
          }
        });
    });
  }

  /**
   * 拉取公开课最近20条消息列表
   */
  public getClassMessageList(): Promise<TIMMsgList> {
    return TMain.instance.getClassMessageData()
      .then(async (historyMsgList: TClassHistroyMessage) => {
        const msgList = new TIMMsgList();
        msgList.msgList = [];
        for (let index = historyMsgList.message.length - 1; index >= 0; index--) {
          // 不能直接用 _convertSingleIMMsgElement，云API返回的字段名不一样。。。
          const msgInfo = JSON.parse(historyMsgList.message[index]);
          const msg = this._convertSingleHistoryMsgElement(msgInfo);
          // 更新最大seq
          this._updateChatGroupLastMsgSeq(msg.seq);
          await this.addMsgToList(msg, false); // 添加至消息列表
        }
        return Promise.resolve(msgList);
      });
  }

  /**
   * 从消息列表删除消息
   * @param msg 要删除的消息
   */
  public removeMsgFromList(msg: TIMMsg) {
    let index = 0;
    while (index < this._msgList.length) {
      if ((!this._msgList[index].tipsMsg && this._msgList[index].seq === msg.seq)
        || (this._msgList[index].msgType === msg.msgType
          && (TIMMsgType.ImgMessage === msg.msgType || TIMMsgType.FileMessage === msg.msgType)
          && this._msgList[index].seq === msg.seq)) {
        const deleteMsgs = this._msgList.splice(index, 1);
        TEvent.instance.notify(TMainEvent.Delete_IM_Msgs, deleteMsgs, false);
        // 更新消息列表状态
        TState.instance.setState(TMainState.IM_Msg_List, this._msgList, this.constructor.name, false);
        break;
      } else {
        index += 1;
      }
    }
  }

  /**
   * 修改消息列表内指定用户的禁言状态
   * @param userId 要修改状态的用户ID
   * @param silence 目标禁言状态
   */
  public silenceUserOnMsgList(userId: string, silence: boolean) {
    const updateMsgs: TIMMsg[] = [];
    this._msgList.forEach((item) => {
      if (item.from === userId) {
        item.silenceMsg = silence;
        updateMsgs.push(item);
      }
    });
    TEvent.instance.notify(TMainEvent.Update_IM_Msgs, updateMsgs, false);
    // 更新消息列表状态
    TState.instance.setState(TMainState.IM_Msg_List, this._msgList, this.constructor.name, false);
  }

  /**
   * 获取课堂默认群组的消息列表
   */
  public getIMMsgList(): TIMMsg[] {
    return this._msgList;
  }

  /**
   * 设置快捷回复词条
   * @param wordsArr 快捷回复词条数组
   */
  public setQuickIMWords(wordsArr: string[]) {
    this._quickIMWords = wordsArr || [];
    TMain.instance.notify(TIMEvent.Set_Quick_IM_Words, wordsArr);
    this._info(TIMEvent.Set_Quick_IM_Words, `quickIMWords is ${wordsArr}`);
  }

  /**
     * 获取快捷回复词条
     */
  public getQuickIMWords(): string[] | [] {
    return this._quickIMWords;
  }

  // 获取类名
  protected _getClassName() {
    return 'TIM';
  }

  private _trySendMsg(sender: TIMMsgSender, senderList: TIMMsgSender[], rightNow = false) {
    if (rightNow || senderList.length === 0) {  // 队列里没有消息，或者明确指定需要马上发送
      if (TState.instance.getState(TMainState.IM_Cmd_Ready, false)) {  // IM未准备就绪前不发送
        sender.retryTimes += 1;  // 重试计数+1
        this._tim.sendMessage(sender.msg)
          .then(sender.resolve)  // 发送成功，触发回调
          .catch((error: any) => {  // 发送失败，放回队列
            if (sender.retryTimes < 5) {  // 最多重试5次
              TIM._addMsgSenderToQueue(sender, senderList);
            } else {  // 超过5次后，打印日志并回调失败
              this._error('tim_trySendMsg', `code: ${error.code}, message: ${error.message}`);
              sender.reject(error);
              // 2801 请求超时
              if (error && error.code && error.code === 2801) {
                const msg = i18next.t('网络异常，请检查网络后，重新进入{{arg_0}} ({{statusCode}})', { arg_0: '', statusCode: error.code || '' });
                TEvent.instance.notify(TMainEvent.Tim_Error_Timeout, new TCICError(-1, msg, JSON.stringify(error)));
              }
            }
          });
        return;
      }
    }
    TIM._addMsgSenderToQueue(sender, senderList);
  }

  private _updateCmdGroupLastMsgSeq(seq: number) {
    if (seq > this._cmdGroupLastMsgSeq) {
      this._cmdGroupLastMsgSeq = seq;
    }
  }

  private _updateChatGroupLastMsgSeq(seq: number) {
    if (seq > this._chatGroupLastMsgSeq) {
      this._chatGroupLastMsgSeq = seq;
    }
  }
  private logUpload() {
    try {
      const htmls = document.getElementsByTagName('html');
      if (htmls.length) {
        TCIC.SDK.instance.reportLog('GetUserbodyInnerHtml', htmls[0].innerHTML);
        // 把iframe里的数据也上报过来
        const iframes = document.getElementsByTagName('iframe');
        for (let i = 0; i < iframes.length; i++) {
          const frame = iframes[i];
          const attr = Array.from(frame.attributes).reduce((obj: any, attr) => {
            obj[attr.name] = attr.value;
            return obj;
          }, {});
          TCIC.SDK.instance.reportLog('GetUserbodyFrameSrc', JSON.stringify(attr));
          try {
            const frameDocument = frame.contentDocument || frame.contentWindow.document;
            const framehtmls = frameDocument.getElementsByTagName('html');
            if (framehtmls.length) {
              TCIC.SDK.instance.reportLog('GetUserbodyFrameInnerHtml', framehtmls[0].innerHTML);
            }
          } catch (err) {
            TCIC.SDK.instance.reportException('GetUserbodyInnerHtmlError', err.toString());
          }
        }
        // @ts-ignore
        window.showToast(i18next.t('上传成功'));
      }
    } catch (err) {
      TCIC.SDK.instance.reportException('GetUserbodyInnerHtmlError', err.toString());
    }
  }
  private iframeLogUpload() {
    try {
      // @ts-ignore
      const ifa = document.getElementsByClassName('tic_iframe_H5')[0].contentWindow;
      if (ifa != null) {
        ifa.postMessage('getTCICReportData');
      }
    } catch (err) {

    }
  }
  private collectLogs(msg: TIMMsg) {
    // 仅处理来自于管理员的提取日志的自定义消息
    if (msg.from == '') {
      if (msg.data == 'collectLogs') {
        this.logUpload();
        this.iframeLogUpload();
      }
    }
  }

  private _afterRecvMsgList(msgList: TIMMsg[]) {
    msgList.forEach((msg) => {
      if (msg.convType === TIMConvType.Group) {
        if (msg.to === this._chatGroupId
            && this.checkIfChatMessage(msg)) {
          // 对群聊天消息统计未读计数
          // 限制未读消息计数集合最大存储为1000，避免业务侧不调用消息已读标记接口导致集合大小无限膨胀
          if (this._unreadSeqSet.size < 1000) {
            this._unreadSeqSet.add(msg.seq);
          }

          // 更新最大seq
          this._updateChatGroupLastMsgSeq(msg.seq);
        } else if (msg.to === this._cmdGroupId) {
          // 更新最大seq
          this._updateCmdGroupLastMsgSeq(msg.seq);
        } else {
          return;  // 群组消息只接收聊天群组或信令群组的消息
        }
      } else {
        if (msg.msgType === 'custom') {
          TEvent.instance.notify(TIMEvent.Recv_C2C_Custom_Msg, msg);
        }
      }
      // 普通聊天消息和自定义消息分开回调
      if (this.checkIfChatMessage(msg)) {
        // 添加到消息列表
        this.addMsgToList(msg);
        // 如果开启了日志打印开关，打印日志（主要供自动化测试断言用）
        TEvent.instance.notify(TIMEvent.Recv_Msg, msg, this._recvTextLogEnabled);
      } else {
        // 如果开启了日志打印开关，打印日志（主要供自动化测试断言用）
        TEvent.instance.notify(TIMEvent.Recv_Custom_Msg, msg, this._recvCustomLogEnabled);
        this.collectLogs(msg);
      }

      // 业务侧可能在收到消息回调的时候直接标记消息为已读，因此更新已读计数状态放到回调后面执行避免计数抖动
      this._updateMessageUnreadCount();
    });
  }

  private _recvMsgHandler(event: any) {
    this._afterRecvMsgList(this._convertMsgList(event.data));
  }

  private _revokedMsgHandler(event: any) {
    const msgList = this._convertMsgList(event.data);
    msgList.forEach((msg) => {
      // 群组消息只接收聊天群组或信令群组的消息
      if (msg.convType === TIMConvType.Group) {
        if (msg.to !== this._chatGroupId && msg.to !== this._cmdGroupId) {
          return;
        }
      }

      // 被撤回的消息也要标记为已读
      this.markMessageAsRead(msg.seq);

      // 普通聊天消息和自定义消息分开回调
      if (this.checkIfChatMessage(msg)) {
        // 从消息列表删除
        this.removeMsgFromList(msg);
        TEvent.instance.notify(TIMEvent.Revoked_Msg, msg, false);
      } else {
        TEvent.instance.notify(TIMEvent.Revoked_Custom_Msg, msg, false);
      }
    });
  }

  private _handleOpenClassDelete(msg: TIMMsg) {
    const deleteMsg = JSON.parse(msg.data);
    // 被撤回的消息也要标记为已读
    this.markMessageAsRead(deleteMsg.data.seq);
    let index = 0;
    while (index < this._msgList.length) {
      if (this._msgList[index].seq === deleteMsg.data.seq) {
        const deleteMsgs = this._msgList.splice(index, 1);
        TEvent.instance.notify(TMainEvent.Delete_IM_Msgs, deleteMsgs, false);
        // 更新消息列表状态
        TState.instance.setState(TMainState.IM_Msg_List, this._msgList, this.constructor.name, false);
        break;
      } else {
        index += 1;
      }
    }
    TEvent.instance.notify(TIMEvent.Revoked_Msg, msg, false);
  }

  private _sendMsgHandler(msgList: TIMMsg[]) {
    msgList.forEach((msg) => {
      // 添加到消息列表
      this.addMsgToList(msg);
      // 如果开启了日志打印开关，打印日志（主要供自动化测试断言用）
      TEvent.instance.notify(TIMEvent.Send_Msg, msg, this._sendTextLogEnabled);
    });
  }

  private _sendCustomMsgHandler(msgList: TIMMsg[]) {
    msgList.forEach((msg) => {
      // 如果开启了日志打印开关，打印日志（主要供自动化测试断言用）
      TEvent.instance.notify(TIMEvent.Send_Custom_Msg, msg, this._sendCustomLogEnabled);
    });
  }

  private _convertMsgList(imMsgList: any): TIMMsg[] {
    const msgList: TIMMsg[] = [];
    imMsgList.forEach((imMsg: any) => {
      const msgSlice = this._convertMsg(imMsg);
      msgList.push(...msgSlice);
    });
    return msgList;
  }

  private _convertMsg(imMsg: any): TIMMsg[] {
    const msgList: TIMMsg[] = [];
    const elements = imMsg.getElements();
    elements.forEach((elem: any) => {
      const msg = this._convertSingleIMMsgElement(imMsg, elem);
      if (!msg) {
        return;
      }
      // 自定义的公开课撤回消息不插入直接删除消息列表中对应seq的msg
      if (msg.msgType === TIMMsgType.Custom && msg.ext === 'CHAT') {
        this._handleOpenClassDelete(msg);
      } else {
        msgList.push(msg);
      }
    });
    return msgList;
  }

  private _convertSingleIMMsgElement(imMsg: any, elem: any): TIMMsg {
    const msg: TIMMsg = {
      id: imMsg.ID,
      seq: imMsg.sequence,
      from: imMsg.from,
      to: imMsg.to,
      time: imMsg.time,
      nickname: imMsg.nick,
      avatar: imMsg.avatar,
      convType: TIMConvType.Unknown,
      msgType: TIMMsgType.Unknown,
      data: '',
      dataExt: null,
      ext: '',
      desc: '',
      preview1: '',
      preview2: '',
    };
    const convType = imMsg.conversationType;
    if (convType === JSTIM.TYPES.CONV_GROUP) {
      msg.convType = TIMConvType.Group;
    } else if (convType === JSTIM.TYPES.CONV_C2C) {
      msg.convType = TIMConvType.C2C;
    } else {
      console.warn('tim_convertSingleIMMsgElement, unknown convType', imMsg.conversationType, imMsg, elem);
      return;
    }
    const elemType = elem.type;
    if (elemType === JSTIM.TYPES.MSG_TEXT) {
      msg.msgType = TIMMsgType.Text;
      msg.data = elem.content.text;
    } else if (elemType === JSTIM.TYPES.MSG_CUSTOM) {
      msg.msgType = TIMMsgType.Custom;
      msg.data = elem.content.data;
      msg.ext = elem.content.extension;
      msg.desc = elem.content.description;
    } else if (elemType === JSTIM.TYPES.MSG_FILE) {
      msg.msgType = TIMMsgType.FileMessage;
      msg.data = elem.content.fileUrl;
      msg.ext = elem.content.fileSize.toString();
      msg.desc = elem.content.fileName;
    } else if (elem.type === JSTIM.TYPES.MSG_IMAGE) {
      msg.msgType = TIMMsgType.ImgMessage;
      switch (elem.content.imageInfoArray.length) {
        case 1: {
          msg.data = elem.content.imageInfoArray[0].url;
        }
          break;
        case 2: {
          msg.data = elem.content.imageInfoArray[0].url;
          msg.preview1 = elem.content.imageInfoArray[1].url;
        }
          break;
        case 3: {
          msg.data = elem.content.imageInfoArray[0].url;
          msg.preview1 = elem.content.imageInfoArray[1].url;
          msg.preview2 = elem.content.imageInfoArray[2].url;
        }
          break;
      }
    } else {
      console.warn('tim_convertSingleIMMsgElement, unknown msgType', elem.type, imMsg, elem);
      return;
    }
    if (imMsg.cloudCustomData) {
      try {
        msg.dataExt = JSON.parse(imMsg.cloudCustomData);
      } catch (err) {
        console.warn('tim_convertSingleIMMsgElement, parse cloudCustomData error', imMsg.cloudCustomData, imMsg, elem);
      }
    }
    // console.log('tim_convertSingleIMMsgElement', imMsg, elem, msg);
    return msg;
  }

  private _convertSingleHistoryMsgElement(historyMsg: any): TIMMsg {
    const msg: TIMMsg = {
      seq: historyMsg.MsgSeq,
      from: historyMsg.From_Account,
      to: historyMsg.Operator_Account,
      time: historyMsg.MsgTime,
      nickname: historyMsg.NickName,
      convType: TIMConvType.Group, // 历史消息都是 Group 消息
      msgType: TIMMsgType.Unknown,
      data: '',
      dataExt: null,
      ext: '',
      desc: '',
      preview1: '',
      preview2: '',
    };
    const elem = historyMsg.MsgBody[0];
    if (elem.MsgType === 'TIMTextElem') {
      msg.msgType = TIMMsgType.Text;
      msg.data = elem.MsgContent.Text;
    } else if (elem.MsgType === 'TIMFileElem') {
      msg.msgType = TIMMsgType.FileMessage;
      msg.data = elem.MsgContent.fileUrl ?? elem.MsgContent.Url ?? '';
      msg.desc = elem.MsgContent.fileName ?? elem.MsgContent.FileName ?? '';
      const fileSize = elem.MsgContent.fileSize ?? elem.MsgContent.FileSize;
      msg.ext = fileSize !== undefined && fileSize !== null ? fileSize.toString() : '';
    } else if (elem.MsgType === 'TIMImageElem') {
      msg.msgType = TIMMsgType.ImgMessage;
      switch (elem.MsgContent.ImageInfoArray.length) {
        case 1: {
          msg.data = elem.MsgContent.ImageInfoArray[0].URL;
        }
          break;
        case 2: {
          msg.data = elem.MsgContent.ImageInfoArray[0].URL;
          msg.preview1 = elem.MsgContent.ImageInfoArray[1].URL;
        }
          break;
        case 3: {
          msg.data = elem.MsgContent.ImageInfoArray[0].URL;
          msg.preview1 = elem.MsgContent.ImageInfoArray[1].URL;
          msg.preview2 = elem.MsgContent.ImageInfoArray[2].URL;
        }
          break;
      }
    }
    if (historyMsg.CloudCustomData) {
      try {
        msg.dataExt = JSON.parse(historyMsg.CloudCustomData);
      } catch (err) {
        console.warn('tim_convertSingleHistoryMsgElement, parse cloudCustomData error', historyMsg.CloudCustomData, historyMsg, elem);
      }
    }
    // console.log('tim_convertSingleHistoryMsgElement', historyMsg, elem, msg);
    return msg;
  }

  private _updateMessageUnreadCount() {
    TState.instance
      .setState(TMainState.Message_Unread_Count, this._unreadSeqSet.size, this.constructor.name);
  }

  private _fetchMsgList(
    groupId: string,
    msgSeqExclude: number,
    totalMsgList: TIMMsg[] = null,
    nextMsgId: string = null,
  ) {
    if (!totalMsgList) {
      totalMsgList = [];
    }
    let fetchCount;
    if (totalMsgList.length === 0) {  // 刚开始拉，第一次只拉一条
      fetchCount = 1;
    } else {
      fetchCount = Math.min(15, totalMsgList[0].seq - msgSeqExclude - 1);  // 只拉到需要的消息为止
    }
    this.getMessageList(groupId, fetchCount, nextMsgId)
      .then((msgList) => {
        for (let index = msgList.msgList.length - 1; index >= 0; index--) {  // 倒序处理
          if (msgList.msgList[index].seq > msgSeqExclude) {  // 序号大于排除消息序号
            totalMsgList.unshift(msgList.msgList[index]);  // 添加到最前面
          } else {
            msgList.isCompleted = true;  // 标记为已拉完
            break;
          }
        }
        if (totalMsgList[0].seq === msgSeqExclude + 1) {
          msgList.isCompleted = true;  // 标记为已拉完
        }

        if (msgList.isCompleted) {  // 拉取完成或者已拉到无效消息
          totalMsgList.sort((a, b) => a.seq - b.seq);  // 按seq排序
          this._afterRecvMsgList(totalMsgList);  // 处理收到的消息
        } else {  // 继续拉取
          this._fetchMsgList(groupId, msgSeqExclude, totalMsgList, msgList.nextMsgId);
        }
      })
      .catch((error) => {
        this._error('tim_fetchMsgList', `${error.toString()}`);
      });
  }

  private _handlePendingMsgList(notifyQuickMsg = true) {
    const num = this._msgList.length > 5 ? 5 : this._msgList.length;
    // 先将_msgList最后num个元素删除, 然后和_pendingMsgList合并后排序
    const toSortMsgs = this._pendingMsgList.concat(this._msgList.splice(this._msgList.length - num, num));
    const sortedMsgs = toSortMsgs.sort((item1, item2) => {
      if (item1.time !== item2.time) {
        return item1.time - item2.time;
      }
      return item1.seq - item2.seq;
    });
    if (TSession.instance.isIOS() && TSession.instance.isMobile()) {
      this._msgList = this._msgList.concat(sortedMsgs);
    } else {
      this._msgList.push(...sortedMsgs);
    }

    if (this._msgList.length > this._maxMsgListSize) {
      const deleteNum = this._msgList.length - this._maxMsgListSize;
      this._msgList.slice(0, deleteNum);
    }
    TState.instance.setState(TMainState.IM_Msg_List, this._msgList, this.constructor.name, false);
    if (notifyQuickMsg) {
      TEvent.instance.notify(TMainEvent.Recv_IM_Msgs, this._pendingMsgList, false);
    } else {
      TEvent.instance.notify(TMainEvent.Recv_IM_Msgs_Only_Message_List, this._pendingMsgList, false);
    }
    this._pendingMsgList = [];
  }
  // 事件监听
  private _initEventListener() {
    this._tim.on(TIM.EVENT.SDK_READY, () => {
      this._logEnded(costActionLabel, TLevel.INFO, {
        module: this._getClassName(),
        action: costActionLabel,
        param: 'tim logined',
      });
      TState.instance.setState(TMainState.IM_Logined, true, this.constructor.name);
      // AVChatroom 只允许有一个群组，此时后台返回的 2个群组ID为同一个。
      // 这里通过判断是否同一个群ID来决定是否加2次群。
      if (this._cmdGroupId === this._chatGroupId) {
        this.joinGroup(this._cmdGroupId)
          .then(() => {
            TState.instance.setState(TMainState.IM_Cmd_Ready, true, this.constructor.name);
            TState.instance.setState(TMainState.IM_Chat_Ready, true, this.constructor.name);
          })
          .catch((error: any) => {
            this._error('joinGroup_failed', TUtil.getErrorDetailMessage(error));
            TEvent.instance.notify(TMainEvent.Error, new TCICError(error.code, i18next.t('加入群组失败'), `${JSON.stringify(error)}`));
          });
      } else {
        // 加入群组
        this.joinGroup(this._cmdGroupId)
          .then(() => {
            TState.instance.setState(TMainState.IM_Cmd_Ready, true, this.constructor.name);
            this.getMessageList(this._cmdGroupId, 1).then((msgList) => {  // 拉取最后一条消息，确保记录seq
              if (msgList.msgList.length > 0) {
                this._updateCmdGroupLastMsgSeq(msgList.msgList[0].seq);
              }
            });
          })
          .catch((error: any) => {
            TEvent.instance.notify(TMainEvent.Error, new TCICError(error.code, i18next.t('加入信令群组失败'), `${JSON.stringify(error)}`));
          });
        this.joinGroup(this._chatGroupId)
          .then(() => {
            TState.instance.setState(TMainState.IM_Chat_Ready, true, this.constructor.name);
            this.getMessageList(this._chatGroupId, 1).then((msgList) => {  // 拉取最后一条消息，确保记录seq
              if (msgList.msgList.length > 0) {
                this._updateChatGroupLastMsgSeq(msgList.msgList[0].seq);
              }
            });
          })
          .catch((error: any) => {
            TEvent.instance.notify(TMainEvent.Error, new TCICError(error.code, i18next.t('加入信令群组失败'), `${JSON.stringify(error)}`));
          });
      }
    });

    this._tim.on(TIM.EVENT.SDK_NOT_READY, () => {
      const joinedClass = TState.instance.getState(TMainState.Joined_Class);
      if (joinedClass) {
        this._logEnded(costActionLabel, TLevel.ERROR, {
          module: this._getClassName(),
          action: costActionLabel,
          param: 'tim login failed',
        });
        this._error('tim_sdk_not_ready', '');
      } else {
        this._logEnded(costActionLabel, TLevel.INFO, {
          module: this._getClassName(),
          action: costActionLabel,
          param: 'tim login failed',
        });
        this._info('tim_sdk_not_ready', '');
      }
      TState.instance.setState(TMainState.IM_Logined, false, this.constructor.name);
    });

    this._tim.on(JSTIM.EVENT.MESSAGE_RECEIVED, this._recvMsgHandler, this);

    this._tim.on(JSTIM.EVENT.MESSAGE_REVOKED, this._revokedMsgHandler, this);

    this._tim.on(TIM.EVENT.MESSAGE_READ_BY_PEER, () => {
    });

    this._tim.on(TIM.EVENT.CONVERSATION_LIST_UPDATED, () => {
    });

    this._tim.on(TIM.EVENT.GROUP_LIST_UPDATED, () => {
    });

    this._tim.on(TIM.EVENT.PROFILE_UPDATED, () => {
    });

    this._tim.on(TIM.EVENT.BLACKLIST_UPDATED, () => {
    });

    this._tim.on(TIM.EVENT.KICKED_OUT, (event: any) => {
      this._warn('tim_kicked_out', `type: ${event.data.type}`);
      TState.instance.setState(TMainState.IM_Cmd_Ready, false, this.constructor.name);
      TState.instance.setState(TMainState.IM_Chat_Ready, false, this.constructor.name);
      TState.instance.setState(TMainState.IM_Logined, false, this.constructor.name);
      if (event && event.data && event.data.type === JSTIM.TYPES.KICKED_OUT_USERSIG_EXPIRED) {
        TState.instance.setState(TMainState.IM_SIG_EXPIRED, true, this.constructor.name);
      } else {
        TState.instance.setState(TMainState.IM_Kicked, true, this.constructor.name);
      }
    });

    this._tim.on(TIM.EVENT.ERROR, (event: any) => {
      const classInfo = TSession.instance.getClassInfo();
      const classStatus = classInfo?.status;
      if (classStatus === TClassStatus.Has_Ended || classStatus === TClassStatus.Has_Expired) {
        console.log('tim_error', `classStatus: ${classStatus} - ${event.data.code} - ${event.data.message}`);
      } else {
        this._error('tim_error', `classStatus: ${classStatus} - ${event.data.code} - ${event.data.message}`);
      }
    });

    this._tim.on(TIM.EVENT.NET_STATE_CHANGE, (event: any) => {
      this._info('tim_net_state_change', `${event.data.state}`);
      if (event.data.state === TIM.TYPES.NET_STATE_CONNECTED) {
        TCIC.SDK.instance.updateClassInfo().then(r => this._info('tim_net_state_change', `Class Info Updated.`));
      }
      // TODO 网络处理
    });

    // this._tim.on(TIM.EVENT.SDK_RELOAD, (event: any) => {
    //   this._warn('tim_reload', '');
    //   this._fetchMsgList(this._cmdGroupId, this._cmdGroupLastMsgSeq);  // 拉取信令消息
    //   this._fetchMsgList(this._chatGroupId, this._chatGroupLastMsgSeq);  // 拉取聊天消息
    // });
  }
  // IM 不支持消息记录，后台查最近20条。课堂结束后到处消息记录[ ]
  // 互动课因为群人数上限的原因
  // 必须使用AVChatRoom，由于IM侧的限制，只能加入1个必须使用AVChatRoom
  // 未读计数关注的是原ChatGroup对应的消息，因此这里多做一层判断，判断是否是聊天信息
  // 并且新增判断是否是发给自己的消息，适配私聊场景
  private checkIfChatMessage(msg: TIMMsg) {
    const msgType = msg.msgType;
    const isPrivateMsg = !!msg.dataExt?.IsPrivateMsg;
    const currentUserId = TSession.instance.getUserId();
    const forMe = isPrivateMsg ? msg.dataExt?.PrivateInfo?.From.ID === currentUserId
        || msg.dataExt?.PrivateInfo?.To.ID === currentUserId : true;
    return [TIMMsgType.ImgMessage,
      TIMMsgType.Text,
      TIMMsgType.FileMessage].includes(msgType)
        && forMe;
  }

  // 检测是否im已登陆
  private checkIMLogin() {
    return TState.instance.getState(TMainState.IM_Logined, false);
  }

  private imStatsReport() {
    const self = TIM.instance;
    if (self._stats) {
      self._stats.userId = self._userId;
      self._stats.classId = self._cmdGroupId;
      const stats = self._stats.serialize();
      self._reportEvent('im_stats', stats);
    }
    self._stats = new TIMStats();
  }
}
